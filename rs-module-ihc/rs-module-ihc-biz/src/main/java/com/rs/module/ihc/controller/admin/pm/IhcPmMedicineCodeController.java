package com.rs.module.ihc.controller.admin.pm;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.bsp.common.cache.RedisClient;
import com.rs.framework.common.cons.CommonConstants;
import com.rs.framework.common.cons.RedisConstants;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineCodeImportErrorVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineCodeImportVO;
import com.rs.module.ihc.listener.MedicineCodeListener;
import com.rs.module.ihc.service.pm.IhcPmMedicineCodeService;
import com.rs.module.ihc.task.pm.MedicineCodeImportTask;
import com.rs.util.DicUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@Slf4j
@RestController
@RequestMapping("/ihc/medicine/code")
public class IhcPmMedicineCodeController {

    @Value("${system-mark}")
    private String systemMark;

    @Autowired
    private IhcPmMedicineCodeService ihcPmMedicineCodeService;

    @Autowired
    private FileStorageService fileStorageService;


    @ApiOperation(value = "Excel文件数据导入")
    @PostMapping(value = "/importExcel")
    public CommonResult importExcel(MultipartFile file) {
        // 收集结果
        List<MedicineCodeImportErrorVO> errorList = new ArrayList<>();
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        try {
            List<MedicineCodeImportVO> list = new ArrayList<>();
            EasyExcel.read(file.getInputStream(),
                            MedicineCodeImportVO.class,
                            new MedicineCodeListener(list))
                    .sheet()
                    .headRowNumber(3)
                    .doRead();
            // 获取药品剂型字典
            //Map<String, String> dosageFormDic = RedisClient.hgetAll(RedisConstants.DIC_CODE_CACHE_KEY+ ":" + systemMark + ":" + CommonConstants.COM_DIC_NAME_DOSAGE_FORM);
            Map<String, String> dosageFormDic = DicUtils.getMap(CommonConstants.COM_DIC_NAME_DOSAGE_FORM);
            List<Future<MedicineCodeImportErrorVO>> futures = new ArrayList<>();
            // 提交任务到线程池
            for (MedicineCodeImportVO medicineCodeImportVO : list) {
                futures.add(executorService.submit(new MedicineCodeImportTask(medicineCodeImportVO, dosageFormDic, ihcPmMedicineCodeService)));
            }
            for (Future<MedicineCodeImportErrorVO> future : futures) {
                MedicineCodeImportErrorVO result = future.get();
                if (result != null) {
                    errorList.add(result);
                }
            }
        } catch (Exception e) {
            return CommonResult.error("导入药品数据失败，原因：" + e.getMessage());
        } finally {
            // 关闭线程池
            executorService.shutdown();
        }
        if (!errorList.isEmpty()) {
            FileInfo upload = null;
            File fileUpload = null;
            try {
                String fileName = System.getProperty("user.dir") + File.separator + file.getOriginalFilename();
                EasyExcel.write(fileName, MedicineCodeImportErrorVO.class)
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .sheet("失败结果").doWrite(errorList);
                fileUpload = new File(fileName);
                upload = fileStorageService.of(fileUpload)
                        .setSaveFilename(fileUpload.getName())
                        .upload();
            } finally {
                if (fileUpload != null) {
                    fileUpload.delete();
                }
            }

            return CommonResult.error(upload);
        }
        return CommonResult.success("导入药品数据成功");
    }


}
