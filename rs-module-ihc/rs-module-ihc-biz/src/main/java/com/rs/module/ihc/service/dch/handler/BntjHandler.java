package com.rs.module.ihc.service.dch.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.dao.pm.DataChangeEventLogDao;
import com.rs.module.base.entity.pm.DataChangeEventLogDO;

import com.rs.module.base.enums.DataChangeEventStatusEnum;
import com.rs.module.base.enums.DataChangeEventTypeEnum;
import com.rs.module.ihc.constant.IhcsHealthCheckupConstant;
import com.rs.module.ihc.entity.hc.HealthCheckupDO;
import com.rs.module.ihc.service.dch.context.DataChangeEventContext;
import com.rs.module.ihc.service.hc.HealthCheckupService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 半年体检
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Slf4j
@Component
@AllArgsConstructor
public class BntjHandler extends AbstractDataChangeEventHandler {
    public final HealthCheckupService healthCheckupService;
    private final DataChangeEventLogDao dataChangeEventLogDao;

    @Override
    public String getSupportedBusinessType() {
        return BntjHandler.class.getSimpleName();
    }

    @Override
    public int getPriority() {
        return 1;
    }

    @Override
    protected List<String> getSupportedTableNames() {
        return Arrays.asList(
                "acp_pm_prisoner_kss_in",
                "acp_pm_prisoner_jds_in",
                "acp_pm_prisoner_jls_in"
        );
    }

    @Override
    protected Set<DataChangeEventTypeEnum> getSupportedEventTypes() {
        return new HashSet<>(Arrays.asList(
                DataChangeEventTypeEnum.INSERT
        ));
    }

    @Override
    protected DataChangeEventHandlerResult doHandle(DataChangeEventContext context) {
        PrisonerVwRespVO inJgry = getInJgry(context);
        if (inJgry == null) {
            return DataChangeEventHandlerResult.success();
        }
        HealthCheckupDO checkupDO = HealthCheckupDO.builder()
                .jgrybm(context.getNewDataMap().get("jgrybm").toString())
                .checkupStatus(IhcsHealthCheckupConstant.CHECKUP_STATUS_DTJ)
                .build();
        healthCheckupService.save(checkupDO);
        log.info("半年体检新增处理完成");
        DataChangeEventLogDO rawData = context.getRawData();
        //生成新的半年体检
        DataChangeEventLogDO bntjBean = BeanUtil.toBean(rawData, DataChangeEventLogDO.class);
        bntjBean.setId(null);
        bntjBean.setStatus(DataChangeEventStatusEnum.PENDING);
        bntjBean.setEventTime(new Date());
        bntjBean.setExpectationExecuteDate(DateUtil.offsetMonth(new Date(), 6));
        bntjBean.setProcessStartTime(null);
        bntjBean.setProcessEndTime(null);
        dataChangeEventLogDao.insert(bntjBean);

        return DataChangeEventHandlerResult.success();
    }

}
