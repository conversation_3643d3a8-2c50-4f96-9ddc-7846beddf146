package com.rs.module.ihc.controller.admin.mr.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 病历管理新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MedicalRecordSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("主诉")
    private String mainComplaint;

    @ApiModelProperty("病史")
    private String medicalHistory;

    @ApiModelProperty("既往史")
    private String pastMedicalHistory;

    @ApiModelProperty("体格检查")
    private String physicalCheck;

    @ApiModelProperty("外院检查")
    private String externalHospitalCheck;

    @ApiModelProperty("外院病历")
    private String externalHospitalRecord;

    @ApiModelProperty("诊断结论")
    private String diagnosisResult;

    @ApiModelProperty("医疗计划")
    private String medicalPlan;

    @ApiModelProperty("医生身份证号")
    private String doctorIdCard;

    @ApiModelProperty("医生姓名")
    private String doctorName;

    @ApiModelProperty("医生签名图片URL")
    private String doctorSignature;

    @ApiModelProperty("签名时间")
    private Date signatureTime;

    @ApiModelProperty("第三方业务id")
    private String bizId;

}
