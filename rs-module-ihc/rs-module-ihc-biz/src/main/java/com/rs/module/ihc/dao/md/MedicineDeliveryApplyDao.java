package com.rs.module.ihc.dao.md;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.rs.module.base.entity.pm.PrisonerListDO;
import com.rs.module.ihc.controller.admin.md.vo.MedicineDeliveryApplyListReqVO;
import com.rs.module.ihc.controller.admin.md.vo.MedicineDeliveryApplyPageReqVO;
import com.rs.module.ihc.entity.md.MedicineDeliveryApplyDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * 药品顾送管理-药品顾送申请 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface MedicineDeliveryApplyDao extends IBaseDao<MedicineDeliveryApplyDO> {


    default PageResult<MedicineDeliveryApplyDO> selectPage(MedicineDeliveryApplyPageReqVO reqVO) {
        Page<MedicineDeliveryApplyDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());

        MPJLambdaWrapperX<MedicineDeliveryApplyDO> innerJoin = new MPJLambdaWrapperX<MedicineDeliveryApplyDO>()
                .eqIfPresent(MedicineDeliveryApplyDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(MedicineDeliveryApplyDO::getJgryxm, reqVO.getJgryxm())
                .betweenIfPresent(MedicineDeliveryApplyDO::getExpectedStartDate, reqVO.getExpectedStartDate())
                .betweenIfPresent(MedicineDeliveryApplyDO::getExpectedEndDate, reqVO.getExpectedEndDate())
                .eqIfPresent(MedicineDeliveryApplyDO::getDrugSource, reqVO.getDrugSource())
                .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryReason, reqVO.getDeliveryReason())
                .eqIfPresent(MedicineDeliveryApplyDO::getRemark, reqVO.getRemark())
                .eqIfPresent(MedicineDeliveryApplyDO::getIsConsistent, reqVO.getIsConsistent())
                .eqIfPresent(MedicineDeliveryApplyDO::getReasonForInconsistency, reqVO.getReasonForInconsistency())
                .eqIfPresent(MedicineDeliveryApplyDO::getImgUrl, reqVO.getImgUrl())
                .betweenIfPresent(MedicineDeliveryApplyDO::getDeliveryDate, reqVO.getDeliveryDate())
                .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryRemark, reqVO.getDeliveryRemark())
                .eqIfPresent(MedicineDeliveryApplyDO::getExceptionReason, reqVO.getExceptionReason())
                .eqIfPresent(MedicineDeliveryApplyDO::getExceptionRemark, reqVO.getExceptionRemark())
                .eqIfPresent(MedicineDeliveryApplyDO::getStatus, reqVO.getStatus())
                .eqIfPresent(MedicineDeliveryApplyDO::getActInstId, reqVO.getActInstId())
                .eqIfPresent(MedicineDeliveryApplyDO::getTaskId, reqVO.getTaskId());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            innerJoin.orderByDesc(MedicineDeliveryApplyDO::getAddTime);
        }
        if (StringUtils.isNotEmpty(reqVO.getRoomId())) {
            innerJoin.innerJoin(PrisonerListDO.class, PrisonerListDO::getJgrybm, MedicineDeliveryApplyDO::getJgrybm);
            innerJoin.eq(PrisonerListDO::getJsh, reqVO.getRoomId());
        }
        if (StringUtils.isNotEmpty(reqVO.getType())) {
            LocalDate today = LocalDate.now();
            LocalDateTime startDateTime;
            LocalDateTime endDateTime;
            switch (reqVO.getType()) {
                case "0": // 全部
                    return null;
                case "1": // 今天
                    startDateTime = today.atStartOfDay();
                    endDateTime = today.atTime(LocalTime.MAX);
                    break;
                case "2": // 昨天
                    LocalDate yesterday = today.minusDays(1);
                    startDateTime = yesterday.atStartOfDay();
                    endDateTime = yesterday.atTime(LocalTime.MAX);
                    break;
                case "3": // 近一周
                    LocalDate weekAgo = today.minusDays(6); // 包含今天共7天
                    startDateTime = weekAgo.atStartOfDay();
                    endDateTime = today.atTime(LocalTime.MAX);
                    break;
                default:
                    return null;
            }

            Date startDate = Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant());
            Date endDate = Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant());
            innerJoin.between(MedicineDeliveryApplyDO::getAddTime, startDate, endDate);
        }
        Page<MedicineDeliveryApplyDO> medicineDeliveryApplyPage = selectJoinPage(page, MedicineDeliveryApplyDO.class, innerJoin);
        return new PageResult<>(medicineDeliveryApplyPage.getRecords(), medicineDeliveryApplyPage.getTotal());
    }

    default List<MedicineDeliveryApplyDO> selectList(MedicineDeliveryApplyListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MedicineDeliveryApplyDO>()
                .eqIfPresent(MedicineDeliveryApplyDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(MedicineDeliveryApplyDO::getJgryxm, reqVO.getJgryxm())
                .betweenIfPresent(MedicineDeliveryApplyDO::getExpectedStartDate, reqVO.getExpectedStartDate())
                .betweenIfPresent(MedicineDeliveryApplyDO::getExpectedEndDate, reqVO.getExpectedEndDate())
                .eqIfPresent(MedicineDeliveryApplyDO::getDrugSource, reqVO.getDrugSource())
                .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryReason, reqVO.getDeliveryReason())
                .eqIfPresent(MedicineDeliveryApplyDO::getRemark, reqVO.getRemark())
                .eqIfPresent(MedicineDeliveryApplyDO::getIsConsistent, reqVO.getIsConsistent())
                .eqIfPresent(MedicineDeliveryApplyDO::getReasonForInconsistency, reqVO.getReasonForInconsistency())
                .eqIfPresent(MedicineDeliveryApplyDO::getImgUrl, reqVO.getImgUrl())
                .betweenIfPresent(MedicineDeliveryApplyDO::getDeliveryDate, reqVO.getDeliveryDate())
                .eqIfPresent(MedicineDeliveryApplyDO::getDeliveryRemark, reqVO.getDeliveryRemark())
                .eqIfPresent(MedicineDeliveryApplyDO::getExceptionReason, reqVO.getExceptionReason())
                .eqIfPresent(MedicineDeliveryApplyDO::getExceptionRemark, reqVO.getExceptionRemark())
                .eqIfPresent(MedicineDeliveryApplyDO::getStatus, reqVO.getStatus())
                .eqIfPresent(MedicineDeliveryApplyDO::getActInstId, reqVO.getActInstId())
                .eqIfPresent(MedicineDeliveryApplyDO::getTaskId, reqVO.getTaskId())
                .orderByDesc(MedicineDeliveryApplyDO::getAddTime));
    }


}


