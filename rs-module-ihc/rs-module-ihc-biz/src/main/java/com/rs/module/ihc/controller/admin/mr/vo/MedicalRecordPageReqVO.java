package com.rs.module.ihc.controller.admin.mr.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 病历管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MedicalRecordPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("主诉")
    private String mainComplaint;

    @ApiModelProperty("病史")
    private String medicalHistory;

    @ApiModelProperty("既往史")
    private String pastMedicalHistory;

    @ApiModelProperty("体格检查")
    private String physicalCheck;

    @ApiModelProperty("外院检查")
    private String externalHospitalCheck;

    @ApiModelProperty("外院病历")
    private String externalHospitalRecord;

    @ApiModelProperty("诊断结论")
    private String diagnosisResult;

    @ApiModelProperty("医疗计划")
    private String medicalPlan;

    @ApiModelProperty("医生身份证号")
    private String doctorIdCard;

    @ApiModelProperty("医生姓名")
    private String doctorName;

    @ApiModelProperty("医生签名图片URL")
    private String doctorSignature;

    @ApiModelProperty("签名时间")
    private Date[] signatureTime;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
