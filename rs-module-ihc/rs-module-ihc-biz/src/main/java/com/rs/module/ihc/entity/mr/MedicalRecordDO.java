package com.rs.module.ihc.entity.mr;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 病历管理 DO
 *
 * <AUTHOR>
 */
@TableName("ihc_mr_medical_record")
@KeySequence("ihc_mr_medical_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_mr_medical_record")
public class MedicalRecordDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 主诉
     */
    private String mainComplaint;
    /**
     * 病史
     */
    private String medicalHistory;
    /**
     * 既往史
     */
    private String pastMedicalHistory;
    /**
     * 体格检查
     */
    private String physicalCheck;
    /**
     * 外院检查
     */
    private String externalHospitalCheck;
    /**
     * 外院病历
     */
    private String externalHospitalRecord;
    /**
     * 诊断结论
     */
    private String diagnosisResult;
    /**
     * 医疗计划
     */
    private String medicalPlan;
    /**
     * 医生身份证号
     */
    private String doctorIdCard;
    /**
     * 医生姓名
     */
    private String doctorName;
    /**
     * 医生签名图片URL
     */
    private String doctorSignature;
    /**
     * 签名时间
     */
    private Date signatureTime;
    /**
     * 扩展字段
     */
    private String extId;

}
