package com.rs.module.ihc.service.dch.util;


import com.rs.module.base.enums.DataChangeEventTypeEnum;
import com.rs.module.ihc.service.dch.context.DataChangeEventContext;
import com.rs.module.ihc.service.dch.processor.DataChangeEventProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据变更事件工具类
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Slf4j
@Component
public class DataChangeEventUtil {

    @Autowired
    private DataChangeEventProcessor eventProcessor;

    /**
     * 手动触发数据变更事件
     *
     * @param eventType    事件类型
     * @param tableName    表名
     * @param businessType 业务类型
     * @param primaryKeyId 主键ID
     * @param oldData      变更前数据
     * @param newData      变更后数据
     */
    public void triggerDataChangeEvent(DataChangeEventTypeEnum eventType, String tableName,
                                       String businessType, String primaryKeyId,
                                       Map<String, Object> oldData, Map<String, Object> newData) {
        try {
            DataChangeEventContext context = new DataChangeEventContext(eventType, tableName, businessType, primaryKeyId);
            context.setOldDataMap(oldData);
            context.setNewDataMap(newData);

            log.info("手动触发数据变更事件: 业务类型={}, 表名={}, 事件类型={}, 主键ID={}",
                     businessType, tableName, eventType, primaryKeyId);

            eventProcessor.processDataChangeEvent(context);
        } catch (Exception e) {
            log.error("手动触发数据变更事件失败: 业务类型={}, 表名={}, 事件类型={}, 主键ID={}",
                     businessType, tableName, eventType, primaryKeyId, e);
        }
    }

    /**
     * 异步触发数据变更事件
     *
     * @param eventType    事件类型
     * @param tableName    表名
     * @param businessType 业务类型
     * @param primaryKeyId 主键ID
     * @param oldData      变更前数据
     * @param newData      变更后数据
     */
    public void triggerDataChangeEventAsync(DataChangeEventTypeEnum eventType, String tableName,
                                            String businessType, String primaryKeyId,
                                            Map<String, Object> oldData, Map<String, Object> newData) {
        try {
            DataChangeEventContext context = new DataChangeEventContext(eventType, tableName, businessType, primaryKeyId);
            context.setOldDataMap(oldData);
            context.setNewDataMap(newData);

            log.info("异步触发数据变更事件: 业务类型={}, 表名={}, 事件类型={}, 主键ID={}",
                     businessType, tableName, eventType, primaryKeyId);

            eventProcessor.processDataChangeEventAsync(context);
        } catch (Exception e) {
            log.error("异步触发数据变更事件失败: 业务类型={}, 表名={}, 事件类型={}, 主键ID={}",
                     businessType, tableName, eventType, primaryKeyId, e);
        }
    }

    /**
     * 触发插入事件
     *
     * @param tableName    表名
     * @param businessType 业务类型
     * @param primaryKeyId 主键ID
     * @param newData      新数据
     */
    public void triggerInsertEvent(String tableName, String businessType, String primaryKeyId, Map<String, Object> newData) {
        triggerDataChangeEvent(DataChangeEventTypeEnum.INSERT, tableName, businessType, primaryKeyId, null, newData);
    }

    /**
     * 触发更新事件
     *
     * @param tableName    表名
     * @param businessType 业务类型
     * @param primaryKeyId 主键ID
     * @param oldData      旧数据
     * @param newData      新数据
     */
    public void triggerUpdateEvent(String tableName, String businessType, String primaryKeyId,
                                   Map<String, Object> oldData, Map<String, Object> newData) {
        triggerDataChangeEvent(DataChangeEventTypeEnum.UPDATE, tableName, businessType, primaryKeyId, oldData, newData);
    }

    /**
     * 触发删除事件
     *
     * @param tableName    表名
     * @param businessType 业务类型
     * @param primaryKeyId 主键ID
     * @param oldData      旧数据
     */
    public void triggerDeleteEvent(String tableName, String businessType, String primaryKeyId, Map<String, Object> oldData) {
        triggerDataChangeEvent(DataChangeEventTypeEnum.DELETE, tableName, businessType, primaryKeyId, oldData, null);
    }

    /**
     * 创建简单的数据Map
     *
     * @return 数据Map
     */
    public static Map<String, Object> createDataMap() {
        return new HashMap<>();
    }

    /**
     * 创建包含单个键值对的数据Map
     *
     * @param key   键
     * @param value 值
     * @return 数据Map
     */
    public static Map<String, Object> createDataMap(String key, Object value) {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put(key, value);
        return dataMap;
    }

    /**
     * 创建包含多个键值对的数据Map
     *
     * @param data 键值对数组，格式为 [key1, value1, key2, value2, ...]
     * @return 数据Map
     */
    public static Map<String, Object> createDataMap(Object... data) {
        Map<String, Object> dataMap = new HashMap<>();
        if (data != null && data.length % 2 == 0) {
            for (int i = 0; i < data.length; i += 2) {
                String key = String.valueOf(data[i]);
                Object value = data[i + 1];
                dataMap.put(key, value);
            }
        }
        return dataMap;
    }

    /**
     * 监管人员相关事件触发器
     */
    public static class PrisonerEventTrigger {

        private final DataChangeEventUtil eventUtil;

        public PrisonerEventTrigger(DataChangeEventUtil eventUtil) {
            this.eventUtil = eventUtil;
        }

        /**
         * 触发监管人员新增事件
         */
        public void triggerPrisonerInsert(String prisonerId, String prisonerCode, String prisonerName) {
            Map<String, Object> newData = createDataMap(
                "id", prisonerId,
                "jgrybm", prisonerCode,
                "xm", prisonerName,
                "add_time", System.currentTimeMillis()
            );
            eventUtil.triggerInsertEvent("pam_pm_prisoner", "PRISONER", prisonerId, newData);
        }

        /**
         * 触发监管人员更新事件
         */
        public void triggerPrisonerUpdate(String prisonerId, Map<String, Object> oldData, Map<String, Object> newData) {
            eventUtil.triggerUpdateEvent("pam_pm_prisoner", "PRISONER", prisonerId, oldData, newData);
        }

        /**
         * 触发监管人员删除事件
         */
        public void triggerPrisonerDelete(String prisonerId, Map<String, Object> oldData) {
            eventUtil.triggerDeleteEvent("pam_pm_prisoner", "PRISONER", prisonerId, oldData);
        }
    }

    /**
     * 获取监管人员事件触发器
     *
     * @return 监管人员事件触发器
     */
    public PrisonerEventTrigger prisoner() {
        return new PrisonerEventTrigger(this);
    }
}
