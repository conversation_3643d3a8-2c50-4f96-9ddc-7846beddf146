package com.rs.module.ihc.dao.mr;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.ihc.entity.mr.MedicalRecordDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.ihc.controller.admin.mr.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 病历管理 Dao
*
* <AUTHOR>
*/
@Mapper
public interface MedicalRecordDao extends IBaseDao<MedicalRecordDO> {


    default PageResult<MedicalRecordDO> selectPage(MedicalRecordPageReqVO reqVO) {
        Page<MedicalRecordDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<MedicalRecordDO> wrapper = new LambdaQueryWrapperX<MedicalRecordDO>()
            .eqIfPresent(MedicalRecordDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MedicalRecordDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(MedicalRecordDO::getMainComplaint, reqVO.getMainComplaint())
            .eqIfPresent(MedicalRecordDO::getMedicalHistory, reqVO.getMedicalHistory())
            .eqIfPresent(MedicalRecordDO::getPastMedicalHistory, reqVO.getPastMedicalHistory())
            .eqIfPresent(MedicalRecordDO::getPhysicalCheck, reqVO.getPhysicalCheck())
            .eqIfPresent(MedicalRecordDO::getExternalHospitalCheck, reqVO.getExternalHospitalCheck())
            .eqIfPresent(MedicalRecordDO::getExternalHospitalRecord, reqVO.getExternalHospitalRecord())
            .eqIfPresent(MedicalRecordDO::getDiagnosisResult, reqVO.getDiagnosisResult())
            .eqIfPresent(MedicalRecordDO::getMedicalPlan, reqVO.getMedicalPlan())
            .eqIfPresent(MedicalRecordDO::getDoctorIdCard, reqVO.getDoctorIdCard())
            .likeIfPresent(MedicalRecordDO::getDoctorName, reqVO.getDoctorName())
            .eqIfPresent(MedicalRecordDO::getDoctorSignature, reqVO.getDoctorSignature())
            .betweenIfPresent(MedicalRecordDO::getSignatureTime, reqVO.getSignatureTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(MedicalRecordDO::getAddTime);
        }
        Page<MedicalRecordDO> medicalRecordPage = selectPage(page, wrapper);
        return new PageResult<>(medicalRecordPage.getRecords(), medicalRecordPage.getTotal());
    }
    default List<MedicalRecordDO> selectList(MedicalRecordListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MedicalRecordDO>()
            .eqIfPresent(MedicalRecordDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(MedicalRecordDO::getJgryxm, reqVO.getJgryxm())
            .eqIfPresent(MedicalRecordDO::getMainComplaint, reqVO.getMainComplaint())
            .eqIfPresent(MedicalRecordDO::getMedicalHistory, reqVO.getMedicalHistory())
            .eqIfPresent(MedicalRecordDO::getPastMedicalHistory, reqVO.getPastMedicalHistory())
            .eqIfPresent(MedicalRecordDO::getPhysicalCheck, reqVO.getPhysicalCheck())
            .eqIfPresent(MedicalRecordDO::getExternalHospitalCheck, reqVO.getExternalHospitalCheck())
            .eqIfPresent(MedicalRecordDO::getExternalHospitalRecord, reqVO.getExternalHospitalRecord())
            .eqIfPresent(MedicalRecordDO::getDiagnosisResult, reqVO.getDiagnosisResult())
            .eqIfPresent(MedicalRecordDO::getMedicalPlan, reqVO.getMedicalPlan())
            .eqIfPresent(MedicalRecordDO::getDoctorIdCard, reqVO.getDoctorIdCard())
            .likeIfPresent(MedicalRecordDO::getDoctorName, reqVO.getDoctorName())
            .eqIfPresent(MedicalRecordDO::getDoctorSignature, reqVO.getDoctorSignature())
            .betweenIfPresent(MedicalRecordDO::getSignatureTime, reqVO.getSignatureTime())
        .orderByDesc(MedicalRecordDO::getAddTime));    }


    }
