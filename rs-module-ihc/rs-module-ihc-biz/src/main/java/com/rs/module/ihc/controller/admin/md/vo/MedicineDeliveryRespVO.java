package com.rs.module.ihc.controller.admin.md.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.rs.framework.mybatis.annotation.Query;
import com.rs.framework.mybatis.entity.BaseDO;
import com.rs.module.ihc.controller.admin.ipm.vo.PrescribeRespVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineCodeRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 药品顾送管理-药品顾送申请关联药品 DO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 药品顾送管理-药品顾送申请关联药品新增/修改 Request VO")
@TableName("ihc_md_medicine_delivery")
@KeySequence("ihc_md_medicine_delivery_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ihc_md_medicine_delivery")
public class MedicineDeliveryRespVO extends MedicineCodeRespVO implements VO, Serializable {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;
    /**
     * 药品顾送申请
     */
    @ApiModelProperty("药品顾送申请")
    private String applyId;
    /**
     * 药品名称
     */
    @ApiModelProperty("药品名称")
    private String medicineName;
    /**
     * 剂型
     */
    @ApiModelProperty("剂型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_DOSAGE_FORM")
    private String dosageForm;
    /**
     * 规格
     */
    @ApiModelProperty("规格")
    private String specs;
    /**
     * 生产单位
     */
    @ApiModelProperty("生产单位")
    private String productUnit;
    /**
     * 批准文号
     */
    @ApiModelProperty("批准文号")
    private String approvalNum;
    /**
     * 原批准文号
     */
    @ApiModelProperty("原批准文号")
    private String originalApprovalNum;
    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private BigDecimal totalNum;

    @ApiModelProperty("计量单位")
    @Trans(type = TransType.DICTIONARY, key = "ZD_MEASUREMENT_UNIT",ref = "jldwName")
    private String jldw;
    private String jldwName;
    /**
     * 药品库-药品id
     */
    @ApiModelProperty("药品库-药品id")
    @Query(sql = "select * from ihc_pm_medicine_code where id = '${medicineId}' and is_del =0",beanClass = MedicineCodeRespVO.class,isTile = true)
    private String medicineId;

}
