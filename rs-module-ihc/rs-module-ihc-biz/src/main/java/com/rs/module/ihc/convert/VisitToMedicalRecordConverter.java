package com.rs.module.ihc.convert;

import com.rs.module.ihc.entity.ipm.VisitDO;
import com.rs.module.ihc.entity.mr.MedicalRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * VisitDO 转 MedicalRecordDO 的 MapStruct 转换器
 * 
 * <AUTHOR>
 */
@Mapper
public interface VisitToMedicalRecordConverter {

    VisitToMedicalRecordConverter INSTANCE = Mappers.getMapper(VisitToMedicalRecordConverter.class);

    /**
     * 将 VisitDO 转换为 MedicalRecordDO
     * 
     * @param visitDO 巡诊记录
     * @return 病历记录
     */
    @Mapping(source = "jgrybm", target = "jgrybm")
    @Mapping(source = "ryxm", target = "jgryxm")
    @Mapping(source = "mainComplaint", target = "mainComplaint")
    @Mapping(source = "visitConclusion", target = "diagnosisResult")
    @Mapping(source = "visitUserid", target = "doctorIdCard")
    @Mapping(source = "visitUserName", target = "doctorName")
    @Mapping(source = "visitTime", target = "signatureTime")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "medicalHistory", ignore = true)
    @Mapping(target = "pastMedicalHistory", ignore = true)
    @Mapping(target = "physicalCheck", ignore = true)
    @Mapping(target = "externalHospitalCheck", ignore = true)
    @Mapping(target = "externalHospitalRecord", ignore = true)
    @Mapping(target = "medicalPlan", ignore = true)
    @Mapping(target = "doctorSignature", ignore = true)
    MedicalRecordDO visitToMedicalRecord(VisitDO visitDO);
}
