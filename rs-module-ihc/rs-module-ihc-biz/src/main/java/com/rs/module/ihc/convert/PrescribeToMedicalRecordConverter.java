package com.rs.module.ihc.convert;

import com.rs.module.ihc.entity.ipm.PrescribeDO;
import com.rs.module.ihc.entity.mr.MedicalRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * PrescribeDO 转 MedicalRecordDO 的 MapStruct 转换器
 * 
 * <AUTHOR>
 */
@Mapper
public interface PrescribeToMedicalRecordConverter {

    PrescribeToMedicalRecordConverter INSTANCE = Mappers.getMapper(PrescribeToMedicalRecordConverter.class);

    /**
     * 将 PrescribeDO 转换为 MedicalRecordDO
     * 
     * @param prescribeDO 处方记录
     * @return 病历记录
     */
    @Mapping(source = "jgrybm", target = "jgrybm")
    @Mapping(source = "ryxm", target = "jgryxm")
    @Mapping(source = "mainComplaint", target = "mainComplaint")
    @Mapping(source = "medicalHistory", target = "medicalHistory")
    @Mapping(source = "physicalCheck", target = "physicalCheck")
    @Mapping(source = "primaryDiagnosis", target = "diagnosisResult")
    @Mapping(source = "suggestion", target = "medicalPlan")
    @Mapping(source = "prescribeUserid", target = "doctorIdCard")
    @Mapping(source = "prescribeUserName", target = "doctorName")
    @Mapping(source = "prescribeTime", target = "signatureTime")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "pastMedicalHistory", ignore = true)
    @Mapping(target = "externalHospitalCheck", ignore = true)
    @Mapping(target = "externalHospitalRecord", ignore = true)
    @Mapping(target = "doctorSignature", ignore = true)
    @Mapping(target = "extId", ignore = true)
    MedicalRecordDO prescribeToMedicalRecord(PrescribeDO prescribeDO);
}