package com.rs.module.ihc.controller.admin.hc;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.hc.vo.HealthCheckupAdditionalRecordVO;
import com.rs.module.ihc.controller.admin.hc.vo.HealthCheckupRespVO;
import com.rs.module.ihc.controller.admin.hc.vo.HealthCheckupSaveReqVO;
import com.rs.module.ihc.service.hc.HealthCheckupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "半年-体检")
@RestController
@RequestMapping("/ihc/hc/healthCheckup")
@Validated
public class HealthCheckupController {

    @Resource
    private HealthCheckupService healthCheckupService;

    @PostMapping("/create")
    @ApiOperation(value = "半年体检-体检结果-体检补录")
    @LogRecordAnnotation(bizModule = "ihc:hc-healthCheckup:create", operateType = LogOperateType.CREATE, title = "五项体检-数据补录",
            success = "五项体检-数据补录-成功", fail = "五项体检-数据补录-失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createHealthCheckup(@Valid @RequestBody HealthCheckupAdditionalRecordVO createReqVO) {
        healthCheckupService.createHealthCheckup(createReqVO);
        return success();
    }

    @ApiOperation(value = "半年体检-体检申请")
    @ApiImplicitParam(name = "ids", value = "申请单id，多个用,分割", dataType = "string", paramType = "query", required = true)
    @PostMapping("/apply")
    @LogRecordAnnotation(bizModule = "ihc:hc-healthCheckup:apply", operateType = LogOperateType.UPDATE, title = "五项体检-体验申请",
            success = "五项体检-体验申请-成功", fail = "五项体检-体验申请-失败，错误信息：{{#_ret[msg]}}", extraInfo = "{#jgrybms}")
    public CommonResult<Boolean> apply(@RequestParam String ids) {
        healthCheckupService.apply(ids);
        return success(true);
    }

    @PostMapping("/update")
    @ApiOperation(value = "半年体检-体检结果-数据录入")
    @LogRecordAnnotation(bizModule = "ihc:hc-healthCheckup:update", operateType = LogOperateType.UPDATE, title = "五项体检-录入",
            success = "五项体检-数据录入-成功", fail = "五项体检-数据补录-失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateHealthCheckup(@Valid @RequestBody HealthCheckupSaveReqVO updateReqVO) {
        healthCheckupService.updateHealthCheckup(updateReqVO);
       return success(true);
   }

    @GetMapping("/get")
    @ApiOperation(value = "获取五项体检详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:hc-healthCheckup:getHealthCheckup", operateType = LogOperateType.QUERY, title = "五项体检-体验申请数据信息",
            success = "五项体检-体验申请-数据信息-成功", fail = "五项体检-体验申请-失败，错误信息：{{#_ret[msg]}}", extraInfo = "{#id}")
    public CommonResult<HealthCheckupRespVO> getHealthCheckup(@RequestParam("id") String id) {
        HealthCheckupRespVO healthCheckup = healthCheckupService.getHealthCheckup(id);
        return success(BeanUtils.toBean(healthCheckup, HealthCheckupRespVO.class));
    }



}
