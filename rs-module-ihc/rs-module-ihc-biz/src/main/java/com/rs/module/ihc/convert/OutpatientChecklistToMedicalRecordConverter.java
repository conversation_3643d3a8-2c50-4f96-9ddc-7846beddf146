package com.rs.module.ihc.convert;

import com.rs.module.ihc.entity.ipm.OutpatientChecklistDO;
import com.rs.module.ihc.entity.mr.MedicalRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * OutpatientChecklistDO 转 MedicalRecordDO 的 MapStruct 转换器
 * 
 * <AUTHOR>
 */
@Mapper
public interface OutpatientChecklistToMedicalRecordConverter {

    OutpatientChecklistToMedicalRecordConverter INSTANCE = Mappers.getMapper(OutpatientChecklistToMedicalRecordConverter.class);

    /**
     * 将 OutpatientChecklistDO 转换为 MedicalRecordDO
     * 
     * @param outpatientChecklistDO 门诊检查单记录
     * @return 病历记录
     */
    @Mapping(source = "jgrybm", target = "jgrybm")
    @Mapping(source = "mainComplaint", target = "mainComplaint")
    @Mapping(source = "illnessResume", target = "medicalHistory")
    @Mapping(source = "checkConclusion", target = "diagnosisResult")
    @Mapping(source = "registerUserid", target = "doctorIdCard")
    @Mapping(source = "registerUserName", target = "doctorName")
    @Mapping(source = "registerTime", target = "signatureTime")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "jgryxm", ignore = true)
    @Mapping(target = "pastMedicalHistory", ignore = true)
    @Mapping(target = "physicalCheck", ignore = true)
    @Mapping(target = "externalHospitalCheck", ignore = true)
    @Mapping(target = "externalHospitalRecord", ignore = true)
    @Mapping(target = "medicalPlan", ignore = true)
    @Mapping(target = "doctorSignature", ignore = true)
    @Mapping(target = "extId", ignore = true)
    MedicalRecordDO outpatientChecklistToMedicalRecord(OutpatientChecklistDO outpatientChecklistDO);
}