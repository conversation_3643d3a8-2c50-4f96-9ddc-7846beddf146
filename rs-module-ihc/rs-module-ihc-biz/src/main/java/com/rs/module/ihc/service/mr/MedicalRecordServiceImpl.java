package com.rs.module.ihc.service.mr;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.ihc.controller.admin.mr.vo.*;
import com.rs.module.ihc.entity.mr.MedicalRecordDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.ihc.dao.mr.MedicalRecordDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 病历管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MedicalRecordServiceImpl extends BaseServiceImpl<MedicalRecordDao, MedicalRecordDO> implements MedicalRecordService {

    @Resource
    private MedicalRecordDao medicalRecordDao;

    @Override
    public String createMedicalRecord(MedicalRecordSaveReqVO createReqVO) {
        // 插入
        MedicalRecordDO medicalRecord = BeanUtils.toBean(createReqVO, MedicalRecordDO.class);
        medicalRecordDao.insert(medicalRecord);
        // 返回
        return medicalRecord.getId();
    }

    @Override
    public void updateMedicalRecord(MedicalRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateMedicalRecordExists(updateReqVO.getId());
        // 更新
        MedicalRecordDO updateObj = BeanUtils.toBean(updateReqVO, MedicalRecordDO.class);
        medicalRecordDao.updateById(updateObj);
    }

    @Override
    public void deleteMedicalRecord(String id) {
        // 校验存在
        validateMedicalRecordExists(id);
        // 删除
        medicalRecordDao.deleteById(id);
    }

    private void validateMedicalRecordExists(String id) {
        if (medicalRecordDao.selectById(id) == null) {
            throw new ServerException("病历管理数据不存在");
        }
    }

    @Override
    public MedicalRecordDO getMedicalRecord(String id) {
        return medicalRecordDao.selectById(id);
    }

    @Override
    public PageResult<MedicalRecordDO> getMedicalRecordPage(MedicalRecordPageReqVO pageReqVO) {
        return medicalRecordDao.selectPage(pageReqVO);
    }

    @Override
    public List<MedicalRecordDO> getMedicalRecordList(MedicalRecordListReqVO listReqVO) {
        return medicalRecordDao.selectList(listReqVO);
    }


}
