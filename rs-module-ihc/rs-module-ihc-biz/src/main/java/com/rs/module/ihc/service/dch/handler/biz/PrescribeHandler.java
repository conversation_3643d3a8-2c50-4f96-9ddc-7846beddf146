package com.rs.module.ihc.service.dch.handler.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.rs.module.base.enums.DataChangeEventTypeEnum;
import com.rs.module.ihc.convert.PrescribeToMedicalRecordConverter;
import com.rs.module.ihc.entity.ipm.PrescribeDO;
import com.rs.module.ihc.entity.mr.MedicalRecordDO;
import com.rs.module.ihc.service.dch.context.DataChangeEventContext;
import com.rs.module.ihc.service.dch.handler.AbstractDataChangeEventHandler;
import com.rs.module.ihc.service.dch.handler.DataChangeEventHandlerResult;
import com.rs.module.ihc.service.ipm.PrescribeService;
import com.rs.module.ihc.service.mr.MedicalRecordService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 处方处理器
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Slf4j
@Component
@AllArgsConstructor
public class PrescribeHandler extends AbstractDataChangeEventHandler {
    public final MedicalRecordService medicalRecordService;
    public final PrescribeService prescribeService;

    @Override
    public String getSupportedBusinessType() {
        return PrescribeHandler.class.getSimpleName();
    }

    @Override
    public int getPriority() {
        return 1;
    }

    @Override
    protected List<String> getSupportedTableNames() {
        return Arrays.asList(
                "ihc_ipm_prescribe"
        );
    }

    @Override
    protected Set<DataChangeEventTypeEnum> getSupportedEventTypes() {
        return new HashSet<>(Arrays.asList(
                DataChangeEventTypeEnum.INSERT
                , DataChangeEventTypeEnum.UPDATE
                , DataChangeEventTypeEnum.DELETE
        ));
    }

    @Override
    protected DataChangeEventHandlerResult doHandle(DataChangeEventContext context) {
        switch (context.getEventType()) {
            case INSERT:
                return handleInsert(context);
            case UPDATE:
                return handleUpdate(context);
            case DELETE:
                return handleDelete(context);
        }
        return DataChangeEventHandlerResult.success();
    }

    /**
     * 处理插入事件
     */
    private DataChangeEventHandlerResult handleInsert(DataChangeEventContext context) {

        PrescribeDO prescribeDO = getPrescribe(getBizId(context));
        if (prescribeDO != null) {
            MedicalRecordDO medicalRecordDO = PrescribeToMedicalRecordConverter.INSTANCE.prescribeToMedicalRecord(prescribeDO);
            medicalRecordDO.setExtId(getBizId(context));
            medicalRecordService.save(medicalRecordDO);
        }

        return DataChangeEventHandlerResult.success("处理成功");
    }

    /**
     * 处理更新事件
     */
    private DataChangeEventHandlerResult handleUpdate(DataChangeEventContext context) {

        if (isDelete(context)) {
            handleDelete(context);
        }
        PrescribeDO prescribeDO = getPrescribe(getBizId(context));
        if (prescribeDO != null) {
            MedicalRecordDO medicalRecordDO = PrescribeToMedicalRecordConverter.INSTANCE.prescribeToMedicalRecord(prescribeDO);
            MedicalRecordDO recordDO = medicalRecordService.lambdaQuery()
                    .eq(MedicalRecordDO::getExtId, getBizId(context))
                    .one();
            if (recordDO != null) {
                CopyOptions copier = CopyOptions.create();
                copier.setIgnoreNullValue(true);
                BeanUtil.copyProperties(medicalRecordDO, recordDO, copier);
                medicalRecordService.updateById(recordDO);
            } else {
                medicalRecordService.save(medicalRecordDO);
            }
        }
        return DataChangeEventHandlerResult.success("处理成功");
    }

    /**
     * 处理删除事件
     */
    private DataChangeEventHandlerResult handleDelete(DataChangeEventContext context) {
        medicalRecordService.remove(new LambdaQueryWrapper<MedicalRecordDO>()
                .eq(MedicalRecordDO::getExtId, getBizId(context)));
        return DataChangeEventHandlerResult.success("处理成功");
    }

    public PrescribeDO getPrescribe(String id) {
        return prescribeService.getPrescribe(id);
    }

}
