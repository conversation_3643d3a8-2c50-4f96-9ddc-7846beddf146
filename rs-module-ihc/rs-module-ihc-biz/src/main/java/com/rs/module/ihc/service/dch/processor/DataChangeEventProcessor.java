package com.rs.module.ihc.service.dch.processor;



import com.rs.module.base.enums.DataChangeEventTypeEnum;
import com.rs.module.ihc.service.dch.context.DataChangeEventContext;

import java.util.Map;

/**
 * 数据变更事件处理器接口
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
public interface DataChangeEventProcessor {

    /**
     * 处理数据变更事件
     *
     * @param eventType    事件类型
     * @param tableName    表名
     * @param businessType 业务类型
     * @param primaryKeyId 主键ID
     * @param oldDataMap   变更前数据
     * @param newDataMap   变更后数据
     */
    void processDataChangeEvent(DataChangeEventTypeEnum eventType, String tableName,
                                String businessType, String primaryKeyId,
                                Map<String, Object> oldDataMap, Map<String, Object> newDataMap);

    /**
     * 处理数据变更事件
     *
     * @param context 事件上下文
     */
    void processDataChangeEvent(DataChangeEventContext context);

    /**
     * 异步处理数据变更事件
     *
     * @param context 事件上下文
     */
    void processDataChangeEventAsync(DataChangeEventContext context);

    /**
     * 处理待处理的事件
     *
     * @param limit 限制数量
     * @return 处理数量
     */
    int processPendingEvents(int limit);

    /**
     * 处理需要重试的事件
     *
     * @param limit 限制数量
     * @return 处理数量
     */
    int processRetryEvents(int limit);

    /**
     * 处理指定业务类型的事件
     *
     * @param businessType 业务类型
     * @param limit        限制数量
     * @return 处理数量
     */
    int processEventsByBusinessType(String businessType, int limit);
}
