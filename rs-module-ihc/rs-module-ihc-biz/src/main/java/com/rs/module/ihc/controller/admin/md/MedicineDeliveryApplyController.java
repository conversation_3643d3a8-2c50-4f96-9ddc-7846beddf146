package com.rs.module.ihc.controller.admin.md;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.vo.SimpleApproveReqVO;
import com.rs.module.ihc.controller.admin.md.vo.*;
import com.rs.module.ihc.entity.md.MedicineDeliveryApplyDO;
import com.rs.module.ihc.entity.md.MedicineDeliveryDO;
import com.rs.module.ihc.service.md.MedicineDeliveryApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "药品顾送管理-药品顾送申请")
@RestController
@RequestMapping("/ihc/md/medicineDeliveryApply")
@Validated
public class MedicineDeliveryApplyController {

    @Resource
    private MedicineDeliveryApplyService medicineDeliveryApplyService;

    @PostMapping("/create")
    @ApiOperation(value = "创建药品顾送管理-药品顾送申请")
    public CommonResult<String> createMedicineDeliveryApply(@Valid @RequestBody MedicineDeliveryApplySaveReqVO createReqVO) {
        return success(medicineDeliveryApplyService.createMedicineDeliveryApply(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新药品顾送管理-药品顾送申请")
    public CommonResult<Boolean> updateMedicineDeliveryApply(@Valid @RequestBody MedicineDeliveryApplySaveReqVO updateReqVO) {
        medicineDeliveryApplyService.updateMedicineDeliveryApply(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除药品顾送管理-药品顾送申请")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteMedicineDeliveryApply(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            medicineDeliveryApplyService.deleteMedicineDeliveryApply(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得药品顾送管理-药品顾送申请")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<MedicineDeliveryApplyRespVO> getMedicineDeliveryApply(@RequestParam("id") String id) {
        return success(medicineDeliveryApplyService.getMedicineDeliveryApply(id));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得药品顾送管理-药品顾送申请分页")
    public CommonResult<PageResult<MedicineDeliveryApplyRespVO>> getMedicineDeliveryApplyPage(@Valid @RequestBody MedicineDeliveryApplyPageReqVO pageReqVO) {
        PageResult<MedicineDeliveryApplyDO> pageResult = medicineDeliveryApplyService.getMedicineDeliveryApplyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MedicineDeliveryApplyRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得药品顾送管理-药品顾送申请列表")
    public CommonResult<List<MedicineDeliveryApplyRespVO>> getMedicineDeliveryApplyList(@Valid @RequestBody MedicineDeliveryApplyListReqVO listReqVO) {
        List<MedicineDeliveryApplyDO> list = medicineDeliveryApplyService.getMedicineDeliveryApplyList(listReqVO);
        return success(BeanUtils.toBean(list, MedicineDeliveryApplyRespVO.class));
    }
    // ==================== 子表（药品顾送管理-药品顾送申请关联药品） ====================

    @GetMapping("/medicine-delivery/list-by-apply-id")
    @ApiOperation(value = "获得药品顾送管理-药品顾送申请关联药品列表")
    @ApiImplicitParam(name = "applyId", value = "药品顾送申请")
    public CommonResult<List<MedicineDeliveryDO>> getMedicineDeliveryListByApplyId(@RequestParam("applyId") String applyId) {
        return success(medicineDeliveryApplyService.getMedicineDeliveryListByApplyId(applyId));
    }

    @PostMapping("/approve")
    @ApiOperation(value = "药品顾送管理-药品顾送申请-审批")
    public CommonResult<Boolean> approve(@Valid @RequestBody SimpleApproveReqVO approveReqVO) {
        medicineDeliveryApplyService.approve(approveReqVO);
        return success(true);
    }


    @PostMapping("/regInfo")
    @ApiOperation(value = "药品顾送管理-药品顾送登记")
    public CommonResult<Boolean> regInfo(@Valid @RequestBody MedicineDeliveryApplyRegInfoReqVO regInfoReqVO) {
        medicineDeliveryApplyService.regInfo(regInfoReqVO);
        return success(true);
    }

    @PostMapping("/abnormalRegInfo")
    @ApiOperation(value = "药品顾送管理-终止异常登记")
    public CommonResult<Boolean> abnormalRegInfo(@Valid @RequestBody MedicineDeliveryApplyAbnormalReqVO regInfoReqVO) {
        medicineDeliveryApplyService.abnormalRegInfo(regInfoReqVO);
        return success(true);
    }

}
