package com.rs.module.db.config;

/**
 * @ClassName LiquibaseConfig
 * <AUTHOR>
 * @Date 2024/3/4 16:35
 * @Version 1.0
 */

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import liquibase.integration.spring.SpringLiquibase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.DefaultResourceLoader;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * Liquibase配置类
 *
 * 多数据源（master、bsp、job）分别管理各自变更。
 */
@Slf4j
@Configuration
public class LiquibaseConfig {

    /*** liquibase用到的两张表 **/
    private static final String DATABASE_CHANGE_LOG_TABLE = "db_lqb_changelog";
    private static final String DATABASE_CHANGE_LOG_LOCK_TABLE = "db_lqb_lock";

    @Resource
    private DataSource dataSource; // 动态路由数据源（DynamicRoutingDataSource）

    private DataSource getTargetDs(String name) {
        if (dataSource instanceof DynamicRoutingDataSource) {
            return ((DynamicRoutingDataSource) dataSource).getDataSource(name);
        }
        // 非动态数据源场景下退化为单数据源
        return dataSource;
    }

    private SpringLiquibase buildLiquibase(DataSource ds, String changeLog) {
        SpringLiquibase liquibase = new SpringLiquibase();
        liquibase.setChangeLog(changeLog);
        liquibase.setDataSource(ds);
        liquibase.setContexts("db");
        liquibase.setShouldRun(true);
        liquibase.setResourceLoader(new DefaultResourceLoader());
        liquibase.setDatabaseChangeLogTable(DATABASE_CHANGE_LOG_TABLE);
        liquibase.setDatabaseChangeLogLockTable(DATABASE_CHANGE_LOG_LOCK_TABLE);
        liquibase.setTestRollbackOnUpdate(false);
        return liquibase;
    }

    /** master 库 */
    @Bean("liquibaseMaster")
    public SpringLiquibase liquibaseMaster() {
        return buildLiquibase(getTargetDs("master"), "classpath:db/master.xml");
    }

    /** bsp 库 */
    @Bean("liquibaseBsp")
    public SpringLiquibase liquibaseBsp() {
        return buildLiquibase(getTargetDs("bsp"), "classpath:db/bsp.xml");
    }

    /** job 库 */
    @Bean("liquibaseJob")
    public SpringLiquibase liquibaseJob() {
        return buildLiquibase(getTargetDs("job"), "classpath:db/job.xml");
    }
}
