package com.rs.module.tem.service.template;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.tem.controller.admin.template.vo.TalkTemplateListReqVO;
import com.rs.module.tem.controller.admin.template.vo.TalkTemplatePageReqVO;
import com.rs.module.tem.controller.admin.template.vo.TalkTemplateSaveReqVO;
import com.rs.module.tem.dao.template.TalkTemplateContentDao;
import com.rs.module.tem.dao.template.TalkTemplateDao;
import com.rs.module.tem.entity.template.TalkTemplateContentDO;
import com.rs.module.tem.entity.template.TalkTemplateDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;


/**
 * 谈话教育-谈话模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TalkTemplateServiceImpl extends BaseServiceImpl<TalkTemplateDao, TalkTemplateDO> implements TalkTemplateService {

    @Resource
    private TalkTemplateDao talkTemplateDao;
    @Resource
    private TalkTemplateContentDao talkTemplateContentDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTalkTemplate(TalkTemplateSaveReqVO createReqVO) {
        // 插入
        TalkTemplateDO talkTemplate = BeanUtils.toBean(createReqVO, TalkTemplateDO.class);
        talkTemplateDao.insert(talkTemplate);

        // 插入子表
        createTalkTemplateContentList(talkTemplate.getId(), BeanUtils.toBean(createReqVO.getTalkTemplateContents(), TalkTemplateContentDO.class));
        // 返回
        return talkTemplate.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTalkTemplate(TalkTemplateSaveReqVO updateReqVO) {
        // 校验存在
        validateTalkTemplateExists(updateReqVO.getId());
        // 更新
        TalkTemplateDO updateObj = BeanUtils.toBean(updateReqVO, TalkTemplateDO.class);
        talkTemplateDao.updateById(updateObj);

        // 更新子表
        updateTalkTemplateContentList(updateReqVO.getId(), BeanUtils.toBean(updateReqVO.getTalkTemplateContents(), TalkTemplateContentDO.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTalkTemplate(String id) {
        // 校验存在
        validateTalkTemplateExists(id);
        // 删除
        talkTemplateDao.deleteById(id);

        // 删除子表
        deleteTalkTemplateContentByTemplateId(id);
    }

    private void validateTalkTemplateExists(String id) {
        if (talkTemplateDao.selectById(id) == null) {
            throw new ServerException("谈话教育-谈话模板数据不存在");
        }
    }

    @Override
    public TalkTemplateDO getTalkTemplate(String id) {
        return talkTemplateDao.selectById(id);
    }

    @Override
    public PageResult<TalkTemplateDO> getTalkTemplatePage(TalkTemplatePageReqVO pageReqVO) {
        return talkTemplateDao.selectPage(pageReqVO);
    }

    @Override
    public List<TalkTemplateDO> getTalkTemplateList(TalkTemplateListReqVO listReqVO) {
        return talkTemplateDao.selectList(listReqVO);
    }


    // ==================== 子表（谈话教育-谈话模板内容） ====================

    @Override
    public List<TalkTemplateContentDO> getTalkTemplateContentListByTemplateId(String templateId) {
        return talkTemplateContentDao.selectListByTemplateId(templateId);
    }

    private void createTalkTemplateContentList(String templateId, List<TalkTemplateContentDO> list) {
        list.forEach(o -> o.setTemplateId(templateId));
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setPx(i + "");
        }
        list.forEach(o -> talkTemplateContentDao.insert(o));
    }

    private void updateTalkTemplateContentList(String templateId, List<TalkTemplateContentDO> list) {
        deleteTalkTemplateContentByTemplateId(templateId);
        list.forEach(o -> o.setId(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setPx(String.valueOf(i));
        }
        createTalkTemplateContentList(templateId, list);
    }

    private void deleteTalkTemplateContentByTemplateId(String templateId) {
        talkTemplateContentDao.deleteByTemplateId(templateId);
    }

}
