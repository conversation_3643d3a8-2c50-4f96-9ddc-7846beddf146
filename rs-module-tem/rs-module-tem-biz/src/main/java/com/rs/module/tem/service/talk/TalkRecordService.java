package com.rs.module.tem.service.talk;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.ihc.controller.admin.ipm.vo.PrescribeExecutePrintVO;
import com.rs.module.ihc.entity.ipm.PrescribeDO;
import com.rs.module.ihc.entity.ipm.PrescribeExecuteDO;
import com.rs.module.tem.controller.admin.talk.vo.TalkRecordListReqVO;
import com.rs.module.tem.controller.admin.talk.vo.TalkRecordPageReqVO;
import com.rs.module.tem.controller.admin.talk.vo.TalkRecordSaveReqVO;
import com.rs.module.tem.controller.app.record.GetRecordListByJgrybmVO;
import com.rs.module.tem.entity.talk.TalkRecordDO;
import com.rs.util.PDFUtil;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 谈话教育-个人谈话教育 Service 接口
 *
 * <AUTHOR>
 */
public interface TalkRecordService extends IBaseService<TalkRecordDO> {

    /**
     * 创建谈话教育-个人谈话教育
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createTalkRecord(@Valid TalkRecordSaveReqVO createReqVO);

    /**
     * 更新谈话教育-个人谈话教育
     *
     * @param updateReqVO 更新信息
     */
    void updateTalkRecord(@Valid TalkRecordSaveReqVO updateReqVO);

    /**
     * 删除谈话教育-个人谈话教育
     *
     * @param id 编号
     */
    void deleteTalkRecord(String id);

    /**
     * 获得谈话教育-个人谈话教育
     *
     * @param id 编号
     * @return 谈话教育-个人谈话教育
     */
    TalkRecordDO getTalkRecord(String id);

    /**
     * 获得谈话教育-个人谈话教育分页
     *
     * @param pageReqVO 分页查询
     * @return 谈话教育-个人谈话教育分页
     */
    PageResult<TalkRecordDO> getTalkRecordPage(TalkRecordPageReqVO pageReqVO);

    /**
     * 获得谈话教育-个人谈话教育列表
     *
     * @param listReqVO 查询条件
     * @return 谈话教育-个人谈话教育列表
     */
    List<TalkRecordDO> getTalkRecordList(TalkRecordListReqVO listReqVO);

    /**
     * 根据IP获取谈话室
     *
     * @param request
     * @return
     */
    public List<AreaDO> getThs(HttpServletRequest request);

    /**
     * 获取人员结束的谈话列表
     *
     * @param listReqVO
     * @return
     */
    List<TalkRecordDO> getPersonTaskRecordEndListByJgrybm(String jgrybm, String talkReason);

    /**
     * 根据监管人员编码获取谈话记录列表
     * @param getRecordListByJgrybmVO 查询条件，包含jgrybm和rangType(0全部1今天2昨天3近一周)
     * @return 谈话记录列表
     */
    List<TalkRecordDO> getRecordListByJgrybm(GetRecordListByJgrybmVO getRecordListByJgrybmVO);

    public byte[] getPdf(String id);
}
