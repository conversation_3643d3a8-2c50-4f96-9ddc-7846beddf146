package com.rs.module.tem.controller.admin.template.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 谈话教育-谈话模板列表 Request VO")
@Data
public class TalkTemplateTreeReqVO {
private static final long serialVersionUID = 1L;
    private String json;
}
