package com.rs.module.tem.controller.admin.template;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.tem.controller.admin.template.vo.*;
import com.rs.module.tem.dao.template.TalkTemplateContentDao;
import com.rs.module.tem.entity.template.TalkTemplateContentDO;
import com.rs.module.tem.entity.template.TalkTemplateDO;
import com.rs.module.tem.service.template.TalkTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 谈话教育-谈话模板")
@RestController
@RequestMapping("/tem/template/talkTemplate")
@Validated
public class TalkTemplateController {

    @Resource
    private TalkTemplateService talkTemplateService;
    @Resource
    private TalkTemplateContentDao talkTemplateContentDao;

    @PostMapping("/create")
    @ApiOperation(value = "创建谈话教育-谈话模板")
    @LogRecordAnnotation(bizModule = "tem:talkTemplate:create", operateType = LogOperateType.CREATE, title = "创建谈话教育-谈话模板",
            success = "创建谈话教育-谈话模板成功", fail = "创建谈话教育-谈话模板失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createTalkTemplate(@Valid @RequestBody TalkTemplateSaveReqVO createReqVO) {
        return success(talkTemplateService.createTalkTemplate(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新谈话教育-谈话模板")
    @LogRecordAnnotation(bizModule = "tem:talkTemplate:update", operateType = LogOperateType.UPDATE, title = "更新谈话教育-谈话模板",
            success = "更新谈话教育-谈话模板成功", fail = "更新谈话教育-谈话模板失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateTalkTemplate(@Valid @RequestBody TalkTemplateSaveReqVO updateReqVO) {
        talkTemplateService.updateTalkTemplate(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除谈话教育-谈话模板")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "tem:talkTemplate:delete", operateType = LogOperateType.DELETE, title = "删除谈话教育-谈话模板",
            success = "删除谈话教育-谈话模板成功", fail = "删除谈话教育-谈话模板失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteTalkTemplate(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            talkTemplateService.deleteTalkTemplate(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得谈话教育-谈话模板")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<TalkTemplateRespVO> getTalkTemplate(@RequestParam("id") String id) {
        TalkTemplateDO talkTemplate = talkTemplateService.getTalkTemplate(id);
        return success(BeanUtils.toBean(talkTemplate, TalkTemplateRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得谈话教育-谈话模板分页")
    public CommonResult<PageResult<TalkTemplateRespVO>> getTalkTemplatePage(@Valid @RequestBody TalkTemplatePageReqVO pageReqVO) {
        PageResult<TalkTemplateDO> pageResult = talkTemplateService.getTalkTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TalkTemplateRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得谈话教育-谈话模板列表")
    public CommonResult<?> getTalkTemplateList(@Valid @RequestBody TalkTemplateListReqVO listReqVO) {
        List<TalkTemplateDO> list = talkTemplateService.getTalkTemplateList(listReqVO);
        if (!list.isEmpty()) {
            return success(BeanUtils.toBean(list, TalkTemplateRespVO.class));
        }
        TalkTemplateDO talkTemplate = talkTemplateService.getTalkTemplate(listReqVO.getTemplateType());
        if (talkTemplate != null) {
            List<JSONObject> jsonArray = new ArrayList<>();
            List<TalkTemplateContentDO> talkTemplateContentDOS = talkTemplateContentDao.selectList(new LambdaQueryWrapperX<TalkTemplateContentDO>()
                    .eq(TalkTemplateContentDO::getTemplateId, talkTemplate.getId())
                    .orderByAsc(TalkTemplateContentDO::getPx)
            );

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("talkTemplateContents", talkTemplateContentDOS);

            jsonArray.add(jsonObject);

            return success(jsonArray);
        }
        return CommonResult.success(new ArrayList<>());
    }

    @PostMapping("/listThyyTree")
    @ApiOperation(value = "获得谈话教育-谈话模板列表-树形列表")
    public CommonResult<List<?>> listThyyTree(@RequestBody TalkTemplateTreeReqVO listReqVO) {
        String json = listReqVO.getJson();
        if (json == null || json.trim().isEmpty()) {
            return success(new ArrayList<>());
        }
        JSONArray jsonArray = JSONArray.parseArray(json);
        // 递归填充所有层级的 subTreeData
        fillSubTree(jsonArray);
        // 直接返回填充后的树结构
        return success(jsonArray);
    }

    /**
     * 递归填充树的子节点：
     * - 如果节点已有 subTreeData 且不为空，则继续向下递归；
     * - 否则根据当前节点 id 作为模板类型，从库中查询模板填充为叶子子节点。
     */
    private void fillSubTree(JSONArray nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }
        for (int i = 0; i < nodes.size(); i++) {
            JSONObject jsonObject = nodes.getJSONObject(i);
            JSONArray subTreeData = jsonObject.getJSONArray("subTreeData");
            if (subTreeData != null && !subTreeData.isEmpty()) {
                // 已有子节点，继续递归处理
                fillSubTree(subTreeData);
            } else {
                // 无子节点，按节点 id 作为模板类型补齐模板子节点
                List<TalkTemplateDO> talkTemplateDOList = talkTemplateService.lambdaQuery()
                        .eq(TalkTemplateDO::getTemplateType, jsonObject.getString("code"))
                        .list();
                List<JSONObject> children = talkTemplateDOList.stream().map(p -> {
                    JSONObject child = new JSONObject();
                    child.put("code", p.getId());
                    child.put("name", p.getTemplateName());
                    child.put("id", p.getId());
                    return child;
                }).collect(Collectors.toList());
                if (children != null && children.size() > 0) {
                    jsonObject.put("subTreeData", children);
                }
            }
        }
    }

    // ==================== 子表（谈话教育-谈话模板内容） ====================

    @GetMapping("/talk-template-content/list-by-template-id")
    @ApiOperation(value = "获得谈话教育-谈话模板内容列表")
    @ApiImplicitParam(name = "templateId", value = "谈话模板id")
    public CommonResult<List<TalkTemplateContentDO>> getTalkTemplateContentListByTemplateId(@RequestParam("templateId") String templateId) {
        return success(talkTemplateService.getTalkTemplateContentListByTemplateId(templateId));
    }


}
