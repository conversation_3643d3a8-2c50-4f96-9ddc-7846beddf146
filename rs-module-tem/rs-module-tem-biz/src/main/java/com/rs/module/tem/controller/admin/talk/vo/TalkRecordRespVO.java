package com.rs.module.tem.controller.admin.talk.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.common.annotation.Format;
import com.rs.framework.mybatis.annotation.Query;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.controller.admin.pm.vo.RiskAssmtRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.service.pm.RiskAssmtService;
import com.rs.module.tem.service.talk.TalkTaskService;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;
import java.util.Map;

@ApiModel(description = "管理后台 - 谈话教育-个人谈话教育 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TalkRecordRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    private String orgName;
    @ApiModelProperty("谈话类型 （字典： ZD_THJY_THFS）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_THJY_THFS", ref = "recordTypeName")
    private String recordType;
    private String recordTypeName;
    @ApiModelProperty("谈话记录编号(规则：T+9位单位编号+8位日期+6位序号)")
    private String talkCode;
    @ApiModelProperty("谈话原因（字典：ZD_THJY_THYY）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_THJY_THYY", ref = "talkReasonName")
    private String talkReason;
    private String talkReasonName;
    @ApiModelProperty("谈话室id")
    private String talkRoomId;
    @ApiModelProperty("谈话室名称")
    private String talkRoomName;
    @ApiModelProperty("谈话开始时间")
    private Date startTime;
    @ApiModelProperty("谈话结束时间")
    private Date endTime;
    @ApiModelProperty("谈话内容文本")
    private String talkContent;
    @ApiModelProperty("谈话小结")
    private String talkSummary;
    @ApiModelProperty("标签id，多个,分割")
    private String tagId;
    @ApiModelProperty("标签名称，多个,分割")
    private String tagName;
    @ApiModelProperty("心理评估（字典：	ZD_THJY_THPJ）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_THJY_THPJ", ref = "psychologyAssessName")
    private String psychologyAssess;
    private String psychologyAssessName;
    @ApiModelProperty("岗位协同（字典：	ZD_THJY_GWXT）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_THJY_GWXT", ref = "jobCollaborationName")
    private String jobCollaboration;
    private String jobCollaborationName;
    @ApiModelProperty("推送用户，多个,分割")
    private String pushUserid;
    @ApiModelProperty("推送用户名称，多个,分割")
    private String pushUserName;
    @ApiModelProperty("签名图片地址")
    private String signUrl;
    @ApiModelProperty("捺印图片地址")
    private String sealUrl;
    @ApiModelProperty("签名捺印文件地址")
    private String signSealUrl;
    @ApiModelProperty("视频url2")
    private String videoUrl;
    @ApiModelProperty("视频url")
    private String videoUrl2;
    /**
     * 谈话来源
     */
    @Trans(type = TransType.DICTIONARY, key = "ZD_THJY_THLY", ref = "taskSourceName")
    private String taskSource;
    private String taskSourceName;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    private String timer;


    @ApiModelProperty("谈话民警")
    private String addUser;

    @ApiModelProperty("谈话民警姓名")
    private String addUserName;


    @Format(service = PrisonerService.class, method = "getPrisonerByJgrybm", value = "jgrybm", toBean = PrisonerVwRespVO.class)
    private PrisonerVwRespVO prisoner;

    @ApiModelProperty("谈话任务")
    @Format(service = TalkTaskService.class, method = "getTalkTaskByTalkCode", value = "talkCode", toBean = TalkTaskRespVO.class)
    private TalkTaskRespVO talkTask;

    @ApiModelProperty("风险评估")
    @Format(service = RiskAssmtService.class, method = "getOneRiskAssmtByRybm", value = "jgrybm", toBean = RiskAssmtRespVO.class)
    private RiskAssmtRespVO riskAssmt;

    @ApiModelProperty("人员标签")
    @Query(sql = "select * from acp_pm_prisoner_tag where jgrybm = '${jgrybm}' and is_del = 0")
    public List<Map<String, Object>> tagList;
}
