package com.rs.module.integ.entity.haikang;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 集成服务-海康订阅事件 DO
 *
 * <AUTHOR>
 */
@TableName("integ_third_hk_subscribe_event")
@KeySequence("integ_third_hk_subscribe_event_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "integ_third_hk_subscribe_event")
public class HkSubscribeEventDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 需订阅的事件编码
     */
    private String eventCode;
    /**
     * 事件类型编码
     */
    private String eventType;
    /**
     * 存储的目标表
     */
    private String storageTable;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 是否启用
     */
    private Short isEnabled;

}
