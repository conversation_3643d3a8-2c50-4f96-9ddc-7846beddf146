package com.rs.module.integ.controller.admin.haikang;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.integ.third.component.haikang.HaiKangBaseComponent;
import com.rs.module.integ.third.component.haikang.HaiKangCommonComponent;
import com.rs.module.integ.third.pojo.ResponeResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "海康公共api")
@RestController
@RequestMapping("/api/haikangCommon")
public class HaikangCommonApiController {

    @Autowired
    private HaiKangCommonComponent haiKangCommonComponent;

    @GetMapping("/subscribeEvent")
    @ApiModelProperty("动态订阅指定的事件")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "eventTypes", value = "事件类型，多个逗号隔开", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "eventCallbackUrl", value = "回调地址",paramType = "query", dataType = "String")
    })
    public CommonResult<JSONObject> subscribeEvent(@RequestParam("eventTypes") String eventTypes,
                                                   @RequestParam(value = "eventCallbackUrl",required = false) String eventCallbackUrl) {
        if(ObjectUtil.isEmpty(eventTypes)){
            return CommonResult.error("事件类型不可为空");
        }

        ResponeResult<JSONObject> responeResult = haiKangCommonComponent.subscribeAppointEvent(eventTypes,eventCallbackUrl);
        if("0".equals(responeResult.getCode())){
            return CommonResult.success();
        }
        return CommonResult.error(responeResult.getMsg());
    }

    @GetMapping("/cancelSubscribeEvent")
    @ApiModelProperty("动态取消指定订阅的事件")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "eventType", value = "事件类型，多个逗号隔开", paramType = "query", dataType = "String")
    })
    public CommonResult<JSONObject> cancelSubscribeEvent(@RequestParam("eventTypes") String eventTypes) {
        if(ObjectUtil.isEmpty(eventTypes)){
            return CommonResult.error("事件类型不可为空");
        }
        ResponeResult<JSONObject> responeResult = haiKangCommonComponent.cancelAppointSubscribedEvent(eventTypes);
        if("0".equals(responeResult.getCode())){
            return CommonResult.success();
        }
        return CommonResult.error(responeResult.getMsg());
    }

    @GetMapping("/querySubscribeEvent")
    @ApiModelProperty("查询已订阅的事件")
    public CommonResult<JSONObject> querySubscribeEvent() {
        ResponeResult<JSONObject> responeResult = haiKangCommonComponent.querySubscribedEvent();
        if("0".equals(responeResult.getCode())){
            return CommonResult.success(responeResult.getData());
        }
        return CommonResult.error(responeResult.getMsg());
    }

}
