package com.rs.module.integ.service.haikang;

import com.rs.module.integ.dao.haikang.HkSubscribeEventDao;
import com.rs.module.integ.entity.haikang.HkSubscribeEventDO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;


import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 集成服务-海康订阅事件 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HkSubscribeEventServiceImpl extends BaseServiceImpl<HkSubscribeEventDao, HkSubscribeEventDO> implements HkSubscribeEventService {

    @Resource
    private HkSubscribeEventDao hkSubscribeEventDao;


}
