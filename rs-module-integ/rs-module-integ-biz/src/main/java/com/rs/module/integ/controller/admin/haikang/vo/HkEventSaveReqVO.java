package com.rs.module.integ.controller.admin.haikang.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 集成服务-海康应急报警事件数据新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class HkEventSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("机构编号")
    private String orgCode;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("事件类别")
    private String ability;

    @ApiModelProperty("事件唯一标识")
    @NotEmpty(message = "事件唯一标识不能为空")
    private String eventId;

    @ApiModelProperty("事件源编号，物理设备是资源编号")
    private String srcIndex;

    @ApiModelProperty("事件源类型")
    private String srcType;

    @ApiModelProperty("事件源名称")
    private String srcName;

    @ApiModelProperty("事件类型")
    private String eventType;

    @ApiModelProperty("事件状态")
    private String status;

    @ApiModelProperty("事件等级")
    private Integer eventLvl;

    @ApiModelProperty("脉冲超时时间（单位：秒）")
    private Integer timeout;

    @ApiModelProperty("事件发生时间（设备时间）")
    private String happenTime;

    @ApiModelProperty("事件发生的事件源父设备编码")
    private String srcParentIndex;

    @ApiModelProperty("data数据")
    private String data;

    @ApiModelProperty("事件从接收者（程序处理后）发出的时间")
    private String sendTime;

}
