package com.rs.module.integ.controller.admin.haikang;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.integ.component.HaiKangEventReceiveHandleComponent;
import com.rs.module.integ.third.pojo.ResponeResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "海康事件接收接口")
@RestController
@RequestMapping("/api/haikangEvent")
public class HaiKangEventReceiveController {

    @Autowired
    private HaiKangEventReceiveHandleComponent handleComponent;

    @PostMapping("/receive")
    @ApiModelProperty("公共接收海康推送的事件")
    public CommonResult<JSONObject> subscribeEvent(@RequestBody JSONObject receiveData) {
        handleComponent.handle(receiveData);
        return CommonResult.success();
    }
}
