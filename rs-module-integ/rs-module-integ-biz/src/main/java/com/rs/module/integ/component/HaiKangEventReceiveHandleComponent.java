package com.rs.module.integ.component;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.integ.controller.admin.haikang.vo.HkEventSaveReqVO;
import com.rs.module.integ.entity.haikang.HkSubscribeEventDO;
import com.rs.module.integ.service.haikang.HkSubscribeEventService;
import com.rs.module.integ.service.haikang.HkReportEventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class HaiKangEventReceiveHandleComponent {

    //存储订阅的事件变量
    public static Map<String, HkSubscribeEventDO> subscribeEventMap = new HashMap<>();

    @Value("${haikang.orgCode}")
    private String orgCode;
    @Value("${haikang.orgName}")
    private String orgName;

    @Autowired
    private HkSubscribeEventService hkSubscribeEventService;

    @Autowired
    private HkReportEventService hkReportEventService;


    public void handle(JSONObject receiveData){
        //事件类别，根据这个决定往哪张表存储
        String ability = receiveData.getJSONObject("params").getString("ability");
        List<JSONObject> eventJsonList = JSONArray.parseArray(JSON.toJSONString(receiveData.getJSONObject("params").get("events")),JSONObject.class);
        String sendTime = receiveData.getJSONObject("params").getString("sendTime");
        Map<String,List<HkEventSaveReqVO>> map = parseHkEvent(ability,sendTime,eventJsonList);
        for(Map.Entry<String,List<HkEventSaveReqVO>> eventMap:map.entrySet()){
            hkReportEventService.createHkEvent(getStorageTable(eventMap.getKey()),eventMap.getValue());
        }
    }

    private Map<String,List<HkEventSaveReqVO>> parseHkEvent(String ability,String sendTime,List<JSONObject> eventJsonList){
        Map<String,List<HkEventSaveReqVO>> map = new HashMap<>();
        for(JSONObject eventJson:eventJsonList){
            HkEventSaveReqVO saveReqVO = BeanUtils.toBean(eventJson,HkEventSaveReqVO.class);
            saveReqVO.setId(StringUtil.getGuid32());
            saveReqVO.setAbility(ability);
            saveReqVO.setSendTime(sendTime);
            if(eventJson.containsKey("data")){
                saveReqVO.setData(JSON.toJSONString(eventJson.get("data")));
            }
            saveReqVO.setOrgCode(orgCode);
            saveReqVO.setOrgName(orgName);
            List<HkEventSaveReqVO> saveReqVOList = new ArrayList<>();
            if(map.containsKey(saveReqVO.getEventType())){
                saveReqVOList = map.get(saveReqVO.getEventType());
            }
            saveReqVOList.add(saveReqVO);
            map.put(saveReqVO.getEventType(),saveReqVOList);
        }
        return map;
    }

    private String getStorageTable(String eventType){
        if(subscribeEventMap.containsKey(eventType)){
            return subscribeEventMap.get(eventType).getStorageTable();
        }
        LambdaQueryWrapper<HkSubscribeEventDO> eventDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        eventDOLambdaQueryWrapper.eq(HkSubscribeEventDO::getEventType,eventType).eq(HkSubscribeEventDO::getOrgCode,orgCode);
        HkSubscribeEventDO eventDO = hkSubscribeEventService.getOne(eventDOLambdaQueryWrapper);
        if(ObjectUtil.isEmpty(eventDO)){
            throw new ServerException("查询不到对应的事件类型");
        }
        subscribeEventMap.put(eventDO.getEventType(),eventDO);
        return eventDO.getStorageTable();
    }

}
