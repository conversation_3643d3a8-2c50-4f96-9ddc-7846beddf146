package com.rs.module.integ.component;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.module.integ.entity.haikang.HkSubscribeEventDO;
import com.rs.module.integ.service.haikang.HkSubscribeEventService;
import com.rs.module.integ.third.component.haikang.HaiKangCommonComponent;
import com.rs.module.integ.third.pojo.ResponeResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 项目启动后，自动订阅海康的事件
 */
@Component
public class HaiKangAutoSubscribeEventComponent implements CommandLineRunner {

    @Autowired
    private HaiKangCommonComponent haiKangCommonComponent;

    @Autowired
    private HkSubscribeEventService hkSubscribeEventService;

    @Override
    public void run(String... args) throws Exception {
        LambdaQueryWrapper<HkSubscribeEventDO> subscribeEventDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        subscribeEventDOLambdaQueryWrapper.eq(HkSubscribeEventDO::getOrgCode,haiKangCommonComponent.getProperties().getOrgCode());
        subscribeEventDOLambdaQueryWrapper.eq(HkSubscribeEventDO::getIsEnabled,(short)1);
        List<HkSubscribeEventDO> subscribeEventDOList = hkSubscribeEventService.list(subscribeEventDOLambdaQueryWrapper);
        if(CollectionUtil.isEmpty(subscribeEventDOList)){
            return;
        }
        StringBuilder events = new StringBuilder();
        for(int i=0;i<subscribeEventDOList.size();i++){
            events.append(subscribeEventDOList.get(i).getEventCode());
            if(i<subscribeEventDOList.size()-1){
                events.append(",");
            }
        }
        ResponeResult<JSONObject> result = haiKangCommonComponent.subscribeAppointEvent(events.toString(),null);
        if("0".equals(result.getCode())){
            //存储到静态变量
            subscribeEventDOList.forEach(x->{
                HaiKangEventReceiveHandleComponent.subscribeEventMap.put(x.getEventType(),x);
            });
        }

        System.out.printf("海康事件订阅："+ JSON.toJSONString(result));
    }
}
