package com.rs.module.integ.config;

import com.rs.module.pam.api.PamAqtsApi;
import com.rs.module.pam.api.PamJsgkApi;
import com.rs.module.pam.api.PamJydtApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

import com.rs.module.acp.controller.admin.pm.LocalsenseTagApi;

/**
 * INTEG模块RPC远程访问配置类(备注：rs-module-base模块中RpcConfig类已注册部分api)
 *
 * <AUTHOR>
 * @date 2025年8月14日
 */
@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {LocalsenseTagApi.class})
public class IntegRpcConfig {

}
