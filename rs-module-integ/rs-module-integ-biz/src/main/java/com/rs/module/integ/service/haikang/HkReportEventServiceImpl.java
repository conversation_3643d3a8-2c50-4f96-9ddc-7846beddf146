package com.rs.module.integ.service.haikang;

import com.rs.module.integ.controller.admin.haikang.vo.HkEventSaveReqVO;
import com.rs.module.integ.dao.haikang.HkReportEventDao;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.framework.common.util.object.BeanUtils;


import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 集成服务-海康应急报警事件数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HkReportEventServiceImpl implements HkReportEventService {

    @Resource
    private HkReportEventDao hkReportEventDao;


    @Override
    public boolean createHkEvent(String storageTable,List<HkEventSaveReqVO> eventSaveReqVOList) {
        return hkReportEventDao.createHkEvent(storageTable,eventSaveReqVOList);
    }
}
