package com.rs.module.integ.controller;

import cn.hutool.core.lang.tree.Tree;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.pm.LocalsenseTagApi;
import com.rs.module.acp.controller.admin.pm.dto.AreaTreeNode;
import com.rs.module.acp.controller.admin.pm.dto.AreaTreeNodeDTO;
import com.rs.module.acp.controller.admin.pm.dto.RoomDetailDTO;
import com.rs.module.acp.controller.admin.pm.dto.TagTrackDTO;
import io.swagger.annotations.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "三维地图")
@RestController
@RequestMapping("/api/twinplat/3dMap")
public class AcpTwinPlatFormController {
    @Resource
    private LocalsenseTagApi localsenseTagApi;
    @GetMapping("/areaInfo")
    @ApiOperation(value="提供监所下的监区和监室信息")
    @ApiImplicitParam(name = "areaCode", value = "code", required = true)
    public CommonResult<List<Tree<String>>> getAreaInfo(@RequestParam("areaCode") String areaCode){
        return localsenseTagApi.getAreaInfo(areaCode);
    }

    /**
     * 2、监所、监区、监室三类手环聚合数据
     * 返回某个监所下手环聚合总数（监所、监区、监室）——通过参数返回在押人员和民警的数据
     */
    @GetMapping("/aggregateData")
    @ApiOperation(value="监所、监区、监室三类手环聚合数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaCode", value = "区域编码", required = true),
            @ApiImplicitParam(name = "personType", value = "人员类型（01：在押人员，02：民警，不传则查询所有）", required = false)
    })
    public CommonResult<List<Tree<String>>> getAggregateData(
            @RequestParam("areaCode") String areaCode,
            @RequestParam(value = "personType", required = false) String personType){
        return localsenseTagApi.getAggregateData(areaCode, personType);
    }

    /**
     * 3、提供某个监室下的所有手环定位数据
     * 返回某个监室下的所有手环定位数据，——通过参数返回在押人员和民警的手环或民警卡定位数据
     */
    @GetMapping("/roomPositionData")
    @ApiOperation(value="提供某个监室下的所有手环定位数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaCode", value = "区域编码", required = true),
            @ApiImplicitParam(name = "roomId", value = "监室ID", required = true),
            @ApiImplicitParam(name = "personType", value = "人员类型（01：在押人员，02：民警，不传则查询所有）", required = false)
    })
    public CommonResult<List<RoomDetailDTO>> getRoomPositionData(
            @RequestParam("areaCode") String areaCode,
            @RequestParam("roomId") String roomId,
            @RequestParam(value = "personType", required = false) String personType){
        return localsenseTagApi.getRoomPositionData(areaCode,roomId, personType);
    }

    /**
     * 4、提供某个手环对应的详情数据
     * 返回手环电量、心率、体温基础数据及绑定在押人员的基础数据；（民警卡无详情数据）
     */
    @GetMapping("/tagDetail")
    @ApiOperation(value="提供某个手环对应的详情数据")
    @ApiImplicitParam(name = "tagId", value = "手环ID", required = true)
    public CommonResult<RoomDetailDTO> getTagDetail(@RequestParam("tagId") String tagId){
        return localsenseTagApi.getTagDetail(tagId);
    }

    /**
     * 5、提供手环或民警卡的历史轨迹数据
     * 返回手环或民警卡的历史轨迹数据在地图上展示——通过参数返回在押人员和民警的手环或民警卡历史定位数据
     */
    @GetMapping("/historyTrack")
    @ApiOperation(value="提供手环或民警卡的历史轨迹数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "num", value = "被监管人员编码/民警ID", required = true),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true),
            @ApiImplicitParam(name = "personType", value = "人员类型（01：在押人员，02：民警，不传则查询所有）", required = false)
    })
    public CommonResult<List<TagTrackDTO>> getHistoryTrack(
            @RequestParam("num") String num,
            @RequestParam("startTime") String startTime,
            @RequestParam("endTime") String endTime,
            @RequestParam(value = "personType", required = false) String personType){
        return localsenseTagApi.getHistoryTrack(num, startTime, endTime, personType);
    }

    @GetMapping("/getDeviceTreeByArea")
    @ApiOperation(value="监所下的监区和监室信息及设备信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码", required = true),
            @ApiImplicitParam(name = "deviceStatus" ,required = false),
            @ApiImplicitParam(name = "roomId",required = false),
            @ApiImplicitParam(name = "deviceName",required = false),
            @ApiImplicitParam(name = "deviceTypeId",required = false)
    })
    CommonResult<List<AreaTreeNode>> getDeviceTreeByArea(@RequestParam("orgCode") String orgCode,
                                                         @RequestParam(value = "deviceStatus" ,required = false) String deviceStatus,
                                                         @RequestParam(value = "roomId",required = false) String roomId,
                                                         @RequestParam(value = "deviceName",required = false) String deviceName,
                                                         @RequestParam(value = "deviceTypeId",required = false) String deviceTypeId){
        return localsenseTagApi.getDeviceTreeByArea(orgCode,deviceStatus,roomId,deviceName,deviceTypeId);
    }
}
