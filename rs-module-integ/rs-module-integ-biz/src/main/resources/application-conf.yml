conf:
  server:
    port:
      ihc: 9111
  snowflake:
    worker-id: 1 		#全局唯一guid生成器终端ID,最大值为31，最小值为1
    datacenter-id: 2 	#全局唯一guid生成器数据中心ID,最大值为31，最小值为1
  system-mark: integ
  matchers:
    ignores: /doc.html/**,/swagger/**,/rpc-api/**
  debug: false
  datasource:
    druid:
      log-slow-sql: true
      slow-sql-millis: 100
    dynamic:
      druid:
        initial-size: 1
        min-idle: 1 		# 最小连接池数量
        max-active: 20 		# 最大连接池数量
        max-wait: 600000 	# 配置获取连接等待超时的时间，单位：毫秒
      master:
        url: ********************************************************************************************************************************
        driver-class-name: org.postgresql.Driver
        username: postgres
        password: Go@123456
  mongodb:
    uri: mongodb://*************:27111/bsp
    hosts:
  redis:
    host: *************
    port: 6399
    database: 3
    password: redisbsp
    timeout: 6000  # 连接超时时长（毫秒）
    max-redirects: 3
    lettuce:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接

---
conf:
  nacos:
    enabled: true
    ip: *************
    port: 8848
    username: nacos
    password: nacos@gxx
    namespace: rs
    group: DEFAULT_GROUP

---
conf:
  xxl:
    enabled: true
    admin:
      addresses: http://*************:8080/xxl-job-admin
      username: admin
      password: xxlbsp
    executor:
      port: 9999
      logPath: ./logs/xxl-job

---
conf:
  bsp:
    baseUrl: http://*************:1910
    token:
      url: http://*************:1910/oauth/token
  bpm:
    baseUrl: http://*************:1911
    
---
conf:
  legal-platform-service:
    data-share:
      url: http://************:3254

---
conf:
  dromara:
    x-file-storage:
      enable-storage: true
      access-key: admin
      secret-key: admin123456
      end-point: http://*************:9010
      bucket-name: rgf
      domain: http://*************:9010/rgf/
      base-path:

########################  海康威视 ###################################
---
conf:
  haikang:
    region: root00000000
    orgCode: 110000113
    orgName: 第三看守所
    events: 111111
    eventCallbackUrl: *************:9111
    config:
      host: 127.0.0.1
      appKey: xxxx
      appSecret: xxxx