package com.rs.module.integ.third.pojo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/24 17:02
 */
@Data
public class PageApiResult<T> {
    private static final long serialVersionUID = 1L;

    private List<T> list;

    private Long total;

    public PageApiResult() {
    }

    public PageApiResult(List<T> list, Long total) {
        this.list = list;
        this.total = total;
    }

    public PageApiResult(Long total) {
        this.list = new ArrayList<>();
        this.total = total;
    }
}
