package com.rs.module.integ.third.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 门禁事件类型枚举
 * <AUTHOR>
 */
public enum AccessControlEventType {

    // 门禁操作相关事件
    AUTH_SUCCESS_WAIT_REMOTE_OPEN(197390, "认证成功等待远程开门"),
    CARD_NOT_IN_MULTI_AUTH_GROUP(197391, "卡不属于多重认证群组"),
    CLOSE_PERIOD_START(198148, "常关时段开始"),
    CLOSE_PERIOD_END(198150, "常关时段结束"),
    OPEN_TIMEOUT(198400, "开门超时"),
    OPEN_PERIOD_START(198401, "常开时段开始"),
    OPEN_PERIOD_END(198402, "常开时段结束"),
    ENTER_FIRST_CARD_OPEN(198404, "进入首卡常开状态"),
    EXIT_FIRST_CARD_OPEN(198405, "结束首卡常开状态"),
    DOOR_FORCED_OPEN(198657, "门被外力开启"),
    NORMAL_OPEN(198913, "正常开门"),
    BUTTON_OPEN(198916, "按钮开门"),
    REMOTE_SOFTWARE_OPEN(198919, "远程软件开门"),
    REMOTE_SOFTWARE_KEEP_OPEN(198922, "远程软件常开"),
    REMOTE_SOFTWARE_KEEP_CLOSE(199172, "远程软件常闭"),
    NORMAL_CLOSE(199169, "正常关门"),
    REMOTE_SOFTWARE_CLOSE(199171, "远程软件关门"),
    LOCK_OPEN(199941, "门锁打开"),
    LOCK_CLOSE(199942, "门锁关闭"),

    // 设备状态事件
    READER_TAMPER_ALARM(200453, "读卡器防拆报警"),
    READER_TAMPER_RECOVER(200454, "读卡器防拆报警恢复"),
    READER_OFFLINE_ALARM(200455, "读卡器掉线报警"),
    READER_ONLINE_RECOVER(200456, "读卡器掉线报警恢复"),
    DEVICE_TAMPER_ALARM(199708, "设备防拆报警"),
    DEVICE_TAMPER_RECOVER(199709, "设备防拆恢复"),
    DEVICE_OFFLINE(199710, "装置离线"),
    DEVICE_ONLINE(199711, "装置连线"),
    DEVICE_RESTART(199712, "重新启动"),
    SYSTEM_INIT(199713, "系统初始化"),
    DATA_CORRUPTED(199714, "数据被损坏"),
    CALL_CENTER(199715, "呼叫中心（1T500专有）"),

    // 认证相关事件
    CARD_FINGERPRINT_PASS(196885, "刷卡+指纹认证通过"),
    FINGERPRINT_PASSWORD_PASS(196887, "指纹+密码认证通过"),
    VALID_CARD_PASS(198914, "合法卡比对通过"),
    CARD_PASSWORD_PASS(198915, "刷卡+密码认证通过"),
    PASSWORD_ERROR(197121, "密码错误"),
    CARD_NUMBER_ERROR(197122, "输入卡号错误"),
    INPUT_PASSWORD_INSTEAD_OF_CARD(197123, "输入密码而非卡号"),
    CARD_LENGTH_ERROR(197124, "卡号长度错误"),
    CARD_CHECK_DIGIT_ERROR(197125, "卡号数字检查错误"),
    CARD_NOT_EXIST(197634, "无此卡号"),
    CARD_EXPIRED(197633, "卡号过期"),
    CARD_PASSWORD_NOT_OPEN(198145, "刷卡+密码不开门"),
    CARD_NO_PERMISSION(197635, "卡未分配权限"),
    CARD_FINGERPRINT_PASSWORD_PASS(196886, "刷卡+指纹+密码通过"),
    WORK_ID_PASSWORD_PASS(196897, "工号+密码认证通过"),
    FINGERPRINT_PASS(197127, "指纹比对通过"),
    FINGERPRINT_FAIL(197128, "指纹比对失败"),
    CARD_FINGERPRINT_PASSWORD_FAIL(197136, "刷卡+指纹+密码失败"),
    CARD_FINGERPRINT_PASSWORD_TIMEOUT(197137, "刷卡+指纹+密码超时"),
    FINGERPRINT_NOT_EXIST(197140, "指纹不存在"),
    WORK_ID_PASSWORD_FAIL(197158, "工号+密码认证失败"),

    // 人脸相关认证事件
    FACE_FINGERPRINT_PASS(196888, "人脸+指纹认证通过"),
    FACE_FINGERPRINT_FAIL(197141, "人脸+指纹认证失败"),
    FACE_FINGERPRINT_TIMEOUT(197142, "人脸+指纹认证超时"),
    FACE_PASSWORD_PASS(196889, "人脸+密码认证通过"),
    FACE_PASSWORD_FAIL(197143, "人脸+密码认证失败"),
    FACE_PASSWORD_TIMEOUT(197144, "人脸+密码认证超时"),
    FACE_CARD_PASS(196890, "人脸+刷卡认证通过"),
    FACE_CARD_FAIL(197145, "人脸+刷卡认证失败"),
    FACE_CARD_TIMEOUT(197146, "人脸+刷卡认证超时"),
    FACE_PASSWORD_FINGERPRINT_PASS(196891, "人脸+密码+指纹认证通过"),
    FACE_PASSWORD_FINGERPRINT_FAIL(197147, "人脸+密码+指纹认证失败"),
    FACE_PASSWORD_FINGERPRINT_TIMEOUT(197148, "人脸+密码+指纹认证超时"),
    FACE_CARD_FINGERPRINT_PASS(196892, "人脸+刷卡+指纹认证通过"),
    FACE_CARD_FINGERPRINT_FAIL(197149, "人脸+刷卡+指纹认证失败"),
    FACE_CARD_FINGERPRINT_TIMEOUT(197150, "人脸+刷卡+指纹认证超时"),
    FACE_PASS(196893, "人脸认证通过"),
    FACE_FAIL(197151, "人脸认证失败"),
    RECOGNITION_FAIL(197160, "识别失败"),
    LIVE_DETECTION_FAIL(197161, "真人检测失败"),
    ID_CARD_COMPARISON_PASS(197162, "人证比对通过"),
    ID_CARD_COMPARISON_FAIL(197163, "人证比对失败"),

    // 其他特殊认证事件
    FIRST_CARD_PASS(196874, "首卡比对通过"),
    MULTI_AUTH_PASS(196883, "多重认证成功"),
    MULTI_AUTH_SUPER_PASSWORD_PASS(196884, "多重认证超级密码成功"),
    PATROL_CARD_PASS(198918, "巡查卡比对通过"),
    SUPER_CARD_PASS(198921, "超级卡比对通过"),
    COERCION_CARD_PASS(199425, "胁迫卡比对通过"),
    ANTI_PASSBACK_FAIL(197383, "反潜回认证失败"),
    CARD_NOT_IN_MULTI_AUTH_PERIOD(197392, "卡不在多重认证时段内"),
    MULTI_AUTH_SUPER_PASSWORD_ERROR(197393, "多重认证超级密码错误"),
    MULTI_AUTH_REMOTE_FAIL(197394, "多重认证远程认证失败"),
    COERCION_CARD_NO_PERMISSION(197396, "胁迫卡未分配权限"),
    SUPER_CARD_NO_PERMISSION(197397, "超级卡未分配权限"),
    MULTI_AUTH_DUPLICATE(197400, "多重认证重复认证"),
    MULTI_AUTH_TIMEOUT(197401, "多重认证超时"),
    INTERLOCK_CANNOT_OPEN(198146, "互锁中无法开门"),
    ANTI_PASSBACK_INVALID(198149, "反潜回读卡器刷卡无效"),
    PATROL_CARD_INVALID_PERIOD(197399, "巡查卡无效时段"),
    INTERLOCK_DOOR_NOT_CLOSED(198658, "互锁门未关闭"),
    BLACKLIST_EVENT(197889, "黑名单事件"),
    TIME_GROUP_ERROR(197384, "时段组错误"),
    HOLIDAY_PERMISSION_ERROR(197378, "假期权限不合"),
    PERMISSION_ERROR(197377, "权限不合"),
    FINGERPRINT_PASSWORD_FAIL(197138, "指纹+密码认证失败"),
    CARD_FINGERPRINT_FAIL(197134, "刷卡+指纹认证失败"),
    CARD_AUTH_EXCEED_LIMIT(199429, "卡号认证超次报警"),
    ALARM_RELEASED(199681, "解除警报"),
    COERCION_ALARM(199428, "胁迫报警");

    // 事件类型码
    private final int code;
    // 事件名称
    private final String name;

    // 构造函数
    AccessControlEventType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    // 缓存code与枚举的映射关系（提高查询效率）
    private static final Map<Integer, AccessControlEventType> CODE_MAP = new HashMap<>();

    static {
        // 初始化映射
        for (AccessControlEventType type : values()) {
            CODE_MAP.put(type.code, type);
        }
    }

    /**
     * 根据事件类型码获取枚举实例
     * @param code 事件类型码
     * @return 对应的枚举实例，若不存在则返回null
     */
    public static AccessControlEventType getByCode(int code) {
        return CODE_MAP.get(code);
    }

    /**
     * 根据事件类型码获取事件名称
     * @param code 事件类型码
     * @return 事件名称，若不存在则返回"未知事件"
     */
    public static String getNameByCode(int code) {
        AccessControlEventType type = getByCode(code);
        return type != null ? type.name : "未知事件";
    }

    // getter方法
    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
