package com.rs.module.integ.third.configuration;

import com.rs.module.integ.third.component.haikang.HaiKangDoorControlComponent;
import com.rs.module.integ.third.component.haikang.HaiKangCommonComponent;
import com.rs.module.integ.third.component.haikang.HaiKangTVWallControlComponent;
import com.rs.module.integ.third.config.HaiKangProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(HaiKangProperties.class)
public class HaiKangAutoConfiguration {

    @Bean
    public HaiKangDoorControlComponent doorControlComponent(HaiKangProperties haiKangProperties) {
        return new HaiKangDoorControlComponent(haiKangProperties);
    }
    @Bean
    public HaiKangTVWallControlComponent tvwallControlComponent(HaiKangProperties haiKangProperties) {
        return new HaiKangTVWallControlComponent(haiKangProperties);
    }

    @Bean
    public HaiKangCommonComponent haiKangEmergencyAlarmComponent(HaiKangProperties haiKangProperties) {
        return new HaiKangCommonComponent(haiKangProperties);
    }
}
