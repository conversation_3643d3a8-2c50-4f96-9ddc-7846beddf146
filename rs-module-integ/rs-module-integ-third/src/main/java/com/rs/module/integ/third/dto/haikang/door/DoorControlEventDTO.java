package com.rs.module.integ.third.dto.haikang.door;

import lombok.Data;

/**
 * 门禁事件DTO
 * <AUTHOR>
 * @Date 2025/7/24 17:03
 */
@Data
public class DoorControlEventDTO {
    /**
     * 事件ID，唯一标识这个事件
     */
    private String eventId;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 事件产生时间(事件产生的时间，采用ISO8601时间格式)
     */
    private String eventTime;

    /**
     * 人员唯一编码
     */
    private String personId;
    /**
     * 卡号
     */
    private String cardNo;
    /**
     * 人员姓名
     */
    private String personName;
    /**
     * 人员所属组织编码
     */
    private String orgIndexCode;
    /**
     * 人员所属组织名称
     */
    private String orgName;
    /**
     * 门禁点名称
     */
    private String doorName;
    /**
     * 门禁点编码
     */
    private String doorIndexCode;
    /**
     * 门禁点所在区域编码
     */
    private String doorRegionIndexCode;
    /**
     * 抓拍图片地址(抓拍图片uri，它是一个相对地址，可以通过“获取门禁事件抓拍的图片”的接口，获取到图片的数据)
     */
    private String picUri;
    /**
     * 图片存储服务的唯一标识(与picUri配对输出的字段信息，用于“获取门禁事件抓拍的图片”接口的输入参数)
     */
    private String svrIndexCode;
    /**
     * 事件类型
     */
    private Integer eventType;
    /**
     * 进出类型(1：进 0：出 -1:未知 要求：进门读卡器拨码设置为1，出门读卡器拨码设置为2)
     */
    private Integer inAndOutType;
    /**
     * 读卡器唯一标识
     */
    private String readerDevIndexCode;
    /**
     * 读卡器名称
     */
    private String readerDevName;
    /**
     * 控制器设备唯一标识
     */
    private String devIndexCode;
    /**
     * 控制器设备名称
     */
    private String devName;
    /**
     * 身份证图片地址(身份证图片uri，可以通过“获取门禁事件抓拍的图片”的接口，获取到图片的数据)
     */
    private String identityCardUri;
    /**
     * 事件入库时间，采用ISO8601时间格式
     */
    private String receiveTime;
    /**
     * 工号
     */
    private String jobNo;
    /**
     * 学号
     */
    private String studentId;
    /**
     * 证件号码
     */
    private String certNo;

}
