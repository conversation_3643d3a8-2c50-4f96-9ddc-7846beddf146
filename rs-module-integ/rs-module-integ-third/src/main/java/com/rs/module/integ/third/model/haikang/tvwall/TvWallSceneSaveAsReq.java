package com.rs.module.integ.third.model.haikang.tvwall;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class TvWallSceneSaveAsReq {
    @JSONField(name = "name")
    private String name;

    @JSONField(name = "dlp_id")
    private Integer dlpId;

    @JSONField(name = "scene_id")
    private Integer sceneId;

    @JSONField(name = "device_scene")
    private Integer deviceScene;

    @J<PERSON>NField(name = "copy_cycle")
    private Integer copyCycle;

    @JSONField(name = "copy_cycle_list")
    private String copyCycleList;
}