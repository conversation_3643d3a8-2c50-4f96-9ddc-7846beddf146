package com.rs.module.integ.third.dto.haikang.door;

import lombok.Data;

/**
 * 门禁点反控
 * <AUTHOR>
 * @Date 2025/7/24 10:01
 */
@Data
public class DoorControlDTO {
    /**
     * 门禁点唯一编号
     */
    private String doorIndexCode;
    /**
     * 控制结果编码 (0标识反控成功，其他表示失败，见附录E.3 门禁管理错误码说明)
     *
     */
    private String controlResultCode;
    /**
     * 控制结果描述
     */
    private String controlResultDesc;

}
