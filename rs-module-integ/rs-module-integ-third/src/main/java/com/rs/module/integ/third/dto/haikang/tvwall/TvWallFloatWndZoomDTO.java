package com.rs.module.integ.third.dto.haikang.tvwall;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class TvWallFloatWndZoomDTO {
    @JSONField(name = "id")
    private Integer id;

    @J<PERSON><PERSON>ield(name = "left")
    private Integer left;

    @JSO<PERSON>ield(name = "top")
    private Integer top;

    @JSONField(name = "width")
    private Integer width;

    @JSONField(name = "height")
    private Integer height;

    @JSONField(name = "full_larged")
    private Boolean fullLarged;

    @JSONField(name = "enlarged")
    private Boolean enlarged;

    @JSONField(name = "alarm_link_div")
    private Boolean alarmLinkDiv;

    @JSONField(name = "layer")
    private Integer layer;

    @JSONField(name = "dev_id")
    private Integer devId;

    @JSONField(name = "subwnd_num")
    private Integer subwndNum;

    @JSONField(name = "decoder_id")
    private Integer decoderId;

    @JSONField(name = "wndpos")
    private Integer wndpos;

    @JSONField(name = "wnd_id")
    private Integer wndId;

    @JSONField(name = "uri")
    private String uri;

    @JSONField(name = "attached_uri")
    private String attachedUri;

    @JSONField(name = "dlp_id")
    private Integer dlpId;

    @JSONField(name = "openwnd_mode")
    private Integer openwndMode;

    @JSONField(name = "dlp_row")
    private Integer dlpRow;

    @JSONField(name = "dlp_col")
    private Integer dlpCol;

    @JSONField(name = "device_wall_no")
    private Integer deviceWallNo;
}
