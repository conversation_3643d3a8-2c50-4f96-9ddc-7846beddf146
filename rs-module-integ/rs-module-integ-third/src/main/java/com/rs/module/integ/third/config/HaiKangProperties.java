package com.rs.module.integ.third.config;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "haikang")
@Data
public class HaiKangProperties {

    /**
     * API网关的后端服务上下文为：/artemis
     */
    private String artemisPath = "/artemis";
    /**
     * 海康平台区域根目录
     */
    private String region;
    /**
     * 实战平台组织编码
     */
    private String orgCode;
    /**
     * 海康平台API配置
     */
    private ArtemisConfig config;
    /**
     * 事件回调链接地址
     */
    private String eventCallbackUrl;
    /**
     * 订阅的事件，多个事件以逗号分隔
     */
    private String events;

}
