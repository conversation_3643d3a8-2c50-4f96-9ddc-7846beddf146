package com.rs.module.integ.third.enums;

/**
 * 门禁点反控状态枚举 （0-常开1-门闭2-门开3-常闭)
 *
 * <AUTHOR>
 * @Date 2025/7/23 21:27
 */
public enum DoorControlTypeEnum {
    OPEN(0, "常开"),
    CLOSE(1, "门闭"),
    OPEN_AND_CLOSE(2, "门开"),
    CLOSE_AND_OPEN(3, "常闭");

    public static DoorControlTypeEnum getEnum(int code) {
        for (DoorControlTypeEnum e : DoorControlTypeEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }
        return null;
    }

    private int code;
    private String name;

    DoorControlTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
