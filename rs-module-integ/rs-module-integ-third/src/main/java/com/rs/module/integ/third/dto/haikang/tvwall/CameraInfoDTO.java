package com.rs.module.integ.third.dto.haikang.tvwall;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class CameraInfoDTO {
    @JSONField(name = "index_code")
    private String indexCode;

    @JSONField(name = "stream_url")
    private String streamUrl;

    @JSONField(name = "name")
    private String name;

    @JSONField(name = "channel")
    private Integer channel;

    @JSONField(name = "stream_type")
    private Integer streamType;
}