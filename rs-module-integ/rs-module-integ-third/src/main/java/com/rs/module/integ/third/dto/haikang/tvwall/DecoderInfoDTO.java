package com.rs.module.integ.third.dto.haikang.tvwall;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class DecoderInfoDTO {
    @JSONField(name = "id")
    private Integer id;

    @JSONField(name = "name")
    private String name;

    @JSONField(name = "index_code")
    private String indexCode;

    @JSONField(name = "ip_addr")
    private String ipAddr;

    @JSONField(name = "port")
    private Integer port;

    @JSONField(name = "offline")
    private Boolean isOffline;
}
