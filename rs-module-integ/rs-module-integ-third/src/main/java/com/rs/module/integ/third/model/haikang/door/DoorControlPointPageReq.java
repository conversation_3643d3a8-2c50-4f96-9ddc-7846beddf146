package com.rs.module.integ.third.model.haikang.door;

import lombok.Data;

import java.util.List;

/**
 * @Description: 门禁控制点分页查询请求参数
 * <AUTHOR>
 * @Date 2025/7/23 16:59
 */
@Data
public class DoorControlPointPageReq {
    /**
     * 名称，模糊搜索，最大长度32，若包含中文，最大长度指不超过按照指定编码的字节长度，即getBytes("utf-8").length
     */
    private String name;
    /**
     * 区域编号
     */
    private List<String> regionIndexCodes;
    /**
     * true时，搜索regionIndexCodes及其子孙区域的资源；false时，只搜索 regionIndexCodes的资源
     */
    private Boolean isSubRegion;
    private Integer pageNo;
    private Integer pageSize;
    /**
     * 排序字段,注意：排序字段必须是查询条件，否则返回参数错误
     */
    private String orderBy;
    /**
     * 降序升序,降序：desc 升序：asc
     */
    private String orderType;
}
