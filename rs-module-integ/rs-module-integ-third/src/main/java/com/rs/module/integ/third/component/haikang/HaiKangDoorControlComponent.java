package com.rs.module.integ.third.component.haikang;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.rs.module.integ.third.config.HaiKangProperties;
import com.rs.module.integ.third.dto.haikang.door.DoorControlDTO;
import com.rs.module.integ.third.dto.haikang.door.DoorControlEventDTO;
import com.rs.module.integ.third.dto.haikang.door.DoorStatusDTO;
import com.rs.module.integ.third.enums.DoorControlTypeEnum;
import com.rs.module.integ.third.model.haikang.door.DoorControlEventPageReq;
import com.rs.module.integ.third.model.haikang.door.DoorControlPointPageReq;

import com.rs.module.integ.third.pojo.PageApiResult;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 海康门禁组件
 *
 * <AUTHOR>
 * @Date 2025/7/23 16:00
 */
@Slf4j
public class HaiKangDoorControlComponent extends HaiKangBaseComponent {

    public HaiKangDoorControlComponent(HaiKangProperties properties) {
        super(properties);
    }
    /**
     * v1.4 查询门禁点列表接口
     */
    public JSONObject queryDoorControlPoints(DoorControlPointPageReq pageReq) {
        String url = "/api/resource/v2/door/search";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("name", pageReq.getName());
        paramMap.put("regionIndexCodes", pageReq.getRegionIndexCodes());
        paramMap.put("isSubRegion", pageReq.getIsSubRegion());
        paramMap.put("pageNo", pageReq.getPageNo());
        paramMap.put("pageSize", pageReq.getPageSize());
        paramMap.put("orderBy", pageReq.getOrderBy());
        paramMap.put("orderType", pageReq.getOrderType());
        String request = null;
        try {
            request = sendRequest(paramMap, url);
        } catch (Exception e) {
            log.error("查询门禁点列表失败：{}", e);
        }
        if (request != null) {
            JSONObject jsonObject = JSON.parseObject(request);
            return jsonObject;
        }
        return null;
    }

    /**
     * v1.4	查询门禁点状态
     * 该接口支持门常开、门常闭、门开和门闭四种操作引起的门状态获取。门常开操作，
     * 门会一直处于开状态，不会自动关闭，执行门闭操作，门才会关上；
     * 门常闭操作，门会一直处于关毕状态，普通卡刷卡门不会被打开，执行门开操作，门会打开；门开操作，执行门打开动作，超过门打开时间，
     * 门会自动关上；门闭操作，执行关门动作，会立即把门关上。调用该接口，首先要通过获取门禁点资源列表的接口，
     * 获取到门禁点唯一编号，然后根据门禁点唯一编号进行门禁点状态状态查询。
     * 需要注意的是门通道必须接上门磁才能正常发送门状态变化通知，如果未接门磁，
     * 平台无法通过门状态变更通知来更新门状态。
     */
    public List<DoorStatusDTO> doorStatusQuery(List<String> doorIndexCodes) {
        String url = "/api/acs/v1/door/states";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("doorIndexCodes", doorIndexCodes);
        List<DoorStatusDTO> dtosList = new ArrayList<>();
        String response = null;
        try {
            response = sendRequest(paramMap, url);
        } catch (Exception e) {
            log.error("查询门禁点状态失败：{}", e);
        }
        if (response != null) {
            JSONObject jsonObject = JSON.parseObject(response);
            if (jsonObject == null || !"0".equals(jsonObject.getString("code"))) {
                log.error("查询门禁点状态失败: {}", jsonObject.get("msg"));
                throw new RuntimeException("查询门禁点状态失败: " + jsonObject.get("msg"));
            }
            JSONObject data = jsonObject.getJSONObject("data");
            JSONArray jsonArray = data.getJSONArray("authDoorList");
            for (int i = 0; i < jsonArray.size(); i++) {
                // 将 JSONArray 中的每个元素转换为 DoorControlPointDTO 对象
                DoorStatusDTO dto = jsonArray.getJSONObject(i).toJavaObject(DoorStatusDTO.class);
                dtosList.add(dto);
            }
        }
        return dtosList;
    }

    /**
     * v1.4	门禁点反控
     * 单个编号的门禁点反控
     *
     * @param doorIndexCode
     * @param controlType
     * @return
     */
    public JSONObject controlDoor(String doorIndexCode, DoorControlTypeEnum controlType) {
        List<DoorControlDTO> doorControlDTOS = controlDoor(new ArrayList<String>() {{
            add(doorIndexCode);
        }}, controlType);
        JSONObject jsonObject = new JSONObject();
        if (doorControlDTOS != null && doorControlDTOS.size() > 0) {
            DoorControlDTO doorControlDTO = doorControlDTOS.get(0);
            jsonObject.put("doorIndexCode", doorControlDTO.getDoorIndexCode());
            jsonObject.put("controlResultCode", doorControlDTO.getControlResultCode());
            jsonObject.put("controlResultDesc", doorControlDTO.getControlResultDesc());
        }
        return jsonObject;
    }

    /**
     * v1.4	门禁点反控
     * 该接口支持门常开、门常闭、门开和门闭四种操作。门常开操作，门会一直处于开状态，不会自动关闭，执行门闭操作，门才会关上；
     * 门常闭操作，门会一直处于关毕状态，普通卡刷卡门不会被打开，执行门开操作，门会打开；
     * 门开操作，执行门打开动作，超过门打开时间，门会自动关上；门闭操作，执行关门动作，会立即把门关上。
     * 调用该接口，首先要通过获取门禁点资源列表的接口，获取到门禁点唯一编号，然后根据门禁点唯一编号进行反控操作，该接口支持单个和多个门禁点操作，
     * 如果所有门禁点反控操作成功，则返回成功，其他情况都返回失败，在失败的情况下，会按每个门禁点返回对应的错误。
     */
    public List<DoorControlDTO> controlDoor(List<String> doorIndexCodes, DoorControlTypeEnum controlType) {
        String url = "/api/acs/v1/door/doControl";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("doorIndexCodes", doorIndexCodes);
        paramMap.put("controlType", controlType.getCode());
        List<DoorControlDTO> list = new ArrayList<>();
        String response = null;
        try {
            response = sendRequest(paramMap, url);
        } catch (Exception e) {
            log.error("查询门禁点反控失败：{}", e);
        }
        if (response != null) {
            JSONObject jsonObject = JSON.parseObject(response);
            if (jsonObject == null || !"0".equals(jsonObject.getString("code"))) {
                log.error("门禁点反控失败: {}", jsonObject.get("msg"));
                throw new RuntimeException("门禁点反控失败: " + jsonObject.get("msg"));
            }
            list = jsonObject.getObject("data", new TypeReference<List<DoorControlDTO>>() {
            });
        }
        return list;
    }

    /**
     * 1.4	查询门禁点事件v2
     * 功能描述：该接口可以查询发生在门禁点上的人员出入事件，支持多个维度来查询，支持按时间、人员、门禁点、事件类型四个维度来查询；
     * 其中按事件类型来查询的方式，如果查询不到事件，存在两种情况，一种是该类型的事件没有发生过，所以查询不到，还有一种情况，
     * 该类型的事件发生过，但是由于门禁管理组件对该事件类型订阅配置处于关闭状态，
     * 所以不会存储该类型的事件，导致查询不到，对于这种情况，需要到门禁管理组件中，将该事件类型的订阅配置打开。
     */
    public PageApiResult<DoorControlEventDTO> queryDoorEvents(DoorControlEventPageReq pageReq) {
        String url = "/api/acs/v2/door/events";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("pageNo", pageReq.getPageNo());
        paramMap.put("pageSize", pageReq.getPageSize());
        paramMap.put("startTime", formatISO8601WithTimezone(pageReq.getStartTime()));
        paramMap.put("endTime", formatISO8601WithTimezone(pageReq.getEndTime()));
        paramMap.put("receiveStartTime", formatISO8601WithTimezone(pageReq.getReceiveStartTime()));
        paramMap.put("receiveEndTime", formatISO8601WithTimezone(pageReq.getReceiveEndTime()));
        paramMap.put("doorIndexCodes", pageReq.getDoorIndexCodes());
        paramMap.put("eventTypes", pageReq.getEventTypes());
        paramMap.put("sort", pageReq.getSort());
        paramMap.put("order", pageReq.getOrder());
        String response = null;
        try {
            response = sendRequest(paramMap, url);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        PageApiResult pageApiResult = new PageApiResult();
        if (response != null) {
            JSONObject jsonObject = JSON.parseObject(response);
            if (!"0".equals(jsonObject.getString("code"))) {
                log.error("查询门禁点事件失败: {}", jsonObject.get("msg"));
                throw new RuntimeException("查询门禁点事件失败: " + jsonObject.get("msg"));
            }
            JSONObject data = jsonObject.getJSONObject("data");
            Long total = data.getLong("total");
            pageApiResult.setTotal(total);
            JSONArray jsonArray = data.getJSONArray("list");
            List<DoorControlEventDTO> dtos = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                // 将 JSONArray 中的每个元素转换为 DoorControlEventDTO 对象
                DoorControlEventDTO dto = jsonArray.getJSONObject(i).toJavaObject(DoorControlEventDTO.class);
                dtos.add(dto);
            }
            pageApiResult.setList(dtos);
            return pageApiResult;
        } else {
            pageApiResult.setList(new ArrayList());
            pageApiResult.setTotal(0L);
        }
        return pageApiResult;
    }


}
