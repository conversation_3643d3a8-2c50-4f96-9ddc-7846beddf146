package com.rs.module.integ.third.dto.haikang.tvwall;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class DlpInfoDTO {
    @J<PERSON>NField(name = "dlp_id")
    private Integer dlpId;

    @J<PERSON>NField(name = "dlp_name")
    private String dlpName;

    @JSONField(name = "index_code")
    private String indexCode;

    @JSONField(name = "row")
    private Integer row;

    @JSONField(name = "col")
    private Integer col;

    @JSONField(name = "belong_tvwall_indexcode")
    private String belongTvwallIndexcode;

    @JSONField(name = "dlp_type")
    private Integer dlpType;

    @JSONField(name = "decoder_id")
    private Integer decoderId;

    @JSONField(name = "attached_device_num")
    private Integer attachedDeviceNum;

    @JSONField(name = "cur_scene_id")
    private Integer curSceneId;

    @JSONField(name = "cur_scene_name")
    private String curSceneName;

    @<PERSON>SO<PERSON>ield(name = "layout_modified")
    private Boolean layoutModified;

    @JSO<PERSON>ield(name = "order")
    private Integer order;

    @JSONField(name = "floatwmd_list")
    private List<FloatWndDTO> floatwmdList;

    @JSONField(name = "monitor_list")
    private List<MonitorDTO> monitorList;
}
