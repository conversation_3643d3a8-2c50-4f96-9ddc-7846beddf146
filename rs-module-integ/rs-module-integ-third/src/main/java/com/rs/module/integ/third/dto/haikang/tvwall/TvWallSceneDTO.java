package com.rs.module.integ.third.dto.haikang.tvwall;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class TvWallSceneDTO {
    @JSONField(name = "tvwall_id")
    private Integer tvwallId;

    @JSONField(name = "scene_id")
    private Integer sceneId;

    @JSONField(name = "scene_name")
    private String sceneName;

    @JSONField(name = "default")
    private Boolean isDefault;

    @JSONField(name = "using")
    private Boolean isUsing;

    @JSONField(name = "scene_order")
    private Integer sceneOrder;

    @JSONField(name = "belong_dlp_id")
    private Integer belongDlpId;

    @JSONField(name = "device_scene")
    private Boolean isDeviceScene;
}