package com.rs.module.integ.third.component.haikang;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.constant.Constants;
import com.rs.module.integ.third.config.HaiKangProperties;
import com.rs.module.integ.third.cons.HaiKangConstants;
import com.rs.module.integ.third.pojo.ResponeResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 海康基础组件
 *
 * <AUTHOR>
 * @Date 2025/7/23 15:53
 */
@Slf4j
public class HaiKangBaseComponent {

    /**
     * 根据需求调整超时时间
     */
    static {
        //连接超时时间
        Constants.DEFAULT_TIMEOUT = 10000;
        //读取超时时间
        Constants.SOCKET_TIMEOUT = 60000;
    }

    @Autowired
    private HaiKangProperties properties;

    public HaiKangBaseComponent(HaiKangProperties properties) {
        this.properties = properties;
    }

    public HaiKangProperties getProperties() {
        return properties;
    }

    public String sendRequest(Map<String, Object> paramMap, String url) throws Exception {
        String body = JSON.toJSON(paramMap).toString();
        final String getCamsApi = properties.getArtemisPath() + url;
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getCamsApi);
            }
        };
        return ArtemisHttpUtil.doPostStringArtemis(properties.getConfig(), path, body, null, null, "application/json");
    }

    /**
     * 订阅事件
     * @return
     */
    public ResponeResult<JSONObject> subscribeEvent(){
        // 初始化订阅事件
        String eventCallbackUrl = properties.getEventCallbackUrl();
        String events = properties.getEvents();
        return subscribeEvent(events,eventCallbackUrl);
    }

    /**
     * 订阅指定的事件
     * @param eventTypes,多个使用逗号隔开
     * @param eventCallbackUrl,如果不传入，默认使用配置文件的
     * @return
     */
    public ResponeResult<JSONObject> subscribeAppointEvent(String eventTypes,String eventCallbackUrl){
        String actualEventCallbackUrl = ObjectUtils.isEmpty(eventCallbackUrl)?properties.getEventCallbackUrl():eventCallbackUrl;
        return subscribeEvent(eventTypes,actualEventCallbackUrl);
    }

    private ResponeResult<JSONObject> subscribeEvent(String eventTypes,String eventCallbackUrl){
        if (StringUtils.isNotEmpty(eventCallbackUrl) && StringUtils.isNotEmpty(eventTypes)) {
            // 先查询是否已经订阅过
            List<String> eventsList = Arrays.asList(eventTypes.split(","));
            List<String> subscribed = getsubscribed();
            List<String> notSubscribed = new ArrayList<>();
            for (String event : eventsList) {
                if (!subscribed.contains(event)) {
                    notSubscribed.add(event);
                }
            }
            if (notSubscribed.size() > 0) {
                // 未订阅的事件，进行订阅
                Map<String, Object> paramMap = new HashMap<>(3);
                paramMap.put("eventTypes", notSubscribed);
                paramMap.put("subWay", 1);
                paramMap.put("eventDest", eventCallbackUrl);
                String response = null;
                try {
                    response = sendRequest(paramMap,"/api/eventService/v1/eventSubscriptionByEventTypes");
                }catch (Exception e){
                    log.error("响应异常: {}", e.getMessage());
                }
                return parseResponse(response,JSONObject.class);
            }
        }
        return ResponeResult.success();
    }

    private List<String> getsubscribed(){
        List<String> subscribed = new ArrayList<>();
        ResponeResult result = querySubscribedEvent();
        if("0".equals(result.getCode())){
            JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(result.getData()));
            JSONObject data = jsonObject.getJSONObject("data");
            if (data != null) {
                JSONArray jsonArray = data.getJSONArray("detail");
                if (jsonArray != null && jsonArray.size() > 0) {
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject arrayJSONObject = jsonArray.getJSONObject(i);
                        List<String> types = arrayJSONObject.getObject("eventTypes", new TypeReference<List<String>>() {
                        });
                        subscribed.addAll(types);
                    }
                }
            }
        }
        return subscribed;
    }

    /**
     * 查询已订阅的事件列表
     * @return
     */
    public ResponeResult<JSONObject> querySubscribedEvent(){
        // 构造符合规范的请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("subWay", 1); // 默认值处理
        String response = null;
        try {
            // 发送带版本标识的请求
            response = sendRequest(requestBody, "/api/eventService/v1/eventSubscriptionView");
        } catch (Exception e) {
            log.error("响应异常: {}", e.getMessage());
        }
        if(ObjectUtils.isEmpty(response)){
            return ResponeResult.error();
        }
        return parseResponse(response,JSONObject.class);
    }

    /**
     * 取消事件订阅
     * @return
     */
    public ResponeResult<JSONObject> cancelSubscribedEvent(){
        // 构造符合规范的请求体
        if(StringUtils.isEmpty(properties.getEvents())){
            return ResponeResult.error("1","需要取消的事件不可为空");
        }
        return cancelSubscribedEvent(properties.getEvents());
    }

    /**
     * 取消指定的事件类型订阅
     * @param eventTypes
     * @return
     */
    public ResponeResult<JSONObject> cancelAppointSubscribedEvent(String eventTypes){
        return cancelSubscribedEvent(eventTypes);
    }

    private ResponeResult<JSONObject> cancelSubscribedEvent(String eventTypes){

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("eventTypes", Arrays.asList(eventTypes)); // 默认值处理

        String response = null;
        try {
            // 发送带版本标识的请求
            response = sendRequest(requestBody, "/api/eventService/v1/eventUnSubscriptionByEventTypes");
        } catch (Exception e) {
            log.error("响应异常: {}", e.getMessage());
        }
        if(ObjectUtils.isEmpty(response)){
            return ResponeResult.error();
        }
        return parseResponse(response,JSONObject.class);
    }

    protected String formatISO8601WithTimezone(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
        return sdf.format(date);
    }


    public  <T> ResponeResult<T> parseResponse(String response, Class<T> dataType) {
        if (StringUtils.isBlank(response)) {
            return new ResponeResult<>(HaiKangConstants.DEFAULT_ERROR_CODE, "Empty response", null);
        }

        try {
            JSONObject jsonObject = JSON.parseObject(response);
            String code = jsonObject.getString("code");
            String msg = jsonObject.getString("msg");
            T data = jsonObject.getObject("data", dataType);
            return new ResponeResult<>(code, msg, data);
        } catch (Exception e) {
            log.error("解析API响应失败: {}", response, e);
            return new ResponeResult<>(HaiKangConstants.DEFAULT_ERROR_CODE,
                    "Parse response error: " + e.getMessage(), null);
        }
    }

    public  <T> ResponeResult<T> errorResponse(Exception e) {
        return new ResponeResult<>(HaiKangConstants.DEFAULT_ERROR_CODE,
                "Request failed: " + e.getMessage(), null);
    }

}
