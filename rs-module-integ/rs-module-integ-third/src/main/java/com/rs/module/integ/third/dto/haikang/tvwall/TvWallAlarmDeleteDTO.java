package com.rs.module.integ.third.dto.haikang.tvwall;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class TvWallAlarmDeleteDTO {
    // 根据文档，删除报警接口返回空data
    // 实际响应结构：{"code":"0","msg":"SUCCESS","data":{}}
    // 所以这个DTO可以为空或包含基础字段
    @JSONField(name = "error_code")
    private Integer errorCode;

    @JSONField(name = "error_info")
    private String errorInfo;
}
