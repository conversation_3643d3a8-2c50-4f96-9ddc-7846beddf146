package com.rs.module.integ.third.dto.haikang.door;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 门禁点
 *
 * <AUTHOR>
 * @Date 2025/7/23 16:05
 */
@Data
public class DoorControlPointDTO {
    /**
     * 索引编码
     */
    private String indexCode;
    /**
     * 资源类型（此处固定为"door"）
     */
    private String resourceType;
    /**
     * 资源名称
     */
    private String name;
    /**
     * 门编号
     */
    private String doorNo;
    /**
     * 通道编号
     */
    private String channelNo;
    /**
     * 父级索引编码
     */
    private String parentIndexCode;
    /**
     * 控制一ID
     */
    private String controlOneId;
    /**
     * 控制二ID
     */
    private String controlTwoId;
    /**
     * 进门读卡器ID
     */
    private String readerInId;
    /**
     * 出门读卡器ID
     */
    private String readerOutId;
    /**
     * 门序号
     */
    private Integer doorSerial;
    /**
     * 协议类型
     */
    private String treatyType;
    /**
     * 区域索引编码
     */
    private String regionIndexCode;
    /**
     * 区域路径
     */
    private String regionPath;

    /**
     * 创建时间
     * 对应JSON中的"2018-11-28T16:47:27:358+08:00"格式
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 描述信息
     */
    private String description;
    /**
     * 通道类型
     */
    private String channelType;
    /**
     * 区域名称
     */
    private String regionName;
    /**
     * 区域路径名称
     */
    private String regionPathName;
    /**
     * 安装位置
     */
    private String installLocation;
}
