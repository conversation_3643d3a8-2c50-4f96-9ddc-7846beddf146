package com.rs.module.integ.third.enums;

/**
 * 门禁状态枚举 （0 初始状态，1 开门状态，2关门状态，3离线状态)
 *
 * <AUTHOR>
 * @Date 2025/7/23 21:27
 */
public enum DoorStatusEnum {
    INIT(0, "初始"),
    OPEN(1, "开门"),
    CLOSE(2, "关门"),
    OFFLINE(3, "离线");

    private int code;
    private String name;

    DoorStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
