package com.rs.module.integ.third.model.haikang.tvwall;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class TvWallFloatWndsCreateReq {
    @JSONField(name = "floatwnd_list")
    private List<FloatWndCreateItem> floatwndList;

    @Data
    public static class FloatWndCreateItem {
        @JSO<PERSON>ield(name = "dlp_id")
        private Integer dlpId;

        @JSONField(name = "wnd_pos")
        private Integer wndPos;

        @JSONField(name = "wnd_left")
        private Integer wndLeft;

        @JSONField(name = "wnd_top")
        private Integer wndTop;

        @JSONField(name = "wnd_width")
        private Integer wndWidth;

        @JSONField(name = "wnd_height")
        private Integer wndHeight;
    }
}
