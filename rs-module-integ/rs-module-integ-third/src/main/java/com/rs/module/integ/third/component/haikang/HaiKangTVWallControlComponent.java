package com.rs.module.integ.third.component.haikang;

import com.rs.module.integ.third.config.HaiKangProperties;
import com.rs.module.integ.third.dto.haikang.tvwall.*;
import com.rs.module.integ.third.model.haikang.tvwall.*;

import com.rs.module.integ.third.pojo.ResponeResult;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;


@Slf4j
public class HaiKangTVWallControlComponent extends HaiKangBaseComponent {

    public HaiKangTVWallControlComponent(HaiKangProperties properties) {
        super(properties);
    }


    // =============== 1. 获取电视墙大屏信息 ===============
    public ResponeResult<TvWallAllResourcesDTO> getAllResources() {
        String url = "/api/tvms/v1/tvwall/allResources";
        try {
            String response = sendRequest(new HashMap<>(), url);
            return parseResponse(response, TvWallAllResourcesDTO.class);
        } catch (Exception e) {
            log.error("获取电视墙大屏信息失败", e);
            return errorResponse(e);
        }
    }

    // =============== 2. 获取电视墙场景列表信息 ===============
    public ResponeResult<TvWallScenesDTO> getScenes() {
        String url = "/api/tvms/v1/tvwall/scenes";
        try {
            String response = sendRequest(new HashMap<>(), url);
            return parseResponse(response, TvWallScenesDTO.class);
        } catch (Exception e) {
            log.error("获取电视墙场景列表失败", e);
            return errorResponse(e);
        }
    }

    // =============== 3. 获取电视墙窗口信息列表 ===============
    public ResponeResult<TvWallWndsDTO> getWindowList(TvWallWndsReq req) {
        String url = "/api/tvms/v1/tvwall/wnds/get";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("dlp_id", req.getDlpId());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallWndsDTO.class);
        } catch (Exception e) {
            log.error("获取电视墙窗口信息列表失败", e);
            return errorResponse(e);
        }
    }

    // =============== 4. 批量上墙 ===============
    public ResponeResult<TvWallRealPlayDTO> batchAddToWall(TvWallRealPlayReq req) {
        String url = "/api/tvms/v1/tvwall/realplay/add";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("realplay_list", req.getRealplayList());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallRealPlayDTO.class);
        } catch (Exception e) {
            log.error("批量上墙失败", e);
            return errorResponse(e);
        }
    }

    // =============== 5. 批量下墙 ===============
    public ResponeResult<TvWallRealPlayDTO> batchRemoveFromWall(TvWallRealPlayReq req) {
        String url = "/api/tvms/v1/tvwall/realplay/delete";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("realplay_list", req.getRealplayList());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallRealPlayDTO.class);
        } catch (Exception e) {
            log.error("批量下墙失败", e);
            return errorResponse(e);
        }
    }

    // =============== 6. 场景创建 ===============
    public ResponeResult<TvWallSceneDTO> createScene(TvWallSceneCreateReq req) {
        String url = "/api/tvms/v1/public/tvwall/scene/addition";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("dlp_id", req.getDlpId());
            params.put("name", req.getName());
            params.put("device_scene", req.getDeviceScene());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallSceneDTO.class);
        } catch (Exception e) {
            log.error("创建场景失败", e);
            return errorResponse(e);
        }
    }

    // =============== 7. 场景修改 ===============
    public ResponeResult<TvWallSceneDTO> updateScene(TvWallSceneUpdateReq req) {
        String url = "/api/tvms/v1/public/tvwall/scene/update";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("scene_id", req.getSceneId());
            params.put("name", req.getName());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallSceneDTO.class);
        } catch (Exception e) {
            log.error("修改场景失败", e);
            return errorResponse(e);
        }
    }

    // =============== 8. 场景删除 ===============
    public ResponeResult<TvWallSceneDeleteDTO> deleteScene(TvWallSceneDeleteReq req) {
        String url = "/api/tvms/v1/public/tvwall/scene/deletion";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("scene_id", req.getSceneId());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallSceneDeleteDTO.class);
        } catch (Exception e) {
            log.error("删除场景失败", e);
            return errorResponse(e);
        }
    }

    // =============== 9. 场景另存为 ===============
    public ResponeResult<TvWallSceneDTO> saveAsScene( TvWallSceneSaveAsReq req) {
        String url = "/api/tvms/v1/public/tvwall/scene/saveAs";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("name", req.getName());
            params.put("dlp_id", req.getDlpId());
            params.put("scene_id", req.getSceneId());
            params.put("device_scene", req.getDeviceScene());
            params.put("copy_cycle", req.getCopyCycle());
            params.put("copy_cycle_list", req.getCopyCycleList());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallSceneDTO.class);
        } catch (Exception e) {
            log.error("场景另存为失败", e);
            return errorResponse(e);
        }
    }

    // =============== 10. 电视墙场景切换 ===============
    public ResponeResult<TvWallSceneSwitchDTO> switchScene(TvWallSceneSwitchReq req) {
        String url = "/api/tvms/v1/tvwall/scene/switch";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("scene_id", req.getSceneId());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallSceneSwitchDTO.class);
        } catch (Exception e) {
            log.error("切换场景失败", e);
            return errorResponse(e);
        }
    }

    // =============== 11. 新增报警 ===============
    public ResponeResult<TvWallAlarmDTO> addAlarm( TvWallAlarmAddReq req) {
        String url = "/api/tvms/v1/public/tvwall/alarm/addition";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("event_name", req.getEventName());
            params.put("event_index", req.getEventIndex());
            params.put("dlp_indexcode", req.getDlpIndexcode());
            params.put("event_level", req.getEventLevel());
            params.put("event_keep_time", req.getEventKeepTime());
            params.put("alarm_stream_type", req.getAlarmStreamType());
            params.put("camera_list", req.getCameraList());
            params.put("wnd_list", req.getWndList());
            params.put("event_desc", req.getEventDesc());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallAlarmDTO.class);
        } catch (Exception e) {
            log.error("新增报警失败", e);
            return errorResponse(e);
        }
    }

    // =============== 12. 删除报警 ===============
    public ResponeResult<TvWallAlarmDeleteDTO> deleteAlarm(TvWallAlarmDeleteReq req) {
        String url = "/api/tvms/v1/public/tvwall/alarm/deletion";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("event_index", req.getEventIndex());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallAlarmDeleteDTO.class);
        } catch (Exception e) {
            log.error("删除报警失败", e);
            return errorResponse(e);
        }
    }

    // =============== 13. 窗口分割 ===============
    public ResponeResult<TvWallFloatWndDivisionDTO> divideWindow( TvWallFloatWndDivisionReq req) {
        String url = "/api/tvms/v1/public/tvwall/floatWnd/division";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("dlp_id", req.getDlpId());
            params.put("floatwnd_id", req.getFloatwndId());
            params.put("div_num", req.getDivNum());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallFloatWndDivisionDTO.class);
        } catch (Exception e) {
            log.error("窗口分割失败", e);
            return errorResponse(e);
        }
    }

    // =============== 14. 窗口批量创建 ===============
    public ResponeResult<TvWallFloatWndsDTO> batchCreateWindows( TvWallFloatWndsCreateReq req) {
        String url = "/api/tvms/v1/public/tvwall/floatWnds/addition";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("floatwnd_list", req.getFloatwndList());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallFloatWndsDTO.class);
        } catch (Exception e) {
            log.error("批量创建窗口失败", e);
            return errorResponse(e);
        }
    }

    // =============== 15. 窗口批量删除 ===============
    public ResponeResult<TvWallFloatWndsDeleteDTO> batchDeleteWindows(TvWallFloatWndsDeleteReq req) {
        String url = "/api/tvms/v1/public/tvwall/floatWnds/deletion";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("floatwnd_list", req.getFloatwndList());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallFloatWndsDeleteDTO.class);
        } catch (Exception e) {
            log.error("批量删除窗口失败", e);
            return errorResponse(e);
        }
    }

    // =============== 16. 窗口放大 ===============
    public ResponeResult<TvWallFloatWndZoomDTO> zoomInWindow(TvWallFloatWndZoomReq req) {
        String url = "/api/tvms/v1/public/tvwall/floatWnd/zoomIn";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("dlp_id", req.getDlpId());
            params.put("floatwnd_id", req.getFloatwndId());
            params.put("wnd_uri", req.getWndUri());
            params.put("type", req.getType());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallFloatWndZoomDTO.class);
        } catch (Exception e) {
            log.error("窗口放大失败", e);
            return errorResponse(e);
        }
    }

    // =============== 17. 窗口漫游 ===============
    public ResponeResult<TvWallFloatWndMoveDTO> moveWindow(TvWallFloatWndMoveReq req) {
        String url = "/api/tvms/v1/public/tvwall/floatWnd/move";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("dlp_id", req.getDlpId());
            params.put("floatwnd_id", req.getFloatwndId());
            params.put("wnd_left", req.getWndLeft());
            params.put("wnd_top", req.getWndTop());
            params.put("wnd_width", req.getWndWidth());
            params.put("wnd_height", req.getWndHeight());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallFloatWndMoveDTO.class);
        } catch (Exception e) {
            log.error("窗口漫游失败", e);
            return errorResponse(e);
        }
    }

    // =============== 18. 窗口置顶或置底 ===============
    public ResponeResult<TvWallFloatWndLayerCtrlDTO> setWindowLayer(TvWallFloatWndLayerCtrlReq req) {
        String url = "/api/tvms/v1/public/tvwall/floatWnd/layerCtrl";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("dlp_id", req.getDlpId());
            params.put("floatwnd_id", req.getFloatwndId());
            params.put("layer_ctrl", req.getLayerCtrl());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallFloatWndLayerCtrlDTO.class);
        } catch (Exception e) {
            log.error("窗口置顶/置底失败", e);
            return errorResponse(e);
        }
    }

    // =============== 19. 窗口还原 ===============
    public ResponeResult<TvWallFloatWndZoomDTO> zoomOutWindow(TvWallFloatWndZoomOutReq req) {
        String url = "/api/tvms/v1/public/tvwall/floatWnd/zoomOut";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("dlp_id", req.getDlpId());
            params.put("floatwnd_id", req.getFloatwndId());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallFloatWndZoomDTO.class);
        } catch (Exception e) {
            log.error("窗口还原失败", e);
            return errorResponse(e);
        }
    }

    // =============== 20. 非开窗设备窗口分割 ===============
    public ResponeResult<TvWallMonitorDivisionDTO> divideMonitor( TvWallMonitorDivisionReq req) {
        String url = "/api/tvms/v1/public/tvwall/monitor/division";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("dlp_id", req.getDlpId());
            params.put("monitor_pos", req.getMonitorPos());
            params.put("div_num", req.getDivNum());
            String response = sendRequest(params, url);
            return parseResponse(response, TvWallMonitorDivisionDTO.class);
        } catch (Exception e) {
            log.error("非开窗设备窗口分割失败", e);
            return errorResponse(e);
        }
    }

    /**
     * 获取所有监控点资源
     * @param treeCode
     * @return
     */
    public ResponeResult<CameraPointDTO> getAllCameras(String treeCode) {
        //调用getCamerasPage 获取所有数据
        return getCamerasPage(1,1000,treeCode);
    }

    /**
     * 4.1.2.1分页获取监控点资源
     * @param pageNo
     * @param pageSize
     * @param treeCode
     * @return
     */
    public ResponeResult<CameraPointDTO> getCamerasPage(int pageNo,int pageSize,String treeCode) {
        String url = "/api/resource/v1/cameras";
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("pageNo", pageNo);
            params.put("pageSize", pageSize);
            params.put("treeCode", treeCode);
            String response = sendRequest(params, url);
            return parseResponse(response, CameraPointDTO.class);
        } catch (Exception e) {
            log.error("非开窗设备窗口分割失败", e);
            return errorResponse(e);
        }

    }

}