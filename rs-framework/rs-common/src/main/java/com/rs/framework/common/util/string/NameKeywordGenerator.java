package com.rs.framework.common.util.string;

import cn.hutool.extra.pinyin.PinyinUtil;

import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Set;

public class NameKeywordGenerator {

    // 复姓列表（可扩展）
    private static final Set<String> COMPOUND_SURNAMES = new HashSet<>(Arrays.asList(
            "欧阳", "上官", "皇甫", "司马", "夏侯", "诸葛", "宇文", "尉迟", "慕容", "令狐",
            "百里", "端木", "东方", "独孤", "东郭", "东里", "东宫", "第五", "达奚", "段干",
            "公孙", "公良", "公仲", "公上", "公户", "公玉", "公仪", "公皙", "公西", "公冶",
            "公羊", "公乘", "公山", "公祖", "公坚", "公良", "贯丘", "即墨", "夹谷", "亓官", "乐正",
            "梁丘", "闾丘", "泠沦", "令狐", "南宫", "南荣", "南门", "濮阳", "漆雕", "壤驷", "汝嫣", "司寇",
            "司空", "司徒", "申屠", "叔孙", "澹台", "太史", "太叔", "拓跋", "闻人", "万俟", "巫马", "微生",
            "鲜于", "夏侯", "轩辕", "颛孙", "长孙", "钟离", "仲孙", "仲长", "左丘", "宗政"));

    public static String generateKeywords(String name) {
        Set<String> keywords = new LinkedHashSet<>();
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        // 识别复姓（如"欧阳"）
        String surname = identifySurname(name);
        String givenName = name.substring(surname.length());
        if (isChinese(name)) {
            // 姓氏名称组合
            keywords.addAll(generateCharacterCombinations(surname, givenName));
        }
        // 全拼
        keywords.add(getFullPinyin(name).toUpperCase());
        if (isChinese(name)) {
            // 生成组合拼音关键词
            keywords.addAll(generateInitialCombinations(surname, givenName));
        }
        String join = String.join(" ", keywords);
        return join.length() > 60 ? join.substring(0, 60) : join;
    }

    private static boolean isChinese(String text) {
        for (int i = 0; i < text.length(); i++) {
            if (PinyinUtil.isChinese(text.charAt(i))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 识别复姓（默认取首字）
     */
    private static String identifySurname(String name) {
        if (name.length() > 1) {
            String firstTwoChars = name.substring(0, 2);
            if (COMPOUND_SURNAMES.contains(firstTwoChars)) {
                return firstTwoChars;
            }
        }
        return name.substring(0, 1);
    }

    private static String getFirstLetter(String text) {
        StringBuffer firstLetter = new StringBuffer();
        for (int i = 0; i < text.length(); i++) {
            char first = PinyinUtil.getFirstLetter(text.charAt(i));
            firstLetter.append(first);
        }
        return firstLetter.toString().toUpperCase();
    }

    /**
     * 生成简写组合（如 "K KYX KY KX YX"）
     */
    private static Set<String> generateInitialCombinations(String surname, String givenName) {
        Set<String> initials = new LinkedHashSet<>();
        String surnameInitial = getFirstLetter(surname);
        String givenNameInitial = getFirstLetter(givenName);
        // 姓首字母 (K)
        initials.add(surnameInitial);
        // 全简写 (KYX)
        initials.add(surnameInitial + givenNameInitial);
        // 姓首字母+名首字 (KY)
        if (!givenName.isEmpty()) {
            initials.add(surnameInitial + givenNameInitial.charAt(0));
        }
        // 姓首字母+名次字 (KX) ✔️ 新增逻辑
        if (givenName.length() > 1) {
            initials.add(surnameInitial + givenNameInitial.charAt(1));
        }
        // 名字双字母组合 (YX)
        if (givenName.length() > 1) {
            for (int i = 1; i < givenName.length(); i++) {
                initials.add(givenNameInitial.substring(i - 1, i + 1));
            }
        }
        return initials;
    }

    /**
     * 生成汉字两两组合（如 "孔云", "孔熙", "云熙"）
     */
    private static Set<String> generateCharacterCombinations(String surname, String givenName) {
        Set<String> combinations = new LinkedHashSet<>();
        // 姓+名各字组合 (孔云,孔熙)
        for (int i = 0; i < givenName.length(); i++) {
            combinations.add(surname + givenName.charAt(i));  // ✔️ 生成孔熙
        }
        // 名字相邻组合 (云熙)
        for (int i = 0; i < givenName.length() - 1; i++) {
            combinations.add(givenName.substring(i, i + 2));
        }
        return combinations;
    }

    /**
     * 获取全拼
     */
    private static String getFullPinyin(String text) {
        return PinyinUtil.getPinyin(text, "");
    }

    /**
     * 获取首字母缩写
     */
    private static String getInitials(String text) {
        return getFullPinyin(text).replaceAll("[^a-z]", ""); // 过滤非字母字符
    }

}