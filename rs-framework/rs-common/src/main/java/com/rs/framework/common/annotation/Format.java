package com.rs.framework.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @ClassName FetchFromDatabase
 *
 * <AUTHOR>
 * @Date 2025/3/22 10:26
 * @Version 1.0
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Format {
    String value(); // 字段ID的名称
    Class<?> service(); // 服务类的类型
    String method(); // 服务类中用于查询的方法名称
    Class toBean() default Object.class;
    boolean isTile() default false;
    String ignore() default "id";

}
