package com.rs.framework.common.util.tool;

import javax.sql.DataSource;
import cn.hutool.db.ds.simple.SimpleDataSource;

/**
 * 数据源工具类
 * <AUTHOR>
 * @date 2025年8月8日
 */
public class DataSourceUtil {

	/**
	 * 获取简单数据源
	 * @param url String 连接地址
	 * @param user String 用户
	 * @param password String 密码
	 * @param driver String 驱动程序
	 * @return DataSource
	 */
	public static DataSource getSimpleDataSource(String url, String user, String password, String driver) {		
		SimpleDataSource ds = new SimpleDataSource(url, user, password, driver);
		return ds;
	}
	
	/**
	 * 获取bsp数据源
	 * @return DataSource
	 */
	public static DataSource getBspDataSource() {
		String url = "*******************************************************************************************************************************************************************************";
		String user = "root";
		String password = "sundun_bsp";
		String driver = "com.mysql.cj.jdbc.Driver";
		return getSimpleDataSource(url, user, password, driver);
	}
}
