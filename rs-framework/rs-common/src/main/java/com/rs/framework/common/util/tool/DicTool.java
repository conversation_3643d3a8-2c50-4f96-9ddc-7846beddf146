package com.rs.framework.common.util.tool;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.sql.DataSource;

import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.common.util.io.FileUtil;

import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典工具类
 * <AUTHOR>
 * @date 2025年8月7日
 */
public class DicTool {
	
	/**
	 * 文件类型
	 * <AUTHOR>
	 * @date 2025年8月7日
	 */
	@Data
	public class FileType{
		List<String> ignoreFileNames = new ArrayList<>();
		List<String> availableSuffixNames = new ArrayList<>();
		String dicPattern;
		Pattern pattern;
	}
	
	/**
	 * Vue文件类型
	 * <AUTHOR>
	 * @date 2025年8月7日
	 */
	@Data
	@EqualsAndHashCode(callSuper = true)
	public class VueFileType extends FileType {
		
		public VueFileType() {
			ignoreFileNames.add("node_modules");
			ignoreFileNames.add(".git");
			ignoreFileNames.add(".cursor");
			ignoreFileNames.add("dist");
			availableSuffixNames.add(".vue");
			dicPattern = "dicName\\s*[=|:]\\s*['\"]([^'\"]+)['\"]";
			pattern = Pattern.compile(dicPattern);
		}
	}
	
	/**
	 * Java文件类型
	 * <AUTHOR>
	 * @date 2025年8月7日
	 */
	@Data
	@EqualsAndHashCode(callSuper = true)
	public class JavaFileType extends FileType {
		
		public JavaFileType() {
			ignoreFileNames.add(".lingma");
			ignoreFileNames.add(".settings");
			ignoreFileNames.add("build");
			ignoreFileNames.add("config");
			ignoreFileNames.add("doc");
			availableSuffixNames.add(".java");
			dicPattern = "@Trans\\(.*?key\\s*=\\s*\"([^\"]+)\".*?\\)";
			pattern = Pattern.compile(dicPattern);
		}
	}

	/**
	 * 从Vue文件中获取字典名称
	 * @param filePath String 文件路径
	 * @param fileType FileType 文件类型
	 * @return Map<String, String>
	 */
	public static Map<String, String> getDicNameFromFile(String filePath, FileType fileType){
		Map<String, String> dicMap = new HashMap<>();
		List<String> fileNames = new ArrayList<>();
		File file = new File(filePath);
		recursiveFile(file, fileNames, fileType);
		for(String fileName : fileNames) {
			String fileText = FileUtil.readText(new File(fileName));
			fileText = fileText.replaceAll("\\s+", "");
			Matcher m = fileType.pattern.matcher(fileText);
			while(m.find()) {
				String dicName = m.group(1);
				if(StringUtil.isNotEmpty(dicName)) {
					dicMap.put(dicName, filePath);
				}
			}
		}
		
		return dicMap;
	}
	
	/**
	 * 从Java文件中获取字典名称
	 * @param filePath String 文件路径
	 * @return Map<String, String>
	 */
	public static Map<String, String> getDicNameFromJavaFile(String filePath){
		DicTool dicTool = new DicTool();
		return getDicNameFromFile(filePath, dicTool.new JavaFileType());
	}
	
	/**
	 * 从Vue文件中获取字典名称
	 * @param filePath String 文件路径
	 * @return Map<String, String>
	 */
	public static Map<String, String> getDicNameFromVueFile(String filePath){
		DicTool dicTool = new DicTool();
		return getDicNameFromFile(filePath, dicTool.new VueFileType());
	}
	
	/**
	 * 递归文件获取所有文件路径
	 * @param directory File 文件目录
	 * @param fileNames List<String> 文件路径
	 * @param fileType FileType 文件类型
	 */
	public static void recursiveFile(File directory, List<String> fileNames, FileType fileType) {
		if(directory.exists()) {
	        if (directory.isDirectory()) {
	            File[] files = directory.listFiles();	            
	            if (files != null) {
	                for (File file : files) {
	                	if(!fileType.ignoreFileNames.contains(file.getName())) {
		                    if (file.isDirectory()) {
		                    	recursiveFile(file, fileNames, fileType);
		                    }
		                    else if(isLegalFile(file, fileType)){		                    	
		                    	fileNames.add(file.getAbsolutePath());
		                    }
	                	}
	                }
	            }
	        }
	        else if(isLegalFile(directory, fileType)){
	        	fileNames.add(directory.getAbsolutePath());
	        }
		}
    }

	/**
	 * 是否合法的文件
	 * @param file File 文件
	 * @param availableSuffixNames List<String> 允许的文件后缀
	 * @param fileType FileType 文件类型
	 * @return boolean
	 */
	private static boolean isLegalFile(File file, FileType fileType) {
		for(String suffix : fileType.availableSuffixNames) {
			if(file.getName().toLowerCase().endsWith(suffix)) {
				return true;
			}
		}
		
		return false;
	}
	
	/**
	 * 输出字典文件
	 * @param dicMap
	 */
	public static void printDicName(Map<String, String> dicMap) {
		List<String> dicList = dicMap.keySet().stream().collect(Collectors.toList());
		System.out.println("===========================");
		for(String dicName : dicList) {
			System.out.println(dicName);
		}
		System.out.println("===========================" + dicList.size());
	}
	
	public static void compareDic() {		
		try {
			DataSource ds = DataSourceUtil.getBspDataSource();
			Map<String, Map<String, String>> resultMap = new HashMap<>();
			
			//源字典(公安监管标准字典)
			List<Entity> sourceDicList = Db.use(ds).query("select id, cat_id, name, cname from ops_dic t where t.APP_ID is null and t.NAME like 'ZD_GAJGSJHJBZ%'");
			List<Entity> sourceDicCodeList = Db.use(ds).query("select t.DIC_ID, t.NAME from ops_dic_code t where t.DIC_ID in(select id from ops_dic t where t.APP_ID is null and t.NAME like 'ZD_GAJGSJHJBZ%')");
			
			//源字典<字典名称, <代码名称, 字典Id>>
			Map<String, Map<String, String>> sourceMap = initDicMap(sourceDicList, sourceDicCodeList);
			
			//目标字典(其它字典)
			List<Entity> targetDicList = Db.use(ds).query("select id, cat_id, name, cname from ops_dic t where t.APP_ID is null and t.NAME not like 'ZD_GAJGSJHJBZ%'");
			List<Entity> targetDicCodeList = Db.use(ds).query("select t.DIC_ID, t.NAME from ops_dic_code t where t.DIC_ID in(select id from ops_dic t where t.APP_ID is null and t.NAME not like 'ZD_GAJGSJHJBZ%')");
			
			//目标字典<字典名称, <代码名称, 字典Id>>
			Map<String, Map<String, String>> targetMap = initDicMap(targetDicList, targetDicCodeList);
			
			//比较字典
			for(Map.Entry<String, Map<String, String>> sourceEntry : sourceMap.entrySet()) {
				String sourceDicName = sourceEntry.getKey();
				Map<String, String> sourceCodeMap = sourceEntry.getValue();
				
				for(Map.Entry<String, Map<String, String>> targetEntry : targetMap.entrySet()) {
					String targetDicName = targetEntry.getKey();
					Map<String, String> targetCodeMap = targetEntry.getValue();
					
					//判断两个字典的字典项全部一致
					if(targetCodeMap.keySet().containsAll(sourceCodeMap.keySet())) {
						if(resultMap.containsKey(sourceDicName)) {
							resultMap.get(sourceDicName).put(targetDicName, targetDicName);
						}
						else {
							Map<String, String> dicMap = new HashMap<>();
							dicMap.put(targetDicName, targetDicName);
							resultMap.put(sourceDicName, dicMap);
						}
					}
				}
			}
			
			System.out.println("===========================");
			System.out.println(sourceMap.size());
			System.out.println(targetMap.size());
			System.out.println(resultMap.size());
			System.out.println("===========================");
			printCompareDicName(resultMap);
			System.out.println("===========================");
		}
		catch(Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 输出字典文件
	 * @param dicMap
	 */
	public static void printCompareDicName(Map<String, Map<String, String>> resultMap) {		
		for(Map.Entry<String, Map<String, String>> sourceEntry : resultMap.entrySet()) {
			for(Map.Entry<String, String> targetEntry : sourceEntry.getValue().entrySet()) {
				System.out.println(sourceEntry.getKey() + "---" + targetEntry.getKey());
			}
			System.out.println("--------------------------------");
		}
	}
	
	/**
	 * 构建字典映射Map
	 * @param sourceDicList List<Entity> 字典集合
	 * @param sourceDicCodeList List<Entity> 字典代码集合
	 * @return Map<String, Map<String, String>>
	 */
	private static Map<String, Map<String, String>> initDicMap(List<Entity> sourceDicList, List<Entity> sourceDicCodeList) {
		Map<String, Map<String, String>> sourceMap = new HashMap<>();
		for(Entity dic : sourceDicList) {
			String dicId = dic.getStr("id");
			String dicName = dic.getStr("name");
			List<Entity> dicCodeList = sourceDicCodeList.stream().filter(item -> item.getStr("dic_id").equals(dicId)).collect(Collectors.toList());
			if(CollectionUtil.isNotNull(dicCodeList)) {
				for(Entity dicCode : dicCodeList) {
					if(sourceMap.containsKey(dicName)) {
						sourceMap.get(dicName).put(dicCode.getStr("name"), dicId);
					}
					else {
						Map<String, String> dicMap = new HashMap<>();
						dicMap.put(dicCode.getStr("name"), dicId);
						sourceMap.put(dicName, dicMap);
					}
				}
			}
			else {
				System.out.println("字典代码为空的字典：" + dicName);
			}
		}
		return sourceMap;
	}
	
	public static void main(String[] args) {
		//从前端代码获取所有字典名称
//		String filePath = "E:\\A.Code\\gosuncn\\pd\\pd-rs\\rs-acp-web";
//		Map<String, String> dicMap = getDicNameFromVueFile(filePath);
//		printDicName(dicMap);
		
		//从后端代码获取所有字典名称
//		String filePath = "E:\\A.Code\\gosuncn\\pd\\pd-rs\\rs-master";
//		Map<String, String> dicMap = getDicNameFromJavaFile(filePath);
//		printDicName(dicMap);
		
		//比较数据库字典
		compareDic();
	}
}
