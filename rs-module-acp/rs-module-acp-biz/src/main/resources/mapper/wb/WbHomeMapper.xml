<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.wb.WbHomeDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="getToDayMeetingUpdates" resultType="com.alibaba.fastjson.JSONObject">
        select * from (
        SELECT
            '提讯/询' businessname,
            '0' business,
            COALESCE(SUM ( todaymeetingtotal ),0) todaymeetingtotal,
            COALESCE(SUM ( signin ),0) signin,
            COALESCE(SUM ( completed ),0) completed,
            COALESCE(SUM ( unfinished ),0) unfinished
        from (
                 SELECT
                     COUNT( 1 ) todaymeetingtotal,
                     COALESCE(SUM ( CASE WHEN check_in_time IS NOT NULL THEN 1 ELSE 0 END ),0) signin,
                     COALESCE(SUM ( CASE WHEN status = '4' then 1 else 0 end ),0) completed,
                     COALESCE(SUM ( CASE WHEN status != '4' then 1 else 0 end ),0) unfinished
                 FROM
                     acp_wb_arraignment
                 WHERE
                     org_code = #{orgCode}
                   AND to_char( start_apply_arraignment_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                   AND is_del = 0

                 UNION ALL

                 SELECT
                     COUNT( 1 ) todaymeetingtotal,
                     COALESCE(SUM ( CASE WHEN check_in_time IS NOT NULL THEN 1 ELSE 0 END ),0) signin,
                     COALESCE(SUM ( CASE WHEN status = '4' then 1 else 0 end ),0) completed,
                     COALESCE(SUM ( CASE WHEN status != '4' then 1 else 0 end ),0) unfinished
                 FROM
                     acp_wb_bring_interrogation
                 WHERE
                     org_code = #{orgCode}
                   AND to_char( start_apply_arraignment_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                   AND is_del = 0
             ) txtj

        UNION ALL

        SELECT
            '提解' businessname,
            '1' business,
            COUNT( 1 ) todaymeetingtotal,
            COALESCE(COUNT( 1 ),0) signin,
            COALESCE(SUM ( CASE WHEN status = '2' then 1 else 0 end ),0) completed,
            COALESCE(SUM ( CASE WHEN status != '2' then 1 else 0 end ),0) unfinished
        FROM
            acp_wb_escort
        WHERE
            org_code = #{orgCode}
          AND apply_escort_date = CURRENT_DATE
          AND is_del = 0

        UNION ALL

        SELECT
            '律师会见' businessname,
            '2' business,
            COUNT( 1 ) todaymeetingtotal,
            COALESCE(SUM ( CASE WHEN check_in_time IS NOT NULL THEN 1 ELSE 0 END ),0) signin,
            COALESCE(SUM ( CASE WHEN status = '4' then 1 else 0 end ),0) completed,
            COALESCE(SUM ( CASE WHEN status != '4' then 1 else 0 end ),0) unfinished
        FROM
            acp_wb_lawyer_meeting
        WHERE
            org_code = #{orgCode}
          AND to_char( apply_meeting_start_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
          AND is_del = 0

        UNION ALL

        SELECT
            '家属会见' businessname,
            '3' business,
            COALESCE(SUM ( todaymeetingtotal ),0) todaymeetingtotal,
            COALESCE(SUM ( signin ),0) signin,
            COALESCE(SUM ( completed ),0) completed,
            COALESCE(SUM ( unfinished ),0) unfinished
        from (
                 SELECT
                     COUNT( 1 ) todaymeetingtotal,
                     COALESCE(SUM ( CASE WHEN check_in_time IS NOT NULL THEN 1 ELSE 0 END ),0) signin,
                     COALESCE(SUM ( CASE WHEN status = '4' then 1 else 0 end ),0) completed,
                     COALESCE(SUM ( CASE WHEN status != '4' then 1 else 0 end ),0) unfinished
                 FROM
                     acp_wb_family_meeting
                 WHERE
                     org_code = #{orgCode}
                   AND to_char( apply_meeting_start_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                   AND is_del = 0

                 UNION ALL

                 SELECT
                     COUNT( 1 ) todaymeetingtotal,
                     COALESCE(SUM ( CASE WHEN notification_meeting_date IS NOT NULL THEN 1 ELSE 0 END ),0) signin,
                     COALESCE(SUM ( CASE WHEN status = '2' then 1 else 0 end ),0) completed,
                     COALESCE(SUM ( CASE WHEN status != '2' then 1 else 0 end ),0) unfinished
                 FROM
                     acp_wb_family_meeting_video
                 WHERE
                     org_code = #{orgCode}
                   AND to_char( notification_meeting_date, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                   AND is_del = 0
             ) jshj

        UNION ALL

        SELECT
            '使馆领事会见' businessname,
            '4' business,
            COUNT( 1 ) todaymeetingtotal,
            COALESCE(SUM ( CASE WHEN check_in_time IS NOT NULL THEN 1 ELSE 0 END ),0) signin,
            COALESCE(SUM ( CASE WHEN status = '4' then 1 else 0 end ),0) completed,
            COALESCE(SUM ( CASE WHEN status != '4' then 1 else 0 end ),0) unfinished
        FROM
            acp_wb_consular_meeting
        WHERE
            org_code = #{orgCode}
          AND to_char( apply_meeting_start_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
          AND is_del = 0) res
    </select>

    <select id="getLawyerMeetingList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            room_id
        FROM
            acp_wb_lawyer_meeting
        WHERE
            apply_meeting_start_time &lt; CURRENT_TIMESTAMP
          AND apply_meeting_end_time > CURRENT_TIMESTAMP
          AND status IN ( '2', '3' )
          AND is_del = 0
    </select>
    
    <select id="getMeetingMumStatistics" resultType="com.alibaba.fastjson.JSONObject">
        select * from (
              SELECT
                  '提讯/询' businessname,
                  '0' business,
                  COALESCE(SUM ( num ),0) num
              from (
                       SELECT
                           COUNT( 1 ) num
                       FROM
                           acp_wb_arraignment
                       WHERE
                           org_code = #{orgCode}
                         AND to_char( start_apply_arraignment_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                         AND is_del = 0

                       UNION ALL

                       SELECT
                           COUNT( 1 ) num
                       FROM
                           acp_wb_bring_interrogation
                       WHERE
                           org_code = #{orgCode}
                         AND to_char( start_apply_arraignment_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                         AND is_del = 0
                   ) txtj

              UNION ALL

              SELECT
                  '提解' businessname,
                  '1' business,
                  COUNT( 1 ) num
              FROM
                  acp_wb_escort
              WHERE
                  org_code = #{orgCode}
                AND apply_escort_date = CURRENT_DATE
                AND is_del = 0

              UNION ALL

              SELECT
                  '律师会见' businessname,
                  '2' business,
                  COUNT( 1 ) num
              FROM
                  acp_wb_lawyer_meeting
              WHERE
                  org_code = #{orgCode}
                AND to_char( apply_meeting_start_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                AND is_del = 0

              UNION ALL

              SELECT
                  '家属会见' businessname,
                  '3' business,
                  COALESCE(SUM ( num ),0) num
              from (
                       SELECT
                           COUNT( 1 ) num
                       FROM
                           acp_wb_family_meeting
                       WHERE
                           org_code = #{orgCode}
                         AND to_char( apply_meeting_start_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                         AND is_del = 0
                   ) jshj

              UNION ALL

              SELECT
                  '使馆领事会见' businessname,
                  '4' business,
                  COUNT( 1 ) num
              FROM
                  acp_wb_consular_meeting
              WHERE
                  org_code = #{orgCode}
                AND to_char( apply_meeting_start_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                AND is_del = 0
          ) res
    </select>
    
    <select id="getWithdrawalStatusStatistics" resultType="com.alibaba.fastjson.JSONObject">
        select
            sum(dtc) dtc,
            sum(dth) dth
        from (
                 SELECT
                     sum(dtc) dtc,
                     sum(dth) dth
                 from (
                     SELECT
                     sum(case when status in('0','1','2') or (status = '99' and inspection_time is null) then 1 else 0 end) dtc,
                     sum(case when status = '3' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then 1 else 0 end) dth

                     FROM
                     acp_wb_arraignment
                     WHERE
                     org_code = #{orgCode}
                     AND to_char( start_apply_arraignment_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                     AND is_del = 0

                     UNION ALL

                     SELECT
                     sum(case when status in('0','1','2') or (status = '99' and inspection_time is null) then 1 else 0 end) dtc,
                     sum(case when status = '3' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then 1 else 0 end) dth
                     FROM
                     acp_wb_bring_interrogation
                     WHERE
                     org_code = #{orgCode}
                     AND to_char( start_apply_arraignment_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                     AND is_del = 0
                     ) txtj

                 UNION ALL

                 SELECT
                     sum(case when status = '0' or (status = '99' and inspection_time is null) then 1 else 0 end) dtc,
                     sum(case when status = '1' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then 1 else 0 end) dth
                 FROM
                     acp_wb_escort
                 WHERE
                     org_code = #{orgCode}
                   AND apply_escort_date = CURRENT_DATE
                   AND is_del = 0

                 UNION ALL

                 SELECT
                     sum(case when status in('0','1','2') or (status = '99' and inspection_time is null) then 1 else 0 end) dtc,
                     sum(case when status = '3' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then 1 else 0 end) dth
                 FROM
                     acp_wb_lawyer_meeting
                 WHERE
                     org_code = #{orgCode}
                   AND to_char( apply_meeting_start_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                   AND is_del = 0

                 UNION ALL

                 SELECT
                     sum(dtc) dtc,
                     sum(dth) dth
                 from (
                     SELECT
                     sum(case when status in('0','1','2') or (status = '99' and inspection_time is null) then 1 else 0 end) dtc,
                     sum(case when status = '3' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then 1 else 0 end) dth
                     FROM
                     acp_wb_family_meeting
                     WHERE
                     org_code = #{orgCode}
                     AND to_char( apply_meeting_start_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                     AND is_del = 0
                     ) jshj

                 UNION ALL

                 SELECT
                     sum(case when status in('0','1','2') or (status = '99' and inspection_time is null) then 1 else 0 end) dtc,
                     sum(case when status = '3' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then 1 else 0 end) dth
                 FROM
                     acp_wb_consular_meeting
                 WHERE
                     org_code = #{orgCode}
                   AND to_char( apply_meeting_start_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                   AND is_del = 0
             ) res
    </select>
    
    <select id="getToDayMeetingList" resultType="com.alibaba.fastjson.JSONObject">
        select
            res.*,
            pr.xm,
            case
                when pr.xb = '0' then '未知的性别'
                when pr.xb = '1' then '男'
                when pr.xb = '2' then '女'
                when pr.xb = '9' then '未说明的性别'
                else '未知' end xb,
            pr.room_name jsname,
            area.area_name hjsname
        from (
                 SELECT
                     '提解' businessname,
                     '1' business,
                     jgrybm,
                     kssj,
                     room_id,
                     meetingtime,
                     dwmc,
                     status,
                     statusname
                 from (
                          SELECT
                              jgrybm,
                              start_apply_arraignment_time kssj,
                              room_id,
                              concat_ws('~', to_char(start_apply_arraignment_time,'HH24:MI'),to_char(end_apply_arraignment_time,'HH24:MI'))  meetingtime,
                              tjjgmc dwmc,
                              case
                                  when status in('0','1','2') or (status = '99' and inspection_time is null) then 'dtc'
                                  when status = '3' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then 'dth'
                                  when status = '4'   then 'yth'
                                  else '' end status,
                              case
                                  when status in('0','1','2') or (status = '99' and inspection_time is null) then '待提出'
                                  when status = '3' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then '待提回'
                                  when status = '4'   then '已提回'
                                  else '' end statusname
                          FROM
                              acp_wb_arraignment
                          WHERE
                              org_code = #{orgCode}
                            AND to_char( start_apply_arraignment_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                            AND is_del = 0

                          UNION ALL

                          SELECT
                              jgrybm,
                              start_apply_arraignment_time kssj,
                              room_id,
                              concat_ws('~', to_char(start_apply_arraignment_time,'HH24:MI'),to_char(end_apply_arraignment_time,'HH24:MI'))  meetingtime,
                              tjjgmc dwmc,
                              case
                                  when status in('0','1','2') or (status = '99' and inspection_time is null) then 'dtc'
                                  when status = '3' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then 'dth'
                                  when status = '4'   then 'yth'
                                  else '' end status,
                              case
                                  when status in('0','1','2') or (status = '99' and inspection_time is null) then '待提出'
                                  when status = '3' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then '待提回'
                                  when status = '4'   then '已提回'
                                  else '' end statusname
                          FROM
                              acp_wb_bring_interrogation
                          WHERE
                              org_code = #{orgCode}
                            AND to_char( start_apply_arraignment_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                            AND is_del = 0
                      ) txtj

                 UNION ALL

                 SELECT
                     '提解' businessname,
                     '1' business,
                     jgrybm,
                     apply_escort_date kssj,
                     room_id,
                     to_char(apply_escort_date,'YYYY-MM-DD')  meetingtime,
                     tjjgmc dwmc,
                     case
                         when status in('0','1','2') or (status = '99' and inspection_time is null) then 'dtc'
                         when status = '3' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then 'dth'
                         when status = '4'   then 'yth'
                         else '' end status,
                     case
                         when status in('0','1','2') or (status = '99' and inspection_time is null) then '待提出'
                         when status = '3' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then '待提回'
                         when status = '4'   then '已提回'
                         else '' end statusname
                 FROM
                     acp_wb_escort
                 WHERE
                     org_code = #{orgCode}
                   AND apply_escort_date = CURRENT_DATE
                   AND is_del = 0

                 UNION ALL

                 SELECT
                     '律师会见' businessname,
                     '2' business,
                     jgrybm,
                     apply_meeting_start_time kssj,
                     room_id,
                     concat_ws('~', to_char(apply_meeting_start_time,'HH24:MI'),to_char(apply_meeting_end_time,'HH24:MI'))  meetingtime,
                     lawyer1_firm dwmc,
                     case
                         when status in('0','1','2') or (status = '99' and inspection_time is null) then 'dtc'
                         when status = '3' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then 'dth'
                         when status = '4'   then 'yth'
                         else '' end status,
                     case
                         when status in('0','1','2') or (status = '99' and inspection_time is null) then '待提出'
                         when status = '3' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then '待提回'
                         when status = '4'   then '已提回'
                         else '' end statusname

                 FROM
                     acp_wb_lawyer_meeting
                 WHERE
                     org_code = #{orgCode}
                   AND to_char( apply_meeting_start_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                   AND is_del = 0

                 UNION ALL

                 SELECT
                     '家属会见' businessname,
                     '3' business,
                     jgrybm,
                     kssj,
                     room_id,
                     meetingtime,
                     dwmc,
                     status,
                     statusname
                 from (
                          SELECT
                              jgrybm,
                              apply_meeting_start_time kssj,
                              room_id,
                              concat_ws('~', to_char(apply_meeting_start_time,'HH24:MI'),to_char(apply_meeting_end_time,'HH24:MI'))  meetingtime,
                              family_member1_work_unit dwmc,
                              case
                                  when status in('0','1','2') or (status = '99' and inspection_time is null) then 'dtc'
                                  when status = '3' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then 'dth'
                                  when status = '4'   then 'yth'
                                  else '' end status,
                              case
                                  when status in('0','1','2') or (status = '99' and inspection_time is null) then '待提出'
                                  when status = '3' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then '待提回'
                                  when status = '4'   then '已提回'
                                  else '' end statusname
                          FROM
                              acp_wb_family_meeting
                          WHERE
                              org_code = #{orgCode}
                            AND  to_char( apply_meeting_start_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                            AND is_del = 0
                      ) jshj

                 UNION ALL

                 SELECT
                     '使馆领事会见' businessname,
                     '4' business,
                     jgrybm,
                     apply_meeting_start_time kssj,
                     room_id,
                     concat_ws('~', to_char(apply_meeting_start_time,'HH24:MI'),to_char(apply_meeting_end_time,'HH24:MI'))  meetingtime,
                     (select work_unit from acp_wb_consular_meeting_person t2 where t1.id = t2.consular_meeting_id and rylx = '0' limit 1) dwmc,
             case
            when status in('0','1','2') or (status = '99' and inspection_time is null) then 'dtc'
							when status = '3' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then 'dth'
							when status = '4'   then 'yth'
					 else '' end status,
					 case
							when status in('0','1','2') or (status = '99' and inspection_time is null) then '待提出'
							when status = '3' or (status = '99' and return_inspection_time is null and inspection_time is not null)  then '待提回'
							when status = '4'   then '已提回'
						else '' end statusname
        FROM
            acp_wb_consular_meeting t1
        WHERE
            org_code = #{orgCode}
        AND to_char( apply_meeting_start_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
        AND is_del = 0
        ) res left join vw_acp_pm_prisoner_list pr on res.jgrybm = pr.jgrybm
        left join acp_pm_area area on res.room_id = area.id
        where 1=1
        <if test="tczt != '' and tczt != null and tczt != 'all'">
            and res.status = #{tczt}
        </if>
        <if test="ywlx != '' and ywlx != null and ywlx != 'all'">
            and res.business = #{ywlx}
        </if>
        order by res.kssj asc
    </select>

</mapper>
