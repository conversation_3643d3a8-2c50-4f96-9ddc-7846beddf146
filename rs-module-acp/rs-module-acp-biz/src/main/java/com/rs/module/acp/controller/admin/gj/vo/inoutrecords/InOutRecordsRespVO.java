package com.rs.module.acp.controller.admin.gj.vo.inoutrecords;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-出入登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InOutRecordsRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    @Trans(type = "ZD_DATA_SOURCES")
    private String dataSources;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员编码")
    private String jgryxm;
    @ApiModelProperty("进出监室类型（字典：ZD_GJJCJSLX）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJJCJSLX")
    private String inoutType;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("旧监室id")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PRISON_ROOM")
    private String roomId;
    @ApiModelProperty("出入监室时间")
    private Date inoutTime;
    @ApiModelProperty("带出带入民警身份证号")
    private String inoutPoliceSfzh;
    @ApiModelProperty("业务类型（字典：ZD_GJXZDCSY）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJXZDCSY")
    private String businessType;
    @ApiModelProperty("带出带入民警")
    private String inoutPolice;
    @ApiModelProperty("出入事由")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJXZDCSY")
    private String inoutReason;
    @ApiModelProperty("检查结果")
    private String inspectionResult;
    @ApiModelProperty("业务ID")
    private String businessId;
    @ApiModelProperty("违禁物品登记")
    private String prohibitedItems;
    @ApiModelProperty("管教民警身份证号")
    private String supervisorPoliceSfzh;
    @ApiModelProperty("体表检查登记")
    private String physicalExam;
    @ApiModelProperty("管教民警姓名")
    private String supervisorPolice;
    @ApiModelProperty("异常情况登记")
    private String abnormalSituations;
    @ApiModelProperty("管教民警人脸信息存储路径")
    private String supervisorFaceInfoPath;
    @ApiModelProperty("违禁物品登记照片存储地址")
    private String prohibitedItemsImgUrl;
    @ApiModelProperty("被监管人员人脸信息存储路径")
    private String prisonerFaceInfoPath;
    @ApiModelProperty("体表检查登记照片存储地址")
    private String physicalExamImgUrl;
    @ApiModelProperty("异常情况登记照片存储地址")
    private String abnormalSituationsImgUrl;
    @ApiModelProperty("是否查出违禁物品")
    private Short isProhibitedItems;
    @ApiModelProperty("检查时间")
    private Date inspectionTime;
    @ApiModelProperty("检查民警身份证号")
    private String inspectorSfzh;
    @ApiModelProperty("检查民警身份证号")
    private String inspector;

    private String businessTypeName;
}
