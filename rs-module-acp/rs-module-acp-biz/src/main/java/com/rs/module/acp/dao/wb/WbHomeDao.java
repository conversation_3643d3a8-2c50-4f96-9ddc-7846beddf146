package com.rs.module.acp.dao.wb;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.wb.vo.ArraignmentListReqVO;
import com.rs.module.acp.controller.admin.wb.vo.ArraignmentPageReqVO;
import com.rs.module.acp.entity.wb.ArraignmentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 实战平台-窗口业务-提讯登记 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface WbHomeDao  {

    List<JSONObject> getToDayMeetingUpdates(String orgCode);


    List<JSONObject> getLawyerMeetingList();

    List<JSONObject> getMeetingMumStatistics(String orgCode);

    JSONObject getWithdrawalStatusStatistics(String orgCode);

    List<JSONObject> getToDayMeetingList(@Param("orgCode") String orgCode,@Param("tczt")String tczt,@Param("ywlx")String ywlx);
}
