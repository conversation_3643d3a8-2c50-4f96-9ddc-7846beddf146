package com.rs.module.acp.service.wb;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.wb.vo.MessageBookSaveReqVO;
import com.rs.module.acp.dao.wb.MessageBookDao;
import com.rs.module.acp.entity.wb.MessageBookDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 实战平台-窗口业务-留言内容 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MessageBookServiceImpl extends BaseServiceImpl<MessageBookDao, MessageBookDO> implements MessageBookService {

    @Resource
    private MessageBookDao messageBookDao;

    @Override
    public String createMessageBook(MessageBookSaveReqVO createReqVO) {
        // 插入
        MessageBookDO messageBook = BeanUtils.toBean(createReqVO, MessageBookDO.class);
        messageBookDao.insert(messageBook);
        // 返回
        return messageBook.getId();
    }

    @Override
    public void updateMessageBook(MessageBookSaveReqVO updateReqVO) {
        // 校验存在
        validateMessageBookExists(updateReqVO.getId());
        // 更新
        MessageBookDO updateObj = BeanUtils.toBean(updateReqVO, MessageBookDO.class);
        messageBookDao.updateById(updateObj);
    }

    @Override
    public void deleteMessageBook(String id) {
        // 校验存在
        validateMessageBookExists(id);
        // 删除
        messageBookDao.deleteById(id);
    }

    private void validateMessageBookExists(String id) {
        if (messageBookDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-留言内容数据不存在");
        }
    }

    @Override
    public MessageBookDO getMessageBook(String id) {
        return messageBookDao.selectById(id);
    }

}
