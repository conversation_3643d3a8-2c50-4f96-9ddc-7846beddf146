package com.rs.module.acp.service.pm;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.cache.RedisClient;
import com.bsp.common.cache.base.StringRedisService;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.config.QingyanDeviceProperties;
import com.rs.module.acp.util.QingyanLocalSenseUtil;
import com.rs.module.acp.util.QingyanLocalsenseHttpUtil;
import com.tsingoal.pojo.LsTagPosition;
import com.tsingoal.pojo.LsVitalSignInfo;
import org.beetl.ext.fn.Json;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.LocalsenseTagPersonDO;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.pm.LocalsenseTagPersonDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-监管管理-定位标签与人员绑定 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LocalsenseTagPersonServiceImpl extends BaseServiceImpl<LocalsenseTagPersonDao, LocalsenseTagPersonDO> implements LocalsenseTagPersonService {

    @Resource
    private LocalsenseTagPersonDao localsenseTagPersonDao;
    @Autowired
    private StringRedisService stringRedisService;

    @Autowired
    private QingyanDeviceProperties qingyanDeviceProperties;
    @Override
    public String createLocalsenseTagPerson(LocalsenseTagPersonSaveReqVO reqVO) {
        // 插入
        LocalsenseTagPersonDO entity = BeanUtils.toBean(reqVO, LocalsenseTagPersonDO.class);
        localsenseTagPersonDao.insert(entity);
        // 返回
        return entity.getId();
    }

    @Override
    public void updateLocalsenseTagPerson(LocalsenseTagPersonSaveReqVO reqVO) {
        // 校验存在
        validateLocalsenseTagPersonExists(reqVO.getId());
        // 更新
        LocalsenseTagPersonDO entity = BeanUtils.toBean(reqVO, LocalsenseTagPersonDO.class);
        localsenseTagPersonDao.updateById(entity);
    }

    @Override
    public void deleteLocalsenseTagPerson(String id) {
        // 校验存在
        validateLocalsenseTagPersonExists(id);
        // 删除
        localsenseTagPersonDao.deleteById(id);
    }

    private void validateLocalsenseTagPersonExists(String id) {
        if (localsenseTagPersonDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-定位标签与人员绑定数据不存在");
        }
    }

    @Override
    public LocalsenseTagPersonDO getLocalsenseTagPerson(String id) {
        return localsenseTagPersonDao.selectById(id);
    }

    /**
     * 入所被监管人员绑定标签(手环)
     * @param reqVO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String rsCreateLocalsenseTagPerson(LocalsenseTagPersonRsSaveReqVO reqVO) {
        try{
            validateBindInfo(reqVO.getTagId(),reqVO.getBindPersonId());
        }catch (Exception e){
            throw new ServerException(e.getMessage());
        }
        LocalsenseTagPersonDO entity = BeanUtils.toBean(reqVO, LocalsenseTagPersonDO.class);
        entity.setBindSource("01");
        entity.setPersonType("01");
        return doBindCommon(entity);
    }
    private String bindToThird(LocalsenseTagPersonDO entity) throws Exception{
        QingyanLocalsenseHttpUtil qingyanLocalsenseHttpUtil = new QingyanLocalsenseHttpUtil(qingyanDeviceProperties);
        //查询当前人员是否已经绑定过标签 == 判定jgrybm 是否已经写入 清研
        List<LocalsenseTagPersonDO> list = localsenseTagPersonDao.selectList(new LambdaQueryWrapper<LocalsenseTagPersonDO>()
                .eq(LocalsenseTagPersonDO::getBindPersonId, entity.getBindPersonId()));
        if(CollectionUtil.isNotNull(list)){
            return qingyanLocalsenseHttpUtil.bindTag(entity.getBindPersonId(),entity.getTagId());
            //return qingyanLocalsenseHttpUtil.updateTag(entity.getOrgCode(),entity.getTagId(),entity.getBindPersonName(),entity.getBindPersonId());
        }
        return qingyanLocalsenseHttpUtil.addTag(entity.getOrgCode(),entity.getTagId(),entity.getBindPersonName(),entity.getBindPersonId());
    }
    /**
     * 智能腕带模块绑定 被监管人员绑定标签(手环)
     * @param reqVO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String znwdCreateLocalsenseTagPerson(LocalsenseTagPersonZnwdSaveReqVO reqVO) {
        try{
            validateBindInfo(reqVO.getTagId(),reqVO.getBindPersonId());
        }catch (Exception e){
            throw new ServerException(e.getMessage());
        }
        // 插入
        LocalsenseTagPersonDO entity = BeanUtils.toBean(reqVO, LocalsenseTagPersonDO.class);
        entity.setBindSource("02");
        entity.setPersonType("01");
        return doBindCommon(entity);
    }
    @Transactional(rollbackFor = Exception.class)
    String doBindCommon(LocalsenseTagPersonDO entity){
        entity.setStatus("01");//已绑定
        if(entity.getBindTime() == null )entity.setBindTime(new Date());
        if (StringUtil.isEmpty(entity.getOrgCode())) entity.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        // 调用清研绑定接口，如果失败则回滚事务
        try {
            String bindRecordId = bindToThird(entity);
            if(StringUtil.isEmpty(bindRecordId)) throw new ServerException("绑定失败：未获取到绑定后的记录ID");
            entity.setBindRecordId(bindRecordId);
            localsenseTagPersonDao.insert(entity);
        } catch (Exception e) {
            throw new ServerException("绑定失败：" + e.getMessage());
        }
        return entity.getId();
    }
    /**
     * 民警绑定工牌
     * @param reqVO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String mjCreateLocalsenseTagPerson(LocalsenseTagPersonMjSaveReqVO reqVO) {
        try{
            validateBindInfo(reqVO.getTagId(),reqVO.getBindPersonId());
        }catch (Exception e){
            throw new ServerException(e.getMessage());
        }
        // 插入
        LocalsenseTagPersonDO entity = BeanUtils.toBean(reqVO, LocalsenseTagPersonDO.class);
        entity.setBindSource("02");
        entity.setPersonType("02");
        return doBindCommon(entity);
    }

    /**
     * 查询bindPersonId 是否已经绑定过手环
     * @param bindPersonIds
     * @return
     */
    @Override
    public List<LocalsenseTagPersonDO> getLocalsenseTagPersonByBindPersonIds(String bindPersonIds) {
        if(StringUtil.isEmpty(bindPersonIds)) return null;
        return localsenseTagPersonDao.selectList(new LambdaQueryWrapper<LocalsenseTagPersonDO>()
                .in(LocalsenseTagPersonDO::getBindPersonId, bindPersonIds.split(",")).eq(LocalsenseTagPersonDO::getStatus, "01"));
    }
    /**
     * 根据tagId查询是否已绑定信息
     * @param tagIds
     * @return
     */
    @Override
    public List<LocalsenseTagPersonDO> getLocalsenseTagPersonByTagIds(String tagIds) {
        if(StringUtil.isEmpty(tagIds)) return null;
        return localsenseTagPersonDao.selectList(new LambdaQueryWrapper<LocalsenseTagPersonDO>()
                .in(LocalsenseTagPersonDO::getTagId, tagIds.split(",")).eq(LocalsenseTagPersonDO::getStatus, "01"));
    }
    /**
     * 解绑 id或tagId,bindPersonId 2选1 作为参数
     * @param reqVO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String unbind(LocalsenseTagPersonUnbindSaveReqVO reqVO) {
        LocalsenseTagPersonDO entity = null;
        if(StringUtil.isNotEmpty(reqVO.getId())){
            entity = localsenseTagPersonDao.selectById(reqVO.getId());
        }else if(StringUtil.isNotEmpty(reqVO.getTagId()) && StringUtil.isNotEmpty(reqVO.getBindPersonId())){
            entity = localsenseTagPersonDao.selectOne(new LambdaQueryWrapper<LocalsenseTagPersonDO>()
                    .eq(LocalsenseTagPersonDO::getTagId, reqVO.getTagId())
                    .eq(LocalsenseTagPersonDO::getBindPersonId, reqVO.getBindPersonId())
                    .eq(LocalsenseTagPersonDO::getStatus, "01"));
        }
        if (entity == null) throw new ServerException("请确保解绑参数正确 id或tagId,bindPersonId 2选1");
        entity.setUnbindReason(reqVO.getUnbindReason());
        if(reqVO.getUnbindTime() != null) entity.setUnbindTime(reqVO.getUnbindTime());
        if(entity.getUnbindTime() == null) entity.setUnbindTime(new Date());
        entity.setStatus("02");//已解绑
        try {
            QingyanLocalsenseHttpUtil qingyanLocalsenseHttpUtil = new QingyanLocalsenseHttpUtil(qingyanDeviceProperties);
            qingyanLocalsenseHttpUtil.unbindTags(entity.getBindPersonId());
            localsenseTagPersonDao.updateById(entity);
        } catch (Exception e) {
            throw new ServerException("解绑失败：" + e.getMessage());
        }
        // 返回
        return entity.getId();
    }

    /**
     * 查询人员所有历史绑定记录
     * @param bindPersonId
     * @return
     */
    @Override
    public List<LocalsenseTagPersonDO> getLocalsenseTagPersonHistoryList(String bindPersonId) {
        return localsenseTagPersonDao.selectList(new LambdaQueryWrapper<LocalsenseTagPersonDO>()
                .eq(LocalsenseTagPersonDO::getBindPersonId, bindPersonId).orderByDesc(LocalsenseTagPersonDO::getBindTime));
    }

    /**
     * 查询标签所有绑定记录
     * @param tagId
     * @return
     */
    @Override
    public List<LocalsenseTagPersonDO> getLocalsenseTagPersonHistoryListByTagId(String tagId) {
        return localsenseTagPersonDao.selectList(new LambdaQueryWrapper<LocalsenseTagPersonDO>()
                .eq(LocalsenseTagPersonDO::getTagId, tagId).orderByDesc(LocalsenseTagPersonDO::getBindTime));
    }
    public void validateBindInfo(String tagId,String bindPersonId)  {
        //查询tagId 当前是否已被绑定
        List<LocalsenseTagPersonDO> list = getLocalsenseTagPersonByTagIds(tagId);
        if(CollectionUtil.isNotNull(list)){
            throw new ServerException("该标签已绑定人员："+list.get(0).getBindPersonName());
        }
        list = getLocalsenseTagPersonByBindPersonIds(bindPersonId);
        if(CollectionUtil.isNotNull(list)){
            throw new ServerException("该人员已绑定标签ID："+list.get(0).getTagId());
        }
    }
    @Override
    public List<JSONObject> getLocalSenseInfo(String tagIds){
        String[] tagIdArray = tagIds.split(",");
        List<JSONObject> list = new ArrayList<>();
        for (String tagIdStr : tagIdArray) {
            if (StringUtil.isNotEmpty(tagIdStr)) {
                long tagId = Long.parseLong(tagIdStr);
                JSONObject result = new JSONObject();
                result.put("tagId",tagId);
                // 获取体征信息
                JSONObject vitalSignInfo = new JSONObject();
                vitalSignInfo.put("xyName","血氧");
                vitalSignInfo.put("xy",getTzxx(tagId,QingyanLocalSenseUtil.TZLX_XY));
                vitalSignInfo.put("xlName","心率");
                vitalSignInfo.put("xl",getTzxx(tagId,QingyanLocalSenseUtil.TZLX_XL));
                vitalSignInfo.put("twName","体温");
                vitalSignInfo.put("tw",getTzxx(tagId,QingyanLocalSenseUtil.TZLX_TW));
                result.put("vitalSignInfo",vitalSignInfo);
                // 获取位置信息
                String positionStr = stringRedisService.getString(QingyanLocalSenseUtil.getPosKey(tagId));
                LsTagPosition position = JSONObject.parseObject(positionStr,LsTagPosition.class);
                result.put("position",position);
                result.put("batteryPercent",position != null ? position.getBatPer() : "0");
                list.add(result);
            }
        }
        return list;
    }
    private String getTzxx(Long tagId,Integer tzlx){
        String vitalSignInfoStr = stringRedisService.getString(QingyanLocalSenseUtil.getVitalsignKey(tagId,tzlx));
        String value = "";
        if(StringUtil.isNotEmpty(vitalSignInfoStr)){
            JSONObject twVitalSignInfo = JSONObject.parseObject(vitalSignInfoStr,JSONObject.class);
            value = twVitalSignInfo.getString("value");
        }
        return value;
    }
    //查询指定监室下人员绑定记录
    @Override
    public List<LocalsenseTagPersonRespVO> getBindPersonRoomList(String orgCode, String roomId){
        return localsenseTagPersonDao.selectTagPersonWithRoomInfo("01",orgCode,roomId);
    }
    @Override
    public List<Map<String, Object>> selectTagJgryCountByRoom(String orgCode, String roomId){
        return localsenseTagPersonDao.selectTagJgryCountByRoom("01",orgCode,roomId);
    }
    @Override
    public List<Map<String, Object>> selectTagPersonCountByRoom(String orgCode, String roomId){
        return localsenseTagPersonDao.selectTagPersonCountByRoom(orgCode,roomId);
    }
    //根据tagId bindRecordId 更新 bloodOxygenTaskId,heartRateTaskId,temperatureTaskId
    /**
     * 设置手环体征数据推送采样周期
     * @param saveReqVO
     * @return
     * @throws Exception
     */
    @Override
    public boolean setHandBandVitalSignPushInterval(LocalsenseHeartRateSaveReqVO saveReqVO) throws Exception {
        if (saveReqVO.getTemperatureValue() == null && saveReqVO.getHeartRateValue() == null && saveReqVO.getBloodOxygenValue() == null) {
            throw new ServerException("请至少设置一个采样周期");
        }

        if (saveReqVO.getTemperatureValue() <= 1 && saveReqVO.getHeartRateValue() <= 1 && saveReqVO.getBloodOxygenValue() <= 1) {
            throw new ServerException("请设置合理的采样周期");
        }
        QingyanLocalsenseHttpUtil qingyanLocalsenseHttpUtil = new QingyanLocalsenseHttpUtil(qingyanDeviceProperties);
        List<LocalsenseHeartRateSaveReqVO.SamplingPeriodTagInfo> tagInfoList = saveReqVO.getTagInfoList();
        if (tagInfoList == null || tagInfoList.isEmpty()) {
            throw new ServerException("标签信息不可为空");
        }
        boolean setBloodOxygenValue = false;
        if(saveReqVO.getBloodOxygenValue() > 1) setBloodOxygenValue = true;

        boolean setHeartRateValue = false;
        if(saveReqVO.getHeartRateValue() > 1) setHeartRateValue = true;

        boolean setTemperatureValue = false;
        if(saveReqVO.getTemperatureValue() > 1) setTemperatureValue = true;
        for (LocalsenseHeartRateSaveReqVO.SamplingPeriodTagInfo tagInfo : tagInfoList) {
            if (tagInfo == null) {
                continue;
            }
            String tagId = tagInfo.getTagId();
            String bloodOxygenTaskId = tagInfo.getBloodOxygenTaskId();
            String heartRateTaskId = tagInfo.getHeartRateTaskId();
            String temperatureTaskId = tagInfo.getTemperatureTaskId();
            LocalsenseTagPersonDO tagPerson = new LocalsenseTagPersonDO();
            tagPerson.setTagId(tagId);
            tagPerson.setBindRecordId(tagInfo.getBindRecordId());

            if(setBloodOxygenValue) {
                qingyanLocalsenseHttpUtil.savePhysicalTask(bloodOxygenTaskId,QingyanLocalSenseUtil.TZLX_XY, saveReqVO.getBloodOxygenValue(), tagInfo.getBindRecordId());
                tagPerson.setBloodOxygenPeriod(saveReqVO.getBloodOxygenValue());
                tagPerson.setBloodOxygenTaskId(bloodOxygenTaskId);
            }
            if(setHeartRateValue) {
                qingyanLocalsenseHttpUtil.savePhysicalTask(heartRateTaskId,QingyanLocalSenseUtil.TZLX_XL, saveReqVO.getHeartRateValue(), tagInfo.getBindRecordId());
                tagPerson.setHeartRateTaskId(heartRateTaskId);
                tagPerson.setHeartRatePeriod(saveReqVO.getHeartRateValue());
            }
            if(setTemperatureValue) {
                qingyanLocalsenseHttpUtil.savePhysicalTask(temperatureTaskId,QingyanLocalSenseUtil.TZLX_TW, saveReqVO.getTemperatureValue(), tagInfo.getBindRecordId());
                tagPerson.setTemperatureTaskId(temperatureTaskId);
                tagPerson.setTemperaturePeriod(saveReqVO.getTemperatureValue());
            }
            //根据tagId,tagInfo.getBindRecordId() 修改 tagPerson设置的项
            localsenseTagPersonDao.updateTagSetInfo(tagPerson);
        }
        return true;
    }

    @Override
    public JSONObject getAlarmByParam(String alarmType, String areaId, String beginTime, String endTime, Integer num, Integer page, String state) throws Exception {
        return new QingyanLocalsenseHttpUtil(qingyanDeviceProperties).getAlarmByParam(alarmType,areaId,beginTime,endTime,num,page,state);
    }
}
