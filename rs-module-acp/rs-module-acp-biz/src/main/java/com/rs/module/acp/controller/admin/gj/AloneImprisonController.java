package com.rs.module.acp.controller.admin.gj;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.cons.CommonConstants;
import com.rs.module.acp.controller.admin.gj.vo.aloneimprison.AloneImprisonApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsACPSaveVO;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomRespVO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.util.BspApprovalUtil;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.gj.vo.aloneimprison.*;
import com.rs.module.acp.entity.gj.AloneImprisonDO;
import com.rs.module.acp.service.gj.AloneImprisonService;

@Api(tags = "实战平台-管教业务-单独关押登记")
@RestController
@RequestMapping("/acp/gj/aloneImprison")
@Validated
public class AloneImprisonController {

    @Resource
    private AloneImprisonService aloneImprisonService;

    /*@PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-单独关押登记")
    public CommonResult<String> createAloneImprison(@Valid @RequestBody AloneImprisonSaveReqVO createReqVO) {
        return success(aloneImprisonService.createAloneImprison(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-单独关押登记")
    public CommonResult<Boolean> updateAloneImprison(@Valid @RequestBody AloneImprisonSaveReqVO updateReqVO) {
        aloneImprisonService.updateAloneImprison(updateReqVO);
        return success(true);
    }*/

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-单独关押登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteAloneImprison(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           aloneImprisonService.deleteAloneImprison(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-单独关押登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<AloneImprisonRespVO> getAloneImprison(@RequestParam("id") String id) {
        AloneImprisonDO aloneImprison = aloneImprisonService.getAloneImprison(id);
        return success(BeanUtils.toBean(aloneImprison, AloneImprisonRespVO.class));
    }

    /*@PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-单独关押登记分页")
    public CommonResult<PageResult<AloneImprisonRespVO>> getAloneImprisonPage(@Valid @RequestBody AloneImprisonPageReqVO pageReqVO) {
        PageResult<AloneImprisonDO> pageResult = aloneImprisonService.getAloneImprisonPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AloneImprisonRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-单独关押登记列表")
    public CommonResult<List<AloneImprisonRespVO>> getAloneImprisonList(@Valid @RequestBody AloneImprisonListReqVO listReqVO) {
        List<AloneImprisonDO> list = aloneImprisonService.getAloneImprisonList(listReqVO);
        return success(BeanUtils.toBean(list, AloneImprisonRespVO.class));
    }*/
    @PostMapping("/getJgryAloneImprisonList")
    @ApiOperation(value = "获得实战平台-管教业务-單個監管人員单独关押登记列表")
    public CommonResult<List<AloneImprisonRespVO>> getJgryAloneImprisonList(String jgrybm) {
        AloneImprisonListReqVO listReqVO = new AloneImprisonListReqVO();
        listReqVO.setJgrybm(jgrybm);
        List<AloneImprisonDO> list = aloneImprisonService.getAloneImprisonList(listReqVO);
        return success(BeanUtils.toBean(list, AloneImprisonRespVO.class));
    }
    @PostMapping("/getAloneImprisonRoomList")
    @ApiOperation(value = "获得实战平台-管教业务-单押监室选择")
    public CommonResult<List<AreaPrisonRoomRespVO>> getAloneImprisonRoomList(String nowRoomId) {
        List<AreaPrisonRoomDO> list = aloneImprisonService.getAloneImprisonRoomList(nowRoomId);
        return success(BeanUtils.toBean(list, AreaPrisonRoomRespVO.class));
    }

    @PostMapping("/save")
    @ApiOperation(value = "创建实战平台-管教业务-单独关押登记")
    //@BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{'监室编号':'roomId‘,'创建时间':'ddd'}")
    public CommonResult<String> save(@Valid @RequestBody AloneImprisonSaveReqVO createReqVO) {
        try  {
            return success(aloneImprisonService.save(createReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }

    @PostMapping("/approve")
    @ApiOperation(value = "领导审批")
    /*@LogRecordAnnotation(bizModule = "acp:aloneImprison:approve", operateType = LogOperateType.UPDATE, title = "管教业务-单独关押-领导审批",
            success = "管教业务-单独关押-领导审批成功", fail = "管教业务-单独关押-领导审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")*/
    public CommonResult<Boolean> approve(@Valid @RequestBody AloneImprisonApproveReqVO approveReqVO) {
        try  {
            return success(aloneImprisonService.approve(approveReqVO));
        }catch (Exception e){
            e.printStackTrace();
            return CommonResult.error(e.getMessage());
        }
    }
    //查询jgrybm 对应的历史关押记录
    @GetMapping("/getJgryAloneImprisonHistoryList")
    @ApiOperation(value = "获得实战平台-管教业务-单個監管人員关押历史列表")
    public CommonResult<List<AloneImprisonRespVO>> getJgryAloneImprisonHistoryList(String jgrybm) {
        AloneImprisonListReqVO listReqVO = new AloneImprisonListReqVO();
        listReqVO.setJgrybm(jgrybm);
        listReqVO.setStatus("1");
        List<AloneImprisonDO> list = aloneImprisonService.getAloneImprisonList(listReqVO);
        return success(BeanUtils.toBean(list, AloneImprisonRespVO.class));
    }
    @PostMapping("/saveInOutRecords")
    @ApiOperation(value = "带入带出登记")
    public CommonResult<Boolean> saveInOutRecords(@Valid @RequestBody InOutRecordsACPSaveVO respVO) {
        try {
            return success(aloneImprisonService.saveInOutRecords(respVO));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
    @PostMapping("/getAloneImprisonListNow")
    @ApiOperation(value = "当前单独关押人员")
    public CommonResult<List<AloneImprisonRespVO>> getAloneImprisonListNow() {
        return success(BeanUtils.toBean(aloneImprisonService.getAloneImprisonListNow(), AloneImprisonRespVO.class));
    }
    @PostMapping("/finishAloneImprison")
    @ApiOperation(value = "结束单独关押人员")
    public CommonResult<Boolean> finishAloneImprison(String jgrybm) {
        try {
            aloneImprisonService.finishAloneImprison(jgrybm,null);
        } catch (Exception e) {
            return error(e.getMessage());
        }
        return success(true);
    }
    @GetMapping("/test")
    @ApiOperation(value = "领导审批")
    public CommonResult<?> testAutoCompleteProcess() {

        String defKey="demotiaojianfenzhi";
        Map<String, Object> variables = new HashMap<>();
        variables.put("status", 1);
        return BspApprovalUtil.autoCompleteProcess( defKey,  "111111111111",  "",  "",
                 variables, HttpUtils.getAppCode());
    }
}
