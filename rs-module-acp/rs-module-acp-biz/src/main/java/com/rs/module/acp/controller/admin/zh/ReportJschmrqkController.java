package com.rs.module.acp.controller.admin.zh;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.DateUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.zh.vo.ReportJschmrqkListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.ReportJschmrqkPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.ReportJschmrqkRespVO;
import com.rs.module.acp.controller.admin.zh.vo.ReportJschmrqkSaveReqVO;
import com.rs.module.acp.entity.zh.ReportJschmrqkDO;
import com.rs.module.acp.service.zh.ReportJschmrqkService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@Api(tags = "监所晨会每日情况")
@RestController
@RequestMapping("/acp/zh/reportJschmrqk")
@Validated
public class ReportJschmrqkController {

    @Resource
    private ReportJschmrqkService reportJschmrqkService;
    
    @Resource
    private FileStorageService fileStorageService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监所晨会每日情况")
    public CommonResult<String> createReportJschmrqk(@Valid @RequestBody ReportJschmrqkSaveReqVO createReqVO) {
    	SessionUser user = SessionUserUtil.getSessionUser();
    	ReportJschmrqkDO reportJschmrqkDO = reportJschmrqkService.getByReportDate(user.getOrgCode(), createReqVO.getReportDate());
    	if(reportJschmrqkDO == null) {
    		return success(reportJschmrqkService.createReportJschmrqk(createReqVO));
    	}
    	else {
    		String reportDate = DateUtil.getDateFormat(reportJschmrqkDO.getReportDate(), "yyyy-MM-dd");
    		return CommonResult.error("【" + reportDate +  "】已经创建晨会每日情况，不允许重复创建！");
    	}        
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新监所晨会每日情况")
    public CommonResult<Boolean> updateReportJschmrqk(@Valid @RequestBody ReportJschmrqkSaveReqVO updateReqVO) {
    	SessionUser user = SessionUserUtil.getSessionUser();
    	ReportJschmrqkDO reportJschmrqkDO = reportJschmrqkService.getByReportDate(user.getOrgCode(), updateReqVO.getReportDate());
    	if(reportJschmrqkDO != null && !reportJschmrqkDO.getId().equals(updateReqVO.getId())) {
    		String reportDate = DateUtil.getDateFormat(reportJschmrqkDO.getReportDate(), "yyyy-MM-dd");
    		return CommonResult.error("【" + reportDate +  "】已经创建晨会每日情况，不允许重复创建！");
    	}
    	else {
    		reportJschmrqkService.updateReportJschmrqk(updateReqVO);
            return success(true);
    	}        
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监所晨会每日情况")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteReportJschmrqk(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           reportJschmrqkService.deleteReportJschmrqk(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得监所晨会每日情况")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ReportJschmrqkRespVO> getReportJschmrqk(@RequestParam("id") String id) {
        ReportJschmrqkDO reportJschmrqk = reportJschmrqkService.getReportJschmrqk(id);
        return success(BeanUtils.toBean(reportJschmrqk, ReportJschmrqkRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得监所晨会每日情况分页")
    public CommonResult<PageResult<ReportJschmrqkRespVO>> getReportJschmrqkPage(@Valid @RequestBody ReportJschmrqkPageReqVO pageReqVO) {
        PageResult<ReportJschmrqkDO> pageResult = reportJschmrqkService.getReportJschmrqkPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ReportJschmrqkRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得监所晨会每日情况列表")
    public CommonResult<List<ReportJschmrqkRespVO>> getReportJschmrqkList(@Valid @RequestBody ReportJschmrqkListReqVO listReqVO) {
        List<ReportJschmrqkDO> list = reportJschmrqkService.getReportJschmrqkList(listReqVO);
        return success(BeanUtils.toBean(list, ReportJschmrqkRespVO.class));
    }
    
    @PostMapping("/buildReportJschmrqk")
    @ApiOperation(value = "生成监所晨会每日情况报告")
    @ApiImplicitParam(name = "id", value = "主键")
    public CommonResult<?> buildReportJschmrqk(@RequestParam("id") String id) {
    	try {
    		ReportJschmrqkDO reportJschmrqk = reportJschmrqkService.getReportJschmrqk(id);
    		if(reportJschmrqk != null) {
    			
    			//构建业务数据
    			JSONObject formData = reportJschmrqkService.buildFormData(reportJschmrqk);
    			
        		return CommonResult.success(formData);
    		}
    		else {
    			return CommonResult.error("【监所晨会每日情况】生成报告异常！异常信息：找不到业务数据");
    		}
    	}
    	catch(Exception e) {
    		return CommonResult.error("【监所晨会每日情况】生成报告异常！异常信息：" + e.getMessage());
    	}
    }
    
    @PostMapping("/uploadReportJschmrqk")
    @ApiOperation(value = "上传监所晨会每日情况报告")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "id", value = "主键"),
    	@ApiImplicitParam(name = "isPdf", value = "是否pdf(true:pdf,false:word)"),
    	@ApiImplicitParam(name = "file", value = "报告文件")
    })    
    public CommonResult<?> uploadReportJschmrqk(@RequestParam("id") String id,
    		@RequestParam(value = "isPdf", defaultValue = "false") boolean isPdf, 
    		@RequestParam("file") MultipartFile file) {
    	try {
    		byte[] reportBytes = file.getBytes();
    		if(reportBytes != null && reportBytes.length > 0) {
    			ReportJschmrqkDO reportJschmrqk = reportJschmrqkService.getReportJschmrqk(id);
        		if(reportJschmrqk != null) {
        			String fileId = StringUtil.getGuid32();
        			String fileName = fileId + (isPdf ? ".pdf" : ".docx");
        			FileInfo fileInfo = fileStorageService.of(reportBytes)
        					.setSaveFilename(fileName)
        					.setObjectId(fileId)
        					.upload();
        			reportJschmrqk.setStatus("1");
        			reportJschmrqk.setWordUrl(fileInfo.getUrl());
        			reportJschmrqkService.updateById(reportJschmrqk);
        			
        			return CommonResult.success("【上传监所晨会每日情况报告】上传成功！");
        		}
        		else {
        			return CommonResult.error("【上传监所晨会每日情况报告】上传失败！异常信息：找不到记录");
        		}	
    		}
    		else {
    			return CommonResult.error("【上传监所晨会每日情况报告】上传失败！异常信息：上传文件为空");
    		}    		
    	}
    	catch(Exception e) {
    		return CommonResult.error("【监所晨会每日情况报告】上传失败！异常信息：" + e.getMessage());
    	}
    }
}
