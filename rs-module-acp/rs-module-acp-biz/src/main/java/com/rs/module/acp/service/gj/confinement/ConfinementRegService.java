package com.rs.module.acp.service.gj.confinement;

import java.util.*;
import javax.validation.*;

import com.rs.module.acp.controller.admin.gj.vo.confinement.*;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsACPSaveVO;
import com.rs.module.acp.entity.gj.confinement.ConfinementRegDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;

/**
 * 实战平台-管教业务-禁闭登记 Service 接口
 *
 * <AUTHOR>
 */
public interface ConfinementRegService extends IBaseService<ConfinementRegDO>{

    /**
     * 创建实战平台-管教业务-禁闭登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createConfinementReg(@Valid ConfinementRegSaveReqVO createReqVO) throws Exception;

    /**
     * 更新实战平台-管教业务-禁闭登记
     *
     * @param updateReqVO 更新信息
     */
    void updateConfinementReg(@Valid ConfinementRegSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-禁闭登记
     *
     * @param id 编号
     */
    void deleteConfinementReg(String id);

    /**
     * 获得实战平台-管教业务-禁闭登记
     *
     * @param id 编号
     * @return 实战平台-管教业务-禁闭登记
     */
    ConfinementRegDO getConfinementReg(String id);

    /**
    * 获得实战平台-管教业务-禁闭登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-管教业务-禁闭登记分页
    */
    PageResult<ConfinementRegDO> getConfinementRegPage(ConfinementRegPageReqVO pageReqVO);

    /**
    * 获得实战平台-管教业务-禁闭登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-管教业务-禁闭登记列表
    */
    List<ConfinementRegDO> getConfinementRegList(ConfinementRegListReqVO listReqVO);


    boolean approve(ConfinementRegApproveVO approveReqVO) throws Exception;

    //根据id 更新实际禁闭到期时间
    boolean updateActualEndTimeByNum(String id, int daynum);
    boolean updateActualEndTime(String id, Date endDate);

    List<AreaPrisonRoomDO> getConfinementRoomList(String nowRoomId);

    boolean saveInOutRecords(InOutRecordsACPSaveVO saveReqVO) throws Exception;

    List<ConfinementRegDO> getNotAllowedRecords(String jgrybm);

    void updateStatus(String confinementId, String status);
}
