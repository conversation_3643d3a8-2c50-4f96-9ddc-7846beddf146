package com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-权力义务告知书 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RightsObligationsNoticeRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("签名url")
    private String signUrl;
    @ApiModelProperty("签名时间")
    private Date signTime;
    @ApiModelProperty("捺印url")
    private String fingerprintUrl;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("签名类型 1：线上  2：线下")
    @Trans(type = TransType.DICTIONARY, key = "ZD_QLYWGZS_SIGN_TYPE")
    private String signType;

    @ApiModelProperty("线下上传url")
    private String offlineUrl;

    private String cityName;

    private String cityCode;

    private String regName;

    private String regCode;

    private String orgName;

    private String orgCode;

    private Boolean isDel;

    private String addUser;

    private String addUserName;

    private Date addTime;

    private String updateUser;

    private String updateUserName;

    private Date updateTime;
}
