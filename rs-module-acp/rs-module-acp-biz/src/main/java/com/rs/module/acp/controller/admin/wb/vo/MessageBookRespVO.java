package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-留言内容 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MessageBookRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("留言时间")
    private Date messageTime;
    @ApiModelProperty("留言人姓名")
    private String messageUserName;
    @ApiModelProperty("留言人联系电话")
    private String messageUserPhone;
    @ApiModelProperty("人员类型")
    private String userType;
    @ApiModelProperty("工作单位")
    private String workUnit;
    @ApiModelProperty("地址")
    private String address;
    @ApiModelProperty("留言内容")
    private String messageContent;
}
