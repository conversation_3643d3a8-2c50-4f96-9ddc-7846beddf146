package com.rs.module.acp.dao.gj;

import java.util.*;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.riskIndicator.RiskIndicatorListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.riskIndicator.RiskIndicatorPageReqVO;
import com.rs.module.acp.entity.gj.RiskIndicatorDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 实战平台-管教业务-风险指标 Dao
*
* <AUTHOR>
*/
@Mapper
public interface RiskIndicatorDao extends IBaseDao<RiskIndicatorDO> {


    default PageResult<RiskIndicatorDO> selectPage(RiskIndicatorPageReqVO reqVO) {
        Page<RiskIndicatorDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<RiskIndicatorDO> wrapper = new LambdaQueryWrapperX<RiskIndicatorDO>()
            .eqIfPresent(RiskIndicatorDO::getIndicatorTypeCode, reqVO.getIndicatorTypeCode())
            .likeIfPresent(RiskIndicatorDO::getIndicatorName, reqVO.getIndicatorName())
            .eqIfPresent(RiskIndicatorDO::getIndicatorDescription, reqVO.getIndicatorDescription())
            .eqIfPresent(RiskIndicatorDO::getRiskLevel, reqVO.getRiskLevel())
            .eqIfPresent(RiskIndicatorDO::getPositiveAnomalous, reqVO.getPositiveAnomalous())
            .eqIfPresent(RiskIndicatorDO::getQueryScript, reqVO.getQueryScript())
            .eqIfPresent(RiskIndicatorDO::getDisplayTemplate, reqVO.getDisplayTemplate())
            .eqIfPresent(RiskIndicatorDO::getIsEnabled, reqVO.getIsEnabled())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(RiskIndicatorDO::getAddTime);
        }
        Page<RiskIndicatorDO> riskIndicatorPage = selectPage(page, wrapper);
        return new PageResult<>(riskIndicatorPage.getRecords(), riskIndicatorPage.getTotal());
    }
    default List<RiskIndicatorDO> selectList(RiskIndicatorListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<RiskIndicatorDO>()
            .eqIfPresent(RiskIndicatorDO::getIndicatorTypeCode, reqVO.getIndicatorTypeCode())
            .likeIfPresent(RiskIndicatorDO::getIndicatorName, reqVO.getIndicatorName())
            .eqIfPresent(RiskIndicatorDO::getIndicatorDescription, reqVO.getIndicatorDescription())
            .eqIfPresent(RiskIndicatorDO::getRiskLevel, reqVO.getRiskLevel())
            .eqIfPresent(RiskIndicatorDO::getPositiveAnomalous, reqVO.getPositiveAnomalous())
            .eqIfPresent(RiskIndicatorDO::getQueryScript, reqVO.getQueryScript())
            .eqIfPresent(RiskIndicatorDO::getDisplayTemplate, reqVO.getDisplayTemplate())
            .eqIfPresent(RiskIndicatorDO::getIsEnabled, reqVO.getIsEnabled())
                .eqIfPresent(RiskIndicatorDO::getOrgCode, reqVO.getOrgCode())
        .orderByAsc(RiskIndicatorDO::getAddTime));    }

    @InterceptorIgnore(tenantLine = "true")
    JSONObject getOne(@Param("sql") String sql);

    @InterceptorIgnore(tenantLine = "true")
    boolean riskCondition(@Param("sql") String sql);

    }
