package com.rs.module.acp.controller.admin.wb;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.wb.vo.AntiDrugEducationRespVO;
import com.rs.module.acp.controller.admin.wb.vo.AntiDrugEducationSaveReqVO;
import com.rs.module.acp.entity.wb.AntiDrugEducationDO;
import com.rs.module.acp.service.wb.AntiDrugEducationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-窗口业务-禁毒教育")
@RestController
@RequestMapping("/acp/wb/antiDrugEducation")
@Validated
public class AntiDrugEducationController {

    @Resource
    private AntiDrugEducationService antiDrugEducationService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-窗口业务-禁毒教育")
    public CommonResult<String> createAntiDrugEducation(@Valid @RequestBody AntiDrugEducationSaveReqVO createReqVO) {
        return success(antiDrugEducationService.createAntiDrugEducation(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-窗口业务-禁毒教育")
    public CommonResult<Boolean> updateAntiDrugEducation(@Valid @RequestBody AntiDrugEducationSaveReqVO updateReqVO) {
        antiDrugEducationService.updateAntiDrugEducation(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-窗口业务-禁毒教育")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteAntiDrugEducation(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           antiDrugEducationService.deleteAntiDrugEducation(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-窗口业务-禁毒教育")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<AntiDrugEducationRespVO> getAntiDrugEducation(@RequestParam("id") String id) {
        AntiDrugEducationDO antiDrugEducation = antiDrugEducationService.getAntiDrugEducation(id);
        return success(BeanUtils.toBean(antiDrugEducation, AntiDrugEducationRespVO.class));
    }
}
