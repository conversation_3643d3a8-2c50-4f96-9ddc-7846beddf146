package com.rs.module.acp.controller.admin.wb.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-禁毒教育 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AntiDrugEducationRespVO extends BaseVO implements TransPojo {
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("活动时间")
    private Date activityTime;
    @ApiModelProperty("活动类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_DWFW_JDJJ_HDLX")
    private String activityType;
    @ApiModelProperty("具体活动类型")
    private String specificActivityType;
    @ApiModelProperty("授课人身份证号")
    private String lecturerUserSfzh;
    @ApiModelProperty("授课人姓名")
    private String lecturerUserName;
    @ApiModelProperty("授课人单位")
    private String lecturerUnit;
    @ApiModelProperty("授课题目")
    private String lectureTopic;
    @ApiModelProperty("授课内容摘要")
    private String lectureContent;
    @ApiModelProperty("听课人数")
    private Integer attendeeCount;
    @ApiModelProperty("听课单位")
    private String attendeeUnit;
    @ApiModelProperty("活动地点")
    private String activityLocation;
}
