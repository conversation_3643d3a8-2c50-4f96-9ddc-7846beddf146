package com.rs.module.acp.service.dch.job;

import com.bsp.common.util.StringUtil;
import com.rs.framework.common.cons.RedisConstants;
import com.rs.module.acp.service.dch.DataChangeEventLogService;
import com.rs.module.acp.service.dch.processor.DataChangeEventProcessor;
import com.rs.module.base.util.RedisDistributedLockUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数据变更事件定时任务 (XXL-JOB)
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Slf4j
@Component
public class DataChangeEventJob {

    @Autowired
    private DataChangeEventProcessor eventProcessor;

    @Autowired
    private DataChangeEventLogService eventLogService;

    @Autowired
    private RedisDistributedLockUtil redisLockUtil;

    /**
     * 处理待处理的事件
     */
    @XxlJob("processAcpPendingEvents")
    @Transactional
    public void processPendingEvents() {
        String lockKey = RedisConstants.ACP_DATA_CHANGE_EVENT_PROCESSING_PENDING_LOCK;

        // 尝试获取分布式锁，锁过期时间设置为30分钟（防止任务执行时间过长）
        if (redisLockUtil.tryLock(lockKey, 1800)) {
            try {
                String uuid = StringUtil.getGuid32();
                String jobParam = XxlJobHelper.getJobParam();
                int batchSize = 500;

                // 支持通过任务参数传递批处理大小
                if (jobParam != null && !jobParam.trim().isEmpty()) {
                    try {
                        batchSize = Integer.parseInt(jobParam.trim());
                    } catch (NumberFormatException e) {
                        log.warn("[{}]任务参数格式错误，使用默认批处理大小: {}", uuid, batchSize);
                        XxlJobHelper.log("[{}]任务参数格式错误，使用默认批处理大小: {}", uuid, batchSize);
                    }
                }

                log.debug("[{}]开始处理待处理的数据变更事件，批处理大小: {}", uuid, batchSize);
                XxlJobHelper.log("[{}]开始处理待处理的数据变更事件，批处理大小: {}", uuid, batchSize);
                long startTime = System.currentTimeMillis();

                int processedCount = eventProcessor.processPendingEvents(batchSize);

                double duration = (System.currentTimeMillis() - startTime) / 1000.0;
                if (processedCount > 0) {
                    log.info("[{}]处理待处理事件完成: 处理数量={}, 用时: {}s", uuid, processedCount, duration);
                    XxlJobHelper.log("[{}]处理待处理事件完成: 处理数量={}, 用时: {}s", uuid, processedCount, duration);
                } else {
                    log.debug("[{}]处理待处理事件完成: 无待处理事件, 用时: {}s", uuid, duration);
                    XxlJobHelper.log("[{}]处理待处理事件完成: 无待处理事件, 用时: {}s", uuid, duration);
                }
            } catch (Exception e) {
                log.error("处理待处理事件异常", e);
                XxlJobHelper.handleFail("处理待处理事件异常: " + e.getMessage());
                e.printStackTrace();
            } finally {
                // 释放分布式锁
                redisLockUtil.releaseLock(lockKey);
            }
        } else {
            log.info("处理待处理事件任务正在执行中，跳过本次执行");
            XxlJobHelper.log("处理待处理事件任务正在执行中，跳过本次执行");
        }
    }

}
