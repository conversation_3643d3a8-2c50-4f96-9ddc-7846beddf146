package com.rs.module.acp.service.wb;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.wb.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.Date;

@Service
@Validated
public class WbBusinessApiServiceImpl implements WbBusinessApiService{

    @Autowired
    private ArraignmentService arraignmentService;

    @Autowired
    private BringInterrogationService bringInterrogationService;

    @Autowired
    private EscortService escortService;

    @Autowired
    private FamilyMeetingService familyMeetingService;

    @Autowired
    private LawyerMeetingService lawyerMeetingService;

    @Autowired
    private ConsularMeetingService consularMeetingService;

    @Autowired
    private FamilyMeetingVideoService familyMeetingVideoService;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean inOutBusinessDrive(WbInOutSaveReqVO saveReqVO, String businessType, String inoutType) {
        if(ObjectUtil.isEmpty(saveReqVO.getId())){
            throw new ServerException("业务ID不可为空！");
        }
        if(!"0302".equals(saveReqVO.getBusinessSubType())){
            if("01".equals(inoutType)){
                saveReqVO.setMeetingStartTime(DateUtil.offsetMinute(saveReqVO.getInoutTime(),2));
            }else {
                saveReqVO.setMeetingEndTime(DateUtil.offsetMinute(saveReqVO.getInoutTime(),-2));
            }
        }

        JSONObject targetJson = assembly(saveReqVO,inoutType);
        if("01".equals(businessType) && "0101".equals(saveReqVO.getBusinessSubType())){
            return arraignment(targetJson,inoutType);
        }else if("01".equals(businessType) && "0102".equals(saveReqVO.getBusinessSubType())){
            return bringInterrogation(targetJson,inoutType);
        }else if("02".equals(businessType)){
            return escort(targetJson,inoutType);
        }else if("03".equals(businessType) && "0301".equals(saveReqVO.getBusinessSubType())){
            return familyMeeting(targetJson,inoutType);
        }else if("03".equals(businessType) && "0302".equals(saveReqVO.getBusinessSubType())){
            return familyMeetingVideo(targetJson,inoutType);
        }else if("04".equals(businessType)){
            return lawyerMeeting(targetJson,inoutType);
        }else if("07".equals(businessType)){
            return consularMeeting(targetJson,inoutType);
        }else {
            throw new ServerException("未找到对应的业务类型！");
        }
    }

    private JSONObject assembly(WbInOutSaveReqVO inOutSaveReqVO,String inoutType){
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(inOutSaveReqVO));
        if("01".equals(inoutType)){
            jsonObject.put("escortingOperatorTime",ObjectUtil.isNotEmpty(inOutSaveReqVO.getOperateTime())?inOutSaveReqVO.getOperateTime():new Date());
            jsonObject.put("escortingOperator",ObjectUtil.isNotEmpty(inOutSaveReqVO.getOperator())?
                    inOutSaveReqVO.getOperator():ObjectUtil.isNotEmpty(sessionUser)?sessionUser.getName():"");
            jsonObject.put("escortingOperatorSfzh",ObjectUtil.isNotEmpty(inOutSaveReqVO.getOperatorSfzh())?
                    inOutSaveReqVO.getOperatorSfzh():ObjectUtil.isNotEmpty(sessionUser)?sessionUser.getIdCard():"");
            jsonObject.put("escortingPolice",inOutSaveReqVO.getInoutPolice());
            jsonObject.put("escortingPoliceSfzh",inOutSaveReqVO.getInoutPoliceSfzh());
            jsonObject.put("escortingTime",inOutSaveReqVO.getInoutTime());
        }else {
            jsonObject.put("returnOperatorTime",ObjectUtil.isNotEmpty(inOutSaveReqVO.getOperateTime())?inOutSaveReqVO.getOperateTime():new Date());
            jsonObject.put("returnOperator",ObjectUtil.isNotEmpty(inOutSaveReqVO.getOperator())?
                    inOutSaveReqVO.getOperator():ObjectUtil.isNotEmpty(sessionUser)?sessionUser.getName():"");
            jsonObject.put("returnOperatorSfzh",ObjectUtil.isNotEmpty(inOutSaveReqVO.getOperatorSfzh())?
                    inOutSaveReqVO.getOperatorSfzh():ObjectUtil.isNotEmpty(sessionUser)?sessionUser.getIdCard():"");
            jsonObject.put("returnPolice",inOutSaveReqVO.getInoutPolice());
            jsonObject.put("returnPoliceSfzh",inOutSaveReqVO.getInoutPoliceSfzh());
            jsonObject.put("returnInspectorSfzh",inOutSaveReqVO.getInspectorSfzh());
            jsonObject.put("returnInspector",inOutSaveReqVO.getInspector());
            jsonObject.put("returnInspectionTime",inOutSaveReqVO.getInspectionTime());
            jsonObject.put("returnInspectionResult",inOutSaveReqVO.getInspectionResult());
            jsonObject.put("returnProhibitedItems",inOutSaveReqVO.getProhibitedItems());
            jsonObject.put("returnPhysicalExam",inOutSaveReqVO.getPhysicalExam());
            jsonObject.put("returnAbnormalSituations",inOutSaveReqVO.getAbnormalSituations());
        }
        return jsonObject;
    }

    private boolean arraignment(JSONObject inOutSaveReqVO,String inoutType){
        ArraignmentSaveReqVO saveVO = BeanUtils.toBean(inOutSaveReqVO,ArraignmentSaveReqVO.class);
        saveVO.setDataSources("1");
        if("01".equals(inoutType)){
            saveVO.setArraignmentStartTime(inOutSaveReqVO.getDate("meetingStartTime"));
            return arraignmentService.escortingInspect(saveVO);
        }else {
            saveVO.setArraignmentEndTime(inOutSaveReqVO.getDate("meetingEndTime"));
            return arraignmentService.returnInspection(saveVO);
        }
    }

    private boolean bringInterrogation(JSONObject inOutSaveReqVO,String inoutType){
        BringInterrogationSaveReqVO saveVO = BeanUtils.toBean(inOutSaveReqVO,BringInterrogationSaveReqVO.class);
        saveVO.setDataSources("1");
        if("01".equals(inoutType)){
            saveVO.setArraignmentStartTime(inOutSaveReqVO.getDate("meetingStartTime"));
            return bringInterrogationService.escortingInspect(saveVO);
        }else {
            saveVO.setArraignmentEndTime(inOutSaveReqVO.getDate("meetingEndTime"));
            return bringInterrogationService.returnInspection(saveVO);
        }
    }

    private boolean escort(JSONObject inOutSaveReqVO,String inoutType){
        EscortSaveReqVO saveVO = BeanUtils.toBean(inOutSaveReqVO,EscortSaveReqVO.class);
        saveVO.setDataSources("1");
        if("01".equals(inoutType)){
            saveVO.setArraignmentStartTime(inOutSaveReqVO.getDate("meetingStartTime"));
            return escortService.escortingInspect(saveVO);
        }else {
            saveVO.setArraignmentEndTime(inOutSaveReqVO.getDate("meetingEndTime"));
            return escortService.returnInspection(saveVO);
        }
    }

    private boolean familyMeeting(JSONObject inOutSaveReqVO,String inoutType){
        FamilyMeetingSaveReqVO saveVO = BeanUtils.toBean(inOutSaveReqVO,FamilyMeetingSaveReqVO.class);
        saveVO.setDataSources("1");
        if("01".equals(inoutType)){
            saveVO.setMeetingStartTime(inOutSaveReqVO.getDate("meetingStartTime"));
            return familyMeetingService.escortingInspect(saveVO);
        }else {
            saveVO.setMeetingEndTime(inOutSaveReqVO.getDate("meetingEndTime"));
            return familyMeetingService.returnInspection(saveVO);
        }
    }

    private boolean lawyerMeeting(JSONObject inOutSaveReqVO,String inoutType){
        LawyerMeetingSaveReqVO saveVO = BeanUtils.toBean(inOutSaveReqVO,LawyerMeetingSaveReqVO.class);
        saveVO.setDataSources("1");
        if("01".equals(inoutType)){
            saveVO.setMeetingStartTime(inOutSaveReqVO.getDate("meetingStartTime"));
            return lawyerMeetingService.escortingInspect(saveVO);
        }else {
            saveVO.setMeetingEndTime(inOutSaveReqVO.getDate("meetingEndTime"));
            return lawyerMeetingService.returnInspection(saveVO);
        }
    }

    private boolean consularMeeting(JSONObject inOutSaveReqVO,String inoutType){
        ConsularMeetingSaveReqVO saveVO = BeanUtils.toBean(inOutSaveReqVO,ConsularMeetingSaveReqVO.class);
        saveVO.setDataSources("1");
        if("01".equals(inoutType)){
            saveVO.setMeetingStartTime(inOutSaveReqVO.getDate("meetingStartTime"));
            return consularMeetingService.escortingInspect(saveVO);
        }else {
            saveVO.setMeetingEndTime(inOutSaveReqVO.getDate("meetingEndTime"));
            return consularMeetingService.returnInspection(saveVO);
        }
    }

    private boolean familyMeetingVideo(JSONObject inOutSaveReqVO,String inoutType){
//        LambdaUpdateWrapper<FamilyMeetingVideoDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
//        lambdaUpdateWrapper.eq(FamilyMeetingVideoDO::getId,inOutSaveReqVO.getString("id"));
//        if("01".equals(inoutType)){
//            lambdaUpdateWrapper.set(FamilyMeetingVideoDO::getMeetingStartTime,inOutSaveReqVO.getDate("meetingStartTime"));
//        }else {
//            lambdaUpdateWrapper.set(FamilyMeetingVideoDO::getMeetingEndTime,inOutSaveReqVO.getDate("meetingEndTime"));
//        }
//        return familyMeetingVideoService.update(lambdaUpdateWrapper);
        return true;
    }
}
