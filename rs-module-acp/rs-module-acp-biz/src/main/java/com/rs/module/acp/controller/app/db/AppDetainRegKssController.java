package com.rs.module.acp.controller.app.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.db.vo.BiometricInfoRespVO;
import com.rs.module.acp.controller.admin.db.vo.InjuryAssessmentSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.app.db.vo.AppBiometricRespVO;
import com.rs.module.acp.controller.app.db.vo.AppHealthCheckRespVO;
import com.rs.module.acp.controller.app.db.vo.AppPersonalEffectsSubRespVO;
import com.rs.module.acp.controller.app.db.vo.kss.AppDetainRegKssBasicsRespVO;
import com.rs.module.acp.controller.app.db.vo.kss.AppDetainRegKssRespVO;
import com.rs.module.acp.entity.db.DetainRegKssDO;
import com.rs.module.acp.entity.db.HealthCheckDO;
import com.rs.module.acp.entity.db.InjuryAssessmentDO;
import com.rs.module.acp.entity.db.PersonalEffectsSubDO;
import com.rs.module.acp.service.db.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "移动端-管教业务-收押入所")
@RestController
@RequestMapping("/app/acp/db/detainRegKss")
@Validated
public class AppDetainRegKssController {

    @Resource
    private DetainRegKssService detainRegKssService;
    @Resource
    private HealthCheckService healthCheckService;
    @Resource
    private InjuryAssessmentService injuryAssessmentService;
    @Resource
    private BiometricInfoService biometricInfoService;
    @Resource
    private PersonalEffectsSubService personalEffectsSubService;


    @GetMapping("/getBasics")
    @ApiOperation(value = "查询收押入所基础信息")
    @Parameter(name = "id", description = "收押入所编号", required = true)
    public CommonResult<AppDetainRegKssBasicsRespVO> getBasics(@RequestParam("id") String id) {
        // 获取收押登记信息
        DetainRegKssDO detainRegKssDO = detainRegKssService.getById(id);
        // 获取健康检查信息
        HealthCheckDO healthCheck = healthCheckService.getHealthCheckByRybh(detainRegKssDO.getRybh());
        AppDetainRegKssBasicsRespVO respVO = BeanUtils.toBean(healthCheck, AppDetainRegKssBasicsRespVO.class);
        respVO.setId(detainRegKssDO.getId());
        respVO.setRslx(detainRegKssDO.getRslx());
        respVO.setAddTime(detainRegKssDO.getAddTime());
        return success(respVO);
    }

    @GetMapping("/getRegister")
    @ApiOperation(value = "查询收押入所登记信息")
    @Parameter(name = "id", description = "收押入所编号", required = true)
    public CommonResult<AppDetainRegKssRespVO> getRegister(@RequestParam("id") String id) {
        // 获取收押登记信息
        DetainRegKssDO detainRegKssDO = detainRegKssService.getById(id);
        AppDetainRegKssRespVO respVO = BeanUtils.toBean(detainRegKssDO, AppDetainRegKssRespVO.class);
        return success(respVO);
    }

    @GetMapping("/getCheck")
    @ApiOperation(value = "查询收押入所健康检查信息")
    @Parameter(name = "id", description = "收押入所编号", required = true)
    public CommonResult<AppHealthCheckRespVO> getCheck(@RequestParam("id") String id) {
        DetainRegKssDO detainRegKssDO = detainRegKssService.getById(id);
        // 获取收押登记信息
        HealthCheckDO healthCheck = healthCheckService.getHealthCheckByRybh(detainRegKssDO.getRybh());
        AppHealthCheckRespVO respVO = BeanUtils.toBean(healthCheck, AppHealthCheckRespVO.class);
        List<InjuryAssessmentDO> assessmentList = injuryAssessmentService.list(new LambdaQueryWrapper<InjuryAssessmentDO>()
                .eq(InjuryAssessmentDO::getRybh, detainRegKssDO.getRybh()));
        List<InjuryAssessmentSaveReqVO> assessmentRespVOList = BeanUtils.toBean(assessmentList, InjuryAssessmentSaveReqVO.class);
        respVO.setInjuryAssessmentList(assessmentRespVOList);
        return success(respVO);
    }

    @GetMapping("/getEffects")
    @ApiOperation(value = "查询收押入所物品信息")
    @Parameter(name = "id", description = "收押入所编号", required = true)
    public CommonResult<List<AppPersonalEffectsSubRespVO>> getEffects(@RequestParam("id") String id) {
        DetainRegKssDO detainRegKssDO = detainRegKssService.getById(id);
        // 获取收押登记信息
        List<PersonalEffectsSubDO> list = personalEffectsSubService.list(new LambdaQueryWrapper<PersonalEffectsSubDO>()
                .eq(PersonalEffectsSubDO::getRybh, detainRegKssDO.getRybh()));
        List<AppPersonalEffectsSubRespVO> respList = BeanUtils.toBean(list, AppPersonalEffectsSubRespVO.class);
        return success(respList);
    }

    @GetMapping("/getBiometric")
    @ApiOperation(value = "查询收押入所生物采集信息")
    @Parameter(name = "id", description = "收押入所编号", required = true)
    public CommonResult<AppBiometricRespVO> getBiometric(@RequestParam("id") String id) {
        DetainRegKssDO detainRegKssDO = detainRegKssService.getById(id);
        List<BiometricInfoRespVO> biometricList = biometricInfoService.getBiometricVOByRybh(detainRegKssDO.getRybh());

        AppBiometricRespVO respVO = BeanUtils.toBean(detainRegKssDO, AppBiometricRespVO.class);
        respVO.setBiometricList(biometricList);
        return success(respVO);
    }

    @PostMapping("/approval")
    @ApiOperation(value = "收押入所审批")
    public CommonResult<Boolean> approval(@Valid @RequestBody GjApproveReqVO approveReqVO) {
        detainRegKssService.approval(approveReqVO);
        return success(true);
    }

}
