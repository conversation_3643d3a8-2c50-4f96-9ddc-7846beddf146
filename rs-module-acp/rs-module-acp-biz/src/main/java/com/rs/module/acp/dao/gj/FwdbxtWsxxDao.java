package com.rs.module.acp.dao.gj;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.entity.gj.FwdbxtWsxxDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 实战平台-管教业务-法务待办协同关联文书信息
 *
 * <AUTHOR>
 */
@Mapper
public interface FwdbxtWsxxDao extends IBaseDao<FwdbxtWsxxDO> {

    default List<FwdbxtWsxxDO> selectListByFwdbxtId(String fwdbxtId) {
        return selectList(new LambdaQueryWrapperX<FwdbxtWsxxDO>().eq(FwdbxtWsxxDO::getFwdbxtId, fwdbxtId));
    }

    default int deleteByFwdbxtId(String fwdbxtId) {
        return delete(new LambdaQueryWrapperX<FwdbxtWsxxDO>().eq(FwdbxtWsxxDO::getFwdbxtId, fwdbxtId));
    }

}
