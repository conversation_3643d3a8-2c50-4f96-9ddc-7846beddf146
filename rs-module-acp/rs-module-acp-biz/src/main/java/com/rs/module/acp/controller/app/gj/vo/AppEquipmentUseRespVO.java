package com.rs.module.acp.controller.app.gj.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-械具使用 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppEquipmentUseRespVO extends BaseVO implements TransPojo {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("使用情形")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GJ_EQUIPMENT_SCENE")
    private String useSituation;

    @ApiModelProperty("使用天数")
    private String useDays;

    @ApiModelProperty("戒具类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GJ_EQUIPMENT_TYPE")
    private String punishmentToolType;

    @ApiModelProperty("使用械具的理由")
    private String useReason;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("执行情况")
    private String executeSituation;

    @ApiModelProperty("是否关联惩罚 1是0否")
    @Trans(type = TransType.DICTIONARY, key = "ZD_BOOLEAN_TYPE")
    private Integer isAssociatedPunishment;

    @ApiModelProperty("惩罚措施多个逗号分割")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJCFNR")
    private String punishmentMeasures;

    @ApiModelProperty("是否临时固定")
    private Integer isTempFixation;

    @ApiModelProperty("固定天数")
    private String fixationDays;

    @ApiModelProperty("使用呈批人姓名")
    private String addUserName;

    @ApiModelProperty("使用呈批时间")
    private Date addTime;

    @ApiModelProperty("实际开始时间")
    private Date actualStartTime;

    @ApiModelProperty("实际结束时间")
    private Date actualEndTime;

    @ApiModelProperty("戒具使用审批任务id")
    private String taskId;

    @ApiModelProperty("延长理由")
    private String extendReason;

    @ApiModelProperty("延长天数")
    private Integer extendDay;

    @ApiModelProperty("戒具延长审批任务id")
    private String extendTaskId;

    @ApiModelProperty("剩余时间")
    private String timeRemaining;

    @ApiModelProperty("提前解除理由")
    private String advanceRemoveReason;

    @ApiModelProperty("戒具提前解除审批任务id")
    private String advanceRemoveTaskId;


}
