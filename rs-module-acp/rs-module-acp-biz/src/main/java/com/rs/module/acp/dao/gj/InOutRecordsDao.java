package com.rs.module.acp.dao.gj;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.bsp.security.model.SessionUser;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsBusinessRespVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsPageReqVO;
import com.rs.module.acp.controller.app.gj.vo.inoutrecords.InOutStatisticDetailVO;
import com.rs.module.acp.controller.app.gj.vo.inoutrecords.PrisonerInOutBusinessTypeRespVO;
import com.rs.module.acp.entity.gj.InOutRecordsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
* 实战平台-管教业务-出入登记 Dao
*
* <AUTHOR>
*/
@Mapper
public interface InOutRecordsDao extends IBaseDao<InOutRecordsDO> {


    default PageResult<InOutRecordsDO> selectPage(InOutRecordsPageReqVO reqVO) {
        Page<InOutRecordsDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<InOutRecordsDO> wrapper = new LambdaQueryWrapperX<InOutRecordsDO>()
            .eqIfPresent(InOutRecordsDO::getDataSources, reqVO.getDataSources())
            .eqIfPresent(InOutRecordsDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(InOutRecordsDO::getInoutType, reqVO.getInoutType())
            .eqIfPresent(InOutRecordsDO::getRoomId, reqVO.getRoomId())
            .betweenIfPresent(InOutRecordsDO::getInoutTime, reqVO.getInoutTime())
            .eqIfPresent(InOutRecordsDO::getBusinessType, reqVO.getBusinessType())
            .eqIfPresent(InOutRecordsDO::getInoutReason, reqVO.getInoutReason())
            .eqIfPresent(InOutRecordsDO::getBusinessId, reqVO.getBusinessId())
            .eqIfPresent(InOutRecordsDO::getSupervisorPoliceSfzh, reqVO.getSupervisorPoliceSfzh())
            .eqIfPresent(InOutRecordsDO::getSupervisorPolice, reqVO.getSupervisorPolice())
            .eqIfPresent(InOutRecordsDO::getSupervisorFaceInfoPath, reqVO.getSupervisorFaceInfoPath())
            .eqIfPresent(InOutRecordsDO::getPrisonerFaceInfoPath, reqVO.getPrisonerFaceInfoPath())
            .eqIfPresent(InOutRecordsDO::getStatus, reqVO.getStatus())
            .eqIfPresent(InOutRecordsDO::getInspectionResult, reqVO.getInspectionResult())
            .eqIfPresent(InOutRecordsDO::getProhibitedItems, reqVO.getProhibitedItems())
            .eqIfPresent(InOutRecordsDO::getPhysicalExam, reqVO.getPhysicalExam())
            .eqIfPresent(InOutRecordsDO::getAbnormalSituations, reqVO.getAbnormalSituations())
            .eqIfPresent(InOutRecordsDO::getProhibitedItemsImgUrl, reqVO.getProhibitedItemsImgUrl())
            .eqIfPresent(InOutRecordsDO::getPhysicalExamImgUrl, reqVO.getPhysicalExamImgUrl())
            .eqIfPresent(InOutRecordsDO::getAbnormalSituationsImgUrl, reqVO.getAbnormalSituationsImgUrl())
            .eqIfPresent(InOutRecordsDO::getIsProhibitedItems, reqVO.getIsProhibitedItems())
            .betweenIfPresent(InOutRecordsDO::getInspectionTime, reqVO.getInspectionTime())
            .eqIfPresent(InOutRecordsDO::getInspectorSfzh, reqVO.getInspectorSfzh())
            .eqIfPresent(InOutRecordsDO::getInspector, reqVO.getInspector())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(InOutRecordsDO::getAddTime);
        }
        Page<InOutRecordsDO> inOutRecordsPage = selectPage(page, wrapper);
        return new PageResult<>(inOutRecordsPage.getRecords(), inOutRecordsPage.getTotal());
    }
    default List<InOutRecordsDO> selectList(InOutRecordsListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InOutRecordsDO>()
            .eqIfPresent(InOutRecordsDO::getDataSources, reqVO.getDataSources())
            .eqIfPresent(InOutRecordsDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(InOutRecordsDO::getInoutType, reqVO.getInoutType())
            .eqIfPresent(InOutRecordsDO::getRoomId, reqVO.getRoomId())
            .betweenIfPresent(InOutRecordsDO::getInoutTime, reqVO.getInoutTime())
            .eqIfPresent(InOutRecordsDO::getBusinessType, reqVO.getBusinessType())
            .eqIfPresent(InOutRecordsDO::getInoutReason, reqVO.getInoutReason())
            .eqIfPresent(InOutRecordsDO::getBusinessId, reqVO.getBusinessId())
            .eqIfPresent(InOutRecordsDO::getSupervisorPoliceSfzh, reqVO.getSupervisorPoliceSfzh())
            .eqIfPresent(InOutRecordsDO::getSupervisorPolice, reqVO.getSupervisorPolice())
            .eqIfPresent(InOutRecordsDO::getSupervisorFaceInfoPath, reqVO.getSupervisorFaceInfoPath())
            .eqIfPresent(InOutRecordsDO::getPrisonerFaceInfoPath, reqVO.getPrisonerFaceInfoPath())
            .eqIfPresent(InOutRecordsDO::getStatus, reqVO.getStatus())
            .eqIfPresent(InOutRecordsDO::getInspectionResult, reqVO.getInspectionResult())
            .eqIfPresent(InOutRecordsDO::getProhibitedItems, reqVO.getProhibitedItems())
            .eqIfPresent(InOutRecordsDO::getPhysicalExam, reqVO.getPhysicalExam())
            .eqIfPresent(InOutRecordsDO::getAbnormalSituations, reqVO.getAbnormalSituations())
            .eqIfPresent(InOutRecordsDO::getProhibitedItemsImgUrl, reqVO.getProhibitedItemsImgUrl())
            .eqIfPresent(InOutRecordsDO::getPhysicalExamImgUrl, reqVO.getPhysicalExamImgUrl())
            .eqIfPresent(InOutRecordsDO::getAbnormalSituationsImgUrl, reqVO.getAbnormalSituationsImgUrl())
            .eqIfPresent(InOutRecordsDO::getIsProhibitedItems, reqVO.getIsProhibitedItems())
            .betweenIfPresent(InOutRecordsDO::getInspectionTime, reqVO.getInspectionTime())
            .eqIfPresent(InOutRecordsDO::getInspectorSfzh, reqVO.getInspectorSfzh())
            .eqIfPresent(InOutRecordsDO::getInspector, reqVO.getInspector())
        .orderByDesc(InOutRecordsDO::getAddTime));    }


    List<InOutRecordsBusinessRespVO> getJgrmBusiness( @Param("jgrybm")String jgrybm,  @Param("startDate")String startDate,  @Param("endDate")String endDate,  @Param("user")SessionUser sessionUser);
    List<PrisonerInOutBusinessTypeRespVO> getPaddingRoomInOutRecords(@Param("roomId")String roomId);

    List<InOutStatisticDetailVO> selectBusinessTypeCount(@Param("roomId") String roomId,
                                                         @Param("startOfDay") LocalDateTime startOfDay,
                                                         @Param("endOfDay") LocalDateTime endOfDay);

    List<InOutStatisticDetailVO> selectBusinessTypeCountByOrgCode(@Param("orgCode") String orgCode,
                                                         @Param("inoutType") String inoutType,
                                                         @Param("startOfDay") LocalDateTime startOfDay,
                                                         @Param("endOfDay") LocalDateTime endOfDay);

}
