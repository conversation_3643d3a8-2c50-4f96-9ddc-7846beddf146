package com.rs.module.acp.controller.app.gj;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.app.gj.vo.tdajp.TdAjpInOutStatisticVO;
import com.rs.module.acp.controller.app.gj.vo.tdajp.TdAjpPrisonerInOutBusinessTypeRespVO;
import com.rs.module.acp.service.gj.InOutRecordsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @Date 2025/8/19 10:01
 */
@Api(tags = "实战平台-筒道安检屏")
@RestController
@RequestMapping("/app/acp/tdajp")
@Validated
public class AppTdajpController {

    @Resource
    private InOutRecordsService inOutRecordsService;

    @GetMapping("/statisticNum")
    @ApiOperation(value = "筒道安检屏-出入登记统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码", required = true),
            @ApiImplicitParam(name = "inoutType", value = "出入类型(01：出监, 02：入监)", required = true)
    })
    public CommonResult<List<TdAjpInOutStatisticVO>> getTdajpInOutStatisticNum(@RequestParam("orgCode") String orgCode,
                                                                               @RequestParam("inoutType") String inoutType) {
        return success(inOutRecordsService.getTdajpInOutStatisticNum(orgCode, inoutType));
    }

    //查询指定监室内所有人员待带出记录及出入事由(拼接)
    @GetMapping("/getTdajpPaddingInOutRecords")
    @ApiOperation(value = "筒道安检屏-人员列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "inoutType", value = "进出监室类型: 01 出监,02 入监", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "businessType", value = "带出事由代码", required = true, dataType = "String", paramType = "query"),
    })
    public CommonResult<List<TdAjpPrisonerInOutBusinessTypeRespVO>> getTdajpPaddingInOutRecords(@RequestParam String orgCode,
                                                                                                @RequestParam String inoutType,
                                                                                                @RequestParam String businessType) {
        return success(inOutRecordsService.getTdajpPaddingInOutRecords(orgCode, inoutType, businessType));
    }


}
