package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;
import javax.validation.constraints.NotEmpty;

import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.entity.db.DetainRegKssDO;
import com.rs.module.acp.entity.db.OutRecordKssDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-羁押业务-出所登记（看守所） Service 接口
 *
 * <AUTHOR>
 */
public interface OutRecordKssService extends IBaseService<OutRecordKssDO>{

    /**
     * 创建实战平台-羁押业务-出所登记（看守所）
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createOutRecordKss(@Valid OutRecordKssSaveReqVO createReqVO);

    /**
     * 更新实战平台-羁押业务-出所登记（看守所）
     *
     * @param updateReqVO 更新信息
     */
    void updateOutRecordKss(@Valid OutRecordKssSaveReqVO updateReqVO);

    void updateOutRecordKssByStatus(OutRecordKssSaveReqVO updateReqVO);

    /**
     * 删除实战平台-羁押业务-出所登记（看守所）
     *
     * @param id 编号
     */
    void deleteOutRecordKss(String id);

    /**
     * 获得实战平台-羁押业务-出所登记（看守所）
     *
     * @param id 编号
     * @return 实战平台-羁押业务-出所登记（看守所）
     */
    OutRecordKssDO getOutRecordKss(String id);

    /**
    * 获得实战平台-羁押业务-出所登记（看守所）分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-羁押业务-出所登记（看守所）分页
    */
    PageResult<OutRecordKssDO> getOutRecordKssPage(OutRecordKssPageReqVO pageReqVO);

    /**
    * 获得实战平台-羁押业务-出所登记（看守所）列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-羁押业务-出所登记（看守所）列表
    */
    List<OutRecordKssDO> getOutRecordKssList(OutRecordKssListReqVO listReqVO);


    OutRecordKssDO getByJgrybm(String jgrybm);

    OutRecordKssDO getPrisonerInfo(@NotEmpty(message = "监管人员编码不能为空") String jgrybm);

    InRecordStatusVO getInRecordStatus(String rybh);

    /**
     * 出所审批
     * @param approveReqVO
     */
    void approval(GjApproveReqVO approveReqVO);
}
