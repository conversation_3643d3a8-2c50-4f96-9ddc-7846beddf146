package com.rs.module.acp.api;

import cn.hutool.core.lang.tree.Tree;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.cache.base.StringRedisService;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.DateUtil;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.db.dto.CasePersonnelRespVO;
import com.rs.module.acp.controller.admin.gj.InOutRecordsApi;
import com.rs.module.acp.controller.admin.gj.dto.InOutStatisticDetailDTO;
import com.rs.module.acp.controller.admin.pm.LocalsenseTagApi;
import com.rs.module.acp.controller.admin.pm.dto.RoomDetailDTO;
import com.rs.module.acp.controller.admin.pm.dto.TagTrackDTO;
import com.rs.module.acp.controller.admin.pm.vo.LocalsenseTagPersonRespVO;
import com.rs.module.acp.controller.app.gj.vo.inoutrecords.InOutStatisticDetailVO;
import com.rs.module.acp.dao.gj.InOutRecordsDao;
import com.rs.module.acp.entity.pm.LocalsenseTagPersonDO;
import com.rs.module.acp.service.pm.LocalsenseTagPersonService;
import com.rs.module.acp.util.QingyanLocalSenseUtil;
import com.rs.module.acp.util.QingyanLocalsenseHttpUtil;
import com.rs.module.base.controller.admin.pm.vo.AreaListReqVO;
import com.rs.module.base.service.pm.AreaService;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.rs.framework.common.pojo.CommonResult.success;

/**
 * RPC服务-API接口实现类，提供RESTful API接口，给Feign调用
 *
 */
@RestController
@Validated
@Api(tags = "出入登记 - api服务")
public class InOutRecordsApiImpl implements InOutRecordsApi {
    private static final Logger log = LoggerFactory.getLogger(InOutRecordsApiImpl.class);

    @Resource
    private InOutRecordsDao inOutRecordsDao;

    @Override
    public List<InOutStatisticDetailDTO> selectBusinessTypeCount(String roomId) {
        List<InOutStatisticDetailVO> resultMaps = inOutRecordsDao.selectBusinessTypeCount(roomId, null, null);
        return BeanUtils.toBean(resultMaps, InOutStatisticDetailDTO.class);
    }
}
