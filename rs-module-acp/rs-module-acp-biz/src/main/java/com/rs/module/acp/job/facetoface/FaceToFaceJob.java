package com.rs.module.acp.job.facetoface;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.module.acp.cons.CommonConstants;
import com.rs.module.acp.job.facetoface.bo.FaceToFaceBO;
import com.rs.module.acp.service.gj.face2face.FaceToFaceService;
import com.rs.module.base.entity.pm.PrisonRoomWarderDO;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;

@Component
@Slf4j
public class FaceToFaceJob {

    private static SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日");

    @Autowired
    private FaceToFaceService faceToFaceService;

    // 公安民警需要每日进入监室进行面对面登记至少2次，如未开展两下监室业务，则需要主动提醒，超期未登记的推送主管中队民警。
    // XXX监室XXX【日期】未进行面对面管理登记，请尽快进行登记！
    // 对应监室主协管民警
    // 每日下午15点
    @XxlJob("faceToFaceRemindJob")
    public void handleFaceToFace() {
        String excludeOrgCode = "";
        try {
            excludeOrgCode = BspDbUtil.getParam("FACETOFACE_EXCLUDE_ORGCODE");
            if (StringUtils.isEmpty(excludeOrgCode)) {
                excludeOrgCode = XxlJobHelper.getJobParam();
            }
        } catch (Exception e) {
            log.error("获取参数FACETOFACE_EXCLUDE_ORGCODE异常：", e);
        }
        log.info("面对面消息提醒，需要排除的机构:{}", excludeOrgCode);
        // 需要排除的org
        List<String> excludeListOrg = StringUtils.isNotEmpty(excludeOrgCode) ? Arrays.asList(excludeOrgCode.split(",")) : null;
        List<FaceToFaceBO> list = faceToFaceService.getNeedRemindRoomId(excludeListOrg);
        Map<String, List<ReceiveUser>> listMap = new HashMap<>();
        List<FaceToFaceBO> listBo = new ArrayList<>();
        for (FaceToFaceBO faceToFaceBO : list) {
            List<ReceiveUser> receiveUsers = listMap.get(faceToFaceBO.getRoomId());
            if (CollectionUtil.isEmpty(receiveUsers)) {
                receiveUsers = new ArrayList<>();
                listMap.put(faceToFaceBO.getRoomId(), receiveUsers);
                listBo.add(faceToFaceBO);
            }
            receiveUsers.add(new ReceiveUser(faceToFaceBO.getPoliceSfzh(), faceToFaceBO.getOrgCode()));
        }
        for (FaceToFaceBO faceToFaceBO : listBo) {
            try {
                List<ReceiveUser> receiveUsers = listMap.get(faceToFaceBO.getRoomId());
                if (CollectionUtil.isNotEmpty(receiveUsers)) {
                    String title = String.format("%s监室%s未进行面对面管理登记，请尽快进行登记！",
                            faceToFaceBO.getRoomName(), simpleDateFormat.format(new Date()));
                    // todo 跳转链接
                    String url = "/#/discipline/faceTofaceManage/faceToface";
                    SendMessageUtil.sendTodoMsg(title, title, url, HttpUtils.getAppCode(), null, "系统",
                            faceToFaceBO.getOrgCode(), faceToFaceBO.getOrgName(), null, IdUtil.fastSimpleUUID(),
                            "pc", faceToFaceBO.getRoomId(), receiveUsers, MsgBusTypeEnum.GJ_MDMDJ.getCode(), null);
                }
            } catch (Exception e) {
                log.error("monthAssmtJds:", e);
            }
        }

    }

}
