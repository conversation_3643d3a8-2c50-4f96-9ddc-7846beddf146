package com.rs.module.acp.controller.app.gj.vo.tdajp;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "筒道安检屏 - 所有在所人员出入事由列表 Response VO")
@Data
public class TdAjpPrisonerInOutBusinessTypeRespVO implements TransPojo {

    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("正面照片")
    private String frontPhoto;
    @ApiModelProperty("人员状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_RYZT")
    private String ryzt;
    @ApiModelProperty("监室号")
    private String jsh;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("姓名")
    private String xm;
    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XB")
    private String xb;
    @ApiModelProperty("出入记录")
    private List<InOutRecordsRespVO> inOutRecordsRespVOList;
    @ApiModelProperty("出入事由")
    private String businessTypeName;


}
