package com.rs.module.acp.controller.app.zh;

import com.bsp.common.util.DateUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.controller.admin.zh.vo.staffduty.PostSynergismRespVO;
import com.rs.module.base.enums.StaffDutyTypeEnum;
import com.rs.module.base.service.zh.staffduty.StaffDutyRecordService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@Api(value = "仓内外屏-综合管理-值班安排", tags = "仓内外屏-综合管理-值班安排")
@Slf4j
@RequestMapping("/app/acp/zh/staffDutyRecord")
@RestController
@Validated
public class AppStaffDutyRecordController {

    @Autowired
    private StaffDutyRecordService staffDutyRecordService;
    @GetMapping("/dutyPostSynergism")
    @ApiOperation("值班岗位协同")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "orgCode", value = "监所编码，不传默认当前登录用户监所编码", required = false),
            @ApiImplicitParam(name = "dutyDate", dataType = "String", value = "不传默认当天", required = false)
    })
    public CommonResult<List<PostSynergismRespVO>> dutyPostSynergism(@RequestParam(required = false) String orgCode,
                                                                     @RequestParam(required = false) String dutyDate) {
        try {
            //如果日期为空则获取当前日期
            if (StringUtil.isEmpty(dutyDate)) {
                dutyDate = DateUtil.getCurrentDate();
            }
            if (StringUtil.isEmpty(orgCode)) {
                orgCode = SessionUserUtil.getSessionUser().getOrgCode();
            }
            return CommonResult.success(staffDutyRecordService.dutyPostSynergism(dutyDate,orgCode, StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode()));
        } catch (Exception e) {
            return CommonResult.error(e.getMessage());
        }
    }
}
