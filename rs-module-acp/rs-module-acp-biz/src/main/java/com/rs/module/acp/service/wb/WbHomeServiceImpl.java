package com.rs.module.acp.service.wb;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.dao.wb.WbHomeDao;
import com.rs.module.base.controller.admin.pm.vo.AreaInfoRespVO;
import com.rs.module.base.service.pm.AreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;

@Service
@Validated
public class WbHomeServiceImpl implements WbHomeService {

    @Resource
    private WbHomeDao wbHomeDao;

    @Autowired
    private AreaService areaService;

    @Autowired
    private WbCommonService wbCommonService;

    @Override
    public List<JSONObject> getToDayMeetingUpdates() {
        return wbHomeDao.getToDayMeetingUpdates(SessionUserUtil.getSessionUser().getOrgCode());
    }

    @Override
    public JSONObject getMeetingRoomStatus() {

        //TODO 暂时无法判断审讯室、律师会见室、家属会见室的 暂停使用状态，咨询邱文新后：先保留在上面，后面会在【区域管理】里面做这块的配置
        /**
         *  status:状态
         *  0：空闲。1：使用中，2：暂停使用
         */
        JSONObject resJson = new JSONObject();
        //获取审讯室情况
        resJson.put("interrogationRoomSituation",getInterrogationRoomSituation());

        //获取律师会见室情况
        resJson.put("lawyerMeetingRoomSituation",getLawyerMeetingRoomSituation());

        //获取家属会见室情况
        resJson.put("familyRoomSituation",getFamilyRoomSituation());
        return resJson;
    }

    private JSONObject getInterrogationRoomSituation(){
        //获取审讯室情况
        JSONObject roomSituation = new JSONObject();
        int idleCount = 0;
        int inuseCount = 0;
        int pauseCount = 0;
        List<JSONObject> interrogationRoomList = wbCommonService.getIdleInterrogationRoom(true);
        for (JSONObject tria : interrogationRoomList) {
            if(tria.getBoolean("idle")){
                tria.put("status","0");
                tria.put("statusName","空闲");
                idleCount+=1;
            }else {
                tria.put("status","1");
                tria.put("statusName","使用中");
                inuseCount+=1;
            }
        }
        roomSituation.put("idleCount",idleCount);
        roomSituation.put("inuseCount",inuseCount);
        roomSituation.put("pauseCount",pauseCount);
        roomSituation.put("roomList",interrogationRoomList);
        roomSituation.put("roomType","提讯/询室");
        return roomSituation;
    }

    private JSONObject getLawyerMeetingRoomSituation(){
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        List<AreaInfoRespVO> areaList = areaService.getAreaByOrgCode(sessionUser.getOrgCode(), "0016");

        List<JSONObject> meetingList = wbHomeDao.getLawyerMeetingList();

        //如果查看的时间与 律师会见室的时间有重叠，则认为律师会见被占用
        Map<String, String> roomMap = new HashMap<>();
        for (JSONObject meeting : meetingList) {
            roomMap.put(meeting.getString("room_id"), meeting.getString("room_id"));
        }

        JSONObject roomSituation = new JSONObject();
        List<JSONObject> lawyerMeetingRoomList = new ArrayList<>();
        int idleCount = 0;
        int inuseCount = 0;
        int pauseCount = 0;
        for(AreaInfoRespVO area:areaList){
            JSONObject temp = new JSONObject();
            temp.put("roomId",area.getAreaCode());
            temp.put("roomName",area.getAreaName());
            if(roomMap.containsKey(area.getAreaCode())){
                temp.put("status","1");
                temp.put("statusName","使用中");
                inuseCount+=1;
            }else {
                temp.put("status","0");
                temp.put("statusName","空闲");
                idleCount+=1;
            }
            lawyerMeetingRoomList.add(temp);
        }

        roomSituation.put("idleCount",idleCount);
        roomSituation.put("inuseCount",inuseCount);
        roomSituation.put("pauseCount",pauseCount);
        roomSituation.put("roomList",lawyerMeetingRoomList);
        roomSituation.put("roomType","律师会见室");
        return roomSituation;
    }

    private JSONObject getFamilyRoomSituation(){
        //获取家属会见情况
        JSONObject roomSituation = new JSONObject();
        int idleCount = 0;
        int inuseCount = 0;
        int pauseCount = 0;
        List<JSONObject> familyRoomList = wbCommonService.getIdleFamilyRoom(true);
        for (JSONObject tria : familyRoomList) {
            if(tria.getBoolean("idle")){
                tria.put("status","0");
                tria.put("statusName","空闲");
                idleCount+=1;
            }else {
                tria.put("status","1");
                tria.put("statusName","使用中");
                inuseCount+=1;
            }
        }
        roomSituation.put("idleCount",idleCount);
        roomSituation.put("inuseCount",inuseCount);
        roomSituation.put("pauseCount",pauseCount);
        roomSituation.put("roomList",familyRoomList);
        roomSituation.put("roomType","家属会见室");
        return roomSituation;
    }

    @Override
    public List<JSONObject> getMeetingMumStatistics() {

        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        //查询会见统计的 待提出、待提回 的数量
        JSONObject withdrawalStatusStatistics = wbHomeDao.getWithdrawalStatusStatistics(sessionUser.getOrgCode());
        //查询提讯/询、提解、律师会见、家属会见、使馆领事会见的今日会见的统计数量
        List<JSONObject> meetingStatistics = wbHomeDao.getMeetingMumStatistics(sessionUser.getOrgCode());

        //整合
        List<JSONObject> resList = new ArrayList<>();
        //待提出
        JSONObject dtc = new JSONObject();
        dtc.put("businessname","待提出");
        dtc.put("business","dtc");
        dtc.put("num", ObjectUtil.isNotEmpty(withdrawalStatusStatistics)?withdrawalStatusStatistics.getInteger("dtc"):0);
        resList.add(dtc);

        //待提回
        JSONObject dth = new JSONObject();
        dth.put("businessname","待提回");
        dth.put("business","dth");
        dth.put("num",ObjectUtil.isNotEmpty(withdrawalStatusStatistics)?withdrawalStatusStatistics.getInteger("dth"):0);
        resList.add(dth);
        resList.addAll(meetingStatistics);
        return resList;
    }

    @Override
    public List<JSONObject> getToDayMeetingList(String tczt,String ywlx) {
        return wbHomeDao.getToDayMeetingList(SessionUserUtil.getSessionUser().getOrgCode(),tczt,ywlx);
    }
}
