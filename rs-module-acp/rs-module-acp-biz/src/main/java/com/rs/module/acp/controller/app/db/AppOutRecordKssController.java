package com.rs.module.acp.controller.app.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.db.vo.BiometricInfoRespVO;
import com.rs.module.acp.controller.admin.db.vo.InjuryAssessmentSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.app.db.vo.AppBiometricRespVO;
import com.rs.module.acp.controller.app.db.vo.AppHealthCheckRespVO;
import com.rs.module.acp.controller.app.db.vo.AppPersonalEffectsSubRespVO;
import com.rs.module.acp.controller.app.db.vo.kss.AppDetainRegKssBasicsRespVO;
import com.rs.module.acp.controller.app.db.vo.kss.AppDetainRegKssRespVO;
import com.rs.module.acp.controller.app.db.vo.kss.AppOutRecordKssBasicsRespVO;
import com.rs.module.acp.controller.app.db.vo.kss.AppOutRecordKssRespVO;
import com.rs.module.acp.entity.db.*;
import com.rs.module.acp.service.db.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "移动端-管教业务-出所")
@RestController
@RequestMapping("/app/acp/db/outRecordKss")
@Validated
public class AppOutRecordKssController {

    @Resource
    private OutRecordKssService outRecordKssService;
    @Resource
    private OutPersonalEffectsSubService outPersonalEffectsSubService;


    @GetMapping("/getBasics")
    @ApiOperation(value = "查询出所登记基础信息")
    @Parameter(name = "id", description = "收押入所编号", required = true)
    public CommonResult<AppOutRecordKssBasicsRespVO> getBasics(@RequestParam("id") String id) {
        OutRecordKssDO recordKssDO = outRecordKssService.getById(id);
        AppOutRecordKssBasicsRespVO respVO = BeanUtils.toBean(recordKssDO, AppOutRecordKssBasicsRespVO.class);
        return success(respVO);
    }

    @GetMapping("/getDetail")
    @ApiOperation(value = "查询出所登记详情")
    @Parameter(name = "id", description = "收押入所编号", required = true)
    public CommonResult<AppOutRecordKssRespVO> getRegister(@RequestParam("id") String id) {
        // 获取出所登记信息
        OutRecordKssDO recordKssDO = outRecordKssService.getById(id);
        AppOutRecordKssRespVO respVO = BeanUtils.toBean(recordKssDO, AppOutRecordKssRespVO.class);
        // 获取出所
        List<OutPersonalEffectsSubDO> list = outPersonalEffectsSubService.list(new LambdaQueryWrapper<OutPersonalEffectsSubDO>()
                .eq(OutPersonalEffectsSubDO::getJgrybm, recordKssDO.getJgrybm()));
        respVO.setEffectsList(BeanUtils.toBean(list, AppPersonalEffectsSubRespVO.class));
        return success(respVO);
    }


    @PostMapping("/approval")
    @ApiOperation(value = "出所审批")
    public CommonResult<Boolean> approval(@Valid @RequestBody GjApproveReqVO approveReqVO) {
        outRecordKssService.approval(approveReqVO);
        return success(true);
    }

}
