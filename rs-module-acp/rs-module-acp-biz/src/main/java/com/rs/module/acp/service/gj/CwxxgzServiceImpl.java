package com.rs.module.acp.service.gj;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.CwxxgzSaveReqVO;
import com.rs.module.acp.dao.gj.CwxxgzDao;
import com.rs.module.acp.entity.gj.CwxxgzDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 实战平台-监管管理-错误信息更正 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CwxxgzServiceImpl extends BaseServiceImpl<CwxxgzDao, CwxxgzDO> implements CwxxgzService {

    @Resource
    private CwxxgzDao cwxxgzDao;

    @Override
    public String createCwxxgz(CwxxgzSaveReqVO createReqVO) {
        // 插入
        CwxxgzDO cwxxgz = BeanUtils.toBean(createReqVO, CwxxgzDO.class);
        cwxxgz.setType("01");
        cwxxgz.setStatus("02");
        cwxxgzDao.insert(cwxxgz);
        // 返回
        return cwxxgz.getId();
    }

    @Override
    public void updateCwxxgz(CwxxgzSaveReqVO updateReqVO) {
        // 校验存在
        validateCwxxgzExists(updateReqVO.getId());
        // 更新
        CwxxgzDO updateObj = BeanUtils.toBean(updateReqVO, CwxxgzDO.class);
        cwxxgzDao.updateById(updateObj);
    }

    @Override
    public void deleteCwxxgz(String id) {
        // 校验存在
        validateCwxxgzExists(id);
        // 删除
        cwxxgzDao.deleteById(id);
    }

    private void validateCwxxgzExists(String id) {
        if (cwxxgzDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-错误信息更正数据不存在");
        }
    }

    @Override
    public CwxxgzDO getCwxxgz(String id) {
        return cwxxgzDao.selectById(id);
    }

}
