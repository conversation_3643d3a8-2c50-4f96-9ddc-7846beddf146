package com.rs.module.acp.controller.admin.db;

import com.gosun.zhjg.prison.room.terminal.modules.inscreen.feign.CnpFaceFeign;
import com.gosun.zhjg.prison.room.terminal.modules.socket.feign.CnpSocketFeign;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.db.vo.CsfwfRespVO;
import com.rs.module.acp.controller.admin.db.vo.CsfwfSaveReqVO;
import com.rs.module.acp.entity.db.CsfwfDO;
import com.rs.module.acp.service.db.CsfwfService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-收押业务-出所防误放")
@RestController
@RequestMapping("/acp/db/csfwf")
@Validated
public class CsfwfController {

    @Resource
    private CsfwfService csfwfService;
    @Resource
    private CnpFaceFeign cnpFaceFeign;
    @Resource
    private CnpSocketFeign cnpSocketFeign;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-收押业务-出所防误放")
    public CommonResult<String> createCsfwf(@Valid @RequestBody CsfwfSaveReqVO createReqVO) {
        return success(csfwfService.createCsfwf(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-收押业务-出所防误放")
    public CommonResult<Boolean> updateCsfwf(@Valid @RequestBody CsfwfSaveReqVO updateReqVO) {
        csfwfService.updateCsfwf(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-收押业务-出所防误放")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteCsfwf(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            csfwfService.deleteCsfwf(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-收押业务-出所防误放")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<CsfwfRespVO> getCsfwf(@RequestParam("id") String id) {
        CsfwfDO csfwf = csfwfService.getCsfwf(id);
        return success(BeanUtils.toBean(csfwf, CsfwfRespVO.class));
    }

    @GetMapping("/getCsfwfByBusinessId")
    @ApiOperation(value = "获得实战平台-收押业务-出所防误放， 通过关联业务ID")
    @ApiImplicitParam(name = "businessId", value = "关联业务ID")
    public CommonResult<CsfwfRespVO> getCsfwfByBusinessId(@RequestParam("businessId") String businessId) {
        CsfwfDO csfwf = csfwfService.getCsfwfByBusinessId(businessId);
        return success(BeanUtils.toBean(csfwf, CsfwfRespVO.class));
    }

    @PostMapping("/createKssCsFwf")
    @ApiOperation(value = "创建实战平台-收押业务-出所防误放")
    public CommonResult<String> createKssCsFwf(@RequestParam("jgrybm") String jgrybm,
    		@RequestParam("businessId") String businessId,
    		@RequestParam("businessType") String businessType,
    		@RequestParam("businessReason") String businessReason) {
        return success(csfwfService.createKssCsFwf(jgrybm, businessId, businessType, businessReason));
    }


}
