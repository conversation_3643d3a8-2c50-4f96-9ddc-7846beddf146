package com.rs.module.acp.job.zh;

import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;

import javax.annotation.Resource;

import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.cons.CommonConstants;
import com.bsp.common.util.StringUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.dto.OrgRespDTO;
import com.rs.module.acp.entity.zh.ReportJschmrqkDO;
import com.rs.module.acp.service.zh.ReportJschmrqkService;
import com.rs.util.DicUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 监所晨会每日情况Job
 * <AUTHOR>
 * @date 2025年8月15日
 */
@Component
public class ReportJschmrqkJob {
	
	@Resource
    private ReportJschmrqkService reportJschmrqkService;
	
	@Resource
    private FileStorageService fileStorageService;
	
	@Resource
	BspApi bspApi;
	
	@Value("${conf.report.mrchOrgCode:}")
	private String mrchOrgCode;
	
	@Value("${conf.ext.baseUrl:}")
	private String baseUrl;

	@XxlJob("reportJschmrqkTask")
    public void reportJschmrqkTask() {
		XxlJobHelper.log("监所晨会每日情况报告生成任务：-----开始-------");
		
		try {
			if(StringUtil.isNotEmpty(mrchOrgCode) && StringUtil.isNotEmpty(baseUrl)) {
				Date reportDate = new Date();
				String[] orgCodes = StringUtil.splitString(mrchOrgCode, CommonConstants.DEFAULT_SPLIT_STR);
				for(String orgCode : orgCodes) {
					ReportJschmrqkDO reportJschmrqk = reportJschmrqkService.getByReportDate(orgCode, reportDate);
					
					//机构代码与系统映射
					String appCode = DicUtils.translate("ZD_APP_CODE_REF", orgCode);
					if(StringUtil.isEmpty(appCode)) {
						continue;
					}
					
					//不存在报告时创建报告
					if(reportJschmrqk == null) {
						OrgRespDTO org = bspApi.getOrgByCode(orgCode);
						if(org != null) {
							reportJschmrqk = new ReportJschmrqkDO();
							reportJschmrqk.setId(StringUtil.getGuid32());
							reportJschmrqk.setOrgCode(orgCode);
							reportJschmrqk.setOrgName(org.getName());
							reportJschmrqk.setCityCode(org.getCityId());
							reportJschmrqk.setCityName(org.getCityName());
							reportJschmrqk.setRegCode(org.getRegionId());
							reportJschmrqk.setRegName(org.getRegionName());
							reportJschmrqk.setStatus("0");
							reportJschmrqk.setReportDate(reportDate);
							
							//保存报告
							reportJschmrqkService.save(reportJschmrqk);
						}
					}
					
					//构建业务数据
	    			JSONObject formData = reportJschmrqkService.buildFormData(reportJschmrqk);
	    			
	    			//初始化流
	    			OutputStream os = null;
	    			OutputStreamWriter osw = null;
	    			ByteArrayOutputStream baos = null;
	    			InputStream is = null;
	    			
	    			try {
	    				//请求生成word返回数据流
		    			String url = baseUrl + "/com/form/report/get";
		    			
		    			//初始化http连接
		    			HttpURLConnection conn = (HttpURLConnection)new URL(url).openConnection();
		    			conn.setRequestMethod("POST");
		    			conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
		    			conn.setRequestProperty("Accept", "application/octet-stream");
		    			conn.setDoOutput(true);		
		    			
		    			//初始化post参数
		    			StringBuilder postParams = new StringBuilder();
		    			postParams.append("mark=" + URLEncoder.encode(appCode + ":jschmrqk", StandardCharsets.UTF_8.name()));
		    			postParams.append("&businessData=" + URLEncoder.encode(formData.toJSONString(), StandardCharsets.UTF_8.name()));
		    			postParams.append("&isPdf=false" + "");
		    			postParams.append("&access_token=" + bspApi.getTempToken());
		    			
		    			//写入post参数
		    			try(OutputStream oos = conn.getOutputStream()) {
		    				byte[] input = postParams.toString().getBytes(StandardCharsets.UTF_8);
		    				oos.write(input, 0, input.length);
		    			}
		    			
		    			//获取word文件流
		    			int reponseCode = conn.getResponseCode();
		    			if(reponseCode == HttpURLConnection.HTTP_OK) {
		    				baos = new ByteArrayOutputStream();
			    			is = conn.getInputStream();
			    			byte[] buffer = new byte[1024];
			    			int len;
			    			while((len = is.read(buffer)) != -1) {
			    				baos.write(buffer, 0, len);
			    			}
		    			}
		    			
		    			//上传Word
		    			byte[] bytes = baos.toByteArray();
		    			String fileId = StringUtil.getGuid32();
		    			String fileName = fileId + ".docx";
	        			FileInfo fileInfo = fileStorageService.of(bytes)
	        					.setSaveFilename(fileName)
	        					.setObjectId(fileId)
	        					.upload();
	        			
	        			//更新报告
	        			reportJschmrqk.setStatus("1");
	        			reportJschmrqk.setWordUrl(fileInfo.getUrl());
	        			reportJschmrqkService.updateById(reportJschmrqk);
	    			}
	    			catch(Exception e) {
	    				e.printStackTrace();
	    			}
	    			finally {
	    				closeStream(baos, is, osw, os);
	    			}
				}
			}
		}
		catch(Exception e) {
			e.printStackTrace();
		}
		
		XxlJobHelper.log("监所晨会每日情况报告生成任务：-----结束-------");
	}
	
	/**
	 * 关闭流资源
	 * @param <T> ios T 流资源动态数组
	 * <AUTHOR>
	 * @date 2025年8月7日
	 */
    @SuppressWarnings("unchecked")
    private <T extends Object> void closeStream(T... ios) {
        for (Object temp : ios) {
            try {
                if (null != temp) {
                    ((Closeable) temp).close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
