package com.rs.module.acp.service.gj.diagnosiassmtjds;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.fhs.core.trans.util.ReflectUtils;
import com.fhs.trans.service.impl.TransService;
import com.gosun.zhjg.common.util.StringUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.entity.OpsDicCode;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.framework.mybatis.util.DicUtil;
import com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds.*;
import com.rs.module.acp.controller.admin.gj.vo.printdocument.PrintDocumentSaveReqVO;
import com.rs.module.acp.dao.gj.MonthlyAssmtDocumentJdsDao;
import com.rs.module.acp.entity.gj.MonthlyAssmtDocumentJdsDO;
import com.rs.module.acp.entity.gj.MonthlyAssmtJdsDO;
import com.rs.module.acp.enums.gj.DiagnosisAssmtJdsPeriodEnum;
import com.rs.module.acp.enums.gj.DiagnosisAssmtJdsStatusEnum;
import com.rs.module.acp.enums.gj.DiagnosisAssmtJdsTypeEnum;
import com.rs.module.acp.enums.gj.PrintDocumentTypeEnum;
import com.rs.module.acp.service.gj.diagnosiassmtjds.bo.AssmtCommonBO;
import com.rs.module.acp.service.gj.printdocument.PrintDocumentService;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.BspApprovalUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import com.rs.module.acp.entity.gj.DiagnosisAssmtJdsDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.DiagnosisAssmtJdsDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-诊断评估(戒毒所) Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DiagnosisAssmtJdsServiceImpl extends BaseServiceImpl<DiagnosisAssmtJdsDao, DiagnosisAssmtJdsDO> implements DiagnosisAssmtJdsService {

    @Resource
    private DiagnosisAssmtJdsDao diagnosisAssmtJdsDao;

    @Resource
    private MonthlyAssmtJdsService monthlyAssmtJdsService;

    @Resource
    private PrintDocumentService printDocumentService;

    @Resource
    private MonthlyAssmtDocumentJdsDao monthlyAssmtDocumentJdsDao;

    private static final String defKey = "zhenduanpinggushenpi";

    private static final String msgUrl = "/#/diagnosticEvaluation/disciplineMedical?id=";

    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public String createDiagnosisAssmtJds(DiagnosisAssmtJdsSaveReqVO createReqVO) {
        // 插入
        DiagnosisAssmtJdsDO diagnosisAssmtJds = BeanUtils.toBean(createReqVO, DiagnosisAssmtJdsDO.class);
        diagnosisAssmtJdsDao.insert(diagnosisAssmtJds);
        // 返回
        return diagnosisAssmtJds.getId();
    }

    @Override
    public void updateDiagnosisAssmtJds(DiagnosisAssmtJdsSaveReqVO updateReqVO) {
        // 校验存在
        validateDiagnosisAssmtJdsExists(updateReqVO.getId());
        // 更新
        DiagnosisAssmtJdsDO updateObj = BeanUtils.toBean(updateReqVO, DiagnosisAssmtJdsDO.class);
        diagnosisAssmtJdsDao.updateById(updateObj);
    }

    @Override
    public void deleteDiagnosisAssmtJds(String id) {
        // 校验存在
        validateDiagnosisAssmtJdsExists(id);
        // 删除
        diagnosisAssmtJdsDao.deleteById(id);
    }

    private void validateDiagnosisAssmtJdsExists(String id) {
        if (diagnosisAssmtJdsDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-诊断评估(戒毒所)数据不存在");
        }
    }

    @Override
    public DiagnosisAssmtJdsRespVO getDiagnosisAssmtJds(String id) {
        DiagnosisAssmtJdsDO assmtJdsDO = diagnosisAssmtJdsDao.selectById(id);
        DiagnosisAssmtJdsRespVO diagnosisAssmtJdsRespVO = BeanUtils.toBean(assmtJdsDO, DiagnosisAssmtJdsRespVO.class);
        setDiagnosisAssmtJdsRespVOAssmtResultValue(diagnosisAssmtJdsRespVO);
        return diagnosisAssmtJdsRespVO;
    }

    @Override
    public PageResult<DiagnosisAssmtJdsDO> getDiagnosisAssmtJdsPage(DiagnosisAssmtJdsPageReqVO pageReqVO) {
        return diagnosisAssmtJdsDao.selectPage(pageReqVO);
    }

    @Override
    public List<DiagnosisAssmtJdsRespVO> getDiagnosisAssmtJdsList(DiagnosisAssmtJdsListReqVO listReqVO) {
        List<DiagnosisAssmtJdsDO> list = diagnosisAssmtJdsDao.selectList(listReqVO);
        List<DiagnosisAssmtJdsRespVO> assmtJdsRespVOS = BeanUtils.toBean(list, DiagnosisAssmtJdsRespVO.class);
        if(CollectionUtil.isNotEmpty(assmtJdsRespVOS)){
            for (DiagnosisAssmtJdsRespVO assmtJdsRespVO : assmtJdsRespVOS) {
                setDiagnosisAssmtJdsRespVOAssmtResultValue(assmtJdsRespVO);
            }
        }
        return assmtJdsRespVOS;
    }

    @Override
    public List<DiagnosisAssmtJdsDO> handleDiagnosisAssmtJds(String periodType, String assmtType, String sqlTime) {
        return diagnosisAssmtJdsDao.handleDiagnosisAssmtJds(periodType, assmtType, sqlTime);
    }

    @Override
    public Map<String, List<AssmtCommonBO>> getModelByAssmtType(String assmtType, String jgrybm) {
        Map<String, List<AssmtCommonBO>> map = new HashMap<>();
        List<AssmtCommonBO> pgnrList = new ArrayList<>();
        List<AssmtCommonBO> pgjgList = new ArrayList<>();
        map.put("pgnr", pgnrList);
        map.put("pgjg", pgjgList);
        if (StringUtils.equals(DiagnosisAssmtJdsTypeEnum.SLTD.getCode(), assmtType)) {
            pgnrList.add(new AssmtCommonBO("assmtContentValue1", "毒品检测结果呈阴性", null));
            pgnrList.add(new AssmtCommonBO("assmtContentValue2", "停止使用控制或缓解戒断症状药物", null));
            pgnrList.add(new AssmtCommonBO("assmtContentValue3", "急性戒断症状完全消除", null));
            pgnrList.add(new AssmtCommonBO("assmtContentValue4", "未出现明显稽延性戒断症状", null));
            pgnrList.add(new AssmtCommonBO("assmtContentValue5", "未出现因吸毒导致的明显精神症状或者原有精神障碍得到有效控制", null));

            pgjgList.add(new AssmtCommonBO("assmtResultValue1", "生理脱毒阶段性评价意见", null));
            pgjgList.add(new AssmtCommonBO("assmtResultValue2", "一年后生理脱毒评估结果", null));
            pgjgList.add(new AssmtCommonBO("assmtResultValue3", "二年期满前生理脱毒评估结果", null));
        } else if (StringUtils.equals(DiagnosisAssmtJdsTypeEnum.SXKF.getCode(), assmtType)) {
            pgnrList.add(new AssmtCommonBO("assmtContentValue1", "身体机能有所改善", null));
            pgnrList.add(new AssmtCommonBO("assmtContentValue2", "体能测试有所提高", null));
            pgnrList.add(new AssmtCommonBO("assmtContentValue3", "戒毒动机明确，信心增强，掌握防止复吸的方法", null));
            pgnrList.add(new AssmtCommonBO("assmtContentValue4", "未出现严重心理问题或者精神症状", null));
            pgnrList.add(new AssmtCommonBO("assmtContentValue5", "有改善家庭与社会关系的愿望和行动", null));

            pgjgList.add(new AssmtCommonBO("assmtResultValue1", "一年后身心康复评估结果", null));
            pgjgList.add(new AssmtCommonBO("assmtResultValue2", "二年期满前身心康复评估结果", null));
        } else if (StringUtils.equals(DiagnosisAssmtJdsTypeEnum.SHHJYSYNL.getCode(), assmtType)) {
            pgnrList.add(new AssmtCommonBO("assmtContentValue1", "签订帮教协议、戒毒康复协议或者有明确意向", null));
            pgnrList.add(new AssmtCommonBO("assmtContentValue2", "家属或者所在社区支持配合其戒毒", null));
            pgnrList.add(new AssmtCommonBO("assmtContentValue3", "有主动接受社会监督和援助的意愿", null));
            pgnrList.add(new AssmtCommonBO("assmtContentValue4", "掌握一定的就业谋生技能", null));
            pgnrList.add(new AssmtCommonBO("assmtContentValue5", "有稳定的生活来源或者固定住所", null));

            pgjgList.add(new AssmtCommonBO("assmtResultValue1", "一年后评估结果", null));
            pgjgList.add(new AssmtCommonBO("assmtResultValue2", "二年期满前评估结果", null));
        } else if (StringUtils.equals(DiagnosisAssmtJdsTypeEnum.XWBX.getCode(), assmtType)) {
            Map<String, String> avgMap = monthlyAssmtJdsService.getAvgRecordByJgrybm(jgrybm);
            pgnrList.add(new AssmtCommonBO("assmtContentValue1", "服从管理教育，遵守所规所纪", avgMap.getOrDefault("assmtContentValue1", "0")));
            pgnrList.add(new AssmtCommonBO("assmtContentValue2", "接受戒毒治疗，参加康复训练", avgMap.getOrDefault("assmtContentValue2", "0")));
            pgnrList.add(new AssmtCommonBO("assmtContentValue3", "参加教育矫治活动", avgMap.getOrDefault("assmtContentValue3", "0")));
            pgnrList.add(new AssmtCommonBO("assmtContentValue4", "参加康复劳动", avgMap.getOrDefault("assmtContentValue4", "0")));
            pgnrList.add(new AssmtCommonBO("assmtContentValue5", "坦白、检举违法犯罪活动", avgMap.getOrDefault("assmtContentValue5", "0")));
            pgnrList.add(new AssmtCommonBO("assmtContentTotalScore", "上述五项内容综合得分", avgMap.getOrDefault("assmtContentTotalScore", "0")));

            pgjgList.add(new AssmtCommonBO("assmtResultValue1", "一年后评估结果", null));
            pgjgList.add(new AssmtCommonBO("assmtResultValue2", "二年期满前评估结果", null));
        } else {
            throw new RuntimeException("非法评估表类型");
        }
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String assmtCreat(DiagnosisAssmtJdsCommitReqVO createReqVO) {
        String id = createReqVO.getId();
        JSONObject wsdata = diagnosisAssmtJdsDao.getJgryxxByJgrybm(createReqVO.getJgrybm());
        DiagnosisAssmtJdsDO diagnosisAssmtJdsDO;
        if (StringUtils.isEmpty(id)) {
            if (Objects.isNull(wsdata)) {
                throw new ServerException("非戒毒所人员，不允许进行诊断评估");
            }
            // 手动填写评估提交
            diagnosisAssmtJdsDO = BeanUtils.toBean(createReqVO, DiagnosisAssmtJdsDO.class);
            diagnosisAssmtJdsDO.setPushTime(new Date());
            String gjmjRolecode = BspDbUtil.getParam("GJMJ_ROLECODE");
            String ylmjRolecode = BspDbUtil.getParam("YLMJ_ROLECODE");
            diagnosisAssmtJdsDO.setPushJobPositions(DiagnosisAssmtJdsTypeEnum.XWBX.getCode().equals(createReqVO.getAssmtType()) ? gjmjRolecode : ylmjRolecode);
            // 01 待评估
            diagnosisAssmtJdsDO.setStatus(DiagnosisAssmtJdsStatusEnum.DPG.getCode());
            diagnosisAssmtJdsDao.insert(diagnosisAssmtJdsDO);
            id = diagnosisAssmtJdsDO.getId();
        }
        diagnosisAssmtJdsDO = diagnosisAssmtJdsDao.selectById(id);
        List<AssmtCommonBO> pgnr = createReqVO.getPgnr();
        int yesCount = 0;
        for (AssmtCommonBO assmtCommonBO : pgnr) {
            try {
                yesCount = yesCount + Integer.parseInt(assmtCommonBO.getKeyValue());
            } catch (Exception e) {
                log.error(e.getMessage());
            }
            ReflectUtil.setFieldValue(diagnosisAssmtJdsDO, assmtCommonBO.getKeyCode(), assmtCommonBO.getKeyValue());
        }
        List<AssmtCommonBO> pgjg = createReqVO.getPgjg();
        for (AssmtCommonBO assmtCommonBO : pgjg) {
            ReflectUtil.setFieldValue(diagnosisAssmtJdsDO, assmtCommonBO.getKeyCode(), assmtCommonBO.getKeyValue());
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        diagnosisAssmtJdsDO.setAssmtUser(sessionUser.getName());
        diagnosisAssmtJdsDO.setAssmtUserSfzh(sessionUser.getIdCard());
        diagnosisAssmtJdsDO.setAsstmContentJson(JSON.toJSONString(pgnr));
        diagnosisAssmtJdsDO.setAssmtResultJson(JSON.toJSONString(pgjg));
        diagnosisAssmtJdsDO.setAssmtTime(SDF.format(new Date()));
        diagnosisAssmtJdsDO.setStatus(DiagnosisAssmtJdsStatusEnum.DZDLDSP.getCode());

        // 提交申请
        //启动流程审批
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", id);
        JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode() + "-" + defKey, id,
                "诊断评估流程审批", msgUrl+id, variables);
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            diagnosisAssmtJdsDO.setActInstId(bpmTrail.getString("actInstId"));
            diagnosisAssmtJdsDO.setTaskId(bpmTrail.getString("taskId"));
            diagnosisAssmtJdsDao.updateById(diagnosisAssmtJdsDO);
            // 存储文书打印需要信息
            PrintDocumentTypeEnum glType;
            if (DiagnosisAssmtJdsTypeEnum.XWBX.getCode().equals(createReqVO.getAssmtType())) {
                glType = PrintDocumentTypeEnum.jdryxwbxpgb;
            } else if (DiagnosisAssmtJdsTypeEnum.SLTD.getCode().equals(createReqVO.getAssmtType())) {
                glType = PrintDocumentTypeEnum.jdrysltdpgb;
            } else if (DiagnosisAssmtJdsTypeEnum.SXKF.getCode().equals(createReqVO.getAssmtType())) {
                glType = PrintDocumentTypeEnum.jdrysxjkpgb;
            } else {
                glType = PrintDocumentTypeEnum.jdryshhjysynlpgb;
            }

            PrintDocumentSaveReqVO printDocumentSaveReqVO = new PrintDocumentSaveReqVO();
            printDocumentSaveReqVO.setGlId(id);
            printDocumentSaveReqVO.setGlType(glType.getCode());

            if (DiagnosisAssmtJdsTypeEnum.XWBX.getCode().equals(createReqVO.getAssmtType())) {
                // 保存子表
                List<MonthlyAssmtJdsDO> recordByJgrybm = monthlyAssmtJdsService.getRecordByJgrybm(createReqVO.getJgrybm());
                List<MonthlyAssmtDocumentJdsDO> tempList = BeanUtils.toBean(recordByJgrybm, MonthlyAssmtDocumentJdsDO.class);
                if(CollectionUtil.isNotEmpty(tempList)){
                    for (MonthlyAssmtDocumentJdsDO monthlyAssmtDocumentJdsDO : tempList) {
                        monthlyAssmtDocumentJdsDO.setMonthlyAssmtId(monthlyAssmtDocumentJdsDO.getId());
                        monthlyAssmtDocumentJdsDO.setWsid(id);
                        monthlyAssmtDocumentJdsDO.setId(null);
                    }
                    monthlyAssmtDocumentJdsDao.insertBatch(tempList);
                }
            }
            wsdata.put("yesCount", yesCount);
            printDocumentSaveReqVO.setWsdata(wsdata.toJSONString());
            printDocumentService.createPrintDocument(printDocumentSaveReqVO);

        } else {
            throw new ServerException("流程启动失败");
        }
        return id;
    }

    @Override
    public void approval(DiagnosisAssmtJdsApprovalReqVO approvalReqVO) {
        DiagnosisAssmtJdsDO assmtJdsDO = diagnosisAssmtJdsDao.selectById(approvalReqVO.getId());
        BspApproceStatusEnum bspApproceStatusEnum;
        if (DiagnosisAssmtJdsStatusEnum.DZDLDSP.getCode().equals(assmtJdsDO.getStatus())) {
            assmtJdsDO.setStatus("0".equals(approvalReqVO.getApprovelResult()) ?
                    DiagnosisAssmtJdsStatusEnum.SPBTG.getCode() : DiagnosisAssmtJdsStatusEnum.DSLDSP.getCode());
            bspApproceStatusEnum = "0".equals(approvalReqVO.getApprovelResult()) ? BspApproceStatusEnum.NOT_PASSED_END :
                    BspApproceStatusEnum.PASSED;
        } else if (DiagnosisAssmtJdsStatusEnum.DSLDSP.getCode().equals(assmtJdsDO.getStatus())) {
            assmtJdsDO.setStatus("0".equals(approvalReqVO.getApprovelResult()) ?
                    DiagnosisAssmtJdsStatusEnum.SPBTG.getCode() : DiagnosisAssmtJdsStatusEnum.SPTG.getCode());
            bspApproceStatusEnum = "0".equals(approvalReqVO.getApprovelResult()) ? BspApproceStatusEnum.NOT_PASSED_END :
                    BspApproceStatusEnum.PASSED_END;
        } else {
            throw new RuntimeException("当前为【" + DiagnosisAssmtJdsStatusEnum.getByCode(assmtJdsDO.getStatus()).getName() + "】状态，无需进行审批");
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(assmtJdsDO.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }
        //assmtJdsDO.setAssmtUserSfzh(sessionUser.getIdCard());
        //assmtJdsDO.setAssmtUser(sessionUser.getName());

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", assmtJdsDO.getId());
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ||
                BspApproceStatusEnum.NOT_PASSED_END.getCode() == bspApproceStatusEnum.getCode();
        JSONObject result = BspApprovalUtil.approvalProcess(HttpUtils.getAppCode() + "-" + defKey, assmtJdsDO.getActInstId(), assmtJdsDO.getTaskId(), assmtJdsDO.getId(),
                bspApproceStatusEnum, approvalReqVO.getApprovelComment(), null, null, terminateTask,
                variables, nowApproveUser, "acp");
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            assmtJdsDO.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程启动失败");
        }
        diagnosisAssmtJdsDao.updateById(assmtJdsDO);

    }

    @Override
    public List<DiagnosisAssmtJdsRespVO> personRecordList(String jgrybm, String assmtType) {
        LambdaQueryWrapper<DiagnosisAssmtJdsDO> wrapper = Wrappers.lambdaQuery(DiagnosisAssmtJdsDO.class);
        wrapper.eq(DiagnosisAssmtJdsDO::getJgrybm, jgrybm);
        if (StringUtils.isNotEmpty(assmtType)) {
            wrapper.eq(DiagnosisAssmtJdsDO::getAssmtType, assmtType);
        }
        wrapper.ne(DiagnosisAssmtJdsDO::getStatus, DiagnosisAssmtJdsStatusEnum.DPG.getCode());
        wrapper.orderByDesc(DiagnosisAssmtJdsDO::getAssmtTime);
        List<DiagnosisAssmtJdsDO> list = diagnosisAssmtJdsDao.selectList(wrapper);
        List<DiagnosisAssmtJdsRespVO> assmtJdsRespVOS = BeanUtils.toBean(list, DiagnosisAssmtJdsRespVO.class);
        if (CollectionUtil.isEmpty(assmtJdsRespVOS)) {
            return assmtJdsRespVOS;
        }
        for (DiagnosisAssmtJdsRespVO assmtJdsRespVO : assmtJdsRespVOS) {
            setDiagnosisAssmtJdsRespVOAssmtResultValue(assmtJdsRespVO);
        }

        return assmtJdsRespVOS;
    }

    @Override
    public Map<String, String> getMaxAssmtPeriod(String assmtType, String jgrybm) {
        LambdaQueryWrapper<DiagnosisAssmtJdsDO> wrapper = Wrappers.lambdaQuery(DiagnosisAssmtJdsDO.class);
        wrapper.eq(DiagnosisAssmtJdsDO::getJgrybm, jgrybm);
        wrapper.eq(DiagnosisAssmtJdsDO::getAssmtType, assmtType);
        wrapper.ne(DiagnosisAssmtJdsDO::getStatus, DiagnosisAssmtJdsStatusEnum.DPG.getCode());
        wrapper.orderByDesc(DiagnosisAssmtJdsDO::getAssmtPeriod);
        wrapper.last(" limit 1");
        DiagnosisAssmtJdsDO assmtJdsDO = diagnosisAssmtJdsDao.selectOne(wrapper);
        Map<String, String> result = new HashMap<>();
        int index;
        if (Objects.isNull(assmtJdsDO)) {
            if (StringUtils.equals(DiagnosisAssmtJdsTypeEnum.SLTD.getCode(), assmtType)) {
                index = 1;
            } else {
                index = 2;
            }
        } else {
            index = Integer.parseInt(assmtJdsDO.getAssmtPeriod()) + 1;
            if (index > 3) {
                index = 3;
            }
        }
        String period = "0" + index;
        DiagnosisAssmtJdsPeriodEnum periodEnum = DiagnosisAssmtJdsPeriodEnum.getByCode(period);
        result.put("code", periodEnum.getCode());
        result.put("name", periodEnum.getName());
        return result;
    }

    @Override
    public List<DiagnosisAssmtJdsBusinessIdRespVO> getFormIdBusinessId(String jgrybm) {
        // 1、获取字典列表  诊断评估手册
        List<OpsDicCode> dicCodes = DicUtil.getDicAsc("ZD_ZDPGSC", "bsp");

        LambdaQueryWrapper<DiagnosisAssmtJdsDO> wrapper = Wrappers.lambdaQuery(DiagnosisAssmtJdsDO.class);
        wrapper.eq(DiagnosisAssmtJdsDO::getJgrybm, jgrybm);
        wrapper.ne(DiagnosisAssmtJdsDO::getStatus, DiagnosisAssmtJdsStatusEnum.DPG.getCode());
        wrapper.orderByDesc(DiagnosisAssmtJdsDO::getAssmtPeriod).orderByDesc(DiagnosisAssmtJdsDO::getUpdateTime);
        List<DiagnosisAssmtJdsDO> list = diagnosisAssmtJdsDao.selectList(wrapper);
        DiagnosisAssmtJdsDO containsKhjl = null;
        Map<String, DiagnosisAssmtJdsDO> map = new HashMap<>();
        for (DiagnosisAssmtJdsDO assmtJdsDO : list) {
            DiagnosisAssmtJdsTypeEnum assmtJdsTypeEnum = DiagnosisAssmtJdsTypeEnum.getByCode(assmtJdsDO.getAssmtType());
            DiagnosisAssmtJdsDO jdsDO = map.get(assmtJdsTypeEnum.getName() + "表");
            if (Objects.isNull(jdsDO)) {
                if (DiagnosisAssmtJdsTypeEnum.XWBX.getCode().equals(assmtJdsTypeEnum.getCode())) {
                    containsKhjl = assmtJdsDO;
                }
                map.put(assmtJdsTypeEnum.getName() + "表", assmtJdsDO);
            }
        }
        // 字典 name 带有“表” 如“戒毒人员身心康复评估表” 枚举不带“表”字  如“戒毒人员身心康复评估”
        List<DiagnosisAssmtJdsBusinessIdRespVO> resultList = new ArrayList<>();
        for (OpsDicCode dicCode : dicCodes) {
            DiagnosisAssmtJdsBusinessIdRespVO dajbirv = new DiagnosisAssmtJdsBusinessIdRespVO();
            dajbirv.setName(dicCode.getName());
            dajbirv.setFormId(dicCode.getCode());
            String businessId = "";
            if (map.size() > 0) {
                if (StringUtils.isNotEmpty(dicCode.getName()) && dicCode.getName().startsWith("戒毒人员")) {
                    DiagnosisAssmtJdsDO assmtJdsDO = map.get(dicCode.getName());
                    if (Objects.nonNull(assmtJdsDO)) {
                        businessId = assmtJdsDO.getId();
                    }
                    if (dicCode.getName().contains("考核记分") && Objects.nonNull(containsKhjl)) {
                        businessId = containsKhjl.getId();
                    }
                } else {
                    businessId = list.get(0).getId();
                }
            }
            dajbirv.setBusinessId(businessId);
            resultList.add(dajbirv);
        }
        return resultList;
    }

    private void  setDiagnosisAssmtJdsRespVOAssmtResultValue(DiagnosisAssmtJdsRespVO assmtJdsRespVO){
        if(Objects.isNull(assmtJdsRespVO)){
            return;
        }
        if (StringUtils.equals(DiagnosisAssmtJdsTypeEnum.SLTD.getCode(), assmtJdsRespVO.getAssmtType())) {
            if (DiagnosisAssmtJdsPeriodEnum.THREE_MONTH.getCode().equals(assmtJdsRespVO.getAssmtPeriod())) {
                assmtJdsRespVO.setAssmtResultValue(assmtJdsRespVO.getAssmtResultValue1());
            } else if (DiagnosisAssmtJdsPeriodEnum.ONE_YEAR.getCode().equals(assmtJdsRespVO.getAssmtPeriod())) {
                assmtJdsRespVO.setAssmtResultValue(assmtJdsRespVO.getAssmtResultValue2());
            } else {
                assmtJdsRespVO.setAssmtResultValue(assmtJdsRespVO.getAssmtResultValue3());
            }
        } else {
            if (DiagnosisAssmtJdsPeriodEnum.ONE_YEAR.getCode().equals(assmtJdsRespVO.getAssmtPeriod())) {
                assmtJdsRespVO.setAssmtResultValue(assmtJdsRespVO.getAssmtResultValue1());
            } else {
                assmtJdsRespVO.setAssmtResultValue(assmtJdsRespVO.getAssmtResultValue2());
            }
        }
    }

}
