package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-留言内容新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MessageBookSaveReqVO extends BaseVO{
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("留言时间")
    @NotNull(message = "留言时间不能为空")
    private Date messageTime;

    @ApiModelProperty("留言人姓名")
    @NotEmpty(message = "留言人姓名不能为空")
    private String messageUserName;

    @ApiModelProperty("留言人联系电话")
    @NotEmpty(message = "留言人联系电话不能为空")
    private String messageUserPhone;

    @ApiModelProperty("人员类型")
    private String userType;

    @ApiModelProperty("工作单位")
    private String workUnit;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("留言内容")
    @NotEmpty(message = "留言内容不能为空")
    private String messageContent;

}
