package com.rs.module.acp.controller.app.db.vo.kss;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "app端 - 实战平台-羁押业务-看守所收押基础 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppDetainRegKssBasicsRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("收押登记时间")
    private Date addTime;
    @ApiModelProperty("收押类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWRSLX")
    private String rslx;
    @ApiModelProperty("医生意见")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYRSYSYJ")
    private String ysyj;
    @ApiModelProperty("检查人")
    private String jcr;
    @ApiModelProperty("检查时间")
    private Date jcsj;
    @ApiModelProperty("备注")
    private String bz;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;

}
