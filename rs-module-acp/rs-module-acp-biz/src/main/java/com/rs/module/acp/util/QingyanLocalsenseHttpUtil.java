package com.rs.module.acp.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.rs.module.acp.config.QingyanDeviceProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 智能腕表 清研
 */
@Slf4j
@Component
public class QingyanLocalsenseHttpUtil {
    private final QingyanDeviceProperties qingyanDeviceProperties;

    public QingyanLocalsenseHttpUtil(QingyanDeviceProperties qingyanDeviceProperties) {
        this.qingyanDeviceProperties = qingyanDeviceProperties;
    }
    private final String LOGIN_URI = "/localsense/login";
    private final String salt = "abcdefghijklmnopqrstuvwxyz20191107salt";
    private final String TAG_TYPE_PERSON = "10001100";
    /**
     * 告警查询 url
     */
    private final String QUERY_ALARM_URI = "/localsense/alarm/getAlarmByParam";
    private final String ADD_PHYSICAL_TASK_URI = "/heartRate/addPhysicalTask";

    private final String UPD_PHYSICAL_TASK_URI = "/heartRate/updatePhysicalTask";

    private final String QUERY_TRACK_URI = "/localsense/historicalTrack/getTrackInfo";

    // 标签相关接口常量
    private final String ADD_TAG_URI = "/localsenseadmin/tag/addTag";
    private final String UPDATA_TAG_URI = "/localsenseadmin/tag/updateTag";
    private final String BIND_TAG_URI = "/localsenseadmin/tag/bind";
    private final String DELETE_TAG_URI = "/localsenseadmin/tag/deleteTag";
    private final String UNBIND_TAG_URI = "/localsenseadmin/tag/unBind";
    private final String GET_TAG_INFO_URI = "/localsenseadmin/tag/getTagInfoByParams";

    // 新增人员标签接口 /localsenseadmin/tag/addTag post 参数: groupId 机构ID,tagId 标签id,tagName 标签名称,type 固定人员类型(10001100)
    // 绑定人员标签接口 /localsenseadmin/tag/bind post 参数: num 编号,tagId 标签id
    // 删除人员标签接口 /localsenseadmin/tag/deleteTag get 参数: nums 不为空，多个以分号;分割，最多支持100个标签删除
    // 解绑标签 /localsenseadmin/tag/unBind get 参数: nums
    // 根据条件获取标签信息 /localsenseadmin/tag/getTagInfoByParams get 参数: num 对象编号（可模糊）,tagName 对象名称（可模糊）,type 类型 ,groupId 群组ID,精确查询,status 绑定状态(不传为全部，1已绑定，0未绑定)

    private String token;
    private long tokenExpireTime;

    public void init() {
        // 初始化时尝试获取token
        try {
            refreshToken();
        } catch (Exception e) {
            log.error("清研httpUtil 初始化token失败", e);
        }
    }

    private void refreshToken() throws Exception {
        String url = concatUrl(LOGIN_URI);
        JSONObject requestBody = new JSONObject();
        requestBody.put("username", qingyanDeviceProperties.getUsername());
        requestBody.put("password", getPassWordMD5());

        String responseStr = HttpRequest.post(url)
                .addHeaders(initHeaders(null))
                .body(requestBody.toString())
                .timeout(30000)
                .execute()
                .body();
        JSONObject jsonObject = JSONObject.parseObject(responseStr);
        if (jsonObject.getInteger("code") != HttpStatus.OK.value()) {
            log.error("登录失败: {}", jsonObject.toJSONString());
            throw new Exception("登录失败");
        }
        token = jsonObject.getString("data");
        // 假设token有效期为1小时
        tokenExpireTime = System.currentTimeMillis() + 3600000;
    }

    private String getToken() throws Exception {
        if (token == null || System.currentTimeMillis() > tokenExpireTime) {
            refreshToken();
        }
        return token;
    }
    private String concatUrl(String url){
        /* 测试 初始化 */
        /*serverIp = "*************";
        webPort = "8180";
        username = "admin";
        password = "#LocalSense";*/
        return "http://" +qingyanDeviceProperties.getServerIp()+":"+qingyanDeviceProperties.getWebPort()+url;
    }
    private Map<String, String> initHeaders(String token) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Accept", "application/json, text/plain, */*");
        headers.put("Accept-Language", "zh-CN,zh;q=0.9");
        headers.put("Connection", "keep-alive");
        headers.put("Content-Type", ContentType.JSON.getValue());
        if(StringUtil.isNotEmpty(token))headers.put("token", token);
        return headers;
    }
    private JSONObject executePost(String uri, Map<String, Object> paramMap) throws Exception {
        return executeRequest(uri, paramMap, true);
    }

    private JSONObject executeGet(String uri, Map<String, Object> paramMap) throws Exception {
        return executeRequest(uri, paramMap, false);
    }
    // 新增：执行请求并返回字符串结果
    private String executeRequestString(String uri, Map<String, Object> paramMap, boolean isPost) throws Exception {
        return executeRequestCommon(uri, paramMap, isPost);
    }
    private JSONObject executeRequest(String uri, Map<String, Object> paramMap, boolean isPost) throws Exception {
        String responseDataStr = executeRequestCommon(uri, paramMap, isPost);
        JSONObject jsonObject = JSONObject.parseObject(responseDataStr);
        return jsonObject;
    }private static final int HTTP_TIMEOUT = 30000;

    private String executeRequestCommon(String uri, Map<String, Object> paramMap, boolean isPost) throws Exception {
        String url = concatUrl(uri);
        Map<String, String> headers = initHeaders(getToken());

        HttpRequest request;
        if (isPost) {
            request = HttpRequest.post(url)
                    .addHeaders(headers)
                    .body(JSONObject.toJSONString(paramMap));
        } else {
            if (paramMap != null && !paramMap.isEmpty()) {
                url = HttpUtil.urlWithForm(url, paramMap, Charset.defaultCharset(), false);
            }
            request = HttpRequest.get(url)
                    .addHeaders(headers);
        }

        HttpResponse response = request.timeout(HTTP_TIMEOUT)
                .execute();

        String responseStr = response.body();
        JSONObject jsonObject = JSONObject.parseObject(responseStr);

        if (jsonObject.getInteger("code") != HttpStatus.OK.value()) {
            log.error("请求失败: URL={}, Params={}, Response={}", url, paramMap, jsonObject.toJSONString());
            throw new Exception("请求失败");
        }
        return jsonObject.getString("data");
    }
    private String executePostString(String uri, Map<String, Object> paramMap) throws Exception {
        return executeRequestString(uri, paramMap, true);
    }
    private String executeGetString(String uri, Map<String, Object> paramMap) throws Exception {
        return executeRequestString(uri, paramMap, false);
    }

    public JSONObject getAlarmByParam(String alarmType, String areaId, String beginTime, String endTime, Integer num, Integer page, String state) throws Exception {
        Map<String, Object> paramMap = initParams(alarmType, areaId, beginTime, endTime, num, page, state);
        return executePost(QUERY_ALARM_URI, paramMap);
    }
    /**
     * 新增体征采样任务
     * @param type 体征类型 必需 1:心率 2:血氧 3:体温
     * @param samplingPeriod 采样周期 必需 单位S
     * @param updateTime 更新时间 必需 10:00
     * @param relates 关联对象/组织列表
     * @return
     * @throws Exception
     */
    public String addPhysicalTask(Integer type, Integer samplingPeriod, String updateTime, String[] relates) throws Exception {
        Integer associationType =0; //关联类型 必需 0:对象 1:组织
        Map<String, Object> paramMap = initPhysicalTask(null,type, samplingPeriod, updateTime, associationType, relates);
        return executePostString(ADD_PHYSICAL_TASK_URI, paramMap);
    }
    public String updatePhysicalTask(String id,Integer type, Integer samplingPeriod, String updateTime, String[] relates) throws Exception {
        Integer associationType =0; //关联类型 必需 0:对象 1:组织
        Map<String, Object> paramMap = initPhysicalTask(id,type, samplingPeriod, updateTime, associationType, relates);
        return executePostString(UPD_PHYSICAL_TASK_URI, paramMap);
    }
    public String savePhysicalTask(String id,Integer type, Integer samplingPeriod, String num) throws Exception {
        String taskId = null;
        if(StringUtil.isNotEmpty(id)){
            updatePhysicalTask(id,type, samplingPeriod, null, new String[]{num});
        }else{
            taskId = addPhysicalTask(type, samplingPeriod, null, new String[]{num});
        }
        return taskId;
    }

    /**
     * 初始化查询告警参数
     *
     * @param alarmType 告警类型 详见附录-数据字典-告警类型字典
     * @param areaId 区域id
     * @param beginTime 开始时间 时间戳(毫秒)
     * @param endTime 结束时间 时间戳(毫秒)
     * @param num 每页条数 示例值: 10
     * @param page 页号 示例值: 1
     * @param state 状态 0-未处理 1-已处理
     * @return 参数映射表
     */
    private Map<String, Object> initParams(String alarmType,String areaId,String beginTime,String endTime,Integer num,Integer page,String state){
        if(num == null) num = 20;
        if(page == null) page = 1;
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("alarmType", alarmType);
        paramMap.put("areaId", areaId);
        //当前传入时间yyyy-MM-dd HH:mm:ss 转换为时间戳
        // 修改时间转换逻辑
        String beginTimeStr;
        if (StringUtil.isNotEmpty(beginTime)) {
            Date startDateTime = DateUtil.parse(beginTime, "yyyy-MM-dd HH:mm:ss");
            beginTimeStr = String.valueOf(startDateTime.getTime());
        } else {
            // 默认值：当日的开始时间（00:00:00）
            LocalDateTime startOfDay = LocalDateTimeUtil.beginOfDay(LocalDateTime.now());
            beginTimeStr = String.valueOf(LocalDateTimeUtil.toEpochMilli(startOfDay));
        }
        paramMap.put("beginTime", beginTimeStr);

        String endTimeStr;
        if (StringUtil.isNotEmpty(endTime)) {
            Date endDateTime = DateUtil.parse(endTime, "yyyy-MM-dd HH:mm:ss");
            endTimeStr = String.valueOf(endDateTime.getTime());
        } else {
            // 默认值：当前时间
            endTimeStr = String.valueOf(System.currentTimeMillis());
        }
        paramMap.put("endTime", endTimeStr);
        paramMap.put("num", num);
        paramMap.put("page", page);
        paramMap.put("state", state);
        return paramMap;
    }
    /**
     * heartRate/addPhysicalTask
     * type integer  体征类型 必需 1:心率 2:血氧 3:体温
     * samplingPeriod integer 采样周期 必需 单位S
     * updateTime string 更新时间 必需 10:00
     * associationType integer 关联类型 必需 0:对象 1:组织
     * relates array[string] 关联对象/组织列表
     */
    private Map<String, Object>  initPhysicalTask(String id,Integer type,Integer samplingPeriod,String updateTime,Integer associationType,String[] relates){
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("type", type);
        paramMap.put("samplingPeriod", samplingPeriod);
        paramMap.put("updateTime", updateTime);
        paramMap.put("associationType", associationType);
        paramMap.put("relates", relates);
        if(id != null) paramMap.put("id", id);
        return paramMap;
    }
    public JSONObject getHistoricalTrack( long beginTime, long endTime, String nums) throws Exception {
        return getHistoricalTrack(null, null, null, beginTime, endTime,
            "0", "0", "0", nums);
    }
    /**
     * 获取历史轨迹信息
     * @param alarmType 告警类型 详见附录-数据字典-告警类型字典 (当areaType为3或4的时候必填 传入对应告警类型)
     * @param areaId 区域id
     * @param areaType 区域类型 （1围栏, 2考勤区域，3超员区域，4滞留区域; 用在查询所有进入过该区域的标签的轨迹时，和areaId同时存在）
     * @param beginTime 查询的开始时间 时间戳(毫秒)
     * @param endTime 查询的结束时间 时间戳(毫秒)
     * @param isHeatMap 是否热力图 0-否 1-是
     * @param isOnlyTime 是否只需要时间数据 0-否 1-是
     * @param isSearchHistoricalVideo 是否查询历史视频 0-否 1-是
     * @param nums 对象编号， 多个编号英文分号隔开
     * @return 轨迹数据
     * @throws Exception 请求异常
     */
    public JSONObject getHistoricalTrack(String alarmType, String areaId, String areaType, long beginTime, long endTime,
                                         String isHeatMap, String isOnlyTime, String isSearchHistoricalVideo, String nums) throws Exception {
        Map<String, Object> paramMap = new HashMap<>();
        if (StringUtil.isNotEmpty(alarmType)) {
            paramMap.put("alarmType", alarmType);
        }
        if (StringUtil.isNotEmpty(areaId)) {
            paramMap.put("areaId", areaId);
        }
        if (StringUtil.isNotEmpty(areaType)) {
            paramMap.put("areaType", areaType);
        }
        paramMap.put("beginTime", beginTime);
        paramMap.put("endTime", endTime);
        if (StringUtil.isNotEmpty(isHeatMap)) {
            paramMap.put("isHeatMap", isHeatMap);
        }
        if (StringUtil.isNotEmpty(isOnlyTime)) {
            paramMap.put("isOnlyTime", isOnlyTime);
        }
        if (StringUtil.isNotEmpty(isSearchHistoricalVideo)) {
            paramMap.put("isSearchHistoricalVideo", isSearchHistoricalVideo);
        }
        if (StringUtil.isNotEmpty(nums)) {
            paramMap.put("nums", nums);
        }

        return executeGet(QUERY_TRACK_URI, paramMap);
    }
    /**
     * 新增人员标签接口
     * @param groupId 机构ID
     * @param tagId 标签id
     * @param tagName 标签名称
     * @param type 固定人员类型(10001100)
     * @return JSONObject
     * @throws Exception 请求异常
     */
    private String addTagCommon(String groupId, String tagId, String tagName, String type,String num) throws Exception {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("groupId", groupId);
        paramMap.put("tagId", tagId);
        paramMap.put("tagName", tagName);
        paramMap.put("type", type);
        paramMap.put("num", num);
        return executePostString(ADD_TAG_URI, paramMap);
    }

    private String updTagCommon(String groupId, String tagId, String tagName, String type,String num) throws Exception {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("groupId", groupId);
        paramMap.put("tagId", tagId);
        paramMap.put("tagName", tagName);
        paramMap.put("type", type);
        paramMap.put("num", num);
        return executePostString(UPDATA_TAG_URI, paramMap);
    }
    /**
     * 新增人员标签接口
     * @param orgCode 机构ID
     * @param tagId 标签id
     * @param tagName 标签名称
     * @return JSONObject
     * @throws Exception 请求异常
     */
    public String addTag(String orgCode, String tagId, String tagName, String num) throws Exception {
        return addTagCommon(getGroupIdByOrgCode(orgCode), tagId, tagName, TAG_TYPE_PERSON,num);
    }
    public String updateTag(String orgCode, String tagId, String tagName, String num) throws Exception {
        return updTagCommon(getGroupIdByOrgCode(orgCode), tagId, tagName, TAG_TYPE_PERSON,num);
    }
    /**
     * 绑定人员标签接口
     * @param num 编号
     * @param tagId 标签id
     * @return JSONObject
     * @throws Exception 请求异常
     */
    public String bindTag(String num, String tagId) throws Exception {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("num", num);
        paramMap.put("tagId", tagId);
        return executePostString(BIND_TAG_URI, paramMap);
    }

    /**
     * 删除人员标签接口
     * @param nums 不为空，多个以分号;分割，最多支持100个标签删除
     * @return JSONObject
     * @throws Exception 请求异常
     */
    public JSONObject deleteTags(String nums) throws Exception {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("nums", nums);
        return executeGet(DELETE_TAG_URI, paramMap);
    }

    /**
     * 解绑标签
     * @param nums 不为空，多个以分号;分割
     * @return JSONObject
     * @throws Exception 请求异常
     */
    public String unbindTags(String nums) throws Exception {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("nums", nums);
        return executeGetString(UNBIND_TAG_URI, paramMap);
    }

    /**
     * 根据条件获取标签信息
     * @param num 对象编号（可模糊）
     * @param tagName 对象名称（可模糊）
     * @param type 类型
     * @param groupId 群组ID,精确查询
     * @param status 绑定状态(不传为全部，1已绑定，0未绑定)
     * @return JSONObject
     * @throws Exception 请求异常
     */
     private JSONObject getTagInfoByParamsCommon(String num, String tagName, String type, String groupId, String status) throws Exception {
        Map<String, Object> paramMap = new HashMap<>();
        if (StringUtil.isNotEmpty(num)) {
            paramMap.put("num", num);
        }
        if (StringUtil.isNotEmpty(tagName)) {
            paramMap.put("tagName", tagName);
        }
        if (StringUtil.isNotEmpty(type)) {
            paramMap.put("type", type);
        }
        if (StringUtil.isNotEmpty(groupId)) {
            paramMap.put("groupId", groupId);
        }
        if (StringUtil.isNotEmpty(status)) {
            paramMap.put("status", status);
        }
        return executeGet(GET_TAG_INFO_URI, paramMap);
    }
    /**
     * 根据条件获取标签信息
     * @param num 对象编号（可模糊）
     * @param orgCode 机构编码
     * @param status 绑定状态(不传为全部，1已绑定，0未绑定)
     * @return JSONObject
     * @throws Exception 请求异常
     */
    public JSONObject getTagInfoByParams(String num,  String orgCode, String status) throws Exception {
        return getTagInfoByParamsCommon(num, null, TAG_TYPE_PERSON, getGroupIdByOrgCode(orgCode), status);
    }
    private String getGroupIdByOrgCode(String orgCode) throws Exception {
        List<QingyanDeviceProperties.GroupOrgMapping> groupOrgMappingList = qingyanDeviceProperties.getGroupOrgMapping();
        if (CollectionUtil.isNull(groupOrgMappingList)) {
            return null;
        }
        for (QingyanDeviceProperties.GroupOrgMapping groupOrgMapping : groupOrgMappingList) {
            if (groupOrgMapping.getOrgCode().equals(orgCode)) {
                return groupOrgMapping.getGroupId();
            }
        }
        return null;
    }
    private String getPassWordMD5(){
        String temp = Hex.encodeHexString(DigestUtils.md5(qingyanDeviceProperties.getPassword()));
        return Hex.encodeHexString(DigestUtils.md5(temp+salt));
    }
   /* //查询体征数据
    private final String QUERY_TZ_DATA = "localsense/heartRate/obtainPhysicalData";
    private final String QUERY_TZ_TYPE = "1,2,3";
    public  Map<String, QYLocalsensePhysicalDataVO> obtainPhysicalData(String startTime,String endTime,String type,String queryNum) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("startTime", startTime);
        paramMap.put("endTime", endTime);
        if(StringUtil.isEmpty(type)) type = QUERY_TZ_TYPE;
        paramMap.put("type", type);
        paramMap.put("num", queryNum);
        String responseStr = HttpUtil.get(QUERY_TZ_DATA,paramMap);
        JSONObject jsonObject = JSONObject.parseObject(responseStr);
        if(jsonObject.getInteger("code") != 200){
            return null;
        }
        JSONObject resultData = jsonObject.getJSONObject("data");
        //血氧数据
        JSONArray bloodOxygenList = resultData.getJSONArray("bloodOxygenList");
        //心率数据
        JSONArray heartRateList = resultData.getJSONArray("heartRateList");
        //体温数据
        JSONArray temperatureList = resultData.getJSONArray("temperatureList");
        // 创建一个 map 来存储每个 num 对应的 QYLocalsensePhysicalDataVO 对象
        Map<String, QYLocalsensePhysicalDataVO> dataMap = new HashMap<>();

        // 处理血氧数据
        for (int i = 0; i < bloodOxygenList.size(); i++) {
            JSONObject bloodOxygenItem = bloodOxygenList.getJSONObject(i);
            String num = bloodOxygenItem.getString("num");
            Integer data = bloodOxygenItem.getInteger("data");

            dataMap.computeIfAbsent(num, k -> new QYLocalsensePhysicalDataVO(num)).setBloodOxygen(data);
        }

        // 处理心率数据
        for (int i = 0; i < heartRateList.size(); i++) {
            JSONObject heartRateItem = heartRateList.getJSONObject(i);
            String num = heartRateItem.getString("num");
            Integer data = heartRateItem.getInteger("data");

            dataMap.computeIfAbsent(num, k -> new QYLocalsensePhysicalDataVO(num)).setHeartRate(data);
        }

        // 处理体温数据
        for (int i = 0; i < temperatureList.size(); i++) {
            JSONObject temperatureItem = temperatureList.getJSONObject(i);
            String num = temperatureItem.getString("num");
            Integer data = temperatureItem.getInteger("data");

            dataMap.computeIfAbsent(num, k -> new QYLocalsensePhysicalDataVO(num)).setTemperature(data);
        }
        return dataMap;
    }*/
    public static void main(String[] args) {

        /*QingyanLocalsenseHttpUtil util = new QingyanLocalsenseHttpUtil();
        JSONObject result = null;
        try {
            result = util.getAlarmByParam(null, null, null, null, null, null, null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        System.out.println(result);*/
        /*String aa = "#LocalSense";
        String salt = "abcdefghijklmnopqrstuvwxyz20191107salt";
        String a = Hex.encodeHexString(DigestUtils.md5(aa));
        String b = Hex.encodeHexString(DigestUtils.md5(a+salt));
        System.out.println(b);*/
        //ca269f1717955a7f16c9b4f2b2c6aebe
    }
}
