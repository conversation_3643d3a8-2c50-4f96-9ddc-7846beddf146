package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.wb.vo.CasePersonnelSaveReqVO;
import com.rs.module.acp.controller.admin.wb.vo.EscortListReqVO;
import com.rs.module.acp.controller.admin.wb.vo.EscortPageReqVO;
import com.rs.module.acp.controller.admin.wb.vo.EscortRespVO;
import com.rs.module.acp.controller.admin.wb.vo.EscortSaveReqVO;
import com.rs.module.acp.controller.admin.wb.vo.WbInspectionResultsSaveReqVO;
import com.rs.module.acp.dao.wb.EscortDao;
import com.rs.module.acp.entity.wb.CasePersonnelDO;
import com.rs.module.acp.entity.wb.EscortDO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.BspApprovalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 实战平台-窗口业务-提解登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EscortServiceImpl extends BaseServiceImpl<EscortDao, EscortDO> implements EscortService {

    @Resource
    private EscortDao escortDao;
    @Autowired
    private CasePersonnelService casePersonnelService;

    @Autowired
    private WbCommonService wbCommonService;

    @Autowired
    private PrisonerService prisonerService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createEscort(EscortSaveReqVO createReqVO) {

        List<CasePersonnelSaveReqVO> casePersonnelList = createReqVO.getCasePersonnelList();
        if (CollectionUtil.isEmpty(casePersonnelList) || casePersonnelList.size() < 2) {
            throw new ServerException("实战平台-窗口业务-提解除登记办案人员数量不能小于2人");
        }

        EscortDO escort = BeanUtils.toBean(createReqVO, EscortDO.class);
        escort.setId(StringUtil.getGuid32());

        //判断casePersonnelList是否为空，则新增/更新数据插入到acp_wb_case_personnel 表
        for (CasePersonnelSaveReqVO casePersonnelSaveReqVO : casePersonnelList) {
            if(ObjectUtil.isEmpty(casePersonnelSaveReqVO.getId())){
                String casePersonnelId = casePersonnelService.createCasePersonnel(casePersonnelSaveReqVO);
                casePersonnelSaveReqVO.setId(casePersonnelId);
            }
        }

        escort.setHandler1Id(casePersonnelList.get(0).getId());
        escort.setHandler1Xm(casePersonnelList.get(0).getXm());
        escort.setHandler2Id(casePersonnelList.get(1).getId());
        escort.setHandler2Xm(casePersonnelList.get(1).getXm());
        if (casePersonnelList.size() > 2) {
            escort.setHandler3Id(casePersonnelList.get(2).getId());
            escort.setHandler3Xm(casePersonnelList.get(2).getXm());
        }
        Set<String> tjjgmcSet = new HashSet<>();
        casePersonnelList.forEach(x->{tjjgmcSet.add(x.getBadwmc());});
        escort.setTjjgmc(String.join("、",tjjgmcSet));


        if(ObjectUtil.isNotEmpty(escort.getEvidenceUrl())){
            String evidenceUrl = wbCommonService.saveFile(null,escort.getEvidenceUrl());
            escort.setEvidenceUrl(evidenceUrl);
        }

        PrisonerVwRespVO prisonerVwRespVO = prisonerService.getPrisonerByJgrybm(createReqVO.getJgrybm());
        if("01".equals(prisonerVwRespVO.getBjgrylx())){
            escort.setStatus("0");
        }else if("02".equals(prisonerVwRespVO.getBjgrylx())){
            //拘留所
            escort.setStatus("10-1");
        }else {
            //其他被监管人员类型，业务暂时没有要求，先采取默认与看守所一样
            escort.setStatus("0");
        }

        //根据状态判断，是否发起审批
        if("10-1".equals(escort.getStatus())){
            Map<String, Object> variables = new HashMap<>();
            variables.put("ywbh", escort.getId());
            variables.put("busType", MsgBusTypeEnum.CK_TJ.getCode());
            variables.put("roomName",prisonerVwRespVO.getRoomName());
            variables.put("prisonerName",prisonerVwRespVO.getXm());
            variables.put("meetingTime",DateUtil.format(escort.getApplyEscortDate(),"yyyy-MM-dd"));
            String msgUrl = String.format("/window/provideSolutionsJls?curId=%s&saveType=sp",escort.getId());
            JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode()+"-"+"chuangkouyewujuliusuotijie",escort.getId(),"提解所领导审批",msgUrl,variables);
            JSONObject data = result.getJSONObject("data");
            JSONObject bpmTrail = data.getJSONObject("bpmTrail");
            escort.setActInstId(bpmTrail.getString("actInstId"));
            escort.setTaskId(bpmTrail.getString("taskId"));
        }else {
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(escort,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(escort.getJgrybm());
            resultsSaveReqVO.setApplMeetingTime(escort.getApplyEscortDate());
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"02","out","0",false);
        }

        escortDao.insert(escort);

        wbCommonService.registrationReminder(JSONObject.parseObject(JSON.toJSONString(escort)), WbConstants.BUSINESS_TYPE_ESCORT);
        wbCommonService.audioBroadcast(WbConstants.BUSINESS_TYPE_ESCORT,JSONObject.parseObject(JSON.toJSONString(escort)));
        return escort.getId();
    }

    @Override
    public void updateEscort(EscortSaveReqVO updateReqVO) {
        // 校验存在
        validateEscortExists(updateReqVO.getId());
        // 更新
        EscortDO updateObj = BeanUtils.toBean(updateReqVO, EscortDO.class);
        escortDao.updateById(updateObj);
    }

    @Override
    public void deleteEscort(String id) {
        // 校验存在
        validateEscortExists(id);
        // 删除
        escortDao.deleteById(id);
    }

    private void validateEscortExists(String id) {
        if (escortDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-提解登记数据不存在");
        }
    }

    @Override
    public EscortDO getEscort(String id) {
        return escortDao.selectById(id);
    }

    @Override
    public PageResult<EscortDO> getEscortPage(EscortPageReqVO pageReqVO) {
        return escortDao.selectPage(pageReqVO);
    }

    @Override
    public List<EscortDO> getEscortList(EscortListReqVO listReqVO) {
        return escortDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean escortingInspect(EscortSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        LambdaUpdateWrapper<EscortDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(EscortDO::getId, updateReqVO.getId())
                .set(EscortDO::getEscortingPolice, updateReqVO.getEscortingPolice())
                .set(EscortDO::getEscortingPoliceSfzh, updateReqVO.getEscortingPoliceSfzh())
                .set(EscortDO::getEscortingTime, updateReqVO.getEscortingTime())
                .set(EscortDO::getInspectionResult, updateReqVO.getInspectionResult())
                .set(EscortDO::getProhibitedItems, updateReqVO.getProhibitedItems())
                .set(EscortDO::getPhysicalExam, updateReqVO.getPhysicalExam())
                .set(EscortDO::getAbnormalSituations, updateReqVO.getAbnormalSituations())
                .set(EscortDO::getInspectionTime, updateReqVO.getInspectionTime())
                .set(EscortDO::getInspector, updateReqVO.getInspector())
                .set(EscortDO::getInspectorSfzh, updateReqVO.getInspectorSfzh())
                .set(EscortDO::getArraignmentStartTime, updateReqVO.getArraignmentStartTime())
                .set(EscortDO::getStatus,"1");
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorSfzh())){
            lambdaUpdateWrapper.set(EscortDO::getEscortingOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(EscortDO::getEscortingOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorTime())){
            lambdaUpdateWrapper.set(EscortDO::getEscortingOperatorTime,new Date());
        }

        if("0".equals(updateReqVO.getDataSources())){
            EscortDO escortDO = getById(updateReqVO.getId());
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(escortDO.getJgrybm());
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"02","out","3",false);
        }

        return update(lambdaUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean returnInspection(EscortSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        LambdaUpdateWrapper<EscortDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(EscortDO::getId, updateReqVO.getId())
                .set(EscortDO::getArraignmentEndTime, updateReqVO.getArraignmentEndTime())
                .set(EscortDO::getReturnInspectorSfzh, updateReqVO.getReturnInspectorSfzh())
                .set(EscortDO::getReturnInspector, updateReqVO.getReturnInspector())
                .set(EscortDO::getReturnInspectionTime, updateReqVO.getReturnInspectionTime())
                .set(EscortDO::getReturnInspectionResult, updateReqVO.getReturnInspectionResult())
                .set(EscortDO::getReturnProhibitedItems, updateReqVO.getReturnProhibitedItems())
                .set(EscortDO::getReturnPhysicalExam, updateReqVO.getReturnPhysicalExam())
                .set(EscortDO::getReturnAbnormalSituations, updateReqVO.getReturnAbnormalSituations())
                .set(EscortDO::getReturnTime, updateReqVO.getReturnTime())
                .set(EscortDO::getReturnPolice, updateReqVO.getReturnPolice())
                .set(EscortDO::getReturnPoliceSfzh, updateReqVO.getReturnPoliceSfzh())
                .set(EscortDO::getStatus,"2");
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorSfzh())){
            lambdaUpdateWrapper.set(EscortDO::getReturnOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(EscortDO::getReturnOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorTime())){
            lambdaUpdateWrapper.set(EscortDO::getReturnOperatorTime,new Date());
        }
        if("0".equals(updateReqVO.getDataSources())){
            EscortDO escortDO = getById(updateReqVO.getId());
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(escortDO.getJgrybm());
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"02","in","3",false);
        }
        return update(lambdaUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean additionalRecording(EscortSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        LambdaUpdateWrapper<EscortDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(EscortDO::getId, updateReqVO.getId())
                .set(EscortDO::getEscortingPolice, updateReqVO.getEscortingPolice())
                .set(EscortDO::getEscortingPoliceSfzh, updateReqVO.getEscortingPoliceSfzh())
                .set(EscortDO::getEscortingTime, updateReqVO.getEscortingTime())
                .set(EscortDO::getInspectionResult, updateReqVO.getInspectionResult())
                .set(EscortDO::getProhibitedItems, updateReqVO.getProhibitedItems())
                .set(EscortDO::getPhysicalExam, updateReqVO.getPhysicalExam())
                .set(EscortDO::getAbnormalSituations, updateReqVO.getAbnormalSituations())
                .set(EscortDO::getInspectionTime, updateReqVO.getInspectionTime())
                .set(EscortDO::getInspector, updateReqVO.getInspector())
                .set(EscortDO::getInspectorSfzh, updateReqVO.getInspectorSfzh())
                .set(EscortDO::getArraignmentStartTime, updateReqVO.getArraignmentStartTime())
                .set(EscortDO::getArraignmentEndTime, updateReqVO.getArraignmentEndTime())
                .set(EscortDO::getReturnInspectorSfzh, updateReqVO.getReturnInspectorSfzh())
                .set(EscortDO::getReturnInspector, updateReqVO.getReturnInspector())
                .set(EscortDO::getReturnInspectionTime, updateReqVO.getReturnInspectionTime())
                .set(EscortDO::getReturnInspectionResult, updateReqVO.getReturnInspectionResult())
                .set(EscortDO::getReturnProhibitedItems, updateReqVO.getReturnProhibitedItems())
                .set(EscortDO::getReturnPhysicalExam, updateReqVO.getReturnPhysicalExam())
                .set(EscortDO::getReturnAbnormalSituations, updateReqVO.getReturnAbnormalSituations())
                .set(EscortDO::getReturnTime, updateReqVO.getReturnTime())
                .set(EscortDO::getReturnPolice, updateReqVO.getReturnPolice())
                .set(EscortDO::getReturnPoliceSfzh, updateReqVO.getReturnPoliceSfzh())
                .set(EscortDO::getStatus,"2");
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorSfzh())){
            lambdaUpdateWrapper.set(EscortDO::getEscortingOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(EscortDO::getEscortingOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorTime())){
            lambdaUpdateWrapper.set(EscortDO::getEscortingOperatorTime,new Date());
        }

        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorSfzh())){
            lambdaUpdateWrapper.set(EscortDO::getReturnOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(EscortDO::getReturnOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorTime())){
            lambdaUpdateWrapper.set(EscortDO::getReturnOperatorTime,new Date());
        }
        if("0".equals(updateReqVO.getDataSources())){
            EscortDO escortDO = getById(updateReqVO.getId());
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(escortDO.getJgrybm());
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"02","out","3",true);
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"02","in","3",true);
        }
        return update(lambdaUpdateWrapper);
    }

    @Override
    public EscortRespVO getEscortById(String id) {
        EscortDO escortDO = getById(id);
        if (ObjectUtil.isEmpty(escortDO)) {
            throw new ServerException("实战平台-窗口业务-提解登记数据不存在");
        }
        EscortRespVO escortRespVO = BeanUtils.toBean(escortDO, EscortRespVO.class);
        //组装办案民警信息
        List<String> casePersonnelIdList = new ArrayList<>();
        casePersonnelIdList.add(escortDO.getHandler1Id());
        casePersonnelIdList.add(escortDO.getHandler2Id());
        if (ObjectUtil.isNotEmpty(escortDO.getHandler3Id())) {
            casePersonnelIdList.add(escortDO.getHandler3Id());
        }

        escortRespVO.setCasePersonnelList(casePersonnelService.getByIds(casePersonnelIdList));

        if(ObjectUtil.isNotEmpty(escortRespVO.getEvidenceUrl())){
            String evidenceUrl = wbCommonService.getFile(escortRespVO.getEvidenceUrl());
            escortRespVO.setEvidenceUrl(evidenceUrl);
        }
        return escortRespVO;
    }

    @Override
    public PageResult<EscortRespVO> getHistoryEscort(String jgrybm, int pageNo, int pageSize) {

        Page<EscortDO> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<EscortDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(EscortDO::getAddTime,EscortDO::getHandler1Id,
                EscortDO::getHandler2Id,EscortDO::getHandler3Id,EscortDO::getEscortReason,EscortDO::getArraignmentStartTime,
                EscortDO::getArraignmentEndTime);
        lambdaQueryWrapper.eq(EscortDO::getJgrybm,jgrybm)
                .orderByDesc(EscortDO::getAddTime);

        Page<EscortDO> escortDOPage = page(page,lambdaQueryWrapper);
        List<EscortRespVO> escortRespVOList = BeanUtils.toBean(escortDOPage.getRecords(),EscortRespVO.class);

        if(CollectionUtil.isEmpty(escortRespVOList)){
            return new PageResult<>(new ArrayList<>() ,0L);
        }

        Set<String> handlerIdSet =  new HashSet<>();

        for(EscortRespVO escortRespVO:escortRespVOList){
            handlerIdSet.add(escortRespVO.getHandler1Id());
            handlerIdSet.add(escortRespVO.getHandler2Id());
            if(ObjectUtil.isNotEmpty(escortRespVO.getHandler3Id())){
                handlerIdSet.add(escortRespVO.getHandler3Id());
            }
        }
        List<String> handlerIdList = handlerIdSet.stream().collect(Collectors.toList());

        LambdaQueryWrapper<CasePersonnelDO> casePersonnelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        casePersonnelLambdaQueryWrapper.select(CasePersonnelDO::getId,CasePersonnelDO::getXm,CasePersonnelDO::getBadwmc)
                .in(CasePersonnelDO::getId,handlerIdList);
        List<CasePersonnelDO> casePersonnelDOList = casePersonnelService.list(casePersonnelLambdaQueryWrapper);

        Map<String,CasePersonnelDO> casePersonnelMap = new HashMap<>();
        casePersonnelDOList.forEach(x->{casePersonnelMap.put(x.getId(),x);});

        for(EscortRespVO escortRespVO:escortRespVOList){
            StringBuilder handlerXm = new StringBuilder();
            Set<String> badwmcSet = new HashSet<>();
            if(casePersonnelMap.containsKey(escortRespVO.getHandler1Id())){
                handlerXm.append(casePersonnelMap.get(escortRespVO.getHandler1Id()).getXm());
                badwmcSet.add(casePersonnelMap.get(escortRespVO.getHandler1Id()).getBadwmc());
            }
            if(casePersonnelMap.containsKey(escortRespVO.getHandler2Id())){
                handlerXm.append("、");
                handlerXm.append(casePersonnelMap.get(escortRespVO.getHandler2Id()).getXm());
                badwmcSet.add(casePersonnelMap.get(escortRespVO.getHandler2Id()).getBadwmc());
            }
            if(ObjectUtil.isNotEmpty(escortRespVO.getHandler3Id())){
                if(casePersonnelMap.containsKey(escortRespVO.getHandler2Id())){
                    handlerXm.append("、");
                    handlerXm.append(casePersonnelMap.get(escortRespVO.getHandler3Id()).getXm());
                    badwmcSet.add(casePersonnelMap.get(escortRespVO.getHandler3Id()).getBadwmc());
                }
            }
            escortRespVO.setHandlerXm(handlerXm.toString());
            escortRespVO.setBadwmc(String.join("、",badwmcSet));
        }

        return new PageResult<>(escortRespVOList,escortDOPage.getTotal());
    }
}
