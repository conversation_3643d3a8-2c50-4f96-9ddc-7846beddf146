package com.rs.module.acp.controller.admin.gj;

import com.rs.module.acp.controller.admin.gj.vo.confinement.*;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsACPSaveVO;
import com.rs.module.acp.util.ConfinementUtil;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomRespVO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.confinement.ConfinementRegDO;
import com.rs.module.acp.service.gj.confinement.ConfinementRegService;

@Api(tags = "实战平台-管教业务-禁闭登记")
@RestController
@RequestMapping("/acp/gj/confinementReg")
@Validated
public class ConfinementRegController {

    @Resource
    private ConfinementRegService confinementRegService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-禁闭登记")
    public CommonResult<String> createConfinementReg(@Valid @RequestBody ConfinementRegSaveReqVO createReqVO) {
        try  {
            return success(confinementRegService.createConfinementReg(createReqVO));
        } catch (Exception e) {
            return CommonResult.error(e.getMessage());
        }
    }
    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-禁闭登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteConfinementReg(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            confinementRegService.deleteConfinementReg(id);
        }
        return success(true);
    }
    @PostMapping("/approve")
    @ApiOperation(value = "领导审批")
    public CommonResult<Boolean> approve(@Valid @RequestBody ConfinementRegApproveVO approveReqVO) {
        try  {
            return success(confinementRegService.approve(approveReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }
/*
    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-禁闭登记")
    public CommonResult<Boolean> updateConfinementReg(@Valid @RequestBody ConfinementRegSaveReqVO updateReqVO) {
        confinementRegService.updateConfinementReg(updateReqVO);
        return success(true);
    }



    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-禁闭登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ConfinementRegRespVO> getConfinementReg(@RequestParam("id") String id) {
        ConfinementRegDO confinementReg = confinementRegService.getConfinementReg(id);
        return success(BeanUtils.toBean(confinementReg, ConfinementRegRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-禁闭登记分页")
    public CommonResult<PageResult<ConfinementRegRespVO>> getConfinementRegPage(@Valid @RequestBody ConfinementRegPageReqVO pageReqVO) {
        PageResult<ConfinementRegDO> pageResult = confinementRegService.getConfinementRegPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ConfinementRegRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-禁闭登记列表")
    public CommonResult<List<ConfinementRegRespVO>> getConfinementRegList(@Valid @RequestBody ConfinementRegListReqVO listReqVO) {
        List<ConfinementRegDO> list = confinementRegService.getConfinementRegList(listReqVO);
        return success(BeanUtils.toBean(list, ConfinementRegRespVO.class));
    }*/
    @PostMapping("/getConfinementRoomList")
    @ApiOperation(value = "获得实战平台-管教业务-禁闭监室选择")
    public CommonResult<List<AreaPrisonRoomRespVO>> getConfinementRoomList(String nowRoomId) {
        List<AreaPrisonRoomDO> list = confinementRegService.getConfinementRoomList(nowRoomId);
        return success(BeanUtils.toBean(list, AreaPrisonRoomRespVO.class));
    }
    @PostMapping("/saveInOutRecords")
    @ApiOperation(value = "带入带出登记")
    public CommonResult<Boolean> saveInOutRecords(@Valid @RequestBody InOutRecordsACPSaveVO respVO) {
        try {
            return success(confinementRegService.saveInOutRecords(respVO));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
    @PostMapping("/getJqryConfinementHistory")
    @ApiOperation(value = "获得实战平台-管教业务-当前人员禁闭登记列表")
    public CommonResult<List<ConfinementRegRespVO>> getJqryConfinementHistory(@RequestParam String jgrybm) {
        ConfinementRegListReqVO listReqVO = new ConfinementRegListReqVO();
        listReqVO.setJgrybm(jgrybm);
        listReqVO.setStatus(ConfinementUtil.ApprovalStatusEnum.RELEASED.getCode());
        List<ConfinementRegDO> list = confinementRegService.getConfinementRegList(listReqVO);
        return success(BeanUtils.toBean(list, ConfinementRegRespVO.class));
    }

    @PostMapping("/getNotAllowedRecords")
    @ApiOperation(value = "获得实战平台-管教业务-禁闭登记不允许状态记录")
    public CommonResult<List<ConfinementRegRespVO>> getNotAllowedRecords(String jgrybm) {
        //该人员是否是处于【禁闭中】【禁闭待审核】【待解除】状态下，若是，则进行对应状态提示，不允许选择该人员
        List<ConfinementRegDO> list = confinementRegService.getNotAllowedRecords(jgrybm);
        return success(BeanUtils.toBean(list, ConfinementRegRespVO.class));
    }
}
