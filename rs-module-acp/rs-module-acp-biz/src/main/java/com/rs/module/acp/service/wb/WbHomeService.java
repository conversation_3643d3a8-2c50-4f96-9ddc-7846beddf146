package com.rs.module.acp.service.wb;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

public interface WbHomeService {

    /**
     * 获取今日会见动态
     * @return
     */
    List<JSONObject> getToDayMeetingUpdates();


    /**
     * 获取会见室状态
     * @return
     */
    JSONObject getMeetingRoomStatus();


    /**
     * 提押岗大屏-会见数量统计
     * @return
     */
    List<JSONObject> getMeetingMumStatistics();

    /**
     * 获取今日会见列表
     * @return
     */
    List<JSONObject> getToDayMeetingList(String tczt,String ywlx);
}
