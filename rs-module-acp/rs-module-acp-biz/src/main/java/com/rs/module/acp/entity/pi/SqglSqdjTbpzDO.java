package com.rs.module.acp.entity.pi;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-巡视管控-所情管理-所情登记-同步配置 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pi_sqgl_sqdj_tbpz")
@KeySequence("acp_pi_sqgl_sqdj_tbpz_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pi_sqgl_sqdj_tbpz")
public class SqglSqdjTbpzDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 同步表名称
     */
    private String storageTable;
    /**
     * 同步最大时间
     */
    private Date syncCurrentTime;
    /**
     * 同步sql
     */
    private String syncSql;
    /**
     * 增量时间字段
     */
    private String timeField;
}
