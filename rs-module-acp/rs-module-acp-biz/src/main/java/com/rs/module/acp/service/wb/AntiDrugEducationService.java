package com.rs.module.acp.service.wb;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.AntiDrugEducationDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-窗口业务-禁毒教育 Service 接口
 *
 * <AUTHOR>
 */
public interface AntiDrugEducationService extends IBaseService<AntiDrugEducationDO>{

    /**
     * 创建实战平台-窗口业务-禁毒教育
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createAntiDrugEducation(AntiDrugEducationSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-禁毒教育
     *
     * @param updateReqVO 更新信息
     */
    void updateAntiDrugEducation(AntiDrugEducationSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-禁毒教育
     *
     * @param id 编号
     */
    void deleteAntiDrugEducation(String id);

    /**
     * 获得实战平台-窗口业务-禁毒教育
     *
     * @param id 编号
     * @return 实战平台-窗口业务-禁毒教育
     */
    AntiDrugEducationDO getAntiDrugEducation(String id);

}
