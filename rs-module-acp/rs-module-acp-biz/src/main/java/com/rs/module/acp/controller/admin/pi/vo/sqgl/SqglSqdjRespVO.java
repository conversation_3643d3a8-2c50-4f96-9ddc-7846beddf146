package com.rs.module.acp.controller.admin.pi.vo.sqgl;
import com.alibaba.fastjson.JSONObject;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-所情登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglSqdjRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("所情编号")
    private String eventCode;
    @ApiModelProperty("所情等级，字典：ZD_JJKS_SQDJ")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JJKS_SQDJ")
    private String eventLevel;
    @ApiModelProperty("所情地点")
    private String areaId;
    @ApiModelProperty("所情地点名称")
    private String areaName;
    @ApiModelProperty("发生时间")
    private Date happenTime;
    @ApiModelProperty("所情模板配置ID")
    private String eventTemplateId;
    @ApiModelProperty("所情名称")
    private String eventName;
    @ApiModelProperty("所情类型")
    private String eventType;
    @ApiModelProperty("所情开始时间（精确时间-开始）")
    private Date eventStartTime;
    @ApiModelProperty("所情结束时间（精确时间-结束）")
    private Date eventEndTime;
    @ApiModelProperty("所情详情")
    private String eventDetails;
    @ApiModelProperty("状态，字典：ZD_JJKS_SQYWZT")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JJKS_SQYWZT")
    private String status;
    @ApiModelProperty("所情来源，字典：ZD_JJKS_SQLY")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JJKS_SQLY")
    private String eventSrc;

    @ApiModelProperty("报警人员列表")
    private List<SqglSqdjGlrySaveReqVO> bjryList;

    @ApiModelProperty("在押人员列表")
    private List<SqglSqdjGlrySaveReqVO> jgryList;

    @ApiModelProperty("工作人员列表")
    private List<SqglSqdjGlrySaveReqVO> gzryList;

    @ApiModelProperty("外来人员列表")
    private List<SqglSqdjGlrySaveReqVO> wlryList;

    @ApiModelProperty("节点办结情况(按顺序排序)")
    private List<JSONObject> jdList;

    @ApiModelProperty("业务轨迹")
    private List<SqglSqczRespVO> trajectoryList;

    @ApiModelProperty("视频分析报警的监控通道ID")
    private String channelId;

    @ApiModelProperty("预警表ID")
    private String alarmEventId;
}
