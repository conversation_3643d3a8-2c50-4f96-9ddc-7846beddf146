package com.rs.module.acp.service.wb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.module.acp.service.gj.samecasemanage.SameCaseManageService;
import com.rs.module.acp.service.gj.samecasemanage.bo.SameCaseManageBO;
import com.rs.util.DicUtils;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.entity.wb.*;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.base.service.pm.PrisonerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.LawyerMeetingDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-律师会见登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LawyerMeetingServiceImpl extends BaseServiceImpl<LawyerMeetingDao, LawyerMeetingDO> implements LawyerMeetingService {

    @Resource
    private LawyerMeetingDao lawyerMeetingDao;
    @Autowired
    private LawyerMeetingConfigService lawyerMeetingConfigService;
    @Autowired
    private LawyerMeetingCompanionService lawyerMeetingCompanionService;
    @Autowired
    private AreaService areaService;
    @Autowired
    private WbCommonService wbCommonService;

    @Autowired
    private PrisonerService prisonerService;

    @Autowired
    private LawyerPrisonerService lawyerPrisonerService;

    @Autowired
    private SameCaseManageService sameCaseManageService;

    @Autowired
    private LawyerService lawyerService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createLawyerMeeting(LawyerMeetingSaveReqVO createReqVO) {

        //这里需要再次判断下现场会见，选择的时间号段是否有超过时间限制
        if ("0".equals(createReqVO.getMeetingMethod())) {
            LambdaQueryWrapper<LawyerMeetingDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(LawyerMeetingDO::getAppointmentTimeSlot).eq(LawyerMeetingDO::getAppointmentTime, createReqVO.getAppointmentTime());
            List<LawyerMeetingDO> lawyerMeetingDOList = list(lambdaQueryWrapper);
            if (CollectionUtil.isNotEmpty(lawyerMeetingDOList)) {
                LawyerMeetingConfigDO configDO = getLawyerMeetingConfig();
                List<JSONObject> applyMeetingTimeSlotList = JSONArray.parseArray(configDO.getApplyMeetingTimeSlot(), JSONObject.class);
                String appointmentTimeStr = DateUtil.format(createReqVO.getAppointmentTime(), "yyyy-MM-dd");
                for (JSONObject jsonObject : applyMeetingTimeSlotList) {
                    if (appointmentTimeStr.equals(jsonObject.getString("time"))) {
                        int remainingNumber = judgeIdleTime(lawyerMeetingDOList, jsonObject.getString("time"), jsonObject.getInteger("number"));
                        if (remainingNumber <= 0) {
                            throw new ServerException("该号段已约满，请刷新页面后重新选择！");
                        }
                    }
                }
            }

            //这里拆分下appointment_time_slot字段，与appointment_time组合，将开始时间、结束时间存储到apply_meeting_start_time、apply_meeting_end_time
            StringBuilder starttimeStr = new StringBuilder();
            StringBuilder endtimeStr = new StringBuilder();
            String appointmentTime = DateUtil.format(createReqVO.getAppointmentTime(), "yyyy-MM-dd");
            starttimeStr.append(appointmentTime).append(" ");
            endtimeStr.append(appointmentTime).append(" ");
            String[] solt = createReqVO.getAppointmentTimeSlot().split("~");
            starttimeStr.append(solt[0]).append(":00");
            endtimeStr.append(solt[1]).append(":00");
            createReqVO.setApplyMeetingStartTime(DateUtil.parse(starttimeStr.toString(), "yyyy-MM-dd HH:mm:ss"));
            createReqVO.setApplyMeetingEndTime(DateUtil.parse(endtimeStr.toString(), "yyyy-MM-dd HH:mm:ss"));
        }

        if (ObjectUtil.isNotEmpty(createReqVO.getApprovalAttachmentPath())) {
            String approvalAttachmentPath = wbCommonService.saveFile(null, createReqVO.getApprovalAttachmentPath());
            createReqVO.setApprovalAttachmentPath(approvalAttachmentPath);
        }
        // 插入
        LawyerMeetingDO lawyerMeeting = BeanUtils.toBean(createReqVO, LawyerMeetingDO.class);

        if(CollectionUtil.isNotEmpty(createReqVO.getLawyerList())){
            List<LawyerMeetingLawyerSaveReqVO> lawyerList = createReqVO.getLawyerList();

            List<String> lawyerIdList = lawyerList.stream().map(LawyerMeetingLawyerSaveReqVO::getId).collect(Collectors.toList());

//            LambdaQueryWrapper<LawyerPrisonerDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//            lambdaQueryWrapper.select(LawyerPrisonerDO::getLawyerId,LawyerPrisonerDO::getPowerOfAttorneyUrl);
//            lambdaQueryWrapper.in(LawyerPrisonerDO::getLawyerId,lawyerIdList).eq(LawyerPrisonerDO::getJgrybm,createReqVO.getJgrybm())
//                    .eq(LawyerPrisonerDO::getStatus,"1");

            List<JSONObject> lawyerPrisonerDOList = lawyerPrisonerService.getLawyerJsonList(createReqVO.getJgrybm(),lawyerIdList);

//            List<LawyerPrisonerDO> lawyerPrisonerDOList = lawyerPrisonerService.list(lambdaQueryWrapper);

            Map<String,JSONObject> lawyerPrisonerMap = new HashMap<>();
            for(JSONObject lawyerPrisonerJson:lawyerPrisonerDOList){
                lawyerPrisonerMap.put(lawyerPrisonerJson.getString("lawyer_id"),lawyerPrisonerJson);
            }

            lawyerMeeting.setLawyer1Id(lawyerList.get(0).getId());
            lawyerMeeting.setLawyer1Name(lawyerList.get(0).getXm());
            lawyerMeeting.setLawyer1Gender(lawyerList.get(0).getXb());
            lawyerMeeting.setLawyer1Type(lawyerList.get(0).getLslx());
            lawyerMeeting.setLawyer1IdNumber(lawyerList.get(0).getZjhm());
            lawyerMeeting.setLawyer1Contact(lawyerList.get(0).getLxfs());
            lawyerMeeting.setLawyer1PracticeLicenseNumber(lawyerList.get(0).getZyzhm());
            lawyerMeeting.setLawyer1EntrustStage(lawyerList.get(0).getEntrustStage());
            lawyerMeeting.setLawyer1Firm(lawyerList.get(0).getLsdw());
            lawyerMeeting.setLawyer1EntrustType(lawyerList.get(0).getEntrustType());
            lawyerMeeting.setLawyer1PrincipalName(lawyerList.get(0).getPrincipal());
            lawyerMeeting.setLawyer1PrincipalIdNumber(lawyerList.get(0).getPrincipalId());
            lawyerMeeting.setLawyer1PowerOfAttorneyType(lawyerList.get(0).getPowerOfAttorneyType());
            lawyerMeeting.setLawyer1LetterNumber(lawyerList.get(0).getLetterNumber());
            lawyerMeeting.setLawyer1ImageUrl(lawyerList.get(0).getZpUrl());

            if(ObjectUtil.isNotEmpty(lawyerPrisonerMap.get(lawyerList.get(0).getId()))){
                lawyerMeeting.setLawyer1PowerOfAttorneyPath(lawyerPrisonerMap.get(lawyerList.get(0).getId()).getString("power_of_attorney_url"));
            }

            if(ObjectUtil.isNotEmpty(lawyerPrisonerMap.get(lawyerList.get(0).getId()))){
                lawyerMeeting.setLawyer1PracticeCertificatePa(lawyerPrisonerMap.get(lawyerList.get(0).getId()).getString("zyzs_url"));
            }

            if(lawyerList.size() > 1){
                //第二位律师
                lawyerMeeting.setLawyer2Id(lawyerList.get(1).getId());
                lawyerMeeting.setLawyer2Name(lawyerList.get(1).getXm());
                lawyerMeeting.setLawyer2Gender(lawyerList.get(1).getXb());
                lawyerMeeting.setLawyer2Type(lawyerList.get(1).getLslx());
                lawyerMeeting.setLawyer2IdNumber(lawyerList.get(1).getZjhm());
                lawyerMeeting.setLawyer2Contact(lawyerList.get(1).getLxfs());
                lawyerMeeting.setLawyer2PracticeLicenseNumber(lawyerList.get(1).getZyzhm());
                lawyerMeeting.setLawyer2EntrustStage(lawyerList.get(1).getEntrustStage());
                lawyerMeeting.setLawyer2Firm(lawyerList.get(1).getLsdw());
                lawyerMeeting.setLawyer2EntrustType(lawyerList.get(1).getEntrustType());
                lawyerMeeting.setLawyer2PrincipalName(lawyerList.get(1).getPrincipal());
                lawyerMeeting.setLawyer2PrincipalIdNumber(lawyerList.get(1).getPrincipalId());
                lawyerMeeting.setLawyer2PowerOfAttorneyType(lawyerList.get(1).getPowerOfAttorneyType());
                lawyerMeeting.setLawyer2LetterNumber(lawyerList.get(1).getLetterNumber());
                lawyerMeeting.setLawyer2ImageUrl(lawyerList.get(1).getZpUrl());
                if(ObjectUtil.isNotEmpty(lawyerPrisonerMap.get(lawyerList.get(1).getId()))) {
                    lawyerMeeting.setLawyer2PowerOfAttorneyPath(lawyerPrisonerMap.get(lawyerList.get(1).getId()).getString("power_of_attorney_url"));
                }
                if(ObjectUtil.isNotEmpty(lawyerPrisonerMap.get(lawyerList.get(1).getId()))){
                    lawyerMeeting.setLawyer2PracticeCertificatePa(lawyerPrisonerMap.get(lawyerList.get(1).getId()).getString("zyzs_url"));
                }
            }
            if(lawyerList.size() > 2){
                //第三位律师
                lawyerMeeting.setLawyer3Id(lawyerList.get(2).getId());
                lawyerMeeting.setLawyer3Name(lawyerList.get(2).getXm());
                lawyerMeeting.setLawyer3Gender(lawyerList.get(2).getXb());
                lawyerMeeting.setLawyer3Type(lawyerList.get(2).getLslx());
                lawyerMeeting.setLawyer3IdNumber(lawyerList.get(2).getZjhm());
                lawyerMeeting.setLawyer3Contact(lawyerList.get(2).getLxfs());
                lawyerMeeting.setLawyer3PracticeLicenseNumber(lawyerList.get(2).getZyzhm());
                lawyerMeeting.setLawyer3EntrustStage(lawyerList.get(2).getEntrustStage());
                lawyerMeeting.setLawyer3Firm(lawyerList.get(2).getLsdw());
                lawyerMeeting.setLawyer3EntrustType(lawyerList.get(2).getEntrustType());
                lawyerMeeting.setLawyer3PrincipalName(lawyerList.get(2).getPrincipal());
                lawyerMeeting.setLawyer3PrincipalIdNumber(lawyerList.get(2).getPrincipalId());
                lawyerMeeting.setLawyer3PowerOfAttorneyType(lawyerList.get(2).getPowerOfAttorneyType());
                lawyerMeeting.setLawyer3LetterNumber(lawyerList.get(2).getLetterNumber());
                lawyerMeeting.setLawyer3ImageUrl(lawyerList.get(2).getZpUrl());
                if(ObjectUtil.isNotEmpty(lawyerPrisonerMap.get(lawyerList.get(2).getId()))){
                    lawyerMeeting.setLawyer3PowerOfAttorneyPath(lawyerPrisonerMap.get(lawyerList.get(2).getId()).getString("power_of_attorney_url"));
                }
                if(ObjectUtil.isNotEmpty(lawyerPrisonerMap.get(lawyerList.get(2).getId()))){
                    lawyerMeeting.setLawyer3PracticeCertificatePa(lawyerPrisonerMap.get(lawyerList.get(2).getId()).getString("zyzs_url"));
                }
            }

            if(lawyerList.size() > 3){
                //第四位律师
                lawyerMeeting.setLawyer4Id(lawyerList.get(3).getId());
                lawyerMeeting.setLawyer4Name(lawyerList.get(3).getXm());
                lawyerMeeting.setLawyer4Gender(lawyerList.get(3).getXb());
                lawyerMeeting.setLawyer4Type(lawyerList.get(3).getLslx());
                lawyerMeeting.setLawyer4IdNumber(lawyerList.get(3).getZjhm());
                lawyerMeeting.setLawyer4Contact(lawyerList.get(3).getLxfs());
                lawyerMeeting.setLawyer4PracticeLicenseNumber(lawyerList.get(3).getZyzhm());
                lawyerMeeting.setLawyer4EntrustStage(lawyerList.get(3).getEntrustStage());
                lawyerMeeting.setLawyer4Firm(lawyerList.get(3).getLsdw());
                lawyerMeeting.setLawyer4EntrustType(lawyerList.get(3).getEntrustType());
                lawyerMeeting.setLawyer4PrincipalName(lawyerList.get(3).getPrincipal());
                lawyerMeeting.setLawyer4PrincipalIdNumber(lawyerList.get(3).getPrincipalId());
                lawyerMeeting.setLawyer4PowerOfAttorneyType(lawyerList.get(3).getPowerOfAttorneyType());
                lawyerMeeting.setLawyer4LetterNumber(lawyerList.get(3).getLetterNumber());
                lawyerMeeting.setLawyer4ImageUrl(lawyerList.get(3).getZpUrl());
                if(ObjectUtil.isNotEmpty(lawyerPrisonerMap.get(lawyerList.get(3).getId()))){
                    lawyerMeeting.setLawyer4PowerOfAttorneyPath(lawyerPrisonerMap.get(lawyerList.get(3).getId()).getString("power_of_attorney_url"));
                }
                if(ObjectUtil.isNotEmpty(lawyerPrisonerMap.get(lawyerList.get(3).getId()))){
                    lawyerMeeting.setLawyer4PracticeCertificatePa(lawyerPrisonerMap.get(lawyerList.get(3).getId()).getString("zyzs_url"));
                }
            }

        }

        if ("2".equals(lawyerMeeting.getMeetingMethod())) {
            lawyerMeeting.setStatus("1");
        } else {
            lawyerMeeting.setStatus("0");
        }
        lawyerMeeting.setId(StringUtil.getGuid32());
        lawyerMeeting.setApplyMeetingStartTime(createReqVO.getApplyMeetingStartTime());
        lawyerMeeting.setApplyMeetingEndTime(createReqVO.getApplyMeetingEndTime());

        //保存同行人信息
        lawyerMeetingCompanionService.saveLawyerMeetingCompanionList(lawyerMeeting.getId(), createReqVO.getCompanionList());
        lawyerMeetingDao.insert(lawyerMeeting);

        wbCommonService.registrationReminder(JSONObject.parseObject(JSON.toJSONString(lawyerMeeting)), WbConstants.BUSINESS_TYPE_LAWYER_MEETING);
        wbCommonService.audioBroadcast(WbConstants.BUSINESS_TYPE_LAWYER_MEETING,JSONObject.parseObject(JSON.toJSONString(lawyerMeeting)));

        LawyerMeetingDO lawyerMeetingDO = getById(lawyerMeeting.getId());
        WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(lawyerMeeting,WbInspectionResultsSaveReqVO.class);
        resultsSaveReqVO.setJgrybm(lawyerMeetingDO.getJgrybm());
        resultsSaveReqVO.setApplMeetingTime(lawyerMeetingDO.getApplyMeetingStartTime());
        wbCommonService.saveInOutRecord(resultsSaveReqVO,"04","out","0",false);

        // 返回
        return lawyerMeeting.getId();
    }

    @Override
    public void updateLawyerMeeting(LawyerMeetingSaveReqVO updateReqVO) {
        // 校验存在
        validateLawyerMeetingExists(updateReqVO.getId());
        // 更新
        LawyerMeetingDO updateObj = BeanUtils.toBean(updateReqVO, LawyerMeetingDO.class);
        lawyerMeetingDao.updateById(updateObj);
    }

    @Override
    public void deleteLawyerMeeting(String id) {
        // 校验存在
        validateLawyerMeetingExists(id);
        // 删除
        lawyerMeetingDao.deleteById(id);
    }

    private void validateLawyerMeetingExists(String id) {
        if (lawyerMeetingDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-律师会见登记数据不存在");
        }
    }

    @Override
    public LawyerMeetingDO getLawyerMeeting(String id) {
        LawyerMeetingDO lawyerMeetingDO = lawyerMeetingDao.selectById(id);
        if (ObjectUtil.isNotEmpty(lawyerMeetingDO.getApprovalAttachmentPath())) {
            lawyerMeetingDO.setApprovalAttachmentPath(wbCommonService.getFile(lawyerMeetingDO.getApprovalAttachmentPath()));
        }
        return lawyerMeetingDO;
    }

    @Override
    public PageResult<LawyerMeetingDO> getLawyerMeetingPage(LawyerMeetingPageReqVO pageReqVO) {
        PageResult<LawyerMeetingDO> s = lawyerMeetingDao.selectPage(pageReqVO);
        return lawyerMeetingDao.selectPage(pageReqVO);
    }

    @Override
    public List<LawyerMeetingDO> getLawyerMeetingList(LawyerMeetingListReqVO listReqVO) {
        return lawyerMeetingDao.selectList(listReqVO);
    }

    @Override
    public JSONObject getMeetingConfig(String applData) {
        LawyerMeetingConfigDO configDO = getLawyerMeetingConfig();
        JSONObject meetingConfigJson = new JSONObject();
        List<String> meetingMethodStrList = Arrays.asList(configDO.getMeetingMethod().split(","));
        List<JSONObject> meetingMethodJsonList = new ArrayList<>();
        for (String meetingMethod : meetingMethodStrList) {
            JSONObject meetingMethJson = new JSONObject();
            meetingMethJson.put("meetingMethod", meetingMethod);
            meetingMethJson.put("meetingMethodName", DicUtils.translate("ZD_WB_LSHJFS", meetingMethod));
            meetingMethodJsonList.add(meetingMethJson);
        }
        meetingConfigJson.put("meetingMethodList", meetingMethodJsonList);

        if (ObjectUtil.isNotEmpty(configDO.getApplyMeetingTimeSlot())) {
            List<JSONObject> applyMeetingTimeSlotList = JSONArray.parseArray(configDO.getApplyMeetingTimeSlot(), JSONObject.class);

            LambdaQueryWrapper<LawyerMeetingDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(LawyerMeetingDO::getAppointmentTimeSlot).eq(LawyerMeetingDO::getAppointmentTime, DateUtil.parse(applData,"yyyy-MM-dd"));
            //这里过滤下可选的
            List<LawyerMeetingDO> lawyerMeetingDOList = list(lambdaQueryWrapper);
            if (CollectionUtil.isEmpty(lawyerMeetingDOList)) {
                for (JSONObject jsonObject : applyMeetingTimeSlotList) {
                    StringBuilder timeTag = new StringBuilder();
                    timeTag.append(jsonObject.getString("time")).append("（");
                    timeTag.append("余").append(jsonObject.getInteger("number")).append("/");
                    timeTag.append(jsonObject.getInteger("number")).append("）");
                    jsonObject.put("timeTag", timeTag.toString());
                    jsonObject.put("select", true);
                }
                meetingConfigJson.put("applyMeetingTimeSlotList", applyMeetingTimeSlotList);
            } else {
                List<JSONObject> resList = new ArrayList<>();
                for (JSONObject jsonObject : applyMeetingTimeSlotList) {
                    StringBuilder timeTag = new StringBuilder();
                    int remainingNumber = judgeIdleTime(lawyerMeetingDOList, jsonObject.getString("time"), jsonObject.getInteger("number"));
                    if (remainingNumber > 0) {
                        timeTag.append(jsonObject.getString("time")).append("（");
                        timeTag.append("余").append(remainingNumber).append("/");
                        timeTag.append(jsonObject.getInteger("number")).append("）");
                        jsonObject.put("select", true);
                    } else {
                        timeTag.append("（已约满）");
                        jsonObject.put("select", false);
                    }
                    jsonObject.put("timeTag", timeTag.toString());
                    resList.add(jsonObject);
                }
                meetingConfigJson.put("applyMeetingTimeSlotList", resList);
            }

        } else {
            meetingConfigJson.put("applyMeetingTimeSlotList", new ArrayList<>());
        }

        return meetingConfigJson;
    }

    /**
     * 判断号的数量
     *
     * @param meetingDOList
     * @param judgeTime
     * @param limitNumber
     * @return 返回数量
     */
    private int judgeIdleTime(List<LawyerMeetingDO> meetingDOList, String judgeTime, Integer limitNumber) {
        int usedNumber = 0;
        for (LawyerMeetingDO lawyerMeetingDO : meetingDOList) {
            if (judgeTime.equals(lawyerMeetingDO.getAppointmentTimeSlot())) {
                usedNumber += 1;
            }
        }
        return limitNumber - usedNumber;
    }

    private LawyerMeetingConfigDO getLawyerMeetingConfig() {
        LawyerMeetingConfigDO configDO = new LawyerMeetingConfigDO();
        configDO = lawyerMeetingConfigService.getOne(new LambdaQueryWrapper<LawyerMeetingConfigDO>()
                .eq(LawyerMeetingConfigDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode()));
        if (ObjectUtil.isEmpty(configDO)) {
            //如果没有，则查询默认的配置
            configDO = lawyerMeetingConfigService.getOne(new LambdaQueryWrapper<LawyerMeetingConfigDO>()
                    .eq(LawyerMeetingConfigDO::getOrgCode, "default"));
            if (ObjectUtil.isEmpty(configDO)) {
                throw new ServerException("会见方式未配置，请联系管理员进行配置！");
            }
        }
        return configDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean signIn(String id, String checkInTime) {
//        LawyerMeetingDO lawyerMeetingDO = getById(id);
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        LambdaUpdateWrapper<LawyerMeetingDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(LawyerMeetingDO::getId, id)
                .set(LawyerMeetingDO::getCheckInTime, DateUtil.parse(checkInTime, "yyyy-MM-dd HH:mm:ss"))
                .set(LawyerMeetingDO::getCheckInPolice, sessionUser.getName())
                .set(LawyerMeetingDO::getCheckInPoliceSfzh, sessionUser.getIdCard()).set(LawyerMeetingDO::getStatus, "1");
        return update(lambdaUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean allocationRoom(String id, String roomId) {

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        return update(new LambdaUpdateWrapper<LawyerMeetingDO>().eq(LawyerMeetingDO::getId, id)
                .set(LawyerMeetingDO::getRoomId, roomId)
                .set(LawyerMeetingDO::getStatus, "2")
                .set(LawyerMeetingDO::getAssignmentPolice, sessionUser.getName())
                .set(LawyerMeetingDO::getAssignmentPoliceSfzh, sessionUser.getIdCard())
                .set(LawyerMeetingDO::getAssignmentRoomTime, new Date()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean escortingInspect(LawyerMeetingSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        LambdaUpdateWrapper<LawyerMeetingDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(LawyerMeetingDO::getId, updateReqVO.getId())
                .set(LawyerMeetingDO::getEscortingPolice, updateReqVO.getEscortingPolice())
                .set(LawyerMeetingDO::getEscortingPoliceSfzh, updateReqVO.getEscortingPoliceSfzh())
                .set(LawyerMeetingDO::getEscortingTime, updateReqVO.getEscortingTime())
                .set(LawyerMeetingDO::getInspectionResult, updateReqVO.getInspectionResult())
                .set(LawyerMeetingDO::getProhibitedItems, updateReqVO.getProhibitedItems())
                .set(LawyerMeetingDO::getPhysicalExam, updateReqVO.getPhysicalExam())
                .set(LawyerMeetingDO::getAbnormalSituations, updateReqVO.getAbnormalSituations())
                .set(LawyerMeetingDO::getInspectionTime, updateReqVO.getInspectionTime())
                .set(LawyerMeetingDO::getInspector, updateReqVO.getInspector())
                .set(LawyerMeetingDO::getInspectorSfzh, updateReqVO.getInspectorSfzh())
                .set(LawyerMeetingDO::getMeetingStartTime, updateReqVO.getMeetingStartTime())
                .set(LawyerMeetingDO::getStatus, "3");
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorSfzh())){
            lambdaUpdateWrapper.set(LawyerMeetingDO::getEscortingOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(LawyerMeetingDO::getEscortingOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorTime())){
            lambdaUpdateWrapper.set(LawyerMeetingDO::getEscortingOperatorTime,new Date());
        }
        if("0".equals(updateReqVO.getDataSources())){
            LawyerMeetingDO lawyerMeetingDO = getById(updateReqVO.getId());
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(lawyerMeetingDO.getJgrybm());
            resultsSaveReqVO.setApplMeetingTime(lawyerMeetingDO.getApplyMeetingEndTime());
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"04","out","3",false);
        }
        return update(lambdaUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean returnInspection(LawyerMeetingSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        LambdaUpdateWrapper<LawyerMeetingDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(LawyerMeetingDO::getId, updateReqVO.getId())
                .set(LawyerMeetingDO::getMeetingEndTime, updateReqVO.getMeetingEndTime())
                .set(LawyerMeetingDO::getReturnInspectorSfzh, updateReqVO.getReturnInspectorSfzh())
                .set(LawyerMeetingDO::getReturnInspector, updateReqVO.getReturnInspector())
                .set(LawyerMeetingDO::getReturnInspectionTime, updateReqVO.getReturnInspectionTime())
                .set(LawyerMeetingDO::getReturnInspectionResult, updateReqVO.getReturnInspectionResult())
                .set(LawyerMeetingDO::getReturnProhibitedItems, updateReqVO.getReturnProhibitedItems())
                .set(LawyerMeetingDO::getReturnPhysicalExam, updateReqVO.getReturnPhysicalExam())
                .set(LawyerMeetingDO::getReturnAbnormalSituations, updateReqVO.getReturnAbnormalSituations())
                .set(LawyerMeetingDO::getReturnTime, updateReqVO.getReturnTime())
                .set(LawyerMeetingDO::getReturnPolice, updateReqVO.getReturnPolice())
                .set(LawyerMeetingDO::getReturnPoliceSfzh, updateReqVO.getReturnPoliceSfzh())
                .set(LawyerMeetingDO::getStatus, "4");
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorSfzh())){
            lambdaUpdateWrapper.set(LawyerMeetingDO::getReturnOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(LawyerMeetingDO::getReturnOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorTime())){
            lambdaUpdateWrapper.set(LawyerMeetingDO::getReturnOperatorTime,new Date());
        }
        if("0".equals(updateReqVO.getDataSources())){
            LawyerMeetingDO lawyerMeetingDO = getById(updateReqVO.getId());
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(lawyerMeetingDO.getJgrybm());
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"04","in","3",false);
        }
        return update(lambdaUpdateWrapper);
    }

    @Override
    public JSONObject sameCaseJudgment(String jgrybm, String lawyerIds) {

        JSONObject res = new JSONObject();
        res.put("prompt", false);
        res.put("proxy", false);
        //同案人员
//        List<JSONObject> prisonerList = lawyerMeetingDao.getPrisonerListByAjbh(ajbh, SessionUserUtil.getSessionUser().getOrgCode());

        PageResult<SameCaseManageBO> caseManageBOPageResult = sameCaseManageService.manageListPage(0,1000,jgrybm);

        if (CollectionUtil.isEmpty(caseManageBOPageResult.getList())) {
            return res;
        }

        //去重，排除掉 被查询人
        Map<String, SameCaseManageBO> prisonerMap = new HashMap<>();
        List<String> jgrybmList = new ArrayList<>();
        for (SameCaseManageBO sameCaseManageBO : caseManageBOPageResult.getList()) {
            if (!jgrybm.equals(sameCaseManageBO.getJgrybm())) {
                if (!prisonerMap.containsKey(sameCaseManageBO.getJgrybm())) {
                    jgrybmList.add(sameCaseManageBO.getJgrybm());
                }
                prisonerMap.put(sameCaseManageBO.getJgrybm(), sameCaseManageBO);
            }
        }

        if(CollectionUtil.isEmpty(jgrybmList)){
            return res;
        }

        Map<String, String> lawyerIdMap = new HashMap<>();
        List<String> lawyerIdList = Arrays.asList(lawyerIds.split(","));
        lawyerIdList.forEach(x -> {
            lawyerIdMap.put(x, x);
        });

        promptSameCase(res,lawyerIdMap,prisonerMap,jgrybmList);

        proxySameCase(res,prisonerMap,lawyerIdList,jgrybmList);


        return res;
    }

    /**
     * 会见过同案人员
     * @param lawyerIdMap
     * @param jgrybmList
     * @return
     */
    private void promptSameCase(JSONObject res,Map<String, String> lawyerIdMap,  Map<String, SameCaseManageBO> prisonerMap,List<String> jgrybmList){

        LambdaQueryWrapper<LawyerMeetingDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(LawyerMeetingDO::getJgrybm, LawyerMeetingDO::getLawyer1Id, LawyerMeetingDO::getLawyer1Name,
                        LawyerMeetingDO::getLawyer2Id, LawyerMeetingDO::getLawyer2Name, LawyerMeetingDO::getLawyer3Id,
                        LawyerMeetingDO::getLawyer3Name,LawyerMeetingDO::getLawyer4Id,LawyerMeetingDO::getLawyer4Name)
                .in(LawyerMeetingDO::getJgrybm, jgrybmList);
        List<LawyerMeetingDO> lawyerMeetingDOList = list(lambdaQueryWrapper);
        if (CollectionUtil.isEmpty(lawyerMeetingDOList)) {
            return;
        }

        StringBuilder promptMsg = new StringBuilder();
        Map<String, String> executedMap = new HashMap<>();
        for (LawyerMeetingDO lawyerMeetingDO : lawyerMeetingDOList) {
            System.out.printf("律师ID："+lawyerMeetingDO.getLawyer1Id()+","+lawyerMeetingDO.getLawyer2Id()+","+lawyerMeetingDO.getLawyer3Id()+","+lawyerMeetingDO.getLawyer4Id());
            if (lawyerIdMap.containsKey(lawyerMeetingDO.getLawyer1Id())) {
                //避免重复提示，这里判断下
                if (!executedMap.containsKey(lawyerMeetingDO.getLawyer1Id() + lawyerMeetingDO.getJgrybm())) {
                    SameCaseManageBO sameCaseManageBO = prisonerMap.get(lawyerMeetingDO.getJgrybm());
                    promptMsg.append(lawyerMeetingDO.getLawyer1Name()).append("会见过同案人员（");
                    promptMsg.append(sameCaseManageBO.getXm()).append("），");
                    executedMap.put(lawyerMeetingDO.getLawyer1Id() + lawyerMeetingDO.getJgrybm(), "1");
                }
            }
            if (lawyerIdMap.containsKey(lawyerMeetingDO.getLawyer2Id())) {
                if (!executedMap.containsKey(lawyerMeetingDO.getLawyer2Id() + lawyerMeetingDO.getJgrybm())) {
                    SameCaseManageBO sameCaseManageBO = prisonerMap.get(lawyerMeetingDO.getJgrybm());
                    promptMsg.append(lawyerMeetingDO.getLawyer2Name()).append("会见过同案人员（");
                    promptMsg.append(sameCaseManageBO.getXm()).append("），");
                    executedMap.put(lawyerMeetingDO.getLawyer2Id() + lawyerMeetingDO.getJgrybm(), "1");
                }
            }
            if (lawyerIdMap.containsKey(lawyerMeetingDO.getLawyer3Id())) {
                if (!executedMap.containsKey(lawyerMeetingDO.getLawyer3Id() + lawyerMeetingDO.getJgrybm())) {
                    SameCaseManageBO sameCaseManageBO = prisonerMap.get(lawyerMeetingDO.getJgrybm());
                    promptMsg.append(lawyerMeetingDO.getLawyer3Name()).append("会见过同案人员（");
                    promptMsg.append(sameCaseManageBO.getXm()).append("），");
                    executedMap.put(lawyerMeetingDO.getLawyer3Id() + lawyerMeetingDO.getJgrybm(), "1");
                }
            }
            if (lawyerIdMap.containsKey(lawyerMeetingDO.getLawyer4Id())) {
                if (!executedMap.containsKey(lawyerMeetingDO.getLawyer4Id() + lawyerMeetingDO.getJgrybm())) {
                    SameCaseManageBO sameCaseManageBO = prisonerMap.get(lawyerMeetingDO.getJgrybm());
                    promptMsg.append(lawyerMeetingDO.getLawyer4Name()).append("会见过同案人员（");
                    promptMsg.append(sameCaseManageBO.getXm()).append("），");
                    executedMap.put(lawyerMeetingDO.getLawyer4Id() + lawyerMeetingDO.getJgrybm(), "1");
                }
            }
        }
        if (ObjectUtil.isNotEmpty(promptMsg.toString())) {
            promptMsg.append("是否允许继续办理？");
            res.put("prompt", true);
            res.put("promptMsg", promptMsg.toString());
        }

    }

    /**
     * 代理过同案人员
     * @param res
     * @param prisonerMap
     * @param lawyerIdList
     * @param jgrybmList
     */
    private void proxySameCase(JSONObject res, Map<String, SameCaseManageBO> prisonerMap,List<String> lawyerIdList,List<String> jgrybmList){
        //代理过同案人员
        LambdaQueryWrapper<LawyerPrisonerDO> lawyerPrisonerDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        lawyerPrisonerDOLambdaQueryWrapper.select(LawyerPrisonerDO::getJgrybm,LawyerPrisonerDO::getLawyerId,
                LawyerPrisonerDO::getStatus);
        lawyerPrisonerDOLambdaQueryWrapper.in(LawyerPrisonerDO::getJgrybm,jgrybmList)
                .in(LawyerPrisonerDO::getLawyerId,lawyerIdList)
                .eq(LawyerPrisonerDO::getStatus,"1");
        List<LawyerPrisonerDO> lawyerPrisonerDOList = lawyerPrisonerService.list(lawyerPrisonerDOLambdaQueryWrapper);
        if(CollectionUtil.isNotEmpty(lawyerPrisonerDOList)){
            //分组
            Map<String,List<LawyerPrisonerDO>> lawyerPrisonerDOMap = new HashMap<>();
            for(LawyerPrisonerDO lawyerPrisonerDO:lawyerPrisonerDOList){
                List<LawyerPrisonerDO> tempList = new ArrayList<>();
                if(lawyerPrisonerDOMap.containsKey(lawyerPrisonerDO.getLawyerId())){
                    tempList = lawyerPrisonerDOMap.get(lawyerPrisonerDO.getLawyerId());
                }
                tempList.add(lawyerPrisonerDO);
                lawyerPrisonerDOMap.put(lawyerPrisonerDO.getLawyerId(),tempList);
            }

            StringBuilder proxyMsg = new StringBuilder();
            for(Map.Entry<String,List<LawyerPrisonerDO>> pMap:lawyerPrisonerDOMap.entrySet()){
                List<LawyerPrisonerDO> tempList = pMap.getValue();
                if(CollectionUtil.isNotEmpty(tempList)){
                    //获取律师
                    LawyerDO lawyerDO = lawyerService.getLawyer(pMap.getKey());
                    proxyMsg.append(lawyerDO.getXm()).append("代理过同案人员（");
                    for(int i=0;i<tempList.size();i++){
                        proxyMsg.append(prisonerMap.get(tempList.get(i).getJgrybm()).getXm());
                        if(i<tempList.size()-1){
                            proxyMsg.append("、");
                        }else {
                            proxyMsg.append("）");
                        }
                    }
                }
            }
            res.put("proxy",true);
            res.put("proxyMsg",proxyMsg.toString());
        }
    }



    @Override
    public JSONObject timeOverlapJudgment(String jgrybm, String meetingMethod, String appointmentTime,
                                          String appointmentTimeSlot, String applyMeetingStartTime, String applyMeetingEndTime) {
        JSONObject res = new JSONObject();
        res.put("prompt", false);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime startTime = null;
        LocalDateTime endTime = null;

        if ("0".equals(meetingMethod)) {
            Map<String, LocalDateTime> tempMap = onSiteMeetingTime(appointmentTime, appointmentTimeSlot);
            startTime = tempMap.get("startTime");
            endTime = tempMap.get("endTime");
        } else {
            startTime = LocalDateTime.parse(applyMeetingStartTime, formatter);
            endTime = LocalDateTime.parse(applyMeetingEndTime, formatter);
        }

        List<JSONObject> jsonObjectList = lawyerMeetingDao.timeOverlapJudgment(jgrybm,startTime.format(formatter));
        if(CollectionUtil.isEmpty(jsonObjectList)){
            return res;
        }

        boolean prompt = false;
        StringBuilder msg = new StringBuilder();
        msg.append("当前预约时段【").append(startTime.format(formatter)).append("~").append(endTime.format(formatter)).append("】");
        msg.append("与以下业务存在时间冲突：");
        for(JSONObject jsonObject:jsonObjectList){
            LocalDateTime compareStartTime = LocalDateTime.parse(jsonObject.getString("starttime"), formatter);
            LocalDateTime compareEndTime = LocalDateTime.parse(jsonObject.getString("endTime"), formatter);
            boolean isOverlapping = !(compareEndTime.isBefore(startTime) || endTime.isBefore(compareStartTime));
            if(isOverlapping){
                prompt = true;
                msg.append(jsonObject.getString("businesstype"));
                msg.append("（").append(jsonObject.getString("starttime")).append("~").append(jsonObject.getString("endTime")).append("）");
                msg.append("，");
            }

        }
        msg.append("是否继续办理？");
        res.put("prompt",prompt);
        res.put("msg",prompt?msg.toString():null);
        return res;
    }

    private Map<String, LocalDateTime> onSiteMeetingTime(String appointmentTime, String appointmentTimeSlot) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        Map<String, LocalDateTime> resMap = new HashMap<>();

        StringBuilder starttimeStr = new StringBuilder();
        StringBuilder endtimeStr = new StringBuilder();
        starttimeStr.append(appointmentTime).append(" ");
        endtimeStr.append(appointmentTime).append(" ");
        String[] solt = appointmentTimeSlot.split("~");
        starttimeStr.append(solt[0]).append(":00");
        endtimeStr.append(solt[1]).append(":00");
        LocalDateTime startTime = LocalDateTime.parse(starttimeStr.toString(), formatter);
        LocalDateTime endTime = LocalDateTime.parse(endtimeStr.toString(), formatter);
        resMap.put("startTime", startTime);
        resMap.put("endTime", endTime);
        return resMap;
    }

    @Override
    public PageResult<LawyerMeetingRespVO> getHistoryMeetingByJgrybm(String jgrybm, int pageNo, int pageSize) {
        Page<LawyerMeetingDO> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<LawyerMeetingDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(LawyerMeetingDO::getId,LawyerMeetingDO::getAddTime,LawyerMeetingDO::getLawyer1Name,
                LawyerMeetingDO::getLawyer2Name,LawyerMeetingDO::getLawyer3Name,LawyerMeetingDO::getLawyer4Name,
                LawyerMeetingDO::getMeetingStartTime,
                LawyerMeetingDO::getMeetingEndTime,LawyerMeetingDO::getMeetingMethod);
        lambdaQueryWrapper.eq(LawyerMeetingDO::getJgrybm,jgrybm)
                .orderByDesc(LawyerMeetingDO::getAddTime);

        Page<LawyerMeetingDO> lawyerMeetingDOPage = page(page,lambdaQueryWrapper);

        List<LawyerMeetingRespVO> lawyerMeetingRespVOList = BeanUtils.toBean(lawyerMeetingDOPage.getRecords(),LawyerMeetingRespVO.class);

        if(CollectionUtil.isEmpty(lawyerMeetingRespVOList)){
            return new PageResult<>(new ArrayList<>() ,0L);
        }

        for(LawyerMeetingDO lawyerMeetingDO:lawyerMeetingDOPage.getRecords()){
            StringBuilder LawyerName = new StringBuilder();
            LawyerName.append(lawyerMeetingDO.getLawyer1Name());
            LawyerName.append("、");
            LawyerName.append(lawyerMeetingDO.getLawyer2Name());
            if(ObjectUtil.isNotEmpty(lawyerMeetingDO.getLawyer3Name())){
                LawyerName.append("、");
                LawyerName.append(lawyerMeetingDO.getLawyer3Name());
            }
            if(ObjectUtil.isNotEmpty(lawyerMeetingDO.getLawyer4Name())){
                LawyerName.append("、");
                LawyerName.append(lawyerMeetingDO.getLawyer4Name());
            }

            StringBuilder meetingTime = new StringBuilder();
            if(ObjectUtil.isNotEmpty(lawyerMeetingDO.getMeetingStartTime())){
                meetingTime.append(DateUtil.format(lawyerMeetingDO.getMeetingStartTime(),"yyyy-MM-dd HH:mm"));
            }
            meetingTime.append("~");
            if(ObjectUtil.isNotEmpty(lawyerMeetingDO.getMeetingEndTime())){
                meetingTime.append(DateUtil.format(lawyerMeetingDO.getMeetingEndTime(),"HH:mm"));
            }

            for(LawyerMeetingRespVO lawyerMeetingRespVO:lawyerMeetingRespVOList){
                if(lawyerMeetingRespVO.getId().equals(lawyerMeetingDO.getId())){
                    lawyerMeetingRespVO.setLawyerName(LawyerName.toString());
                    lawyerMeetingRespVO.setMeetingTime(meetingTime.toString());
                    break;
                }
            }
        }

        return new PageResult<>(lawyerMeetingRespVOList ,lawyerMeetingDOPage.getTotal());
    }

    @Override
    public PageResult<LawyerMeetingRespVO> getHistoryMeetingByLwayerId(String lawyerId, int pageNo, int pageSize) {
        Page<LawyerMeetingRespVO> page = new Page<>(pageNo,pageSize);
        IPage<LawyerMeetingRespVO> iPage = lawyerMeetingDao.getHistoryMeetingByLwayerId(page,lawyerId);
        return new PageResult<>(iPage.getRecords(),iPage.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean additionalRecording(LawyerMeetingSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        LambdaUpdateWrapper<LawyerMeetingDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(LawyerMeetingDO::getId, updateReqVO.getId())
                .set(LawyerMeetingDO::getCheckInTime, updateReqVO.getCheckInTime())
                .set(LawyerMeetingDO::getCheckInPoliceSfzh, updateReqVO.getCheckInPoliceSfzh())
                .set(LawyerMeetingDO::getCheckInPolice, updateReqVO.getCheckInPolice())
                .set(LawyerMeetingDO::getEscortingPolice, updateReqVO.getEscortingPolice())
                .set(LawyerMeetingDO::getEscortingPoliceSfzh, updateReqVO.getEscortingPoliceSfzh())
                .set(LawyerMeetingDO::getEscortingTime, updateReqVO.getEscortingTime())
                .set(LawyerMeetingDO::getInspectionResult, updateReqVO.getInspectionResult())
                .set(LawyerMeetingDO::getProhibitedItems, updateReqVO.getProhibitedItems())
                .set(LawyerMeetingDO::getPhysicalExam, updateReqVO.getPhysicalExam())
                .set(LawyerMeetingDO::getAbnormalSituations, updateReqVO.getAbnormalSituations())
                .set(LawyerMeetingDO::getInspectionTime, updateReqVO.getInspectionTime())
                .set(LawyerMeetingDO::getInspector, updateReqVO.getInspector())
                .set(LawyerMeetingDO::getInspectorSfzh, updateReqVO.getInspectorSfzh())
                .set(LawyerMeetingDO::getMeetingStartTime, updateReqVO.getMeetingStartTime())
                .set(LawyerMeetingDO::getMeetingEndTime, updateReqVO.getMeetingEndTime())
                .set(LawyerMeetingDO::getReturnInspectorSfzh, updateReqVO.getReturnInspectorSfzh())
                .set(LawyerMeetingDO::getReturnInspector, updateReqVO.getReturnInspector())
                .set(LawyerMeetingDO::getReturnInspectionTime, updateReqVO.getReturnInspectionTime())
                .set(LawyerMeetingDO::getReturnInspectionResult, updateReqVO.getReturnInspectionResult())
                .set(LawyerMeetingDO::getReturnProhibitedItems, updateReqVO.getReturnProhibitedItems())
                .set(LawyerMeetingDO::getReturnPhysicalExam, updateReqVO.getReturnPhysicalExam())
                .set(LawyerMeetingDO::getReturnAbnormalSituations, updateReqVO.getReturnAbnormalSituations())
                .set(LawyerMeetingDO::getReturnTime, updateReqVO.getReturnTime())
                .set(LawyerMeetingDO::getReturnPolice, updateReqVO.getReturnPolice())
                .set(LawyerMeetingDO::getReturnPoliceSfzh, updateReqVO.getReturnPoliceSfzh())
                .set(LawyerMeetingDO::getStatus,"4");
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorSfzh())){
            lambdaUpdateWrapper.set(LawyerMeetingDO::getEscortingOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(LawyerMeetingDO::getEscortingOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getEscortingOperatorTime())){
            lambdaUpdateWrapper.set(LawyerMeetingDO::getEscortingOperatorTime,new Date());
        }

        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorSfzh())){
            lambdaUpdateWrapper.set(LawyerMeetingDO::getReturnOperatorSfzh,sessionUser.getIdCard());
            lambdaUpdateWrapper.set(LawyerMeetingDO::getReturnOperator,sessionUser.getName());
        }
        if(ObjectUtil.isEmpty(updateReqVO.getReturnOperatorTime())){
            lambdaUpdateWrapper.set(LawyerMeetingDO::getReturnOperatorTime,new Date());
        }
        if("0".equals(updateReqVO.getDataSources())){
            LawyerMeetingDO lawyerMeetingDO = getById(updateReqVO.getId());
            WbInspectionResultsSaveReqVO resultsSaveReqVO = BeanUtils.toBean(updateReqVO,WbInspectionResultsSaveReqVO.class);
            resultsSaveReqVO.setJgrybm(lawyerMeetingDO.getJgrybm());
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"04","out","3",true);
            wbCommonService.saveInOutRecord(resultsSaveReqVO,"04","in","3",true);
        }
        return update(lambdaUpdateWrapper);
    }

    @Override
    public List<JSONObject> getOnSiteNumbering() {
        return lawyerMeetingDao.getOnSiteNumbering(LocalTime.now().getHour() < 12?"morning":"afternoon",SessionUserUtil.getSessionUser().getOrgCode());
    }

    @Override
    public List<JSONObject> getremoteNumbering() {
        return lawyerMeetingDao.getremoteNumbering(LocalTime.now().getHour() < 12?"morning":"afternoon",SessionUserUtil.getSessionUser().getOrgCode());
    }

    @Override
    public LawyerMeetingRespVO getLawyerMeetingById(String id) {
        LawyerMeetingDO lawyerMeetingDO = lawyerMeetingDao.selectById(id);
        if (ObjectUtil.isNotEmpty(lawyerMeetingDO.getApprovalAttachmentPath())) {
            lawyerMeetingDO.setApprovalAttachmentPath(wbCommonService.getFile(lawyerMeetingDO.getApprovalAttachmentPath()));
        }
        LawyerMeetingRespVO lawyerMeetingRespVO = BeanUtils.toBean(lawyerMeetingDO,LawyerMeetingRespVO.class);

        //同行人
        lawyerMeetingRespVO.setCompanionList(lawyerMeetingCompanionService.getCompanionByLawyerMeetingId(lawyerMeetingRespVO.getId()));

        //律师
        List<LawyerMeetingLawyerReqVO> lawyerList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(lawyerMeetingDO.getLawyer1Id())){
            LawyerMeetingLawyerReqVO lawyer = new LawyerMeetingLawyerReqVO();
            lawyer.setId(lawyerMeetingDO.getLawyer1Id());
            lawyer.setXm(lawyerMeetingDO.getLawyer1Name());
            lawyer.setLslx(lawyerMeetingDO.getLawyer1Type());
            lawyer.setXb(lawyerMeetingDO.getLawyer1Gender());
            lawyer.setZjhm(lawyerMeetingDO.getLawyer1IdNumber());
            lawyer.setLxfs(lawyerMeetingDO.getLawyer1Contact());
            lawyer.setZyzhm(lawyerMeetingDO.getLawyer1PracticeLicenseNumber());
            lawyer.setEntrustStage(lawyerMeetingDO.getLawyer1EntrustStage());
            lawyer.setLsdw(lawyerMeetingDO.getLawyer1Firm());
            lawyer.setEntrustType(lawyerMeetingDO.getLawyer1EntrustType());
            lawyer.setPrincipal(lawyerMeetingDO.getLawyer1PrincipalName());
            lawyer.setPrincipalId(lawyerMeetingDO.getLawyer1PrincipalIdNumber());
            lawyer.setPowerOfAttorneyType(lawyerMeetingDO.getLawyer1PowerOfAttorneyType());
            lawyer.setLetterNumber(lawyerMeetingDO.getLawyer1LetterNumber());
            lawyer.setZpUrl(lawyerMeetingDO.getLawyer1ImageUrl());
            if(ObjectUtil.isNotEmpty(lawyerMeetingDO.getLawyer1PowerOfAttorneyPath())){
                lawyer.setPowerOfAttorneyUrl(wbCommonService.getFile(lawyerMeetingDO.getLawyer1PowerOfAttorneyPath()));
            }
            if(ObjectUtil.isNotEmpty(lawyerMeetingDO.getLawyer1PracticeCertificatePa())){
                lawyer.setZyzsUrl(wbCommonService.getFile(lawyerMeetingDO.getLawyer1PracticeCertificatePa()));
            }
            lawyerList.add(lawyer);
        }
        if(ObjectUtil.isNotEmpty(lawyerMeetingDO.getLawyer2Id())){
            LawyerMeetingLawyerReqVO lawyer = new LawyerMeetingLawyerReqVO();
            lawyer.setId(lawyerMeetingDO.getLawyer2Id());
            lawyer.setXm(lawyerMeetingDO.getLawyer2Name());
            lawyer.setLslx(lawyerMeetingDO.getLawyer2Type());
            lawyer.setXb(lawyerMeetingDO.getLawyer2Gender());
            lawyer.setZjhm(lawyerMeetingDO.getLawyer2IdNumber());
            lawyer.setLxfs(lawyerMeetingDO.getLawyer2Contact());
            lawyer.setZyzhm(lawyerMeetingDO.getLawyer2PracticeLicenseNumber());
            lawyer.setEntrustStage(lawyerMeetingDO.getLawyer2EntrustStage());
            lawyer.setLsdw(lawyerMeetingDO.getLawyer2Firm());
            lawyer.setEntrustType(lawyerMeetingDO.getLawyer2EntrustType());
            lawyer.setPrincipal(lawyerMeetingDO.getLawyer2PrincipalName());
            lawyer.setPrincipalId(lawyerMeetingDO.getLawyer2PrincipalIdNumber());
            lawyer.setPowerOfAttorneyType(lawyerMeetingDO.getLawyer2PowerOfAttorneyType());
            lawyer.setZpUrl(lawyerMeetingDO.getLawyer2ImageUrl());
            lawyer.setLetterNumber(lawyerMeetingDO.getLawyer2LetterNumber());
            if(ObjectUtil.isNotEmpty(lawyerMeetingDO.getLawyer2PowerOfAttorneyPath())){
                lawyer.setPowerOfAttorneyUrl(wbCommonService.getFile(lawyerMeetingDO.getLawyer2PowerOfAttorneyPath()));
            }
            if(ObjectUtil.isNotEmpty(lawyerMeetingDO.getLawyer2PracticeCertificatePa())){
                lawyer.setZyzsUrl(wbCommonService.getFile(lawyerMeetingDO.getLawyer2PracticeCertificatePa()));
            }
            lawyerList.add(lawyer);
        }
        if(ObjectUtil.isNotEmpty(lawyerMeetingDO.getLawyer3Id())){
            LawyerMeetingLawyerReqVO lawyer = new LawyerMeetingLawyerReqVO();
            lawyer.setId(lawyerMeetingDO.getLawyer3Id());
            lawyer.setXm(lawyerMeetingDO.getLawyer3Name());
            lawyer.setLslx(lawyerMeetingDO.getLawyer3Type());
            lawyer.setXb(lawyerMeetingDO.getLawyer3Gender());
            lawyer.setZjhm(lawyerMeetingDO.getLawyer3IdNumber());
            lawyer.setLxfs(lawyerMeetingDO.getLawyer3Contact());
            lawyer.setZyzhm(lawyerMeetingDO.getLawyer3PracticeLicenseNumber());
            lawyer.setEntrustStage(lawyerMeetingDO.getLawyer3EntrustStage());
            lawyer.setLsdw(lawyerMeetingDO.getLawyer3Firm());
            lawyer.setEntrustType(lawyerMeetingDO.getLawyer3EntrustType());
            lawyer.setPrincipal(lawyerMeetingDO.getLawyer3PrincipalName());
            lawyer.setPrincipalId(lawyerMeetingDO.getLawyer3PrincipalIdNumber());
            lawyer.setPowerOfAttorneyType(lawyerMeetingDO.getLawyer3PowerOfAttorneyType());
            lawyer.setZpUrl(lawyerMeetingDO.getLawyer3ImageUrl());
            lawyer.setLetterNumber(lawyerMeetingDO.getLawyer3LetterNumber());
            if(ObjectUtil.isNotEmpty(lawyerMeetingDO.getLawyer3PowerOfAttorneyPath())){
                lawyer.setPowerOfAttorneyUrl(wbCommonService.getFile(lawyerMeetingDO.getLawyer3PowerOfAttorneyPath()));
            }
            if(ObjectUtil.isNotEmpty(lawyerMeetingDO.getLawyer3PracticeCertificatePa())){
                lawyer.setZyzsUrl(wbCommonService.getFile(lawyerMeetingDO.getLawyer3PracticeCertificatePa()));
            }
            lawyerList.add(lawyer);
        }
        if(ObjectUtil.isNotEmpty(lawyerMeetingDO.getLawyer4Id())){
            LawyerMeetingLawyerReqVO lawyer = new LawyerMeetingLawyerReqVO();
            lawyer.setId(lawyerMeetingDO.getLawyer4Id());
            lawyer.setXm(lawyerMeetingDO.getLawyer4Name());
            lawyer.setLslx(lawyerMeetingDO.getLawyer4Type());
            lawyer.setXb(lawyerMeetingDO.getLawyer4Gender());
            lawyer.setZjhm(lawyerMeetingDO.getLawyer4IdNumber());
            lawyer.setLxfs(lawyerMeetingDO.getLawyer4Contact());
            lawyer.setZyzhm(lawyerMeetingDO.getLawyer4PracticeLicenseNumber());
            lawyer.setEntrustStage(lawyerMeetingDO.getLawyer4EntrustStage());
            lawyer.setLsdw(lawyerMeetingDO.getLawyer4Firm());
            lawyer.setEntrustType(lawyerMeetingDO.getLawyer4EntrustType());
            lawyer.setPrincipal(lawyerMeetingDO.getLawyer4PrincipalName());
            lawyer.setPrincipalId(lawyerMeetingDO.getLawyer4PrincipalIdNumber());
            lawyer.setPowerOfAttorneyType(lawyerMeetingDO.getLawyer4PowerOfAttorneyType());
            lawyer.setZpUrl(lawyerMeetingDO.getLawyer4ImageUrl());
            lawyer.setLetterNumber(lawyerMeetingDO.getLawyer4LetterNumber());
            if(ObjectUtil.isNotEmpty(lawyerMeetingDO.getLawyer4PowerOfAttorneyPath())){
                lawyer.setPowerOfAttorneyUrl(wbCommonService.getFile(lawyerMeetingDO.getLawyer4PowerOfAttorneyPath()));
            }
            if(ObjectUtil.isNotEmpty(lawyerMeetingDO.getLawyer4PracticeCertificatePa())){
                lawyer.setZyzsUrl(wbCommonService.getFile(lawyerMeetingDO.getLawyer4PracticeCertificatePa()));
            }
            lawyerList.add(lawyer);
        }
        lawyerMeetingRespVO.setLawyerList(lawyerList);

        return lawyerMeetingRespVO;
    }
}
