package com.rs.module.acp.dao.db;

import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.acp.controller.app.pm.vo.AppCsFwfStatisticsVO;
import com.rs.module.acp.entity.db.CsfwfDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 实战平台-收押业务-出所防误放 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface CsfwfDao extends IBaseDao<CsfwfDO> {

    /**
     * 实战平台-收押业务-出所防误放-获取监所的待防误放统计数据
     *
     * @param orgCode     机构编码
     * @param businessType 业务类型（1：出所、2：入所）
     * @return 待防误放统计数据
     */
    List<AppCsFwfStatisticsVO> getCsfwfStatistics(@Param("orgCode") String orgCode,
                                                  @Param("businessType") String businessType);
}
