package com.rs.module.acp.controller.admin.gj;

import cn.hutool.core.util.StrUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApprovalTraceVO;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.prisonroom.PrisonRoomChangeInOutSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.prisonroom.PrisonRoomChangeProcessVO;
import com.rs.module.acp.controller.admin.gj.vo.prisonroom.PrisonRoomChangeRespVO;
import com.rs.module.acp.controller.admin.gj.vo.prisonroom.PrisonRoomChangeSaveReqVO;
import com.rs.module.acp.entity.gj.PrisonRoomChangeDO;
import com.rs.module.acp.service.gj.prisonroom.PrisonRoomChangeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务--监室调整")
@RestController
@RequestMapping("/acp/gj/prisonRoomChange")
@Validated
public class PrisonRoomChangeController {

    @Resource
    private PrisonRoomChangeService prisonRoomChangeService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务--监室调整")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:create", operateType = LogOperateType.CREATE, title = "管教业务-监室调整-创建",
            success = "管教业务-监室调整-监室调整登记成功", fail = "管教业务-监室调整登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPrisonRoomChange(@Valid @RequestBody PrisonRoomChangeSaveReqVO createReqVO) {
        return success(prisonRoomChangeService.createPrisonRoomChange(createReqVO));
    }

    @PostMapping("/batchCreate")
    @ApiOperation(value = "批量创建实战平台-管教业务--监室调整")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:batchCreate", operateType = LogOperateType.CREATE, title = "管教业务-监室调整-创建",
            success = "管教业务-监室调整-监室调整登记成功", fail = "管教业务-监室调整登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVOList}}")
    public CommonResult<List<String>> batchCreatePrisonRoomChange(@Valid @RequestBody List<PrisonRoomChangeSaveReqVO>  createReqVOList) {
        List<String> result = prisonRoomChangeService.batchCreatePrisonRoomChangeList(createReqVOList);
        return success(result);
    }

    @PostMapping("/approve")
    @ApiOperation(value = "领导审批-管教业务-监室调整")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:leaderApprove", operateType = LogOperateType.UPDATE, title = "管教业务-监室调整-领导审批",
            success = "管教业务-监室调整-领导审批成功", fail = "管教业务-监室调整-领导审审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    public CommonResult<Boolean> leaderApprove(@Valid @RequestBody GjApproveReqVO approveReqVO) {
        prisonRoomChangeService.leaderApprove(approveReqVO);
        return success(true);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务--监室调整")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:update", operateType = LogOperateType.UPDATE, title = "管教业务-监室调整-信息更新",
            success = "管教业务-监室调整-监室调整编辑成功", fail = "管教业务-监室调整更新失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePrisonRoomChange(@Valid @RequestBody PrisonRoomChangeSaveReqVO updateReqVO) {
        prisonRoomChangeService.updatePrisonRoomChange(updateReqVO);
        return success(true);
    }


    @PostMapping("/batchUpdate")
    @ApiOperation(value = "批量更新实战平台-管教业务--监室调整-")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:batchUpdate", operateType = LogOperateType.UPDATE, title = "管教业务-监室调整-信息更新",
            success = "管教业务-监室调整-监室调整编辑成功", fail = "管教业务-监室调整更新失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVOList}}")
    public CommonResult<Boolean> batchUpdatePrisonRoomChange(@Valid @RequestBody List<PrisonRoomChangeSaveReqVO> updateReqVOList) {
        Boolean flag = prisonRoomChangeService.batchUpdatePrisonRoomChange(updateReqVOList);
        return success(flag);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务--监室调整")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:delete", operateType = LogOperateType.DELETE, title = "管教业务-监室调整-删除",
            success = "管教业务-监室调整-删除成功", fail = "管教业务-监室调整-删除失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePrisonRoomChange(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           prisonRoomChangeService.deletePrisonRoomChange(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务--监室调整")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:get", operateType = LogOperateType.QUERY, title = "管教业务-管教业务-监室调整获取",
            bizNo = "{{#id}}", success = "管教业务-管教业务-监室调整获取详情成功", fail = "管教业务-管教业务-监室调整获取详情失败", extraInfo = "{{#id}}")
    public CommonResult<PrisonRoomChangeRespVO> getPrisonRoomChange(@RequestParam("id") String id) {
        PrisonRoomChangeDO prisonRoomChange = prisonRoomChangeService.getPrisonRoomChange(id);
        PrisonRoomChangeRespVO vo = BeanUtils.toBean(prisonRoomChange, PrisonRoomChangeRespVO.class);
        if(vo != null && StrUtil.isNotBlank(vo.getApprovalResult())){
            vo.setApprovalResultName(  BspApproceStatusEnum.NOT_PASSED_END.getCode() == Short.parseShort(vo.getApprovalResult()) ? BspApproceStatusEnum.NOT_PASSED.getName() : BspApproceStatusEnum.PASSED.getName());
        }
        return success(vo);
    }

    @PostMapping("/updateProcessStatus")
    @ApiOperation(value = "管教业务-监室调整-登记状态和流程修改")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:updateProcessStatus", operateType = LogOperateType.UPDATE, title = "管教业务-监室调整-流程状态修改",
            success = "管教业务-监室调整-流程状态修改成功", fail = "管教业务-监室调整-流程状态修改失败，错误信息：{{#_ret[msg]}}",  extraInfo = "{TO_JSON{#processVO}}")
    public CommonResult<Boolean> updateProcessStatus(@Valid @RequestBody PrisonRoomChangeProcessVO processVO) {
        Boolean flag = prisonRoomChangeService.updateProcessStatus(processVO);
        return success(flag);
    }

    @PostMapping("/batchUpdateProcessStatus")
    @ApiOperation(value = "管教业务-信息员管理-登记状态和流程批量修改")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:batchUpdateProcessStatus", operateType = LogOperateType.UPDATE, title = "管教业务-监室调整-流程状态修改",
            success = "管教业务-监室调整-流程状态修改成功", fail = "管教业务-监室调整-流程状态修改失败，错误信息：{{#_ret[msg]}}",  extraInfo = "{TO_JSON{#processVOList}}")
    public CommonResult<Boolean> batchUpdateProcessStatus(@Valid @RequestBody List<PrisonRoomChangeProcessVO> processVOList) {
        Boolean flag = prisonRoomChangeService.batchUpdateProcessStatus(processVOList);
        return success(flag);
    }


    @PostMapping("/batchApprove")
    @ApiOperation(value = "领导审批(批量更新)-管教业务-监室调整")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:batchLeaderApprove", operateType = LogOperateType.UPDATE, title = "管教业务-监室调整-领导审批",
            success = "管教业务-监室调整-领导审批成功", fail = "管教业务-监室调整审批-领导审审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqList}}")
    public CommonResult<Boolean> batchLeaderApprove(@Valid @RequestBody List<GjApproveReqVO> approveReqList) {
        prisonRoomChangeService.batchLeaderApprove(approveReqList);
        return success(true);
    }

    @GetMapping("/getPrisonRoomChangeByJgrybm")
    @ApiOperation(value = "获取监室调整记录")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:jgrybm", operateType = LogOperateType.QUERY, title = "管教业务-监室调整-获取信息员记录列表",
            bizNo = "{{#jgrybm}}", success = "获取监室调整记录信息成功", fail = "获取监室调整详情失败", extraInfo = "{{#jgrybm}}")
    public CommonResult<PageResult<PrisonRoomChangeRespVO>> getPrisonRoomChangeByJgrybm(@RequestParam("jgrybm") String jgrybm,
                                                                                  @RequestParam(value = "pageNo" ,defaultValue = "1") Long pageNo,
                                                                                  @RequestParam(value = "pageSize", defaultValue = "10")  Long pageSize) {
        PageResult<PrisonRoomChangeDO> pageResult = prisonRoomChangeService.getPrisonRoomChangeByJgrybm(jgrybm, pageNo, pageSize);
        return success(BeanUtils.toBean(pageResult, PrisonRoomChangeRespVO.class));
    }


    @PostMapping("/bringIntakeOutReg")
    @ApiOperation(value = "带入带出登记")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomChange:bringIntakeOutReg", operateType = LogOperateType.CREATE, title = "管教业务-监室调整-带入带出登记",
   success = "管教业务-监室调整-带入带出登记成功", fail = "管教业务-监室调整-带入带出登记失败", extraInfo = "{TO_JSON{#changeInOutSaveReqVO}}")
    public CommonResult<Boolean> bringIntakeOutReg(@Valid @RequestBody PrisonRoomChangeInOutSaveReqVO changeInOutSaveReqVO) {
        try{
            prisonRoomChangeService.bringIntakeOutReg(changeInOutSaveReqVO);
        }catch (Exception e){
            return error(e.getMessage());
        }
        return success(true);
    }

    @GetMapping("/getApproveTrack")
    @ApiOperation(value = "管教业务-监室调整-获取轨迹记录")
    public CommonResult<List<GjApprovalTraceVO>> getApproveTrack(@RequestParam("id")String id) {
        List<GjApprovalTraceVO> list = prisonRoomChangeService.getApproveTrack(id);
        return success(list);
    }


}
