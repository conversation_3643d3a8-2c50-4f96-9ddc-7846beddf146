package com.rs.module.acp.config;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "conf.device.qysh")
public class QingyanDeviceProperties {
    private String serverIp = "*************";
    private int websocketPort = 48300;
    private int webPort = 8180;
    private String username = "admin";
    private String password = "#LocalSense";
    private double positionChangeThresholdX = 5;
    private double positionChangeThresholdY = 5;
    private double positionChangeThresholdZ = 2.5;
    private boolean enableWebSocketConnection;
    private List<GroupOrgMapping> groupOrgMapping;

    @Data
    public static class GroupOrgMapping {
        private String groupId;
        private String orgCode;
    }
}
