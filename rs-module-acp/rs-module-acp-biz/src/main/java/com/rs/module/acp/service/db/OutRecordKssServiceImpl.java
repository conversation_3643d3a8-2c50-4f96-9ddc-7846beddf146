package com.rs.module.acp.service.db;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.common.util.spring.SpringUtils;
import com.rs.module.acp.cons.DetainRegStatusEnum;
import com.rs.module.acp.cons.LeaderApprovalStatusEnum;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.dao.db.OutRecordKssDao;
import com.rs.module.acp.entity.db.DetainRegKssDO;
import com.rs.module.acp.entity.db.OutRecordKssDO;
import com.rs.module.acp.enums.db.CsFwfBusinessTypeEnum;
import com.rs.module.acp.service.ds.DSPrisonRoomChangeService;
import com.rs.module.base.util.BspApprovalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import java.util.*;
import java.util.concurrent.CompletableFuture;


/**
 * 实战平台-羁押业务-出所登记（看守所） Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class OutRecordKssServiceImpl extends BaseServiceImpl<OutRecordKssDao, OutRecordKssDO> implements OutRecordKssService {

    @Resource
    private OutRecordKssDao outRecordKssDao;
    @Resource
    private DSPrisonRoomChangeService dsPrisonRoomChangeService;
    @Resource
    private CsfwfService csfwfService;

    private final String defKey = "shifangyewsfcs";

    @Override
    public String createOutRecordKss(OutRecordKssSaveReqVO createReqVO) {
        //根据currentStep和status两个字段判断，是否流转到下一个节点
        boolean isAdvance = advanceProcessStep(createReqVO);

        String id = "";
        // 校验存在
        OutRecordKssDO tmpoutRecordKss = getByJgrybm(createReqVO.getJgrybm());
        OutRecordKssDO entity = tmpoutRecordKss;
        if (tmpoutRecordKss != null) {
            id = tmpoutRecordKss.getId();
            createReqVO.setId(id);
            String spzt = tmpoutRecordKss.getSpzt();
            if (spzt != null && !spzt.equals("1") && createReqVO.getSpzt().equals("1")) {
                createReqVO.setSpzt(spzt);
            }
            tmpoutRecordKss = BeanUtils.toBean(createReqVO, OutRecordKssDO.class);

            outRecordKssDao.updateById(tmpoutRecordKss);
            id = tmpoutRecordKss.getId();
        } else {
            // 插入
            OutRecordKssDO outRecordKss = BeanUtils.toBean(createReqVO, OutRecordKssDO.class);
            outRecordKss.setId(StringUtil.getGuid32());
            outRecordKssDao.insert(outRecordKss);
            entity = outRecordKss;
            id = outRecordKss.getId();
        }
        // 如果到待防误放验证环节，则同步数据到在所表
        if (createReqVO.getCurrentStep().equalsIgnoreCase("05")) {
            //插入
            csfwfService.createKssCsFwf(entity.getJgrybm(), entity.getId(), CsFwfBusinessTypeEnum.OUT.getCode(), entity.getCsyy());
        }


        //判断是否离所确认，若已离所确认则同步数据到出所表，并从在所表删除
        if (createReqVO.getCurrentStep().equalsIgnoreCase("07")) {
            try {
                dsPrisonRoomChangeService.updatePrisonRoomChangeForKssOutPrison(entity);
            } catch (Exception e) {
                log.error("修改人员监室变动记录数据异常！ " + JSON.toJSONString(e));
                throw new RuntimeException(e);
            }
            syncPrisonerDataToOut(createReqVO.getJgrybm());
            syncOutInfoToShopping(getByJgrybm(createReqVO.getJgrybm()));

        }

        // 返回
        return id;
    }

    /**
     * 向购物采买系统发送人员出所信息
     * @param entity
     */
    private void syncOutInfoToShopping(OutRecordKssDO entity) {
        CompletableFuture.runAsync(() -> {
            String url = SpringUtils.getProperty("shopping.csryxxtb.url");
            if (StrUtil.isBlank(url)) {
                return;
            }
            String timeoutStr = SpringUtils.getProperty("shopping.timeout", "10000");
            Integer timeout = Integer.parseInt(timeoutStr);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("jgrybm", entity.getJgrybm());
            jsonObject.put("orgCode", entity.getOrgCode());
            jsonObject.put("orgName", entity.getOrgName());
            String json = HttpUtil.post(url, jsonObject.toJSONString(), timeout);
            CommonResult reuslt = JSON.parseObject(json, CommonResult.class);
            if (!reuslt.getSuccess()) {
                log.error("发送人员离所信息异常！ " + JSON.toJSONString(reuslt));
            }
        });
    }

    /**
     * 推进流程步骤，并更新状态
     *
     * @param reqVO 请求参数
     * @return 是否成功推进流程
     */
    public boolean advanceProcessStep(OutRecordKssSaveReqVO reqVO) {
        String currentStep = reqVO.getCurrentStep();
        //如果currentStep为空,返回异常
        if (currentStep == null || currentStep.isEmpty()) {
            throw new ServerException("currentStep参数不能为空！");
        }
        String status = reqVO.getStatus() == null ? "" : reqVO.getStatus();

        //领导审批环节,status从01到04的，spzt均为1
        if (status.equalsIgnoreCase("02")) {
            //审批状态为未审批
            reqVO.setSpzt(LeaderApprovalStatusEnum.PENDING.getCode());
        }
        if (status.equalsIgnoreCase("05")) {
            //审批状态判断，不能为空，不能为01，只能在02,03范围
            if (reqVO.getSpzt() == null || reqVO.getSpzt().equals(LeaderApprovalStatusEnum.PENDING.getCode())) {
                throw new ServerException("审批状态值异常！！");
            }
        }

        if (!status.equals(DetainRegStatusEnum.SUBMITTED.getCode())) {
            return false;
        }

        // 流程推进逻辑
        switch (currentStep) {
            case "01":
                reqVO.setCurrentStep("02");
                reqVO.setStatus("01"); // 状态变为出所检查
                return true;
            case "02":
                reqVO.setCurrentStep("03");
                reqVO.setStatus("01"); // 状态变为财务交接/物品取出
                reqVO.setSpzt(LeaderApprovalStatusEnum.PENDING.getCode());
                return true;
            case "03":
                reqVO.setCurrentStep("04");
                reqVO.setStatus("01"); // 状态变为领导审批
                return true;
            case "04":
                if (reqVO.getSpzt() != null && reqVO.getSpzt().equals(LeaderApprovalStatusEnum.REJECTED.getCode())) {
                    reqVO.setCurrentStep("07");
                    reqVO.setStatus("03"); // 不同意
                    return true;
                }
                reqVO.setCurrentStep("05");
                reqVO.setStatus("01"); // 状态变为防误放验证
                return true;
            case "05":
                reqVO.setCurrentStep("06");
                reqVO.setStatus("01"); // 状态变为离所确认
//                if (reqVO.getSpzt() == null || LeaderApprovalStatusEnum.PENDING.getCode().equals(reqVO.getSpzt())) {
//                    throw new ServerException("审批状态值异常！！");
//                }
                return true;
            case "06":
                reqVO.setCurrentStep("07");
                reqVO.setStatus("01"); // 状态变为离所确认
                return true;
            default:
                throw new ServerException("未知流程步骤: " + currentStep);
        }

    }

    /**
     * 将在所数据移到出所表中
     * @param jgrybm
     */
    private void syncPrisonerDataToOut(@NotEmpty(message = "监管人员编码不能为空") String jgrybm) {
        //插入出所表
        outRecordKssDao.inserToOut(jgrybm);

        //删除在所表
        outRecordKssDao.deleteFromIn(jgrybm);
    }

    @Override
    public void updateOutRecordKss(OutRecordKssSaveReqVO updateReqVO) {

        try {
            validateOutRecordKssExists(updateReqVO.getId());
        } catch (ServerException e) {
            // 校验存在
            String jgrybm = updateReqVO.getJgrybm();
            OutRecordKssDO db = getByJgrybm(jgrybm);
            if (db != null) {
                updateReqVO.setId(db.getId());
            } else {
                throw new ServerException("未找到JGRYBM为：" + jgrybm + "的记录！");
            }
        }

        // 更新
        OutRecordKssDO updateObj = BeanUtils.toBean(updateReqVO, OutRecordKssDO.class);
        outRecordKssDao.updateById(updateObj);
    }

    @Override
    public void updateOutRecordKssByStatus(OutRecordKssSaveReqVO updateReqVO) {

        try {
            validateOutRecordKssExists(updateReqVO.getId());
        } catch (ServerException e) {
            // 校验存在
            String jgrybm = updateReqVO.getJgrybm();
            OutRecordKssDO db = getByJgrybm(jgrybm);
            if (db != null) {
                updateReqVO.setId(db.getId());
            } else {
                throw new ServerException("未找到JGRYBM为：" + jgrybm + "的记录！");
            }

        }

        advanceProcessStep(updateReqVO);
        // 更新
        OutRecordKssDO updateObj = BeanUtils.toBean(updateReqVO, OutRecordKssDO.class);
        outRecordKssDao.updateById(updateObj);
    }

    @Override
    public void deleteOutRecordKss(String id) {
        // 校验存在
        validateOutRecordKssExists(id);
        // 删除
        outRecordKssDao.deleteById(id);
    }

    private void validateOutRecordKssExists(String id) {
        if (outRecordKssDao.selectById(id) == null) {
            throw new ServerException("实战平台-羁押业务-出所登记（看守所）数据不存在");
        }
    }

    @Override
    public OutRecordKssDO getOutRecordKss(String id) {
//        validateOutRecordKssExists(id);
        if (outRecordKssDao.selectById(id) == null) {
            return new OutRecordKssDO();
        }
        return outRecordKssDao.selectById(id);
    }

    @Override
    public PageResult<OutRecordKssDO> getOutRecordKssPage(OutRecordKssPageReqVO pageReqVO) {
        return outRecordKssDao.selectPage(pageReqVO);
    }

    @Override
    public List<OutRecordKssDO> getOutRecordKssList(OutRecordKssListReqVO listReqVO) {
        return outRecordKssDao.selectList(listReqVO);
    }

    @Override
    public OutRecordKssDO getByJgrybm(String jgrybm) {
        OutRecordKssListReqVO listReqVO = new OutRecordKssListReqVO();
        listReqVO.setJgrybm(jgrybm);
        List<OutRecordKssDO> list = outRecordKssDao.selectList(listReqVO);
        if (list != null && !list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public OutRecordKssDO getPrisonerInfo(String rybh) {
        OutRecordKssListReqVO reqVO = new OutRecordKssListReqVO();
        reqVO.setJgrybm(rybh);
        List<OutRecordKssDO> dlist = outRecordKssDao.selectList(reqVO);

        OutRecordKssDO db = new OutRecordKssDO();
        if (dlist != null && !dlist.isEmpty()) {
            db = dlist.get(0);
        } else if (dlist == null || dlist.isEmpty()) {
            return null;
        }
        return db;
    }

    /**
     * 释放出所的只需要关注currentStep字段，其他返回字段暂时无用
     *
     * @param rybh
     * @return
     */
    @Override
    public InRecordStatusVO getInRecordStatus(String rybh) {
        InRecordStatusVO db = outRecordKssDao.getInRecordStatus(rybh);
        return db;
    }

    @Override
    public void approval(GjApproveReqVO approveReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        OutRecordKssDO entity = outRecordKssDao.selectById(approveReqVO.getId());
        Assert.notNull(entity, StrUtil.format("传入ID有误 id：{}", entity.getId()));
        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(entity.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }
        //
        OutRecordKssSaveReqVO outRecordKssSaveReqVO = new OutRecordKssSaveReqVO();
        outRecordKssSaveReqVO.setJgrybm(entity.getJgrybm());
        outRecordKssSaveReqVO.setJgryxm(entity.getJgryxm());
        outRecordKssSaveReqVO.setStatus("03");
        outRecordKssSaveReqVO.setCurrentStep("04");
        outRecordKssSaveReqVO.setApprovalResult(approveReqVO.getApprovalComments());
        outRecordKssSaveReqVO.setJbr(entity.getJbr());
        outRecordKssSaveReqVO.setJbsj(entity.getJbsj());

        entity.setJbr(sessionUser.getName());
        entity.setJbsj(new Date());
        String approvalResult = approveReqVO.getApprovalResult();
        BspApproceStatusEnum bspApproceStatusEnum;
        if (String.valueOf(BspApproceStatusEnum.PASSED.getCode()).equals(approvalResult)) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED;
            outRecordKssSaveReqVO.setSpzt("2");
        } else {
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
            outRecordKssSaveReqVO.setSpzt("3");
        }
        entity.setApprovalResult(approveReqVO.getApprovalComments());

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", entity.getId());

        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getIdCard());
        nowApproveUser.put("name", sessionUser.getName());
        boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ||
                BspApproceStatusEnum.NOT_PASSED_END.getCode() == bspApproceStatusEnum.getCode();
        String msgUrl = StrUtil.format("/#/detentionBusiness/releaseRegister?eventCode={}", entity.getJgrybm());
        // TODO 移动警务跳转路由待前端提供
        String mobileUrl = StrUtil.format("/todoItem//{}", approveReqVO.getId());
        JSONObject result = BspApprovalUtil.approvalProcess(defKey, entity.getActInstId(), entity.getTaskId(), entity.getId(),
                bspApproceStatusEnum, approveReqVO.getApprovalComments(), null, msgUrl, mobileUrl, terminateTask,
                variables, nowApproveUser, HttpUtils.getAppCode());
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            entity.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程审批失败:" + result.getString("msg"));
        }

        // 判断流程是否结束
        Boolean finishProcinst = BspApprovalUtil.getBpmApi().isFinishProcinst(entity.getActInstId());
        if (finishProcinst) {
            createOutRecordKss(outRecordKssSaveReqVO);
        }
    }


}
