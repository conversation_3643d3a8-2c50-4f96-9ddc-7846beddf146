package com.rs.module.acp.service.db;

import com.rs.module.acp.entity.db.OutRecordKssDO;
import com.rs.module.acp.entity.db.PersonalEffectsInfoDO;
import com.rs.module.acp.util.GeneralUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.OutPersonalEffectsSubDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.db.OutPersonalEffectsSubDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-收押业务-出所随身物品登记子 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OutPersonalEffectsSubServiceImpl extends BaseServiceImpl<OutPersonalEffectsSubDao, OutPersonalEffectsSubDO> implements OutPersonalEffectsSubService {

    @Resource
    private OutPersonalEffectsSubDao outPersonalEffectsSubDao;

    @Resource
    private OutRecordKssService outRecordKssService;

    @Override
    public String createOutPersonalEffectsSub(OutPersonalEffectsSubSaveReqVO createReqVO) {
        // 插入
        OutPersonalEffectsSubDO outPersonalEffectsSub = BeanUtils.toBean(createReqVO, OutPersonalEffectsSubDO.class);
        outPersonalEffectsSubDao.insert(outPersonalEffectsSub);
        // 返回
        return outPersonalEffectsSub.getId();
    }

    @Override
    public void updateOutPersonalEffectsSub(OutPersonalEffectsSubSaveReqVO updateReqVO) {
        // 校验存在
        validateOutPersonalEffectsSubExists(updateReqVO.getId());
        // 更新
        OutPersonalEffectsSubDO updateObj = BeanUtils.toBean(updateReqVO, OutPersonalEffectsSubDO.class);
        outPersonalEffectsSubDao.updateById(updateObj);
    }

    @Override
    public void deleteOutPersonalEffectsSub(String id) {
        // 校验存在
        validateOutPersonalEffectsSubExists(id);
        // 删除
        outPersonalEffectsSubDao.deleteById(id);
    }

    private void validateOutPersonalEffectsSubExists(String id) {
        if (outPersonalEffectsSubDao.selectById(id) == null) {
            throw new ServerException("实战平台-收押业务-出所随身物品登记子数据不存在");
        }
    }

    @Override
    public OutPersonalEffectsSubDO getOutPersonalEffectsSub(String id) {
        return outPersonalEffectsSubDao.selectById(id);
    }

    @Override
    public PageResult<OutPersonalEffectsSubDO> getOutPersonalEffectsSubPage(OutPersonalEffectsSubPageReqVO pageReqVO) {
        return outPersonalEffectsSubDao.selectPage(pageReqVO);
    }

    @Override
    public List<OutPersonalEffectsSubDO> getOutPersonalEffectsSubList(OutPersonalEffectsSubListReqVO listReqVO) {
        return outPersonalEffectsSubDao.selectList(listReqVO);
    }

    @Override
    // TODO 返回信息不完整，待完善
    public PersonalEffectsInfoDO getPersonalEffects(String rybh) {
        List<OutPersonalEffectsSubDO> list = outPersonalEffectsSubDao.getPersonalEffectsByPersonId(rybh);

        PersonalEffectsInfoDO personalEffectsInfoDO = new PersonalEffectsInfoDO();
        personalEffectsInfoDO.setPersonalEffectsList(list);

        return personalEffectsInfoDO;
    }

    @Override
    public String createEffectsSub(PersonalEffectsInfoVO createReqVO) {
        List<OutPersonalEffectsSubDO> list = createReqVO.getPersonalEffectsList();
        //设置这个list的id
        for(OutPersonalEffectsSubDO personalEffectsSubDO:list){
            String id = personalEffectsSubDO.getId();
            if(id!=null && !id.isEmpty()){
                OutPersonalEffectsSubDO db = outPersonalEffectsSubDao.selectById(personalEffectsSubDO.getId());

                if(db!=null){
                    db = BeanUtils.toBean(personalEffectsSubDO, OutPersonalEffectsSubDO.class);
                    outPersonalEffectsSubDao.updateById(db);
                }else{
                    db = BeanUtils.toBean(personalEffectsSubDO, OutPersonalEffectsSubDO.class);
                    db.setId(GeneralUtil.generateUUID());
                    outPersonalEffectsSubDao.insert(db);
                }
            }else{
                personalEffectsSubDO.setId(GeneralUtil.generateUUID());
                OutPersonalEffectsSubDO db = BeanUtils.toBean(personalEffectsSubDO, OutPersonalEffectsSubDO.class);
                outPersonalEffectsSubDao.insert(db);
            }
        }

        //财务交接情况
        String cwjjqk = createReqVO.getCwjjqk();
        //财务交接手续
        String cwjjst = createReqVO.getCwjjst();
        //监管人员编码
        String jgrybm = createReqVO.getJgrybm();

        OutRecordKssDO outRecordKssDO = outRecordKssService.getByJgrybm(jgrybm);
        outRecordKssDO.setCwjjqk(cwjjqk);
        outRecordKssDO.setCwjjst(cwjjst);
        outRecordKssDO.setStatus("04");//所领导审批

        outRecordKssService.updateById(outRecordKssDO);

        return "true";
    }


}
