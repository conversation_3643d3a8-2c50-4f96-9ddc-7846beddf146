package com.rs.module.acp.entity.pm;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 领导巡视 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_leader_inspection")
@KeySequence("acp_pm_leader_inspection_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_leader_inspection")
public class LeaderInspectionDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 巡视人
     */
    private String xsr;
    /**
     * 巡视人
     */
    private String xsrSfzh;
    /**
     * 巡视类型
     */
    private String xslx;
    /**
     * 巡视地点类型：按区域：1；按类型：2
     */
    private String xsddlx;
    /**
     * 巡视地点编码，逗号分割
     */
    private String xsddCode;
    /**
     * 巡视地点名称，逗号分割
     */
    private String xsddName;
    /**
     * 巡视内容
     */
    private String xsnr;
    /**
     * 巡视开始时间
     */
    private Date xskssj;
    /**
     * 巡视结束时间
     */
    private String xsjssj;
    /**
     * 巡视是否发现问题
     */
    private String xssffxwt;
    /**
     * 问题通知人ID
     */
    private String wttzr;
    /**
     * 问题通知人名称
     */
    private String wttzrName;
    /**
     * 巡视结果信息
     */
    private String xsjgxx;
    /**
     * 问题处置人ID
     */
    private String wtczr;
    /**
     * 问题处置人名称
     */
    private String wtczrName;
    /**
     * 处置时间
     */
    private Date czsj;
    /**
     * 处置情况
     */
    private String czqk;
    /**
     * 状态：1：无问题；2：待处置；3：已处置
     */
    private String status;
    /**
     * 领导职务
     */
    private String ldzw;

}
