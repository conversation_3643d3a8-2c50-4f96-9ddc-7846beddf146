package com.rs.module.acp.controller.admin.gj.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.entity.gj.FwdbxtWsxxDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-法务待办协同新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FwdbxtSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("监管人员身份证号")
    private String jgrysfzh;

    @ApiModelProperty("监室号")
    private String jsh;

    @ApiModelProperty("监室名称")
    private String orgName;

    @ApiModelProperty("案由代码")
    private String aydm;

    @ApiModelProperty("案由名称")
    private String aymc;

    @ApiModelProperty("案件环节")
    private String ajhj;

    @ApiModelProperty("办案单位")
    private String badw;

    @ApiModelProperty("办案民警")
    private String bamj;

    @ApiModelProperty("原始文书")
    private String ysws;

    @ApiModelProperty("文书名称")
    private String wsmc;

    @ApiModelProperty("文书数量")
    private Integer wssl;

    @ApiModelProperty("推送时间")
    private Date tssj;

    @ApiModelProperty("是否拒签")
    private String sfjq;

    @ApiModelProperty("签收时间")
    private Date qssj;

    @ApiModelProperty("拒签日期")
    private Date jqrq;

    @ApiModelProperty("拒签理由")
    private String jqly;

    @ApiModelProperty("签收经办民警身份证号")
    private String qsjbmjsfzh;

    @ApiModelProperty("签收经办民警")
    private String qsjbmjxm;

    @ApiModelProperty("回传时间")
    private Date hcsj;

    @ApiModelProperty("回传文书")
    private String hcws;

    @ApiModelProperty("回传信息")
    private String hcxx;

    @ApiModelProperty("回传经办民警身份证号")
    private String hcjbmjsfzh;

    @ApiModelProperty("回传经办民警姓名")
    private String hcjbmjxm;

    @ApiModelProperty("状态")
    @NotEmpty(message = "状态不能为空")
    private String status;

    @ApiModelProperty("实战平台-管教业务-法务待办协同关联文书信息列表")
    private List<FwdbxtWsxxDO> fwdbxtWsxxs;

}
