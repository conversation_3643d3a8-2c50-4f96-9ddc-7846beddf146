package com.rs.module.acp.service.dch.context;

import com.rs.module.base.entity.pm.DataChangeEventLogDO;

import com.rs.module.base.enums.DataChangeEventTypeEnum;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * 数据变更事件上下文
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Data
public class DataChangeEventContext {

    /**
     * 事件ID
     */
    private String eventId;

    /**
     * 事件类型
     */
    private DataChangeEventTypeEnum eventType;

    /**
     * 业务表名
     */
    private String tableName;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 主键ID
     */
    private String primaryKeyId;

    /**
     * 变更前数据
     */
    private Map<String, Object> oldDataMap;

    /**
     * 变更后数据
     */
    private Map<String, Object> newDataMap;

    /**
     * 事件触发时间
     */
    private Date eventTime;

    /**
     * 扩展信息
     */
    private Map<String, Object> extendInfo;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    private DataChangeEventLogDO rawData;

    /**
     * 构造方法
     */
    public DataChangeEventContext() {
        this.eventTime = new Date();
        this.retryCount = 0;
        this.maxRetryCount = 3;
    }

    /**
     * 构造方法
     *
     * @param eventType     事件类型
     * @param tableName     表名
     * @param businessType  业务类型
     * @param primaryKeyId  主键ID
     */
    public DataChangeEventContext(DataChangeEventTypeEnum eventType, String tableName,
                                  String businessType, String primaryKeyId) {
        this();
        this.eventType = eventType;
        this.tableName = tableName;
        this.businessType = businessType;
        this.primaryKeyId = primaryKeyId;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount++;
    }

    /**
     * 是否可以重试
     *
     * @return true-可以重试，false-不可以重试
     */
    public boolean canRetry() {
        return this.retryCount < this.maxRetryCount;
    }

    /**
     * 获取扩展信息
     *
     * @param key 键
     * @return 值
     */
    public Object getExtendInfo(String key) {
        if (extendInfo == null) {
            return null;
        }
        return extendInfo.get(key);
    }

    /**
     * 设置扩展信息
     *
     * @param key   键
     * @param value 值
     */
    public void putExtendInfo(String key, Object value) {
        if (extendInfo == null) {
            extendInfo = new java.util.HashMap<>();
        }
        extendInfo.put(key, value);
    }

}
