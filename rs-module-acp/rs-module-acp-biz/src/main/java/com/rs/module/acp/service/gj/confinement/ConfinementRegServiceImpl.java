package com.rs.module.acp.service.gj.confinement;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.cons.CommonConstants;
import com.rs.module.acp.controller.admin.gj.vo.confinement.*;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsACPSaveVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.prisonroom.PrisonRoomChangAutoRecordVO;
import com.rs.module.acp.controller.admin.gj.vo.prisonroom.PrisonRoomChangeInOutSaveReqVO;
import com.rs.module.acp.entity.gj.PrisonRoomChangeDO;
import com.rs.module.acp.entity.gj.confinement.ConfinementRemoveDO;
import com.rs.module.acp.service.gj.InOutRecordsService;
import com.rs.module.acp.service.gj.prisonroom.PrisonRoomChangeService;
import com.rs.module.acp.util.ConfinementUtil;
import com.rs.module.acp.util.GjBusTraceUtil;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.service.sys.BusTraceService;
import com.rs.module.base.util.BspApprovalUtil;
import com.xxl.job.core.util.DateUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.acp.entity.gj.confinement.ConfinementRegDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.ConfinementRegDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-禁闭登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConfinementRegServiceImpl extends BaseServiceImpl<ConfinementRegDao, ConfinementRegDO> implements ConfinementRegService {

    @Resource
    private ConfinementRegDao confinementRegDao;
    @Resource
    private InOutRecordsService inOutRecordsService;

    @Resource
    private PrisonerService prisonerService;

    @Resource
    private BusTraceService busTraceService;

    @Resource
    private PrisonRoomChangeService prisonRoomChangeService;
    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createConfinementReg(ConfinementRegSaveReqVO createReqVO) throws Exception{
        createReqVO.setId(StringUtil.getGuid());
        // 插入
        ConfinementRegDO confinementReg = BeanUtils.toBean(createReqVO, ConfinementRegDO.class);
        //启动流程
        Map<String,String> processResult = BspApprovalUtil.commonStartProcessMap(ConfinementUtil.FLOW_DEF_KEY_CONFINEMENT_REG,
                confinementReg.getId(), "", "", null, HttpUtils.getAppCode());
        if(StringUtil.isEmpty(processResult.get("actInstId")) || StringUtil.isEmpty(processResult.get("taskId"))){
            throw new Exception("启动流程失败");
        }
        if(confinementReg.getIsAssociatedPunishment() == null) confinementReg.setIsAssociatedPunishment(0);
        confinementReg.setActInstId(processResult.get("actInstId"));
        confinementReg.setTaskId(processResult.get("taskId"));
        confinementReg.setStatus(ConfinementUtil.ApprovalStatusEnum.PENDING_APPROVAL.getCode());
        confinementRegDao.insert(confinementReg);
        // 返回
        return confinementReg.getId();
    }

    @Override
    public void updateConfinementReg(ConfinementRegSaveReqVO updateReqVO) {
        // 校验存在
        validateConfinementRegExists(updateReqVO.getId());
        // 更新
        ConfinementRegDO updateObj = BeanUtils.toBean(updateReqVO, ConfinementRegDO.class);
        confinementRegDao.updateById(updateObj);
    }

    @Override
    public void deleteConfinementReg(String id) {
        // 校验存在
        validateConfinementRegExists(id);
        // 删除
        confinementRegDao.deleteById(id);
    }

    private void validateConfinementRegExists(String id) {
        if (confinementRegDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-禁闭登记数据不存在");
        }
    }

    @Override
    public ConfinementRegDO getConfinementReg(String id) {
        return confinementRegDao.selectById(id);
    }

    @Override
    public PageResult<ConfinementRegDO> getConfinementRegPage(ConfinementRegPageReqVO pageReqVO) {
        return confinementRegDao.selectPage(pageReqVO);
    }

    @Override
    public List<ConfinementRegDO> getConfinementRegList(ConfinementRegListReqVO listReqVO) {
        return confinementRegDao.selectList(listReqVO);
    }
    //根据id 更新实际禁闭到期时间
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approve(ConfinementRegApproveVO approveReqVO) throws Exception{
        //从当前登录用户信息设置审批信息默认值
        if(StringUtil.isEmpty(approveReqVO.getApproverXm())) approveReqVO.setApproverXm(SessionUserUtil.getSessionUser().getName());
        if(StringUtil.isEmpty(approveReqVO.getApproverSfzh())) approveReqVO.setApproverSfzh(SessionUserUtil.getSessionUser().getIdCard());
        if(approveReqVO.getApproverTime() == null) approveReqVO.setApproverTime(new Date());
        ConfinementRegDO confinementRegDO = confinementRegDao.selectById(approveReqVO.getId());
        BspApproceStatusEnum isApprove = BspApproceStatusEnum.NOT_PASSED;
        String status = ConfinementUtil.ApprovalStatusEnum.APPROVAL_REJECTED.getCode();
        if(approveReqVO.getApprovalResult().equals("1")){//審批同意 ZD_DDGY_SPZT
            status = ConfinementUtil.ApprovalStatusEnum.REG_PENDING.getCode();
            isApprove = BspApproceStatusEnum.PASSED;
        }
        approveReqVO.setActInstId(confinementRegDO.getActInstId());
        approveReqVO.setTaskId(confinementRegDO.getTaskId());
        approveReqVO.setDefKey(ConfinementUtil.FLOW_DEF_KEY_CONFINEMENT_REG);
        BeanUtil.copyProperties( approveReqVO,confinementRegDO);
        confinementRegDO.setStatus(status);
        //调用流程审批接口
        String msgTit = "";
        String msgUrl = "";
        Map<String,String> approvalResult = BspApprovalUtil.approvalProcessMap(approveReqVO,
                isApprove,
                msgTit,
                msgUrl,
                true,
                null,
                null,
                HttpUtils.getAppCode()
        );
        if(StringUtil.isEmpty(approvalResult.get("actInstId")) || StringUtil.isEmpty(approvalResult.get("taskId"))){
            throw new ServerException("审批流程失败");
        }
        PrisonerVwRespVO vwRespVO = prisonerService.getPrisonerByJgrybm(confinementRegDO.getJgrybm());
        //写入出入登记表
        //inOutRecordsService.initOutRecords(confinementRegDO.getJgrybm(),  confinementRegDO.getRoomId(),"08", confinementRegDO.getId(),"0801");
        //推送带入带出消息给仓内屏
        confinementRegDO.setActualEndTime(confinementRegDO.getConfinementEndDate());
        String taskId = approvalResult.get("taskId");
        confinementRegDO.setTaskId(taskId);
        confinementRegDao.updateById(confinementRegDO);


        PrisonRoomChangAutoRecordVO prisonRoomChangAutoRecordVO = new PrisonRoomChangAutoRecordVO();
        prisonRoomChangAutoRecordVO.setJgrybm(confinementRegDO.getJgrybm());
        prisonRoomChangAutoRecordVO.setOldRoomId(confinementRegDO.getOriginalRoomId());
        prisonRoomChangAutoRecordVO.setNewRoomId(confinementRegDO.getRoomId());

        prisonRoomChangAutoRecordVO.setOldRoomName(areaPrisonRoomService.getAreaPrisonRoomName(confinementRegDO.getOriginalRoomId()));
        prisonRoomChangAutoRecordVO.setNewRoomName(areaPrisonRoomService.getAreaPrisonRoomName(confinementRegDO.getRoomId()));
        prisonRoomChangAutoRecordVO.setChangeReason("禁闭");
        prisonRoomChangAutoRecordVO.setBusinessType("0801");
        prisonRoomChangAutoRecordVO.setRoomChangeTime(confinementRegDO.getApproverTime());
        prisonRoomChangAutoRecordVO.setSourceBusinessId(confinementRegDO.getId());

        //写入监室待调整记录
        prisonRoomChangeService.automaticGenerationRecord(prisonRoomChangAutoRecordVO);

        busTraceService.saveBusTrace(BusTypeEnum.YEWU_JB, GjBusTraceUtil.buildConfinementBusTraceContent(confinementRegDO,vwRespVO.getXm()),
                confinementRegDO.getJgrybm(),
                SessionUserUtil.getSessionUser().getOrgCode(),
                confinementRegDO.getId());
        if(StringUtil.isNotEmpty(taskId)) {
            return true;
        }
        return false;
    }
    //根据id 更新实际禁闭到期时间
    @Override
    public boolean updateActualEndTimeByNum(String id, int daynum) {
        ConfinementRegDO confinementRegDO = confinementRegDao.selectById(id);
        Date endDate = confinementRegDO.getConfinementEndDate();
        //endDate+daynum 赋值给actualEndTime
        Date actualEndTime = DateUtil.addDays(endDate, daynum);
        confinementRegDO.setActualEndTime(actualEndTime);
        confinementRegDao.updateById(confinementRegDO);
        return true;
    }

    @Override
    public boolean updateActualEndTime(String id, Date endDate) {
        ConfinementRegDO confinementRegDO = new ConfinementRegDO();
        confinementRegDO.setId(id);
        confinementRegDO.setActualEndTime(endDate);
        confinementRegDao.update(confinementRegDO,new UpdateWrapper<ConfinementRegDO>().eq("id", id));
        return true;
    }

    @Override
    public List<AreaPrisonRoomDO> getConfinementRoomList(String nowRoomId) {
        String orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        return confinementRegDao.getConfinementRoomList(nowRoomId,orgCode);
    }
    @Override
    public boolean saveInOutRecords(InOutRecordsACPSaveVO saveReqVO) {
        InOutRecordsSaveReqVO inOutRecordsSaveReqVO = BeanUtils.toBean(saveReqVO, InOutRecordsSaveReqVO.class);
        ConfinementRegDO confinementRegDO = getConfinementReg(saveReqVO.getId());
        if(confinementRegDO == null){
            throw new RuntimeException("请传入正确的参数！");
        }
        if("05".equals(confinementRegDO.getStatus())){
            throw new RuntimeException("当前人员已经禁闭中！");
        }
        inOutRecordsSaveReqVO.setId("");
        inOutRecordsSaveReqVO.setBusinessId(saveReqVO.getId());
        inOutRecordsSaveReqVO.setJgrybm(confinementRegDO.getJgrybm());
        PrisonerVwRespVO vwRespVO = prisonerService.getPrisonerByJgrybm(confinementRegDO.getJgrybm());
        inOutRecordsSaveReqVO.setJgryxm(vwRespVO.getXm());
        inOutRecordsSaveReqVO.setRoomId(vwRespVO.getJsh());
        inOutRecordsSaveReqVO.setBusinessType("08");
        inOutRecordsSaveReqVO.setBusinessSubType("0802");
        inOutRecordsService.acpSaveInOutRecords(inOutRecordsSaveReqVO,confinementRegDO.getRoomId(),saveReqVO.getInTime());
        confinementRegDO.setStatus("05");//更新状态为禁闭中
        confinementRegDao.updateById(confinementRegDO);
        // 同步数据到监室调整
        prisonRoomChangeService.saveInOutRecords(saveReqVO);
        return false;
    }

    @Override
    public List<ConfinementRegDO> getNotAllowedRecords(String jgrybm) {
        //该人员是否是处于【禁闭中】【禁闭待审核】【待解除】状态下，若是，则进行对应状态提示，不允许选择该人员
        return confinementRegDao.selectList(
                new LambdaQueryWrapper<ConfinementRegDO>().eq(ConfinementRegDO::getJgrybm, jgrybm)
                .eq(ConfinementRegDO::getIsDel, 0)
                .in(ConfinementRegDO::getStatus, ConfinementUtil.ApprovalStatusEnum.CONFINING.getCode(), ConfinementUtil.ApprovalStatusEnum.PENDING_APPROVAL.getCode(), ConfinementUtil.ApprovalStatusEnum.RELEASE_PENDING.getCode())
        );
    }

    @Override
    public void updateStatus(String confinementId, String status) {
        //使用wrapper条件更新
        ConfinementRegDO confinementRegDO = new ConfinementRegDO();
        confinementRegDO.setStatus(status);
        UpdateWrapper updateWrapper = new UpdateWrapper<>().eq("id", confinementId);
        confinementRegDao.update(confinementRegDO,updateWrapper);
    }
    //查询禁闭到期记录
}
