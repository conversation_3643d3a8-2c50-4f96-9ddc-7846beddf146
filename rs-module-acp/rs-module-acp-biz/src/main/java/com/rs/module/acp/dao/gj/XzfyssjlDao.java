package com.rs.module.acp.dao.gj;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.gj.vo.xzfyssjl.XzfyssjlListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.xzfyssjl.XzfyssjlPageReqVO;
import com.rs.module.acp.entity.gj.XzfyssjlDO;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;

/**
* 实战平台-管教业务-行政复议讼诉记录 Dao
*
* <AUTHOR>
*/
@Mapper
public interface XzfyssjlDao extends IBaseDao<XzfyssjlDO> {


    default PageResult<XzfyssjlDO> selectPage(XzfyssjlPageReqVO reqVO) {
        Page<XzfyssjlDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<XzfyssjlDO> wrapper = new LambdaQueryWrapperX<XzfyssjlDO>()
            .eqIfPresent(XzfyssjlDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(XzfyssjlDO::getBusinessType, reqVO.getBusinessType())
            .eqIfPresent(XzfyssjlDO::getRemark, reqVO.getRemark())
            .eqIfPresent(XzfyssjlDO::getStatus, reqVO.getStatus())
            .eqIfPresent(XzfyssjlDO::getHandlePoliceSfzh, reqVO.getHandlePoliceSfzh())
            .eqIfPresent(XzfyssjlDO::getHandlePoliceXm, reqVO.getHandlePoliceXm())
            .betweenIfPresent(XzfyssjlDO::getHandlePoliceTime, reqVO.getHandlePoliceTime())
            .betweenIfPresent(XzfyssjlDO::getForwardTime, reqVO.getForwardTime())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(XzfyssjlDO::getAddTime);
        }
        Page<XzfyssjlDO> xzfyssjlPage = selectPage(page, wrapper);
        return new PageResult<>(xzfyssjlPage.getRecords(), xzfyssjlPage.getTotal());
    }
    default List<XzfyssjlDO> selectList(XzfyssjlListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<XzfyssjlDO>()
            .eqIfPresent(XzfyssjlDO::getJgrybm, reqVO.getJgrybm())
            .eqIfPresent(XzfyssjlDO::getBusinessType, reqVO.getBusinessType())
            .eqIfPresent(XzfyssjlDO::getRemark, reqVO.getRemark())
            .eqIfPresent(XzfyssjlDO::getStatus, reqVO.getStatus())
            .eqIfPresent(XzfyssjlDO::getHandlePoliceSfzh, reqVO.getHandlePoliceSfzh())
            .eqIfPresent(XzfyssjlDO::getHandlePoliceXm, reqVO.getHandlePoliceXm())
            .betweenIfPresent(XzfyssjlDO::getHandlePoliceTime, reqVO.getHandlePoliceTime())
            .betweenIfPresent(XzfyssjlDO::getForwardTime, reqVO.getForwardTime())
        .orderByDesc(XzfyssjlDO::getAddTime));    }


    default Page<XzfyssjlDO> getAppXzfyssjlPage(Page<XzfyssjlDO> page, String jgrybm, Date startTime, Date endTime){
        LambdaQueryWrapperX<XzfyssjlDO> wrapper = new LambdaQueryWrapperX<XzfyssjlDO>()
                .eq(XzfyssjlDO::getJgrybm, jgrybm)
                .geIfPresent(XzfyssjlDO::getAddTime, startTime)
                .ltIfPresent(XzfyssjlDO::getAddTime, endTime);
        wrapper.orderByDesc(XzfyssjlDO::getAddTime);
        return selectPage(page, wrapper);
    }
}
