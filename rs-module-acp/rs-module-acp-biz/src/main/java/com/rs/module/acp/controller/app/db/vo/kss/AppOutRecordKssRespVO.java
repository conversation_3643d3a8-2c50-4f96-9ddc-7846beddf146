package com.rs.module.acp.controller.app.db.vo.kss;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.controller.app.db.vo.AppPersonalEffectsSubRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "app端 - 实战平台-羁押业务-出所登记（看守所） Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppOutRecordKssRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("批准/执行机关")
    private String pzzxjg;
    @ApiModelProperty("批准/执行人")
    private String pzzxr;
    @ApiModelProperty("批准日期")
    private Date pzrq;
    @ApiModelProperty("出所时间")
    private Date cssj;
    @ApiModelProperty("出所原因")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SFCSCSYY")
    private String csyy;
    @ApiModelProperty("出所去向")
    private String csqx;
    @ApiModelProperty("出所凭证")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SFCSCSPZ")
    private String cspz;
    @ApiModelProperty("出所凭证文书号")
    private String cspzwsh;
    @ApiModelProperty("出所凭证文书号")
    private String cspzwsdz;
    @ApiModelProperty("转去公安监所名称")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ORG_ID")
    private String zqgajsmc;
    @ApiModelProperty("档案材料移交情况")
    private String daclyjqk;
    @ApiModelProperty("经办人")
    private String jbr;
    @ApiModelProperty("经办时间")
    private Date jbsj;


    @ApiModelProperty("是否为他人捎带物品")
    @Trans(type = TransType.DICTIONARY,key = "ZD_TYDMSFNEW")
    private Short sfwtrsdwp;
    @ApiModelProperty("捎带物品类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_WPLX")
    private String sdwplx;
    @ApiModelProperty("是否有帮其他人串供行为")
    @Trans(type = TransType.DICTIONARY,key = "ZD_TYDMSFNEW")
    private Short sfybzqtrcgxw;
    @ApiModelProperty("详细情况记录")
    private String xxqkjl;
    @ApiModelProperty("是否发现其他违法犯罪行为")
    @Trans(type = TransType.DICTIONARY,key = "ZD_TYDMSFNEW")
    private Short sffxqtwffzxs;
    @ApiModelProperty("其他违法犯罪行为")
    private String qtwffzxw;


    @ApiModelProperty("财物交接情况")
    private String cwjjqk;
    @ApiModelProperty("取出原因")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SFCSQCYY")
    private String qcyy;
    @ApiModelProperty("取出方式")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SFCSQCFS")
    private String qcfs;
    @ApiModelProperty("备注")
    private String bz;
    @ApiModelProperty("物品信息")
    private List<AppPersonalEffectsSubRespVO> effectsList;


}
