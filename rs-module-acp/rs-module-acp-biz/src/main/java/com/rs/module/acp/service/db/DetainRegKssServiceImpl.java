package com.rs.module.acp.service.db;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.cache.DicUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BpmApi;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.common.util.string.NameKeywordGenerator;
import com.rs.module.acp.cons.DetainRegStatusEnum;
import com.rs.module.acp.cons.InProcessStageEnum;
import com.rs.module.acp.cons.InTypeEnums;
import com.rs.module.acp.cons.LeaderApprovalStatusEnum;
import com.rs.module.acp.controller.admin.db.DetainApi;
import com.rs.module.acp.controller.admin.db.dto.CasePersonnelRespVO;
import com.rs.module.acp.controller.admin.db.vo.CollectedPersonDetailVO;
import com.rs.module.acp.controller.admin.db.vo.CombineRespVO;
import com.rs.module.acp.controller.admin.db.vo.DetainRegKssListReqVO;
import com.rs.module.acp.controller.admin.db.vo.DetainRegKssPageReqVO;
import com.rs.module.acp.controller.admin.db.vo.DetainRegKssSaveReqVO;
import com.rs.module.acp.controller.admin.db.vo.HealthCheckListReqVO;
import com.rs.module.acp.controller.admin.db.vo.InRecordStatusVO;
import com.rs.module.acp.controller.admin.db.vo.LeaderApprovalStatusReqVO;
import com.rs.module.acp.controller.admin.db.vo.dbSocialRelationsSaveReqVO;
import com.rs.module.acp.controller.admin.db.vo.prisonerInfoReqVO;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.dao.db.DetainRegKssDao;
import com.rs.module.acp.dao.db.HealthCheckDao;
import com.rs.module.acp.entity.db.DetainRegKssDO;
import com.rs.module.acp.entity.db.HealthCheckDO;
import com.rs.module.acp.entity.gj.EquipmentUseDO;
import com.rs.module.acp.enums.gj.EquipmentStatusEnum;
import com.rs.module.acp.enums.gj.PrisonRoomStatusEnum;
import com.rs.module.acp.service.db.components.PrisonerInInfoSyncContext;
import com.rs.module.acp.service.db.components.RegistrationInfoService;
import com.rs.module.acp.service.ds.DSPrisonRoomChangeService;
import com.rs.module.acp.util.GeneralUtil;
import com.rs.module.acp.util.GjBusTraceUtil;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.util.AgeCalculatorUtil;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;


/**
 * 实战平台-羁押业务-看守所收押登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service("detainRegKssService")
@Validated
@Slf4j
public class DetainRegKssServiceImpl extends BaseServiceImpl<DetainRegKssDao, DetainRegKssDO> implements DetainRegKssService, RegistrationInfoService, DetainApi {

    @Resource
    private BspApi bspApi;

    @Resource
    private DetainRegKssDao detainRegKssDao;

    @Resource
    private DbSocialRelationsService socialRelationsService;

    @Resource
    private HealthCheckDao healthCheckDao;

    @Resource
    private PrisonerService prisonerService;

    @Resource
    private PrisonerInInfoSyncContext prisonerInInfoSyncContext;

    @Resource
    private DSPrisonRoomChangeService dsPrisonRoomChangeService;

    private static final String defKey = "shouyarusuoshenpiliuchengkanshousuo";

    @Override
    @Transactional
    public String createDetainRegKss(DetainRegKssSaveReqVO createReqVO) {
        List<DetainRegKssDO> list = list(new LambdaQueryWrapper<DetainRegKssDO>()
                .eq(DetainRegKssDO::getZjhm, createReqVO.getZjhm())
                .ne(DetainRegKssDO::getCurrentStep, InProcessStageEnum.COMPLETED.getCode()));
        if (list.size() > 0) {
            throw new ServerException("该人员正在登记入所，无法再次入所");
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String orgCode = sessionUser.getOrgCode();
        PrisonerVwRespVO prisoner = prisonerService.getPrisonerByZjhm(orgCode, createReqVO.getZjhm());
        if (ObjectUtil.isNotEmpty(prisoner)) {
            throw new ServerException("该人员已经入所，无法再次入所");
        }


//        String rybh = GeneralUtil.generateRybh(orgCode);
        String rybh = bspApi.executeByRuleCode("acp_jgry_bm_system", null);
        //演示数据使用##########
        if (createReqVO.getZjhm().equals("510104198805133334")) {
            rybh = "000000000000202507010334";
        }
        if (createReqVO.getZjhm().equals("320586198510127890")) {
            rybh = "000000000000202507010113";
        }
        // 插入

        //如果提交的状态为操作02，则设置当前阶段为01
        if (createReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.DRAFT.getCode())) {
            createReqVO.setCurrentStep(InProcessStageEnum.PENDING_REGISTRATION.getCode());
        }
        if (createReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())) {
            createReqVO.setCurrentStep(InProcessStageEnum.PENDING_HEALTH_CHECK.getCode());
        }
        DetainRegKssDO detainRegKss = BeanUtils.toBean(createReqVO, DetainRegKssDO.class);
        detainRegKss.setId(StringUtil.getGuid32());
        detainRegKss.setRybh(rybh);
        detainRegKss.setJgrybm(rybh);
        //如果是快速入所类型，则设置所领导审批为待审批
        if (createReqVO.getRslx() != null && createReqVO.getRslx().equalsIgnoreCase(InTypeEnums.EMERGENCY.getCode())
                && createReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())
                && (createReqVO.getActInstId() == null || createReqVO.getActInstId().equals(""))) {
            detainRegKss.setSpzt(LeaderApprovalStatusEnum.PENDING.getCode());
            detainRegKss.setCurrentStep(InProcessStageEnum.PENDING_LEADER_APPROVAL.getCode());
        }
        // 根据xm生成xm拼音
        if (StrUtil.isNotBlank(detainRegKss.getXm())) {
            String xmpy = NameKeywordGenerator.generateKeywords(detainRegKss.getXm());
            detainRegKss.setXmpy(xmpy);
        }

        detainRegKssDao.insert(detainRegKss);

        // 插入子表
        List<dbSocialRelationsSaveReqVO> socialRelations = createReqVO.getSocialRelations();
        if (socialRelations != null && !socialRelations.isEmpty()) {
            for (dbSocialRelationsSaveReqVO socialRelation : socialRelations) {
                socialRelation.setId(StringUtil.getGuid32());
                socialRelation.setRybh(detainRegKss.getRybh());
                socialRelationsService.createdbSocialRelations(socialRelation);
            }
        }
        // 返回
        return detainRegKss.getJgrybm();
    }

    @Override
    @Transactional
    public void updateDetainRegKss(DetainRegKssSaveReqVO updateReqVO) {
        // 校验存在
        validateDetainRegKssExists(updateReqVO.getId());
        // 更新
        DetainRegKssDO updateObj = BeanUtils.toBean(updateReqVO, DetainRegKssDO.class);
        // 根据xm生成xm拼音
        if (StrUtil.isNotBlank(updateObj.getXm())) {
            String xmpy = NameKeywordGenerator.generateKeywords(updateObj.getXm());
            updateObj.setXmpy(xmpy);
        }
        detainRegKssDao.updateById(updateObj);
    }

    @Override
    public void deleteDetainRegKss(String id) {
        // 校验存在
        validateDetainRegKssExists(id);
        // 删除
        detainRegKssDao.deleteById(id);
    }

    private void validateDetainRegKssExists(String id) {
        if (detainRegKssDao.selectById(id) == null) {
            throw new ServerException("实战平台-羁押业务-看守所收押登记数据不存在");
        }
    }

    @Override
    public DetainRegKssDO getDetainRegKss(String id) {
        return detainRegKssDao.selectById(id);
    }

    @Override
    public PageResult<DetainRegKssDO> getDetainRegKssPage(DetainRegKssPageReqVO pageReqVO) {
        return detainRegKssDao.selectPage(pageReqVO);
    }

    @Override
    public List<DetainRegKssDO> getDetainRegKssList(DetainRegKssListReqVO listReqVO) {
        return detainRegKssDao.selectList(listReqVO);
    }

    @Override
    public CombineRespVO getCombineInfo(String rybh) {
        DetainRegKssListReqVO reqVO = new DetainRegKssListReqVO();
        reqVO.setRybh(rybh);
        List<DetainRegKssDO> dlist = detainRegKssDao.selectList(reqVO);

        DetainRegKssDO db = new DetainRegKssDO();
        if (dlist != null && !dlist.isEmpty()) {
            db = dlist.get(0);
        } else if (dlist != null && dlist.size() == 0) {
            return null;
        }
//        DetainRegKssDO db = detainRegKssDao.selectById(rybh);
        HealthCheckListReqVO listReqVO = new HealthCheckListReqVO();
        listReqVO.setRybh(db.getRybh());
        List<HealthCheckDO> list = healthCheckDao.selectList(listReqVO);
        CombineRespVO combineRespVO = BeanUtils.toBean(db, CombineRespVO.class);
        //无涉嫌罪名字段，用按键类别代替
        combineRespVO.setSszx(db.getAjlb());
        if (list != null && list.size() > 0) {
            HealthCheckDO healthCheckDO = list.get(0);
            combineRespVO.setYsyj(healthCheckDO.getYsyj());
            combineRespVO.setJcr(healthCheckDO.getJcr());
            combineRespVO.setJcsj(healthCheckDO.getJcsj());
            combineRespVO.setBz(healthCheckDO.getBz());
        }

        return combineRespVO;
    }

    @Override
    public void approval(GjApproveReqVO approveReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        DetainRegKssDO entity = detainRegKssDao.selectById(approveReqVO.getId());
        Assert.notNull(entity, StrUtil.format("传入ID有误 id：{}", entity.getId()));

        entity.setJbr(sessionUser.getName());
        entity.setJbsj(new Date());
        String approvalResult = approveReqVO.getApprovalResult();
        BspApproceStatusEnum bspApproceStatusEnum;
        if (String.valueOf(BspApproceStatusEnum.PASSED.getCode()).equals(approvalResult)) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED;
            entity.setSpzt("2");
        } else {
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
            entity.setSpzt("3");
        }
        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(entity.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }
        entity.setApprovalResult(approveReqVO.getApprovalComments());

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", entity.getId());

        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getIdCard());
        nowApproveUser.put("name", sessionUser.getName());
        boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ||
                BspApproceStatusEnum.NOT_PASSED_END.getCode() == bspApproceStatusEnum.getCode();
        String msgUrl = StrUtil.format("/#/detentionBusiness/detentionEnterRegister?eventCode={}", entity.getRybh());
        // TODO 移动警务跳转路由待前端提供
        String mobileUrl = StrUtil.format("/todoItem//{}", approveReqVO.getId());
        JSONObject result = BspApprovalUtil.approvalProcess(defKey, entity.getActInstId(), entity.getTaskId(), entity.getId(),
                bspApproceStatusEnum, approveReqVO.getApprovalComments(), null, msgUrl, mobileUrl, terminateTask,
                variables, nowApproveUser, HttpUtils.getAppCode());
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            entity.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程审批失败:" + result.getString("msg"));
        }

        // 判断流程是否结束
        Boolean finishProcinst = BspApprovalUtil.getBpmApi().isFinishProcinst(entity.getActInstId());
        if (finishProcinst) {
            updateLeaderApprovalStatus(BeanUtils.toBean(entity, LeaderApprovalStatusReqVO.class));
        }
    }

    /**
     * 更新领导审批状态
     *
     * 此方法首先验证指定的被拘留人员登记记录是否存在，然后更新其领导审批状态
     *
     * @param updateReqVO 包含更新请求数据的实体类
     */
    @Override
    public void updateLeaderApprovalStatus(LeaderApprovalStatusReqVO updateReqVO) {
        // 校验存在
        //    validateDetainRegKssExists(updateReqVO.getId());
        log.info("=====================");
        log.info(JSONObject.toJSONString(updateReqVO));
        DetainRegKssDO detainRegKssDO = new DetainRegKssDO();
        if (updateReqVO.getRybh() != null && updateReqVO.getRybh() != "") {
            detainRegKssDO = getPrisonerInfo(updateReqVO.getRybh());
        }

        //入所类型
        String rslx = detainRegKssDO.getRslx();

        // 更新
        DetainRegKssDO updateObj = BeanUtils.toBean(updateReqVO, DetainRegKssDO.class);
        updateObj.setId(detainRegKssDO.getId());
        //如果是快速入所，所领导审批完成后流转到健康检查
        if (rslx != null && rslx.equalsIgnoreCase("02")) {
            //原快速入所所领导审批后流转到健康登记
//            updateObj.setCurrentStep(InProcessStageEnum.PENDING_HEALTH_CHECK.getCode());
            //现所领导审批后需流转到第一步登记信息，便于补录信息
            updateObj.setCurrentStep(InProcessStageEnum.PENDING_REGISTRATION.getCode());
            updateObj.setStatus(DetainRegStatusEnum.DRAFT.getCode());
        } else {
            updateObj.setCurrentStep(InProcessStageEnum.COMPLETED.getCode());
        }
        detainRegKssDao.updateById(updateObj);

        //如果领导审批完成了，则从acp_db_in_record_kss写入在所表acp_pm_prisoner_kss_in
        syncDataToPrisonerKssIn(updateReqVO);
        try {
            dsPrisonRoomChangeService.kssSave(detainRegKssDO);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void syncDataToPrisonerKssIn(LeaderApprovalStatusReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String orgCode = sessionUser.getOrgCode();
        String id = updateReqVO.getId();
        //领导同意，则同步数据
        if (updateReqVO.getSpzt() != null && updateReqVO.getSpzt().equalsIgnoreCase(LeaderApprovalStatusEnum.APPROVED.getCode())) {
            //查詢是否已存在該人員
            String idPrisonerKssIn = detainRegKssDao.ifExistsInPrisonerKssIn(updateReqVO.getRybh());
            if (idPrisonerKssIn != null && !idPrisonerKssIn.equals("")) {
                detainRegKssDao.deleteExitPrisoner(updateReqVO.getRybh());
            }

            detainRegKssDao.syncDataToPrisonerKssIn(id);

            //同步数据到各个模块--TODO 过渡开始日期不能为空
            prisonerInInfoSyncContext.getHandler("kss").syncDataToTransitionRoom(updateReqVO.getRybh(), orgCode, updateReqVO.getId());
            //同步办案人员给其他模块--TODO 姓名不能为空
            prisonerInInfoSyncContext.getHandler("kss").syncDataToCasePersonnel(updateReqVO.getRybh(), orgCode, updateReqVO.getId());

            prisonerInInfoSyncContext.getHandler("kss").syncToPtmGoods(updateReqVO.getRybh(), orgCode, updateReqVO.getId());
            //同步社会关系数据
            prisonerInInfoSyncContext.getHandler("kss").syncSocialRelations(updateReqVO.getRybh(), orgCode, updateReqVO.getId());
        }
    }


    @Override
    public DetainRegKssDO getPrisonerInfo(String rybh) {
        DetainRegKssListReqVO reqVO = new DetainRegKssListReqVO();
        reqVO.setRybh(rybh);
        List<DetainRegKssDO> dlist = detainRegKssDao.selectList(reqVO);

        DetainRegKssDO db = new DetainRegKssDO();
        if (dlist != null && !dlist.isEmpty()) {
            db = dlist.get(0);
        } else if (dlist == null || dlist.isEmpty()) {
            return null;
        }
        return db;
    }

    @Override
    public String ifExists(DetainRegKssSaveReqVO updateReqVO) {
        String id = "";
        if (detainRegKssDao.selectById(updateReqVO.getId()) == null) {
            id = createDetainRegKss(updateReqVO);
        }
        return id;
    }

    @Override
    public InRecordStatusVO getInRecordStatus(String rybh) {
        InRecordStatusVO db = detainRegKssDao.getInRecordStatus(rybh);

        return db;
    }

    @Override
    public void updateStepInfo(String prison, String rybh, String status, String currentStep, String spzt, String actInstId) {
        HealthCheckListReqVO listReqVO = new HealthCheckListReqVO();
        listReqVO.setRybh(rybh);
        List<HealthCheckDO> list = healthCheckDao.selectList(listReqVO);
        HealthCheckDO healthCheckDO = new HealthCheckDO();
        if (list != null && !list.isEmpty()) {
            healthCheckDO = list.get(0);
        }
        DetainRegKssDO prisonerInfo = getPrisonerInfo(rybh);
        String rslx = prisonerInfo.getRslx();
        //prison == ek
        if (prison.equalsIgnoreCase("ek") && rslx != null && rslx.equalsIgnoreCase(InTypeEnums.NORMAL.getCode())) {
            //currentStep == 02 and status == 03 and spzt == 2,then update currentStep to 05,status to 01,spzt to 01
            if (currentStep.equalsIgnoreCase("02") && status.equalsIgnoreCase("03") && spzt.equalsIgnoreCase("2")) {
                detainRegKssDao.updateStepInfo(rybh, DetainRegStatusEnum.PENDING.getCode(), InProcessStageEnum.PENDING_LEADER_APPROVAL.getCode(), LeaderApprovalStatusEnum.PENDING.getCode(), actInstId);
            }
            //currentStep == 02 and status == 03 and spzt == 3,then update currentStep to 05,status to 01,spzt to 01
            if (currentStep.equalsIgnoreCase("02") && status.equalsIgnoreCase("03") && spzt.equalsIgnoreCase("3")) {
                detainRegKssDao.updateStepInfo(rybh, DetainRegStatusEnum.PENDING.getCode(), InProcessStageEnum.PENDING_LEADER_APPROVAL.getCode(), LeaderApprovalStatusEnum.PENDING.getCode(), actInstId);
            }
            //领导审批
            //currentStep == 05 and status == 03 and spzt == 2,then update currentStep to 03,status to 01,spzt to 2
            if (currentStep.equalsIgnoreCase("05") && status.equalsIgnoreCase("03") && spzt.equalsIgnoreCase("2")) {
                String ysyj = healthCheckDO.getYsyj();
                if (ysyj.equalsIgnoreCase("1")) {
                    detainRegKssDao.updateStepInfo(rybh, DetainRegStatusEnum.PENDING.getCode(), InProcessStageEnum.PENDING_BIO_INFO_COLLECTION.getCode(), LeaderApprovalStatusEnum.APPROVED.getCode(), actInstId);
                } else {
                    detainRegKssDao.updateStepInfo(rybh, DetainRegStatusEnum.SUBMITTED.getCode(), InProcessStageEnum.COMPLETED.getCode(), LeaderApprovalStatusEnum.APPROVED.getCode(), actInstId);
                }

            }
            //currentStep == 05 and status == 03 and spzt == 3,then update currentStep to 03,status to 01,spzt to 2
            if (currentStep.equalsIgnoreCase("05") && status.equalsIgnoreCase("03") && spzt.equalsIgnoreCase("3")) {
                String ysyj = healthCheckDO.getYsyj();
                if (ysyj.equalsIgnoreCase("1")) {
                    detainRegKssDao.updateStepInfo(rybh, DetainRegStatusEnum.SUBMITTED.getCode(), InProcessStageEnum.COMPLETED.getCode(), LeaderApprovalStatusEnum.REJECTED.getCode(), actInstId);
                } else {
                    detainRegKssDao.updateStepInfo(rybh, DetainRegStatusEnum.PENDING.getCode(), InProcessStageEnum.PENDING_BIO_INFO_COLLECTION.getCode(), LeaderApprovalStatusEnum.REJECTED.getCode(), actInstId);
                }

            }
            //生物信息采集
            //currentStep == 03 and status == 03 ,then update currentStep to 04,status to 01
            if (currentStep.equalsIgnoreCase("03") && status.equalsIgnoreCase("03")) {
                detainRegKssDao.updateStepInfo(rybh, DetainRegStatusEnum.PENDING.getCode(), InProcessStageEnum.PENDING_ITEM_REGISTRATION.getCode(), "", "");

            }
            //物品登记
            //currentStep == 04 and status == 03 ,then update currentStep to 06,status to 03
            if (currentStep.equalsIgnoreCase("04") && status.equalsIgnoreCase("03")) {
                detainRegKssDao.updateStepInfo(rybh, DetainRegStatusEnum.SUBMITTED.getCode(), InProcessStageEnum.COMPLETED.getCode(), "", "");

                DetainRegKssDO db = detainRegKssDao.selectOne("rybh", rybh);
                LeaderApprovalStatusReqVO updateReqVO = new LeaderApprovalStatusReqVO();
                updateReqVO.setId(db.getId());
                updateReqVO.setSpzt(LeaderApprovalStatusEnum.APPROVED.getCode());
                updateReqVO.setRybh(rybh);
                syncDataToPrisonerKssIn(updateReqVO);
            }
        }
        if (prison.equalsIgnoreCase("ek") && rslx != null && rslx.equalsIgnoreCase(InTypeEnums.EMERGENCY.getCode())) {
            if (currentStep.equalsIgnoreCase("01") && status.equalsIgnoreCase("03") && actInstId != null) {
                detainRegKssDao.updateStepInfo(rybh, DetainRegStatusEnum.SUBMITTED.getCode(), InProcessStageEnum.PENDING_HEALTH_CHECK.getCode(), "", actInstId);
            }
            if (currentStep.equalsIgnoreCase("01") && status.equalsIgnoreCase("03") && (actInstId == null || actInstId == "")) {
                detainRegKssDao.updateStepInfo(rybh, DetainRegStatusEnum.SUBMITTED.getCode(), InProcessStageEnum.PENDING_LEADER_APPROVAL.getCode(), "", actInstId);
            }
            //currentStep == 02 and status == 03 and spzt == 2,then update currentStep to 05,status to 01,spzt to 01
            if (currentStep.equalsIgnoreCase("02") && status.equalsIgnoreCase("03")) {
                detainRegKssDao.updateStepInfo(rybh, DetainRegStatusEnum.SUBMITTED.getCode(), InProcessStageEnum.PENDING_BIO_INFO_COLLECTION.getCode(), "", actInstId);
            }
            //currentStep == 02 and status == 03 and spzt == 3,then update currentStep to 05,status to 01,spzt to 01
            if (currentStep.equalsIgnoreCase("03") && status.equalsIgnoreCase("03")) {
                detainRegKssDao.updateStepInfo(rybh, DetainRegStatusEnum.SUBMITTED.getCode(), InProcessStageEnum.PENDING_ITEM_REGISTRATION.getCode(), "", actInstId);
            }
            //领导审批
            //currentStep == 05 and status == 03 and spzt == 2,then update currentStep to 03,status to 01,spzt to 2
            if (currentStep.equalsIgnoreCase("04") && status.equalsIgnoreCase("03")) {
                detainRegKssDao.updateStepInfo(rybh, DetainRegStatusEnum.SUBMITTED.getCode(), InProcessStageEnum.COMPLETED.getCode(), "", actInstId);

            }
            //currentStep == 05 and status == 03 and spzt == 3,then update currentStep to 03,status to 01,spzt to 2
            if (currentStep.equalsIgnoreCase("05") && status.equalsIgnoreCase("03") && spzt.equalsIgnoreCase("3")) {

                detainRegKssDao.updateStepInfo(rybh, DetainRegStatusEnum.SUBMITTED.getCode(), InProcessStageEnum.COMPLETED.getCode(), LeaderApprovalStatusEnum.REJECTED.getCode(), actInstId);
                DetainRegKssDO db = detainRegKssDao.selectOne("rybh", rybh);
                LeaderApprovalStatusReqVO updateReqVO = new LeaderApprovalStatusReqVO();
                updateReqVO.setId(db.getId());
                updateReqVO.setSpzt(LeaderApprovalStatusEnum.APPROVED.getCode());
                updateReqVO.setRybh(rybh);
                syncDataToPrisonerKssIn(updateReqVO);
            }
            if (currentStep.equalsIgnoreCase("05") && status.equalsIgnoreCase("03") && spzt.equalsIgnoreCase("2")) {

//                detainRegKssDao.updateStepInfo(rybh,DetainRegStatusEnum.SUBMITTED.getCode(),InProcessStageEnum.PENDING_HEALTH_CHECK.getCode(),LeaderApprovalStatusEnum.APPROVED.getCode(),actInstId);
                detainRegKssDao.updateStepInfo(rybh, DetainRegStatusEnum.DRAFT.getCode(), InProcessStageEnum.PENDING_REGISTRATION.getCode(), LeaderApprovalStatusEnum.APPROVED.getCode(), actInstId);

            }
            //生物信息采集
            //currentStep == 03 and status == 03 ,then update currentStep to 04,status to 01
            if (currentStep.equalsIgnoreCase("03") && status.equalsIgnoreCase("03")) {
                detainRegKssDao.updateStepInfo(rybh, DetainRegStatusEnum.PENDING.getCode(), InProcessStageEnum.PENDING_ITEM_REGISTRATION.getCode(), "", "");

            }

        }

        //prison == sk
        if (prison.equalsIgnoreCase("sk")) {

        }
    }

    @Override
    public CommonResult<CasePersonnelRespVO> getCasePersonnel(String jgrybm) {
        DetainRegKssDO detainRegKssDO = getPrisonerInfo(jgrybm);
        CasePersonnelRespVO casePersonnelRespVO = BeanUtils.toBean(detainRegKssDO, CasePersonnelRespVO.class);
        return CommonResult.success(casePersonnelRespVO);
    }


    @Override
    public boolean updateStateInfo(String rybh, String spzt, String currentStep) {
        DetainRegKssDO detainRegKssDO = getPrisonerInfo(rybh);
        if (detainRegKssDO != null) {
            // 如果spzt==null 则保持原始值
            if (spzt == null) {
                spzt = detainRegKssDO.getSpzt();
            }
            // 如果currentStep==null 则保持原始值
            if (currentStep == null) {
                currentStep = detainRegKssDO.getCurrentStep();
            }
            detainRegKssDO.setSpzt(spzt);
            detainRegKssDO.setCurrentStep(currentStep);
            detainRegKssDao.updateById(detainRegKssDO);
        }

        return true;
    }

    @Override
    public boolean updateWristbandInfo(String rybh, String shid, String shbdzt, Date sdbdsj, String status) {
        DetainRegKssDO detainRegKssDO = getPrisonerInfo(rybh);
        if (detainRegKssDO != null) {
            // 更新手环信息
            detainRegKssDO.setShid(shid);
            detainRegKssDO.setShbdzt(shbdzt);
            detainRegKssDO.setSdbdsj(sdbdsj);
            detainRegKssDO.setStatus(status);
            detainRegKssDao.updateById(detainRegKssDO);
        }

        return true;
    }

    @Override
    public String getRslxInfo(String rybh) {
        DetainRegKssDO db = getPrisonerInfo(rybh);
        return db.getRslx();
    }

    @Override
    public CollectedPersonDetailVO getCollectedPersonDetail(prisonerInfoReqVO prisonerInfoReqVO) {
        DetainRegKssDO db = getPrisonerInfo(prisonerInfoReqVO.getJgrybm());
        return convertToCollectedPersonDetailVO(db);
    }

    private CollectedPersonDetailVO convertToCollectedPersonDetailVO(DetainRegKssDO entity) {
        CollectedPersonDetailVO vo = new CollectedPersonDetailVO();
        String appCode = HttpUtils.getAppCode();
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        vo.setJZRYBH(entity.getJgrybm());
        vo.setRYBH(entity.getJgrybm());
        vo.setAJBH(entity.getAjbh());
        vo.setXM(entity.getXm());
        vo.setXMHYPY("");
        vo.setGMSFHM(entity.getZjhm());
        vo.setCYZJDM("");
        vo.setCYZJ("");
        vo.setZJHM("");
        vo.setXBDM(entity.getXb());
        vo.setXB(DicUtil.translate("ZD_XB", entity.getXb()));
        vo.setCSRQ(DateUtil.format(entity.getCsrq(), DateUtil.DATE_PATTERN));
        vo.setGJDM(entity.getGj());
        vo.setGJ(DicUtil.translate("ZD_GABBZ_GJ", entity.getXb()));
        vo.setMZDM(entity.getMz());
        vo.setMZ(DicUtil.translate("ZD_MZ", entity.getXb()));
        vo.setHJD_XZQHDM(entity.getHjd());
        vo.setHJD_XZQH(DicUtil.translate("ZD_JG", entity.getXb()));
        vo.setHJD_DZMC(entity.getHjdxz());
        vo.setXZD_XZQHDM(entity.getXzz());
        vo.setXZD_XZQH(DicUtil.translate("ZD_JG", entity.getXb()));
        vo.setXZD_DZMC(entity.getXzzxz());
        vo.setCSD_XZQHDM("");
        vo.setCSD_XZQH("");
        vo.setCSD_DZMC("");
        vo.setZZMMDM(entity.getZzmm());
        vo.setZZMM(DicUtil.translate("ZD_ZZMM", entity.getXb()));
        vo.setHYZKDM(entity.getHyzk());
        vo.setHYZK(DicUtil.translate("ZD_HYZK", entity.getXb()));
        vo.setZJXYDM(entity.getZjxy());
        vo.setZJXY(DicUtil.translate("ZD_ZJXY", entity.getXb()));
        vo.setXLDM(entity.getWhcd());
        vo.setXL(DicUtil.translate("ZD_WHCD", entity.getXb()));
        vo.setGRSFDM(entity.getSf());
        vo.setGRSF(DicUtil.translate("ZD_SF", entity.getXb()));
        vo.setTSSFDM(entity.getTssf());
        vo.setTSSF(DicUtil.translate("ZD_TSSF", entity.getXb()));
        vo.setLXDH("");
        vo.setBCJRYLBDM(entity.getGllb());
        vo.setBCJRYLB(DicUtil.translate("ZD_KSS_RYGLLB", entity.getXb()));
        vo.setCJR_XM(sessionUser.getName());
        vo.setCJR_SFHM(sessionUser.getIdCard());
        vo.setCJR_JH(sessionUser.getJobId());
        vo.setCJDW_GAJGJGDM(sessionUser.getOrgCode());
        vo.setCJDW_DWMC(sessionUser.getOrgName());
        vo.setCJSJ(DateUtil.nowNonDelimiterDateTimeStr());

        return vo;
    }
}
