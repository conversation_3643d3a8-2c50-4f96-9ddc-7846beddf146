package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-禁毒教育新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AntiDrugEducationSaveReqVO extends BaseVO{
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("活动时间")
    @NotNull(message = "活动时间不能为空")
    private Date activityTime;

    @ApiModelProperty("活动类型")
    @NotEmpty(message = "活动类型不能为空")
    private String activityType;

    @ApiModelProperty("具体活动类型")
    @NotEmpty(message = "具体活动类型不能为空")
    private String specificActivityType;

    @ApiModelProperty("授课人身份证号")
    @NotEmpty(message = "授课人身份证号不能为空")
    private String lecturerUserSfzh;

    @ApiModelProperty("授课人姓名")
    @NotEmpty(message = "授课人姓名不能为空")
    private String lecturerUserName;

    @ApiModelProperty("授课人单位")
    @NotEmpty(message = "授课人单位不能为空")
    private String lecturerUnit;

    @ApiModelProperty("授课题目")
    @NotEmpty(message = "授课题目不能为空")
    private String lectureTopic;

    @ApiModelProperty("授课内容摘要")
    @NotEmpty(message = "授课内容摘要不能为空")
    private String lectureContent;

    @ApiModelProperty("听课人数")
    private Integer attendeeCount;

    @ApiModelProperty("听课单位")
    @NotEmpty(message = "听课单位不能为空")
    private String attendeeUnit;

    @ApiModelProperty("活动地点")
    @NotEmpty(message = "活动地点不能为空")
    private String activityLocation;

}
