package com.rs.module.acp.controller.app.db.vo.kss;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "app端 - 实战平台-羁押业务-出所登记（看守所） Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppOutRecordKssBasicsRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("出所时间")
    private Date cssj;
    @ApiModelProperty("出所原因")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SFCSCSYY")
    private String csyy;
    @ApiModelProperty("经办人")
    private String jbr;
    @ApiModelProperty("经办时间")
    private Date jbsj;
    @ApiModelProperty("出所凭证")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SFCSCSPZ")
    private String cspz;
    @ApiModelProperty("出所凭证文书号")
    private String cspzwsdz;
    @ApiModelProperty("出所去向")
    private String csqx;
    @ApiModelProperty("转去公安监所名称")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ORG_ID")
    private String zqgajsmc;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;
}
