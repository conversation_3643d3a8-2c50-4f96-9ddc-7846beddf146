package com.rs.module.acp.job.pi;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.framework.common.cons.RedisConstants;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.SqglSqdjSaveReqVO;
import com.rs.module.acp.entity.pi.SqglSqdjDO;
import com.rs.module.acp.entity.pi.SqglSqdjTbpzDO;
import com.rs.module.acp.service.pi.SqglSqdjService;
import com.rs.module.acp.service.pi.SqglSqdjTbpzService;
import com.rs.module.base.util.RedisDistributedLockUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 硬件订阅报警事件定时同步到所情登记表
 */
@Component
public class SqdjSyncJob {

    @Autowired
    private SqglSqdjTbpzService sqglSqdjTbpzService;

    @Autowired
    private SqglSqdjService sqglSqdjService;

    @Autowired
    private RedisDistributedLockUtil redisLockUtil;

    @XxlJob("sqdjSync")
    public void sync(){
        XxlJobHelper.log("所情数据同步任务：-----开始-------");
        if (redisLockUtil.tryLock(RedisConstants.JJKS_SQDJ_DATA_SYNC, 1800)) {
            try {
                List<SqglSqdjTbpzDO> tbpzDOList = sqglSqdjTbpzService.list();
                if(CollectionUtil.isEmpty(tbpzDOList)){
                    return;
                }

                for(SqglSqdjTbpzDO sqdjTbpzDO:tbpzDOList){

                    StringBuilder sql = new StringBuilder();
                    sql.append("select * from(");
                    sql.append(sqdjTbpzDO.getSyncSql());
                    sql.append(") syTable");
                    if(ObjectUtil.isNotEmpty(sqdjTbpzDO.getSyncCurrentTime())){
                        sql.append(" where ").append(sqdjTbpzDO.getTimeField()).append(" > ")
                                .append("'").append(DateUtil.format(sqdjTbpzDO.getSyncCurrentTime(),"yyyy-MM-dd HH:mm:ss"))
                                .append("' order by ").append(sqdjTbpzDO.getTimeField()).append(" asc ");
                    }

                    List<JSONObject> zlJsonList = sqglSqdjTbpzService.getSyncList(sql.toString());
                    if(CollectionUtil.isEmpty(zlJsonList)){
                        continue;
                    }
                    Date currentTime = sqdjTbpzDO.getSyncCurrentTime();
                    for(JSONObject jsonObject:zlJsonList){
                        SqglSqdjSaveReqVO saveReqVO = new SqglSqdjSaveReqVO();
                        saveReqVO.setEventSrc(jsonObject.getString("event_src"));
                        saveReqVO.setAlarmType(jsonObject.getString("alarm_type"));
                        saveReqVO.setHappenTime(DateUtil.parse(jsonObject.getString("happen_time"),"yyyy-MM-dd HH:mm:ss"));
                        if(jsonObject.containsKey("bjgrybm")){
                            saveReqVO.setBjgrybm(jsonObject.getString("bjgrybm"));
                        }
                        saveReqVO.setAlarmEventId(jsonObject.getString("alarm_event_id"));
                        sqglSqdjService.createWaitVerify(jsonObject.getString("org_code"),jsonObject.getString("org_name"),saveReqVO);
                        if(ObjectUtil.isEmpty(currentTime)){
                            currentTime = jsonObject.getDate(sqdjTbpzDO.getTimeField());
                        }else {
                            if(jsonObject.getDate(sqdjTbpzDO.getTimeField()).after(currentTime)){
                                currentTime = jsonObject.getDate(sqdjTbpzDO.getTimeField());
                            }
                        }
                    }
                    sqdjTbpzDO.setSyncCurrentTime(currentTime);
                }
                sqglSqdjTbpzService.updateBatchById(tbpzDOList);

            }catch (Exception e){
                e.printStackTrace();
                XxlJobHelper.log("所情数据同步任务异常：{}",e.getMessage());
            }finally {
                    redisLockUtil.releaseLock(RedisConstants.JJKS_SQDJ_DATA_SYNC);
            }
        }else {
            XxlJobHelper.log("所情数据同步任务:-----有其他程序正在执行------");
        }

        XxlJobHelper.log("所情数据同步任务:-----结束------");
    }

}
