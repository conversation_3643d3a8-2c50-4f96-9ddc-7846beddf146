package com.rs.module.acp.controller.admin.pm.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-监管管理-标签体征采样任务新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LocalsenseHeartRateSaveReqVO extends BaseVO{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("血氧-采样周期 单位S")
    private Integer bloodOxygenValue;

    @ApiModelProperty("心率-采样周期 单位S")
    private Integer heartRateValue;

    @ApiModelProperty("体温-采样周期 单位S")
    private Integer temperatureValue;
    @ApiModelProperty("体征采样任务标签信息")
    private List<SamplingPeriodTagInfo> tagInfoList;

    /**
     * 体征设置数据
     */
    @Data
    public static class SamplingPeriodTagInfo {
        @ApiModelProperty("标签ID")
        @NotEmpty(message = "标签ID不能为空")
        private String tagId;

        @ApiModelProperty("血氧-体征采样任务")
        private String bloodOxygenTaskId;

        @ApiModelProperty("心率-体征采样任务")
        private String heartRateTaskId;

        @ApiModelProperty("体温-体征采样任务")
        private String temperatureTaskId;
        @ApiModelProperty("绑定记录ID")
        @NotEmpty(message = "绑定记录ID不能为空")
        private String bindRecordId;

    }

}
