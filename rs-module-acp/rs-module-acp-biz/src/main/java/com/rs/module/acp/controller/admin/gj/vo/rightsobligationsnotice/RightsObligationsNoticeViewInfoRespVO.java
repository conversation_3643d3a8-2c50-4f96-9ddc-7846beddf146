package com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-权力义务告知书 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RightsObligationsNoticeViewInfoRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("businessId")
    private String businessId;
    @ApiModelProperty("formId")
    private String formId;
    @ApiModelProperty("是否签名 true 已签  false 未签")
    private boolean signFlag;

    @ApiModelProperty("签名方式 1：线上  2：线下")
    @Trans(type = TransType.DICTIONARY, key = "ZD_QLYWGZS_SIGN_TYPE")
    private String signType;

    @ApiModelProperty("线下签名url")
    private String offlineUrl;
}
