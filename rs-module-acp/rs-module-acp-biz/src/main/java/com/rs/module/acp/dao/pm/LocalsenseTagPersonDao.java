package com.rs.module.acp.dao.pm;

import java.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.acp.controller.admin.pm.dto.AreaTreeNodeDTO;
import com.rs.module.acp.entity.pm.LocalsenseTagPersonDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.acp.controller.admin.pm.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 实战平台-监管管理-定位标签与人员绑定 Dao
*
* <AUTHOR>
*/
@Mapper
public interface LocalsenseTagPersonDao extends IBaseDao<LocalsenseTagPersonDO> {

    /**
     * 根据人员类型、机构代码和房间ID查询标签人员及房间信息
     *
     * @param personType 人员类型
     * @param orgCode 机构代码
     * @param roomId 房间ID
     * @return 标签人员及房间信息列表
     */
    List<LocalsenseTagPersonRespVO> selectTagPersonWithRoomInfo(@Param("personType") String personType,
                                                                    @Param("orgCode") String orgCode,
                                                                    @Param("roomId") String roomId);

    /**
     * 根据人员类型、机构代码和房间ID统计各房间绑定数量
     *
     * @param personType 人员类型
     * @param orgCode 机构代码
     * @param roomId 房间ID
     * @return 各房间绑定数量统计
     */
    List<Map<String, Object>> selectTagJgryCountByRoom(@Param("personType") String personType,
                                                         @Param("orgCode") String orgCode,
                                                         @Param("roomId") String roomId);

    /**
     * 根据人员类型、机构代码和房间ID统计各房间绑定数量
     *
     * @param orgCode 机构代码
     * @param roomId 房间ID
     * @return 各房间绑定数量统计
     */
    List<Map<String, Object>> selectTagPersonCountByRoom(@Param("orgCode") String orgCode,
                                                         @Param("roomId") String roomId);

    boolean updateTagSetInfo(@Param("form") LocalsenseTagPersonDO tagPerson);
}
