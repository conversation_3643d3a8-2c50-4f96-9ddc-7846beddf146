package com.rs.module.acp.job.edurehabcourses;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rs.module.acp.dao.gj.EdurehabCoursesPlanDao;
import com.rs.module.acp.entity.gj.EdurehabCoursesPlanDO;
import com.rs.module.acp.enums.gj.EdurehabCoursesPlanStatusEnum;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class EdurehabCoursesPlanJob {

    @Resource
    private EdurehabCoursesPlanDao edurehabCoursesPlanDao;

    @XxlJob("changeEdurehabCoursesStatusJob")
    public void changeEdurehabCoursesStatusJob() {
        LambdaQueryWrapper<EdurehabCoursesPlanDO> wrapper = Wrappers.lambdaQuery(EdurehabCoursesPlanDO.class)
                .in(EdurehabCoursesPlanDO::getStatus, EdurehabCoursesPlanStatusEnum.DSP.getCode(), EdurehabCoursesPlanStatusEnum.SXZ.getCode());
        List<EdurehabCoursesPlanDO> edurehabCoursesPlanDOS = edurehabCoursesPlanDao.selectList(wrapper);
        for (EdurehabCoursesPlanDO edurehabCoursesPlanDO : edurehabCoursesPlanDOS) {
            // endDate 格式例如：2025-08-01 00:00:00
            Date newEndDate = new Date(edurehabCoursesPlanDO.getEndDate().getTime() + 1000 * 60 * 60 * 24L);
            if (new Date().compareTo(newEndDate) >= 0) {
                String newStatus = EdurehabCoursesPlanStatusEnum.DSP.getCode().equals(edurehabCoursesPlanDO.getStatus()) ?
                        EdurehabCoursesPlanStatusEnum.SPYQ.getCode() : EdurehabCoursesPlanStatusEnum.YGQ.getCode();
                edurehabCoursesPlanDO.setStatus(newStatus);
                edurehabCoursesPlanDao.updateById(edurehabCoursesPlanDO);
            }
        }
    }


}
