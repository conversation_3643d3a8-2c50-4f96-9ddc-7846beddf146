package com.rs.module.acp.controller.admin.area;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.StrUtil;
import com.rs.module.base.controller.admin.pm.vo.AreaMappingCameraSaveReqVO;
import com.rs.module.base.controller.admin.pm.vo.AreaMappingRespVO;
import com.rs.module.base.controller.admin.pm.vo.AreaMappingSaveReqVO;
import com.rs.module.base.entity.pm.AreaMappingDO;
import com.rs.module.base.service.pm.AreaMappingService;
import com.rs.module.base.service.pm.device.SplwTreeService;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;


@Api(tags = "实战平台-监管管理-区域映射")
@RestController
@RequestMapping("/acp/pm/areaMapping")
@Validated
public class AreaMappingController {

    @Resource
    private AreaMappingService areaMappingService;
    @Resource
    private SplwTreeService splwTreeService;
    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-监管管理-区域映射")
    public CommonResult<String> createAreaMapping(@Valid @RequestBody AreaMappingSaveReqVO createReqVO) {
        return success(areaMappingService.createAreaMapping(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-区域映射")
    public CommonResult<Boolean> updateAreaMapping(@Valid @RequestBody AreaMappingSaveReqVO updateReqVO) {
        areaMappingService.updateAreaMapping(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-监管管理-区域映射")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteAreaMapping(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           areaMappingService.deleteAreaMapping(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-区域映射")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<AreaMappingRespVO> getAreaMapping(@RequestParam("id") String id) {
        AreaMappingDO areaMapping = areaMappingService.getAreaMapping(id);
        return success(BeanUtils.toBean(areaMapping, AreaMappingRespVO.class));
    }

    @GetMapping(value = "getAreaTree")
    @ApiOperation(value = "监所区域树")
    public CommonResult<List<Tree<String>>> getAreaTree(
            @RequestParam("orgCode") String orgCode,
            @RequestParam(name = "areaName", required = false) String areaName) {
        if (StrUtil.isBlank(orgCode)) {
            throw new IllegalArgumentException("orgCode不能为空");
        }
        List<Tree<String>> list = areaMappingService.getAreaTree(orgCode,areaName);
        return success(list);
    }

    //同步视频联网Tree
    @GetMapping(value = "sysSplwData")
    @ApiOperation(value = "同步视频联网Tree")
    public CommonResult<String> sysSplwData(String orgCode){
        try {
            splwTreeService.sysSplwData(orgCode);
        } catch (Exception e) {
            return error( "同步视频联网Tree"+e.getMessage());
        }
        return success();
    }
    @PostMapping("/saveAreaMappingCamera")
    @ApiOperation(value = "更新实战平台-监管管理-区域映射")
    public CommonResult<Boolean> saveAreaMappingCamera(@Valid @RequestBody AreaMappingCameraSaveReqVO saveReqVO) {
        areaMappingService.saveAreaMappingCamera(saveReqVO);
        return success(true);
    }

    @PostMapping("/batchSaveAreaMappingCamera")
    @ApiOperation(value = "更新实战平台-监管管理-区域映射")
    public CommonResult<Boolean> batchSaveAreaMappingCamera(@Valid @RequestBody List<AreaMappingCameraSaveReqVO> saveReqVOList) {
        areaMappingService.batchSaveAreaMappingCamera(saveReqVOList);
        return success(true);
    }
    @GetMapping("/deleteAreaMappingCamera")
    @ApiOperation(value = "删除实战平台-监管管理-区域映射及设备信息")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "bdAreaCode", value = "实战平台-区域映射编号"),
            @ApiImplicitParam(name = "spAreaCode", value = "视频联网平台-区域编号(摄像机设备编码)"),
            @ApiImplicitParam(name = "isDeleteDevice", value = "是否删除设备信息 默认false")
    })
    public CommonResult<Boolean> deleteAreaMappingCamera(@RequestParam("bdAreaCode") String bdAreaCode,
                                                         @RequestParam("spAreaCode") String spAreaCode,
                                                            @RequestParam(value = "isDeleteDevice", required = false,defaultValue = "false") Boolean isDeleteDevice) {
        try {
            areaMappingService.deleteAreaMappingCamera(bdAreaCode, spAreaCode,isDeleteDevice);
        } catch (Exception e) {
            return error( "删除实战平台-区域映射及设备信息"+e.getMessage());
        }
        return success(true);
    }
}
