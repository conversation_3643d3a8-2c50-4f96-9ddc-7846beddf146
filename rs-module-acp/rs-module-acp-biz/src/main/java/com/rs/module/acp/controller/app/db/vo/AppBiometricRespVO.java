package com.rs.module.acp.controller.app.db.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.controller.admin.db.vo.BiometricInfoRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-收押业务-随身物品登记子 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppBiometricRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("生物采集信息")
    List<BiometricInfoRespVO> biometricList;

    @ApiModelProperty("手环ID")
    private String shid;

    @ApiModelProperty("手环绑定状态")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYRSSHBDZT")
    private String shbdzt;

    @ApiModelProperty("手环绑定时间")
    private Date sdbdsj;

    @ApiModelProperty("经办人")
    private String jbr;

    @ApiModelProperty("经办时间")
    private Date jbsj;

}
