package com.rs.module.acp.service.wb;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.wb.vo.MessageBookSaveReqVO;
import com.rs.module.acp.entity.wb.MessageBookDO;

import javax.validation.Valid;

/**
 * 实战平台-窗口业务-留言内容 Service 接口
 *
 * <AUTHOR>
 */
public interface MessageBookService extends IBaseService<MessageBookDO>{

    /**
     * 创建实战平台-窗口业务-留言内容
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createMessageBook(@Valid MessageBookSaveReqVO createReqVO);

    /**
     * 更新实战平台-窗口业务-留言内容
     *
     * @param updateReqVO 更新信息
     */
    void updateMessageBook(@Valid MessageBookSaveReqVO updateReqVO);

    /**
     * 删除实战平台-窗口业务-留言内容
     *
     * @param id 编号
     */
    void deleteMessageBook(String id);

    /**
     * 获得实战平台-窗口业务-留言内容
     *
     * @param id 编号
     * @return 实战平台-窗口业务-留言内容
     */
    MessageBookDO getMessageBook(String id);


}
