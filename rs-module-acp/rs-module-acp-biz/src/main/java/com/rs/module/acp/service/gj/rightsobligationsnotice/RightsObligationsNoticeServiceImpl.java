package com.rs.module.acp.service.gj.rightsobligationsnotice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.util.DbUtil;
import com.bsp.common.util.SnowflakeIdUtil;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.entity.OpsDicCode;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.mybatis.util.DicUtil;
import com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice.RightsObligationsNoticeListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice.RightsObligationsNoticePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice.RightsObligationsNoticeSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice.RightsObligationsNoticeViewInfoRespVO;
import com.rs.module.acp.enums.gj.RightsObligationsNoticeSignTypeEnum;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.base.service.pm.PrisonerService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.rs.module.acp.entity.gj.RightsObligationsNoticeDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.RightsObligationsNoticeDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-权力义务告知书 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RightsObligationsNoticeServiceImpl extends BaseServiceImpl<RightsObligationsNoticeDao, RightsObligationsNoticeDO> implements RightsObligationsNoticeService {

    @Resource
    private RightsObligationsNoticeDao rightsObligationsNoticeDao;

    @Resource
    private PrisonerService prisonerService;

    @Value("${system-mark}")
    private String systemMark;

    @Override
    public String createRightsObligationsNotice(RightsObligationsNoticeSaveReqVO createReqVO) {
        RightsObligationsNoticeDO byJgrybm = getByJgrybm(createReqVO.getJgrybm());
        if (Objects.nonNull(byJgrybm)) {
            throw new ServerException("该人员已" + RightsObligationsNoticeSignTypeEnum.getByCode(byJgrybm.getSignType()).getName()
                    + "签名, 无须重复签名！");
        }
        // 插入
        RightsObligationsNoticeDO rightsObligationsNotice = BeanUtils.toBean(createReqVO, RightsObligationsNoticeDO.class);
        rightsObligationsNotice.setSignType(RightsObligationsNoticeSignTypeEnum.OFFLINE_SIGH.getCode());
        rightsObligationsNoticeDao.insert(rightsObligationsNotice);
        // 返回
        return rightsObligationsNotice.getId();
    }

    @Override
    public void updateRightsObligationsNotice(RightsObligationsNoticeSaveReqVO updateReqVO) {
        // 校验存在
        validateRightsObligationsNoticeExists(updateReqVO.getId());
        // 更新
        RightsObligationsNoticeDO updateObj = BeanUtils.toBean(updateReqVO, RightsObligationsNoticeDO.class);
        rightsObligationsNoticeDao.updateById(updateObj);
    }

    @Override
    public void deleteRightsObligationsNotice(String id) {
        // 校验存在
        validateRightsObligationsNoticeExists(id);
        // 删除
        rightsObligationsNoticeDao.deleteById(id);
    }

    private void validateRightsObligationsNoticeExists(String id) {
        if (rightsObligationsNoticeDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-权力义务告知书数据不存在");
        }
    }

    @Override
    public RightsObligationsNoticeDO getRightsObligationsNotice(String id) {
        return rightsObligationsNoticeDao.selectById(id);
    }

    @Override
    public PageResult<RightsObligationsNoticeDO> getRightsObligationsNoticePage(RightsObligationsNoticePageReqVO pageReqVO) {
        return rightsObligationsNoticeDao.selectPage(pageReqVO);
    }

    @Override
    public List<RightsObligationsNoticeDO> getRightsObligationsNoticeList(RightsObligationsNoticeListReqVO listReqVO) {
        return rightsObligationsNoticeDao.selectList(listReqVO);
    }

    @Override
    public String sign(RightsObligationsNoticeSaveReqVO createReqVO) {
        RightsObligationsNoticeDO byJgrybm = getByJgrybm(createReqVO.getJgrybm());
        if (Objects.nonNull(byJgrybm)) {
            throw new ServerException("该人员已" + RightsObligationsNoticeSignTypeEnum.getByCode(byJgrybm.getSignType()).getName()
                    + "签名, 无须重复签名！");
        }
        // 插入
        RightsObligationsNoticeDO rightsObligationsNotice = BeanUtils.toBean(createReqVO, RightsObligationsNoticeDO.class);
        rightsObligationsNotice.setSignType(RightsObligationsNoticeSignTypeEnum.ONLINE_SIGN.getCode());
        rightsObligationsNotice.setSignTime(new Date());
        rightsObligationsNoticeDao.insert(rightsObligationsNotice);
        return rightsObligationsNotice.getId();
    }

    @Override
    public RightsObligationsNoticeDO getByJgrybm(String jgrybm) {
        LambdaQueryWrapper<RightsObligationsNoticeDO> queryWrapper = Wrappers.lambdaQuery(RightsObligationsNoticeDO.class);
        queryWrapper.eq(RightsObligationsNoticeDO::getJgrybm, jgrybm);
        return rightsObligationsNoticeDao.selectOne(queryWrapper);
    }

    @Override
    public RightsObligationsNoticeViewInfoRespVO getParams(String jgrybm) {
        RightsObligationsNoticeDO noticeDO = getByJgrybm(jgrybm);
        RightsObligationsNoticeViewInfoRespVO respVO = new RightsObligationsNoticeViewInfoRespVO();
        respVO.setSignFlag(Objects.nonNull(noticeDO) && StringUtils.isNotEmpty(noticeDO.getSignType()));
        if (Objects.nonNull(noticeDO)) {
            respVO.setId(noticeDO.getId());
            respVO.setBusinessId(noticeDO.getId());
            respVO.setSignType(noticeDO.getSignType());
            respVO.setOfflineUrl(noticeDO.getOfflineUrl());
        }
        List<OpsDicCode> list = DicUtil.getDicAsc("ZD_YY_QLYWGZS", systemMark);
        Map<String, String> collect = list.stream().collect(Collectors.toMap(OpsDicCode::getCode, OpsDicCode::getName, (v1, v2) -> v2));
        respVO.setFormId(collect.get(HttpUtils.getAppCode()));
        return respVO;
    }

}
