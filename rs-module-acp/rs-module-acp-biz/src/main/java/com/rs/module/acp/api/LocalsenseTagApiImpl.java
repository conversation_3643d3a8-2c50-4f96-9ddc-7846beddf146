package com.rs.module.acp.api;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.cache.base.StringRedisService;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.DateUtil;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.config.QingyanDeviceProperties;
import com.rs.module.acp.controller.admin.pm.LocalsenseTagApi;
import com.rs.module.acp.controller.admin.pm.dto.AreaTreeNode;
import com.rs.module.acp.controller.admin.pm.dto.RoomDetailDTO;
import com.rs.module.acp.controller.admin.pm.dto.TagTrackDTO;
import com.rs.module.acp.controller.admin.pm.vo.LocalsenseTagPersonRespVO;
import com.rs.module.acp.entity.pm.LocalsenseTagPersonDO;
import com.rs.module.acp.service.pm.LocalsenseTagPersonService;
import com.rs.module.acp.util.QingyanLocalSenseUtil;
import com.rs.module.acp.util.QingyanLocalsenseHttpUtil;
import com.rs.module.base.controller.admin.pm.vo.AreaListReqVO;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceTreeReqVO;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.base.service.pm.device.BaseDeviceService;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.rs.framework.common.pojo.CommonResult.success;

/**
 * RPC服务-API接口实现类，提供RESTful API接口，给Feign调用
 *
 * <AUTHOR>
 * @date 2025年4月14日
 */
@RestController
@Validated
@Api(tags = "清研手环 - api服务")
public class LocalsenseTagApiImpl implements LocalsenseTagApi {
    private static final Logger log = LoggerFactory.getLogger(LocalsenseTagApiImpl.class);

    @Resource
    private LocalsenseTagPersonService localsenseTagPersonService;
    @Resource
    private AreaService areaService;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private QingyanDeviceProperties qingyanDeviceProperties;

    @Resource
    private BaseDeviceService baseDeviceService;
    @Override
    public CommonResult<List<Tree<String>>> getAreaInfo(String areaCode) {
        AreaListReqVO listReqVO = new AreaListReqVO();
        listReqVO.setOrgCode(areaCode);
        List<Tree<String>> getAreaTree = areaService.getAreaTree(listReqVO);
        return CommonResult.success(getAreaTree);
    }

    @Override
    public CommonResult<List<Tree<String>>> getAggregateData(String areaCode, String personType) {
        List<Map<String, Object>> queryResult = localsenseTagPersonService.selectTagJgryCountByRoom(areaCode, null);
        AreaListReqVO listReqVO = new AreaListReqVO();
        listReqVO.setOrgCode(areaCode);
        List<Tree<String>> getAreaTree = areaService.getAreaTree(listReqVO);
        Map<String, Long> roomCountMap = new HashMap<>();
        for (Map<String, Object> map : queryResult) {
            String roomid = (String) map.get("roomid");
            Long nums = (Long) map.get("nums");
            if (StringUtil.isNotEmpty(roomid)) {
                roomCountMap.put(roomid, nums);
            }
        }
        // 为树结构添加数量字段
        for (Tree<String> tree : getAreaTree) {
            addCountToTree(tree, roomCountMap);
        }
        return success(getAreaTree);
    }
    private void addCountToTree(Tree<String> treeNode, Map<String, Long> roomCountMap) {
        String countColName = "tagNum";
        if (treeNode.getChildren() != null && !treeNode.getChildren().isEmpty()) {
            Long totalCount = Long.valueOf(0);
            for (Tree<String> child : treeNode.getChildren()) {
                addCountToTree(child, roomCountMap);
                // 累加子节点的数量
                Object countObj = child.get(countColName);
                if (countObj != null) {
                    try {
                        totalCount += (Long) countObj;
                    } catch (ClassCastException e) {
                        // 如果转换失败，尝试其他数值类型
                        if (countObj instanceof Number) {
                            totalCount += ((Number) countObj).intValue();
                        }
                    }
                }
            }
            treeNode.put(countColName, totalCount);
        } else {
            String nodeId = treeNode.getId();
            Long count = roomCountMap.getOrDefault(nodeId, Long.valueOf(0));
            treeNode.put(countColName, count);
        }
    }
    @Override
    public CommonResult<List<RoomDetailDTO>> getRoomPositionData(String areaCode,String roomId, String personType) {
        List<RoomDetailDTO> resultList = new ArrayList<>();
        if("01".equals(personType)){
            List<LocalsenseTagPersonRespVO> list = localsenseTagPersonService.getBindPersonRoomList(areaCode,roomId);
            for (LocalsenseTagPersonRespVO vo:list){
                RoomDetailDTO roomDetailDTO = initRoomDetailDTO(vo.getTagId());
                roomDetailDTO.setPersonId(vo.getBindPersonId());
                roomDetailDTO.setPersonName(vo.getBindPersonName());

                resultList.add(roomDetailDTO);
            }
        }
        return CommonResult.success(resultList);
    }
    private RoomDetailDTO initRoomDetailDTO(String tagIdStr) {
        RoomDetailDTO roomDetailDTO = new RoomDetailDTO();
        roomDetailDTO.setTagId(tagIdStr);
        long tagId = Long.parseLong(tagIdStr);
        // 获取体征信息
        String xyVitalSignInfoStr =stringRedisService.getString(QingyanLocalSenseUtil.getVitalsignKey(tagId,QingyanLocalSenseUtil.TZLX_XY));
        if(StringUtil.isNotEmpty(xyVitalSignInfoStr)){
            roomDetailDTO.setXyVitalSignInfo(JSONObject.parseObject(xyVitalSignInfoStr));
        }
        String twVitalSignInfoStr =stringRedisService.getString(QingyanLocalSenseUtil.getVitalsignKey(tagId,QingyanLocalSenseUtil.TZLX_TW));
        if(StringUtil.isNotEmpty(twVitalSignInfoStr)){
            roomDetailDTO.setXyVitalSignInfo(JSONObject.parseObject(twVitalSignInfoStr));
        }
        String xlVitalSignInfoStr =stringRedisService.getString(QingyanLocalSenseUtil.getVitalsignKey(tagId,QingyanLocalSenseUtil.TZLX_XL));
        if(StringUtil.isNotEmpty(xlVitalSignInfoStr)){
            roomDetailDTO.setXyVitalSignInfo(JSONObject.parseObject(xlVitalSignInfoStr));
        }
        // 获取位置信息
        String positionStr = stringRedisService.getString(QingyanLocalSenseUtil.getPosKey(tagId));
        if(StringUtil.isNotEmpty(positionStr)) {
            JSONObject position = JSONObject.parseObject(positionStr);
            roomDetailDTO.setBatteryPercent(position != null ? position.getString("batPer"): "0");
            roomDetailDTO.setPosition(position);
        }
        return roomDetailDTO;
    }
    @Override
    public CommonResult<RoomDetailDTO> getTagDetail(String tagId) {
        List<LocalsenseTagPersonDO> list = localsenseTagPersonService.getLocalsenseTagPersonByTagIds(tagId);
        if(CollectionUtil.isNull(list)) return CommonResult.error("tagId未查询到绑定信息");
        LocalsenseTagPersonDO localsenseTagPersonDO = list.get(0);
        if(null == localsenseTagPersonDO || StringUtil.isEmpty(localsenseTagPersonDO.getTagId())){
            return CommonResult.error("tagId未查询到绑定信息");
        }
        RoomDetailDTO roomDetailDTO = initRoomDetailDTO(localsenseTagPersonDO.getTagId());
        roomDetailDTO.setPersonId(localsenseTagPersonDO.getBindPersonId());
        roomDetailDTO.setPersonName(localsenseTagPersonDO.getBindPersonName());
        return CommonResult.success(roomDetailDTO);
    }

    @Override
    public CommonResult<List<TagTrackDTO>> getHistoryTrack(String num, String startTimeStr, String endTimeStr, String personType) {
        List<TagTrackDTO> list = new ArrayList<>();
        QingyanLocalsenseHttpUtil qingyanLocalsenseHttpUtil = new QingyanLocalsenseHttpUtil(qingyanDeviceProperties);
        Long startTime = DateUtil.parseDateTime(startTimeStr).getTime();
        Long endTime = DateUtil.parseDateTime(endTimeStr).getTime();
        //定义时间间隔变量 单位小时
        int timeInterval = 24;

        long diffInMillis = Math.abs(endTime - startTime);
        long diffInHours = diffInMillis / (1000 * 60 * 60);
        if (diffInHours > timeInterval) {
            throw new RuntimeException("查询时间间隔不能大于"+timeInterval+"小时");
        }
        try {
            JSONObject result = qingyanLocalsenseHttpUtil.getHistoricalTrack(startTime,endTime,num);
            if (result != null && !result.isEmpty()) {
                for (Map.Entry<String, Object> entry : result.entrySet()) {
                    String numKey = entry.getKey();
                    JSONObject value = (JSONObject) entry.getValue();

                    TagTrackDTO dto = new TagTrackDTO();
                    dto.setNum(numKey);

                    JSONArray posTimeArray = value.getJSONArray("posTime");
                    if (posTimeArray != null && !posTimeArray.isEmpty()) {
                        List<TagTrackDTO.PostTime> postTimeList = new ArrayList<>();
                        for (int i = 0; i < posTimeArray.size(); i++) {
                            JSONObject posTimeObj = posTimeArray.getJSONObject(i);
                            TagTrackDTO.PostTime postTime = new TagTrackDTO.PostTime();
                            postTime.setMapid(posTimeObj.getString("mapid"));

                            // 直接转换时间数组
                            JSONArray timeArray = posTimeObj.getJSONArray("time");
                            if (timeArray != null && !timeArray.isEmpty()) {
                                List<Long> timeList = new ArrayList<>();
                                for (int j = 0; j < timeArray.size(); j++) {
                                    timeList.add(timeArray.getLong(j));
                                }
                                postTime.setTime(timeList);
                            }
                            postTimeList.add(postTime);
                        }
                        dto.setPostTime(postTimeList);
                    }

                    // 解析posData数据
                    JSONArray posDataArray = value.getJSONArray("posData");
                    if (posDataArray != null && !posDataArray.isEmpty()) {
                        List<TagTrackDTO.PostData> postDataList = new ArrayList<>();
                        for (int i = 0; i < posDataArray.size(); i++) {
                            JSONObject posDataObj = posDataArray.getJSONObject(i);
                            TagTrackDTO.PostData postData = new TagTrackDTO.PostData();
                            postData.setMapid(posDataObj.getString("mapid"));

                            // 直接转换坐标数据
                            JSONArray dataArray = posDataObj.getJSONArray("data");
                            if (dataArray != null && !dataArray.isEmpty()) {
                                List<List<Integer>> dataList = new ArrayList<>();
                                for (int j = 0; j < dataArray.size(); j++) {
                                    JSONArray coordArray = dataArray.getJSONArray(j);
                                    if (coordArray != null && !coordArray.isEmpty()) {
                                        List<Integer> coordList = new ArrayList<>();
                                        for (int k = 0; k < coordArray.size(); k++) {
                                            coordList.add(coordArray.getInteger(k));
                                        }
                                        dataList.add(coordList);
                                    }
                                }
                                postData.setData(dataList);
                            }
                            postDataList.add(postData);
                        }
                        dto.setPostData(postDataList);
                    }

                    list.add(dto);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return success(list);
    }

    @Override
    public CommonResult<List<AreaTreeNode>> getDeviceTreeByArea(String orgCode, String deviceStatus, String roomId, String deviceName, String deviceTypeId) {
        BaseDeviceTreeReqVO treeReqVO = new BaseDeviceTreeReqVO();
        treeReqVO.setOrgCode(orgCode);
        treeReqVO.setDeviceStatus(deviceStatus);
        treeReqVO.setRoomId(roomId);
        treeReqVO.setDeviceName(deviceName);
        treeReqVO.setDeviceTypeId(deviceTypeId);
        treeReqVO.setDataTypes("camera");
        //treeReqVO.setDataSources("1");
        List<com.rs.module.base.vo.TreeNode> result = baseDeviceService.getDeviceTreeByArea(treeReqVO);
        return success(BeanUtil.copyToList(result, AreaTreeNode.class));
    }
}
