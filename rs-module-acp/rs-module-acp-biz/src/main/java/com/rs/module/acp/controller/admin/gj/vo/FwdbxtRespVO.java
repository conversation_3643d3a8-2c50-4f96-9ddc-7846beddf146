package com.rs.module.acp.controller.admin.gj.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.base.vo.FileReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-法务待办协同 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FwdbxtRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("监管人员身份证号")
    private String jgrysfzh;
    @ApiModelProperty("监室号")
    private String jsh;
    @ApiModelProperty("监室名称")
    private String orgName;
    @ApiModelProperty("案由代码")
    private String aydm;
    @ApiModelProperty("案由名称")
    private String aymc;
    @ApiModelProperty("案件环节")
    private String ajhj;
    @ApiModelProperty("办案单位")
    private String badw;
    @ApiModelProperty("办案民警")
    private String bamj;
    @ApiModelProperty("原始文书")
    private String ysws;
    @ApiModelProperty("文书名称")
    private String wsmc;
    @ApiModelProperty("文书数量")
    private Integer wssl;
    @ApiModelProperty("推送时间")
    private Date tssj;
    @ApiModelProperty("是否拒签")
    @Trans(type = TransType.DICTIONARY, key = "ZD_BOOLEAN_TYPE")
    private String sfjq;
    @ApiModelProperty("签收时间")
    private Date qssj;
    @ApiModelProperty("拒签日期")
    private Date jqrq;
    @ApiModelProperty("拒签理由")
    private String jqly;
    @ApiModelProperty("签收经办民警身份证号")
    private String qsjbmjsfzh;
    @ApiModelProperty("签收经办民警")
    private String qsjbmjxm;
    @ApiModelProperty("回传时间")
    private Date hcsj;
    @ApiModelProperty("回传文书")
    private List<FileReqVO> hcwsList;
    @ApiModelProperty("回传信息")
    private String hcxx;
    @ApiModelProperty("回传经办民警身份证号")
    private String hcjbmjsfzh;
    @ApiModelProperty("回传经办民警姓名")
    private String hcjbmjxm;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJ_FWDBXT_STATUS")
    private String status;
    @ApiModelProperty("文书信息")
    private List<FwdbxtWsxxRespVO> wsxxList;
}
