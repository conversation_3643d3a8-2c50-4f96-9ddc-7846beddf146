package com.rs.module.acp.controller.app.db.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.controller.admin.db.vo.InjuryAssessmentSaveReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-收押业务-入所健康检查登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppHealthCheckRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("健康状况")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWJKZK")
    private String jkzk;
    @ApiModelProperty("身高")
    private String sg;
    @ApiModelProperty("体重")
    private String tz;
    @ApiModelProperty("足长")
    private String zc;
    @ApiModelProperty("体温")
    private String tw;
    @ApiModelProperty("血型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWXX")
    private String xx;
    @ApiModelProperty("血压收缩压")
    private String xyssy;
    @ApiModelProperty("血压舒张压")
    private String xyszy;
    @ApiModelProperty("心率")
    private String xl;
    @ApiModelProperty("语言表达能力")
    @Trans(type = TransType.DICTIONARY,key = "ZD_TYSF")
    private String yybdnl;
    @ApiModelProperty("语言表达能力具体情况")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWYYBDNLJTQK")
    private String yybdnljtqk;
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWZTHDJTQK")
    @ApiModelProperty("肢体活动具体情况")
    private String zthdjtqk;
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWSLJTQK")
    @ApiModelProperty("视力具体情况")
    private String sljtqk;
    @ApiModelProperty("智力状况具体情况")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWZLJTQK")
    private String zlzkjtqk;
    @ApiModelProperty("精神状况具体情况")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWJSJTQK")
    private String jszkjtqk;
    @ApiModelProperty("有无吸毒史")
    @Trans(type = TransType.DICTIONARY,key = "ZD_JYYWTYYW")
    private String ywdxs;
    @ApiModelProperty("严重传染病名称")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWYZCRBMC")
    private String yzcbrbmc;
    @ApiModelProperty("既往疾病类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWJWJBLX")
    private String jwjblx;


    @ApiModelProperty("血常规")
    private String xcgscdz;
    @ApiModelProperty("心电图")
    private String xdtscdz;
    @ApiModelProperty("B超")
    private String bcscdz;
    @ApiModelProperty("胸片")
    private String xpscdz;
    @ApiModelProperty("胸部CT")
    private String xbctscdz;
    @ApiModelProperty("致伤部位")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SQDJ")
    private String zswb;
    @ApiModelProperty("外伤情况")
    private String wsqkscdz;
    @ApiModelProperty("致伤日期")
    private Date zsrq;
    @ApiModelProperty("致伤原因")
    private String zsyy;
    @ApiModelProperty("伤情鉴定")
    private List<InjuryAssessmentSaveReqVO> injuryAssessmentList;


    @ApiModelProperty("医生意见")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYRSYSYJ")
    private String ysyj;
    @ApiModelProperty("检查人")
    private String jcr;
    @ApiModelProperty("检查时间")
    private Date jcsj;
    @ApiModelProperty("备注")
    private String bz;
}
