package com.rs.module.acp.config;

import com.gosun.zhjg.basic.business.modules.file.feign.FileApi;
import com.gosun.zhjg.prison.room.terminal.modules.app.feign.CnpLogFeign;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.feign.CnpFaceFeign;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.feign.TerminalVersionManagementApi;
import com.gosun.zhjg.prison.room.terminal.modules.socket.feign.CnpSocketFeign;
import com.gosun.zhjg.prison.room.terminal.modules.socket.feign.SocketPushFeign;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.UserApi;
import com.rs.module.pam.api.PamApi;
import com.rs.module.ptm.api.PtmGoodsApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * RPC远程访问配置类
 * <AUTHOR>
 * @date 2025年6月27日
 */
@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {SocketPushFeign.class, TerminalVersionManagementApi.class, CnpFaceFeign.class,
		CnpLogFeign.class, FileApi.class, BspApi.class, UserApi.class, CnpSocketFeign.class, PtmGoodsApi.class, PamApi.class})
public class AcpRpcConfig {

}
