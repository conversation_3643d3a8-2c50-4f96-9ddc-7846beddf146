package com.rs.module.acp.service.gj.riskIndicator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.util.TemplateUtils;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.entity.OpsDicCode;
import com.rs.framework.mybatis.util.DicUtil;
import com.rs.module.acp.controller.admin.gj.vo.riskIndicator.*;
import com.rs.module.acp.enums.gj.RiskIndicatorFxdjEnum;
import com.rs.module.acp.enums.gj.RiskIndicatorPositiveAnomalousEnum;
import com.rs.module.acp.enums.gj.RiskIndicatorTypeEnum;
import com.rs.util.DicUtils;
import com.rs.util.ThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.rs.module.acp.entity.gj.RiskIndicatorDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.gj.RiskIndicatorDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-管教业务-风险指标 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class RiskIndicatorServiceImpl extends BaseServiceImpl<RiskIndicatorDao, RiskIndicatorDO> implements RiskIndicatorService {

    private static final String SELECT_START_SQL_CHECK_REG =
            "^(?i)(\\s*)(select)(\\s+)(((?!(insert |delete |update |drop |alter |truncate |exec |xp_cmdshell |declare |script |alert |;|--|#|/\\*|\\*/)).)+)$";

    @Resource
    private RiskIndicatorDao riskIndicatorDao;

    @Override
    public String createRiskIndicator(RiskIndicatorSaveReqVO createReqVO) {
        // 插入
        RiskIndicatorDO riskIndicator = BeanUtils.toBean(createReqVO, RiskIndicatorDO.class);
        checkRiskIndicator(riskIndicator);
        riskIndicatorDao.insert(riskIndicator);
        // 返回
        return riskIndicator.getId();
    }


    @Override
    public void updateRiskIndicator(RiskIndicatorSaveReqVO updateReqVO) {
        // 校验存在
        validateRiskIndicatorExists(updateReqVO.getId());
        // 更新
        RiskIndicatorDO updateObj = BeanUtils.toBean(updateReqVO, RiskIndicatorDO.class);
        checkRiskIndicator(updateObj);
        riskIndicatorDao.updateById(updateObj);
    }

    @Override
    public void deleteRiskIndicator(String id) {
        // 校验存在
        validateRiskIndicatorExists(id);
        // 删除
        riskIndicatorDao.deleteById(id);
    }

    private void validateRiskIndicatorExists(String id) {
        if (riskIndicatorDao.selectById(id) == null) {
            throw new ServerException("实战平台-管教业务-风险指标数据不存在");
        }
    }

    @Override
    public RiskIndicatorDO getRiskIndicator(String id) {
        return riskIndicatorDao.selectById(id);
    }

    @Override
    public PageResult<RiskIndicatorDO> getRiskIndicatorPage(RiskIndicatorPageReqVO pageReqVO) {
        return riskIndicatorDao.selectPage(pageReqVO);
    }

    @Override
    public List<RiskIndicatorDO> getRiskIndicatorList(RiskIndicatorListReqVO listReqVO) {
        return riskIndicatorDao.selectList(listReqVO);
    }

    @Override
    public RiskIndicatorAllInfoRespVO getReportByJgrybm(String jgrybm, String type) {

        RiskIndicatorTypeEnum typeEnum = RiskIndicatorTypeEnum.getByCode(type);
        String orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        RiskIndicatorListReqVO listReqVO = new RiskIndicatorListReqVO();
        listReqVO.setOrgCode(orgCode);
        listReqVO.setIsEnabled((short) 1);

        if (RiskIndicatorTypeEnum.FXZB.equals(typeEnum)) {
            listReqVO.setPositiveAnomalous(RiskIndicatorPositiveAnomalousEnum.YC.getCode());
        } else if (RiskIndicatorTypeEnum.JFZB.equals(typeEnum)) {
            listReqVO.setPositiveAnomalous(RiskIndicatorPositiveAnomalousEnum.ZX.getCode());
        }
        RiskIndicatorAllInfoRespVO respVO = new RiskIndicatorAllInfoRespVO();
        respVO.setBgList(new ArrayList<>());
        Map<String, String> zblxMap = DicUtils.getMap("ZD_FXZB_ZBLX");
        ConcurrentMap<String, RiskIndicatorBgRespVO> riskBgMap = new ConcurrentHashMap<>();

        List<RiskIndicatorDO> riskIndicatorDOS = riskIndicatorDao.selectList(listReqVO);
        if (CollectionUtil.isNotEmpty(riskIndicatorDOS)) {
            Map<String, Object> params = new HashMap<>();
            params.put("jgrybm", jgrybm);
            // 并发 计算
            CountDownLatch cdl = new CountDownLatch(riskIndicatorDOS.size());
            ThreadPoolExecutor pool = ThreadPoolUtil.getPool();
            for (RiskIndicatorDO riskIndicatorDO : riskIndicatorDOS) {
                try {
                    RiskIndicatorBgRespVO riskIndicatorBgRespVO;
                    if (RiskIndicatorTypeEnum.JFZB.getCode().equals(type) || RiskIndicatorTypeEnum.WZBG.getCode().equals(type)) {
                        riskIndicatorBgRespVO = riskBgMap.get(riskIndicatorDO.getIndicatorTypeCode());
                    } else {
                        riskIndicatorBgRespVO = riskBgMap.get(riskIndicatorDO.getRiskLevel());
                    }

                    if (Objects.isNull(riskIndicatorBgRespVO)) {
                        riskIndicatorBgRespVO = new RiskIndicatorBgRespVO();
                        riskIndicatorBgRespVO.setCount(new AtomicInteger(0));
                        riskIndicatorBgRespVO.setPositiveAnomalous(riskIndicatorDO.getPositiveAnomalous());
                        riskIndicatorBgRespVO.setPositiveAnomalousName(RiskIndicatorPositiveAnomalousEnum.getByCode(riskIndicatorDO.getPositiveAnomalous()).getName());
                        riskIndicatorBgRespVO.setIndicatorList(new CopyOnWriteArrayList<>());
                        respVO.getBgList().add(riskIndicatorBgRespVO);
                        if (RiskIndicatorTypeEnum.JFZB.equals(typeEnum) || RiskIndicatorTypeEnum.WZBG.equals(typeEnum)) {
                            riskIndicatorBgRespVO.setItemCode(riskIndicatorDO.getIndicatorTypeCode());
                            riskIndicatorBgRespVO.setItemName(zblxMap.get(riskIndicatorDO.getIndicatorTypeCode()));
                            riskBgMap.put(riskIndicatorDO.getIndicatorTypeCode(), riskIndicatorBgRespVO);
                        } else {
                            riskIndicatorBgRespVO.setItemCode(riskIndicatorDO.getRiskLevel());
                            riskIndicatorBgRespVO.setItemName(RiskIndicatorFxdjEnum.getByCode(riskIndicatorDO.getRiskLevel()).getName());
                            riskBgMap.put(riskIndicatorDO.getRiskLevel(), riskIndicatorBgRespVO);
                        }
                    }
                    CopyOnWriteArrayList<RiskIndicatorSubBgRespVO> indicatorList = riskIndicatorBgRespVO.getIndicatorList();
                    AtomicInteger conditionCount = riskIndicatorBgRespVO.getCount();
                    RiskIndicatorSubBgRespVO riskIndicatorSubBgRespVO = BeanUtils.toBean(riskIndicatorDO, RiskIndicatorSubBgRespVO.class);
                    riskIndicatorSubBgRespVO.setPositiveAnomalousName(RiskIndicatorPositiveAnomalousEnum.getByCode(riskIndicatorSubBgRespVO.getPositiveAnomalous()).getName());
                    String riskName = StringUtils.isEmpty(riskIndicatorSubBgRespVO.getRiskLevel()) ? null : RiskIndicatorFxdjEnum.getByCode(riskIndicatorSubBgRespVO.getRiskLevel()).getName();
                    riskIndicatorSubBgRespVO.setRiskLevelName(riskName);
                    riskIndicatorSubBgRespVO.setIndicatorTypeCodeName(zblxMap.get(riskIndicatorSubBgRespVO.getIndicatorTypeCode()));
                    pool.execute(() -> {
                        try {
                            // 计算 封装 多线程计算控制最小 防止创建多个相同的指标项
                            cual(riskIndicatorDO, riskIndicatorSubBgRespVO, params, typeEnum, indicatorList, conditionCount);
                        } catch (Exception e) {
                            log.error("风险报告计算异常，报告id：{}，指标：{}，描述：{}  异常：", riskIndicatorDO.getId(),
                                    riskIndicatorDO.getIndicatorName(), riskIndicatorDO.getIndicatorDescription(), e);
                        } finally {
                            cdl.countDown();
                        }
                    });
                } catch (Exception e) {
                    log.error("风险报告初始化异常，报告id：{}，指标：{}，描述：{}  异常：", riskIndicatorDO.getId(),
                            riskIndicatorDO.getIndicatorName(), riskIndicatorDO.getIndicatorDescription(), e);
                    cdl.countDown();
                }
            }
            try {
                cdl.await(10, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                log.error("风险报告 CountDownLatch await 异常：", e);
            }
            handleRiskIndicatorAllInfoRespVO(respVO, typeEnum);
        }

        return respVO;
    }

    private void handleRiskIndicatorAllInfoRespVO(RiskIndicatorAllInfoRespVO respVO, RiskIndicatorTypeEnum typeEnum) {
        respVO.getBgList().sort(Comparator.comparing(RiskIndicatorBgRespVO::getItemCode));
        for (RiskIndicatorBgRespVO riskIndicatorBgRespVO : respVO.getBgList()) {
            CopyOnWriteArrayList<RiskIndicatorSubBgRespVO> indicatorList = riskIndicatorBgRespVO.getIndicatorList();
            indicatorList.sort(Comparator.comparing(RiskIndicatorSubBgRespVO::getId));
            int size = indicatorList.size();
            riskIndicatorBgRespVO.setItemCount(size);
            // 风险指标
            if (RiskIndicatorTypeEnum.FXZB.equals(typeEnum)) {
                if (RiskIndicatorFxdjEnum.GFX.getCode().equals(riskIndicatorBgRespVO.getItemCode())) {
                    respVO.setGfxCount(size);
                } else if (RiskIndicatorFxdjEnum.ZFX.getCode().equals(riskIndicatorBgRespVO.getItemCode())) {
                    respVO.setZfxCount(size);
                } else {
                    respVO.setDfxCount(size);
                }
            } else {
                // 全部
                if (RiskIndicatorPositiveAnomalousEnum.YC.getCode().equals(riskIndicatorBgRespVO.getPositiveAnomalous())) {
                    // 这里计算有风险的总和 而非风险项总和
                    respVO.setFxIndicatorCount(respVO.getFxIndicatorCount() + riskIndicatorBgRespVO.getCount().get());
                } else {
                    respVO.setJfIndicatorCount(respVO.getJfIndicatorCount() + riskIndicatorBgRespVO.getCount().get());
                }
            }
            respVO.setAllCount(respVO.getAllCount() + size);
        }
    }

    @Override
    public List<RiskIndicatorBgRespVO> getBjgryfxzbmo() {
        List<OpsDicCode> zblxList = DicUtil.getDicAsc("ZD_FXZB_ZBLX", "bsp");
        RiskIndicatorListReqVO listReqVO = new RiskIndicatorListReqVO();
        listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        List<RiskIndicatorDO> riskIndicatorDOS = riskIndicatorDao.selectList(listReqVO);
        Map<String, CopyOnWriteArrayList<RiskIndicatorSubBgRespVO>> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(riskIndicatorDOS)) {
            for (RiskIndicatorDO riskIndicatorDO : riskIndicatorDOS) {
                CopyOnWriteArrayList<RiskIndicatorSubBgRespVO> list = map.get(riskIndicatorDO.getIndicatorTypeCode());
                if (CollectionUtil.isEmpty(list)) {
                    list = new CopyOnWriteArrayList<>();
                    map.put(riskIndicatorDO.getIndicatorTypeCode(), list);
                }
                list.add(BeanUtils.toBean(riskIndicatorDO, RiskIndicatorSubBgRespVO.class));
            }
        }
        List<RiskIndicatorBgRespVO> list = new ArrayList<>();
        for (OpsDicCode opsDicCode : zblxList) {
            RiskIndicatorBgRespVO riskIndicatorBgRespVO = new RiskIndicatorBgRespVO();
            riskIndicatorBgRespVO.setItemCode(opsDicCode.getCode());
            riskIndicatorBgRespVO.setItemName(opsDicCode.getName());
            riskIndicatorBgRespVO.setIndicatorList(map.get(opsDicCode.getCode()));
            list.add(riskIndicatorBgRespVO);
        }
        return list;
    }


    // 计算
    private void cual(RiskIndicatorDO riskIndicatorDO, RiskIndicatorSubBgRespVO riskIndicatorSubBgRespVO,
                      Map<String, Object> params, RiskIndicatorTypeEnum typeEnum,
                      CopyOnWriteArrayList<RiskIndicatorSubBgRespVO> indicatorList, AtomicInteger conditionCount) {
        // 结果查询
        JSONObject templateJson = JSON.parseObject(riskIndicatorDO.getDisplayTemplate());
        String sql = TemplateUtils.parseTemplate(riskIndicatorDO.getQueryScript(), params, null);
        JSONObject result = riskIndicatorDao.getOne(sql);
        String oneDesc = templateJson.getString("oneDesc");
        String manyDesc = templateJson.getString("manyDesc");
        riskIndicatorSubBgRespVO.setManyDesc(manyDesc);
        riskIndicatorSubBgRespVO.setOneDesc(oneDesc);

        boolean flag = false;
        // 空值处理
        if (Objects.isNull(result)) {
            riskIndicatorSubBgRespVO.setPositiveAnomalous(null);
            riskIndicatorSubBgRespVO.setOneResult(templateJson.getString("oneDefault"));
            riskIndicatorSubBgRespVO.setManyList(null);
            riskIndicatorSubBgRespVO.setRiskLevel(null);
            riskIndicatorSubBgRespVO.setRiskLevelName(null);
            // 完整报告，即使没有数据也要展示数据项
            flag = RiskIndicatorTypeEnum.WZBG.equals(typeEnum);
        } else {
            // 条件判断
            String condition = templateJson.getString("condition");
            if (StringUtils.isNotEmpty(condition)) {
                String conditionSql = TemplateUtils.parseTemplate(condition, result, null);
                if (!riskIndicatorDao.riskCondition(conditionSql)) {
                    riskIndicatorSubBgRespVO.setRiskLevel(null);
                    riskIndicatorSubBgRespVO.setRiskLevelName(null);
                    // 完整报告 符不符合都添加   其他报告，不符合 不添加
                    flag = RiskIndicatorTypeEnum.WZBG.equals(typeEnum);
                } else {
                    conditionCount.addAndGet(1);
                    flag = true;
                }
            }
            if (flag) {
                // 字典解析
                Map<String, Map<String, String>> dicMap = new HashMap<>();
                JSONObject dic = templateJson.getJSONObject("dic");
                if (CollectionUtil.isNotEmpty(dic)) {
                    for (Map.Entry<String, Object> entry : dic.entrySet()) {
                        dicMap.put(entry.getKey(), DicUtils.getMap((String) entry.getValue()));
                    }
                }

                // 单行结果解析
                if (StringUtils.isNotEmpty(oneDesc)) {
                    String oneResult = templateJson.getString("oneResult");
                    riskIndicatorSubBgRespVO.setOneResult(getStringValue(result, oneResult, dicMap));
                }

                // 多行(明细)结果解析
                if (StringUtils.isNotEmpty(manyDesc)) {
                    String manyResult = templateJson.getString("manyResult");

                    JSONArray jsonArray = result.getJSONArray("json_array");
                    if (null != jsonArray) {
                        List<String> list = new ArrayList<>();
                        for (Object rowJson : jsonArray) {
                            list.add(getStringValue((JSONObject) rowJson, manyResult, dicMap));
                        }
                        riskIndicatorSubBgRespVO.setManyList(list);
                    }
                }
                dicMap = null;
            }
        }
        if (flag) {
            indicatorList.add(riskIndicatorSubBgRespVO);
        }
    }

    private String translate(String code, Map<String, String> map) {
        if (StringUtils.isEmpty(code)) {
            return "";
        }
        if (CollectionUtil.isEmpty(map)) {
            return code;
        }
        if (!code.contains(",")) {
            return map.getOrDefault(code, code);
        }
        String[] split = code.split(",");
        List<String> list = new ArrayList<>();
        for (String c : split) {
            list.add(map.getOrDefault(c, c));
        }
        return CollUtil.join(list, "、");
    }

    private String getStringValue(JSONObject result, String resultStr, Map<String, Map<String, String>> dicMap) {
        List<String> variables = getVariables(resultStr);
        if (CollectionUtil.isNotEmpty(variables)) {
            for (String dicKey : variables) {
                if (dicMap.containsKey(dicKey)) {
                    Map<String, String> map = dicMap.get(dicKey);
                    String code = result.getString(dicKey);
                    String codeName = translate(code, map);
                    result.put(dicKey, codeName);
                }
            }
        }
        return TemplateUtils.parseTemplate(resultStr, result, null);
    }

    private void checkRiskIndicator(RiskIndicatorDO riskIndicator) {
        if (RiskIndicatorPositiveAnomalousEnum.YC.equals(RiskIndicatorPositiveAnomalousEnum.getByCode(riskIndicator.getPositiveAnomalous()))) {
            RiskIndicatorFxdjEnum.getByCode(riskIndicator.getRiskLevel());
        }
        String orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        LambdaQueryWrapper<RiskIndicatorDO> wrapper = Wrappers.lambdaQuery(RiskIndicatorDO.class)
                .eq(RiskIndicatorDO::getIndicatorTypeCode, riskIndicator.getIndicatorTypeCode())
                .eq(RiskIndicatorDO::getIndicatorName, riskIndicator.getIndicatorName())
                .eq(RiskIndicatorDO::getIndicatorDescription, riskIndicator.getIndicatorDescription())
                .eq(RiskIndicatorDO::getOrgCode, orgCode);
        if (StringUtils.isNotEmpty(riskIndicator.getId())) {
            wrapper.ne(RiskIndicatorDO::getId, riskIndicator.getId());
        }
        Integer count = riskIndicatorDao.selectCount(wrapper);
        if (Objects.nonNull(count) && count > 0) {
            throw new ServerException("请勿重复配置指标");
        }
        LambdaQueryWrapper<RiskIndicatorDO> wrapper2 = Wrappers.lambdaQuery(RiskIndicatorDO.class)
                .eq(RiskIndicatorDO::getIndicatorTypeCode, riskIndicator.getIndicatorTypeCode())
                .eq(RiskIndicatorDO::getOrgCode, orgCode).last(" limit 1");
        if (StringUtils.isNotEmpty(riskIndicator.getId())) {
            wrapper2.ne(RiskIndicatorDO::getId, riskIndicator.getId());
        }
        RiskIndicatorDO riskIndicatorDO = riskIndicatorDao.selectOne(wrapper2);
        if (Objects.nonNull(riskIndicatorDO)
                && !riskIndicatorDO.getPositiveAnomalous().equals(riskIndicator.getPositiveAnomalous())) {
            throw new ServerException("同一指标类型，请保持一致的指标分类");
        }

        // 校验合法性
        String queryScript = riskIndicator.getQueryScript();
        if (!isSelect(queryScript)) {
            throw new ServerException("非法查询语句");
        }
        try {
            List<String> variables = getVariables(queryScript);
            Map<String, Object> params = new HashMap<>();
            for (String variable : variables) {
                params.put(variable, "2");
            }
            riskIndicatorDao.getOne(TemplateUtils.parseTemplate(queryScript, params, null));
        } catch (Exception e) {
            log.error("查询语句异常：", e);
            throw new ServerException("查询语句异常：" + e.getMessage());
        }

        String displayTemplate = riskIndicator.getDisplayTemplate();
        try {
            JSONObject templateJson = JSON.parseObject(displayTemplate);
            if (Objects.isNull(templateJson) || templateJson.keySet().size() == 0) {
                throw new ServerException("展示模板不能为空");
            }
            String oneDesc = templateJson.getString("oneDesc");
            if (StringUtils.isNotEmpty(oneDesc)) {
                String oneResult = templateJson.getString("oneResult");
                Assert.notEmpty(oneResult, "oneResult 不能为空");
            }
            String manyDesc = templateJson.getString("manyDesc");
            if (StringUtils.isNotEmpty(manyDesc)) {
                String manyResult = templateJson.getString("manyResult");
                Assert.notEmpty(manyResult, "manyResult 不能为空");
                if (!queryScript.contains("json_array")) {
                    throw new ServerException("展示模板配置了manyResult，请检查sql语句是否配置了json_array，" +
                            "确保json_array的查询结果为[{...},{...},...]格式");
                }
            }
            String condition = templateJson.getString("condition");
            if (StringUtils.isNotEmpty(condition)) {
                if (!isSelect(condition)) {
                    throw new ServerException("非法condition语句");
                }
                try {
                    List<String> variables = getVariables(condition);
                    Map<String, Object> params = new HashMap<>();
                    for (String variable : variables) {
                        params.put(variable, "2");
                    }
                    riskIndicatorDao.riskCondition(TemplateUtils.parseTemplate(condition, params, null));
                } catch (Exception e) {
                    log.error("condition语句异常：", e);
                    throw new ServerException("condition语句异常：" + e.getMessage());
                }
            }
            if (StringUtils.isNotEmpty(templateJson.getString("dic"))) {
                JSONObject dic = templateJson.getJSONObject("dic");
                if (null != dic && dic.size() > 0) {
                    for (Map.Entry<String, Object> entry : dic.entrySet()) {
                        List<OpsDicCode> bsp = DicUtil.getDicAsc((String) entry.getValue(), "bsp");
                        if (CollectionUtil.isEmpty(bsp)) {
                            throw new ServerException(entry.getValue() + "字典集合为空，请检查");
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("展示模板配置异常：", e);
            throw new ServerException("展示模板配置异常：" + e.getMessage());
        }


    }

    public List<String> getVariables(String str) {
        List<String> variables = new ArrayList<>();
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)}");
        Matcher matcher = pattern.matcher(str);
        while (matcher.find()) {
            variables.add(matcher.group(1));
        }
        return variables;
    }

    private boolean isSelect(String sql) {
        return sql.replaceAll("\\t", " ").replaceAll("\\n", " ").matches(SELECT_START_SQL_CHECK_REG);
    }

}
