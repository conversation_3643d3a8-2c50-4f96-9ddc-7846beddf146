package com.rs.module.acp.service.zh;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.DateUtil;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.zh.vo.ReportJschmrqkListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.ReportJschmrqkPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.ReportJschmrqkSaveReqVO;
import com.rs.module.acp.dao.zh.ReportJschmrqkDao;
import com.rs.module.acp.entity.zh.ReportJschmrqkDO;
import com.rs.util.DicUtils;


/**
 * 早850监所晨会每日情况 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ReportJschmrqkServiceImpl extends BaseServiceImpl<ReportJschmrqkDao, ReportJschmrqkDO> implements ReportJschmrqkService {

    @Resource
    private ReportJschmrqkDao reportJschmrqkDao;    

    @Override
    public String createReportJschmrqk(ReportJschmrqkSaveReqVO createReqVO) {    	
        // 插入
        ReportJschmrqkDO reportJschmrqk = BeanUtils.toBean(createReqVO, ReportJschmrqkDO.class);
        reportJschmrqkDao.insert(reportJschmrqk);
        // 返回
        return reportJschmrqk.getId();
    }

    @Override
    public void updateReportJschmrqk(ReportJschmrqkSaveReqVO updateReqVO) {
        // 校验存在
        validateReportJschmrqkExists(updateReqVO.getId());
        // 更新
        ReportJschmrqkDO updateObj = BeanUtils.toBean(updateReqVO, ReportJschmrqkDO.class);
        reportJschmrqkDao.updateById(updateObj);
    }

    @Override
    public void deleteReportJschmrqk(String id) {
        // 校验存在
        validateReportJschmrqkExists(id);
        // 删除
        reportJschmrqkDao.deleteById(id);
    }

    private void validateReportJschmrqkExists(String id) {
        if (reportJschmrqkDao.selectById(id) == null) {
            throw new ServerException("早850监所晨会每日情况数据不存在");
        }
    }

    @Override
    public ReportJschmrqkDO getReportJschmrqk(String id) {
        return reportJschmrqkDao.selectById(id);
    }

    @Override
    public PageResult<ReportJschmrqkDO> getReportJschmrqkPage(ReportJschmrqkPageReqVO pageReqVO) {
        return reportJschmrqkDao.selectPage(pageReqVO);
    }

    @Override
    public List<ReportJschmrqkDO> getReportJschmrqkList(ReportJschmrqkListReqVO listReqVO) {
        return reportJschmrqkDao.selectList(listReqVO);
    }

    /**
     * 根据报告日期获取晨会报告
     * @param orgCode String 机构代码
     * @param reportDate Date 报告日期
     * @return ReportJschmrqkDO
     */
    @Override
    public ReportJschmrqkDO getByReportDate(String orgCode, Date reportDate) {
    	QueryWrapper<ReportJschmrqkDO> wrapper = new QueryWrapper<>();
    	wrapper.eq("org_code", orgCode);
    	wrapper.eq("report_date", reportDate);
    	return getOne(wrapper);
    }
    
    /**
     * 获取前24小时统计数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return JSONObject
     */
    @Override
    public JSONObject getDataOf24Hours(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getDataOf24Hours(orgCode, reportDate);
    }

    /**
     * 截至目前统计数据
     * @param orgCode String 机构代码
     * @return JSONObject
     */
    @Override
    public JSONObject getDataOfUpToNow(String orgCode) {
    	return reportJschmrqkDao.getDataOfUpToNow(orgCode);
    }
    
    /**
     * 获取前24小时提讯数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return JSONObject
     */
    @Override
    public JSONObject getDataOf24Tx(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getDataOf24Tx(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时提解数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return JSONObject
     */
    @Override
    public JSONObject getDataOf24Tj(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getDataOf24Tj(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时律师会见数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return JSONObject
     */
    @Override
    public JSONObject getDataOf24Lshj(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getDataOf24Lshj(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时收押情况-入所人员数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getSyqk24Rsry(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getSyqk24Rsry(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时收押情况-出所人员数据
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getSyqk24Csry(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getSyqk24Csry(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时在押情况-65岁以上人员
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getZyqk24Above65Years(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZyqk24Above65Years(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时在押情况-加戴械具人员
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getZyqk24Jdxj(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZyqk24Jdxj(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时在押情况-单独关押
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getZyqk24Ddgy(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZyqk24Ddgy(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时在押情况-关注群体人员
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getZyqk24Gzqtry(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZyqk24Gzqtry(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时在押情况-精神异常人员
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getZyqk24Jsyc(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZyqk24Jsyc(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时在押情况-吞食异物人员
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getZyqk24Tsyw(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZyqk24Tsyw(orgCode, reportDate);
    }
    
    /**
     * 获取前24小时在押情况-特殊身份
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getZyqk24Tssf(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZyqk24Tssf(orgCode, reportDate);
    }
    
    /**
     * 外籍人员管控情况-国籍分布情况
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getWjrygkGjfb(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getWjrygkGjfb(orgCode, reportDate);
    }
    
    /**
     * 外籍人员管控情况-涉嫌犯罪类型
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getWjrygkSxfzlx(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getWjrygkSxfzlx(orgCode, reportDate);
    }
    
    /**
     * 服务办案工作情况-提讯
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getFwbagzqk24Tx(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getFwbagzqk24Tx(orgCode, reportDate);
    }
    
    /**
     * 服务办案工作情况-提解
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getFwbagzqk24Tj(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getFwbagzqk24Tj(orgCode, reportDate);
    }
    
    /**
     * 服务办案工作情况-律师接待
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getFwbagzqk24Lsjd(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getFwbagzqk24Lsjd(orgCode, reportDate);
    }
    
    /**
     * 服务办案工作情况-接待家属
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getFwbagzqk24Jdjs(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getFwbagzqk24Jdjs(orgCode, reportDate);
    }
    
    /**
     * 服务办案工作情况-交付执行
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getFwbagzqk24Jfzx(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getFwbagzqk24Jfzx(orgCode, reportDate);
    }
    
    /**
     * 服务办案工作情况-使馆会见
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getFwbagzqk24Sghj(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getFwbagzqk24Sghj(orgCode, reportDate);
    }
    
    /**
     * 重点风险人员管控情况-使馆会见
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public Map<String, Object> getZdfxry24Sjfx(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZdfxry24Sjfx(orgCode, reportDate);
    }
    
    /**
     * 重点风险人员管控情况-使馆会见
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getZdfxry24Zyqk(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getZdfxry24Zyqk(orgCode, reportDate);
    }
    
    /**
     * 其他基础数据-巡诊发药
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public Integer getJcsj24Xzfy(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getJcsj24Xzfy(orgCode, reportDate);
    }
    
    /**
     * 其他基础数据-所内治疗
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public Integer getJcsj24Snzl(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getJcsj24Snzl(orgCode, reportDate);
    }
    
    /**
     * 其他基础数据-以绝食、拒绝医疗等方式对抗监管情况
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public Integer getJcsj24Dkjg(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getJcsj24Dkjg(orgCode, reportDate);
    }
    
    /**
     * 其他基础数据-一级
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getJcsj24Yj(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getJcsj24Yj(orgCode, reportDate);
    }
    
    /**
     * 其他基础数据-二级
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getJcsj24Ej(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getJcsj24Ej(orgCode, reportDate);
    }
    
    /**
     * 其他基础数据-三级
     * @param orgCode String 机构代码
     * @param reportDate String 报告日期
     * @return List<Map<String, Object>>
     */
    @Override
    public List<Map<String, Object>> getJcsj24Sj(String orgCode, String reportDate) {
    	return reportJschmrqkDao.getJcsj24Sj(orgCode, reportDate);
    }
    
    /**
     * 构建报告业务数据
     * @param reportJschmrqk ReportJschmrqkDO 晨会报告
     * @return JSONObject
     */
    @Override
    public JSONObject buildFormData(ReportJschmrqkDO reportJschmrqk) {
    	Date reportDate = reportJschmrqk.getReportDate();
		String reportDateStr = DateUtil.getDateStr(reportDate, 2);
		String orgCode = reportJschmrqk.getOrgCode();
		
		//初始化数据
		JSONObject formData = buildBasicData(reportDate);
		JSONObject subData = new JSONObject();
		formData.put("mrqk", reportJschmrqk);
		
		//获取前24小时数据和截止当前数据
		JSONObject dataOf24 = getDataOf24Hours(orgCode, reportDateStr);
		JSONObject dataOfUpToNow = getDataOfUpToNow(orgCode);    			
		JSONObject dataOf24Tj = getDataOf24Tj(orgCode, reportDateStr);
		JSONObject dataOf24Tx = getDataOf24Tx(orgCode, reportDateStr);
		JSONObject dataOf24Lshj = getDataOf24Lshj(orgCode, reportDateStr);
		formData.putAll(dataOf24);
		formData.putAll(dataOfUpToNow);
		formData.putAll(dataOf24Tj);
		formData.putAll(dataOf24Tx);
		formData.putAll(dataOf24Lshj);
		
		//收押情况-出入所人员    			
		List<Map<String, Object>> syqkRsry = getSyqk24Rsry(orgCode, reportDateStr);
		List<Map<String, Object>> syqkCsry = getSyqk24Csry(orgCode, reportDateStr);
		subData.put("syqkRsrySub", syqkRsry);
		subData.put("syqkCsrySub", syqkCsry);
		formData.put("syqkRsryZs", syqkRsry.size());
		formData.put("syqkCsryZs", syqkCsry.size());
		
		//在押情况-65岁以上人员、加戴械具、单独关押、关注群体人员、精神异常、吞食异物、特殊身份
		List<Map<String, Object>> zyqkAbove65 = getZyqk24Above65Years(orgCode, reportDateStr);
		List<Map<String, Object>> zyqkJdxj = getZyqk24Jdxj(orgCode, reportDateStr);
		List<Map<String, Object>> zyqkDdgy = getZyqk24Ddgy(orgCode, reportDateStr);
		List<Map<String, Object>> zyqkGzqtry = getZyqk24Gzqtry(orgCode, reportDateStr);
		List<Map<String, Object>> zyqkJsyc = getZyqk24Jsyc(orgCode, reportDateStr);
		List<Map<String, Object>> zyqkTsyw = getZyqk24Tsyw(orgCode, reportDateStr);
		List<Map<String, Object>> zyqkTssf = getZyqk24Tssf(orgCode, reportDateStr);
		formData.put("zyqkAbove65Zs", zyqkAbove65.size());
		formData.put("zyqkAbove65Data", buildZyryData(zyqkAbove65));
		formData.put("zyqkJdxjZs", zyqkJdxj.size());
		formData.put("zyqkJdxjData", buildZyryData(zyqkJdxj));
		formData.put("zyqkDdgyZs", zyqkDdgy.size());
		formData.put("zyqkDdgyData", buildZyryData(zyqkDdgy));
		formData.put("zyqkGzqtryZs", zyqkGzqtry.size());
		formData.put("zyqkGzqtryData", buildZyryData(zyqkGzqtry));
		formData.put("zyqkJsycZs", zyqkJsyc.size());
		formData.put("zyqkJsycData", buildZyryData(zyqkJsyc));
		formData.put("zyqkTsywZs", zyqkTsyw.size());
		formData.put("zyqkTsywData", buildZyryData(zyqkTsyw));
		formData.put("zyqkTssfZs", zyqkTssf.size());
		formData.put("zyqkTssfData", buildZyryData(zyqkTssf));
		
		//外籍人员管控情况-国籍分布情况、涉嫌犯罪类型
		List<Map<String, Object>> wjrygkGjfb = getWjrygkGjfb(orgCode, reportDateStr);
		List<Map<String, Object>> wjrygkSxfzlx = getWjrygkSxfzlx(orgCode, reportDateStr);
		formData.put("wjrygkGjfbData", buildWjryData(wjrygkGjfb, "gj", "zs"));
		formData.put("wjrygkGjfbZs", wjrygkGjfb.size());
		formData.put("wjrygkRyZs", buildWjryZs(wjrygkGjfb, "zs"));
		formData.put("wjrygkSxfzlxData", buildWjryData(wjrygkSxfzlx, "sxzm", "zs"));
		
		
		//服务办案工作情况-提讯、提解、律师接待、使馆会见
		List<Map<String, Object>> fwbagzqk24Tx = getFwbagzqk24Tx(orgCode, reportDateStr);
		List<Map<String, Object>> fwbagzqk24Tj = getFwbagzqk24Tj(orgCode, reportDateStr);
		List<Map<String, Object>> fwbagzqk24Lsjd = getFwbagzqk24Lsjd(orgCode, reportDateStr);
		List<Map<String, Object>> fwbagzqk24Sghj = getFwbagzqk24Sghj(orgCode, reportDateStr);
		formData.put("fwbagzqk24TxZs", fwbagzqk24Tx.size());
		formData.put("fwbagzqk24TjZs", fwbagzqk24Tj.size());
		formData.put("fwbagzqk24LsjdZs", fwbagzqk24Lsjd.size());
		formData.put("fwbagzqk24SghjZs", fwbagzqk24Sghj.size());
		
		//重点风险人员管控情况-三级风险、医疗重点人员及住院人员具体情况
		Map<String, Object> zdfxry24Sjfx = getZdfxry24Sjfx(orgCode, reportDateStr);
		List<Map<String, Object>> zdfxry24Zyqk = getZdfxry24Zyqk(orgCode, reportDateStr);
		Long yjfxZs = (Long)zdfxry24Sjfx.get("yjfx");
		Long ejfxZs = (Long)zdfxry24Sjfx.get("ejfx");
		Long sjfxZs = (Long)zdfxry24Sjfx.get("sjfx");    			
		formData.put("zdfxry24YjfxZs", yjfxZs);
		formData.put("zdfxry24EjfxZs", ejfxZs);
		formData.put("zdfxry24SjfxZs", sjfxZs);
		formData.put("zdfxry24FxZs", yjfxZs + ejfxZs + sjfxZs);
		formData.put("zdfxry24ZyqkZs", zdfxry24Zyqk.size());
		subData.put("zdfxry24ZyqkSub", zdfxry24Zyqk);
		
		//其他基础数据-巡诊发药、所内治疗、以绝食、拒绝医疗等方式对抗监管情况、一级人员、二级人员、三级人员
		Integer jcsj24XzfyZs = getJcsj24Xzfy(orgCode, reportDateStr);
		Integer jcsj24SnzlZs =  getJcsj24Snzl(orgCode, reportDateStr);
		Integer jcsj24DkjgZs =  getJcsj24Dkjg(orgCode, reportDateStr);
		List<Map<String, Object>> jcsj24Yj = getJcsj24Yj(orgCode, reportDateStr);
		List<Map<String, Object>> jcsj24Ej = getJcsj24Ej(orgCode, reportDateStr);
		List<Map<String, Object>> jcsj24Sj = getJcsj24Sj(orgCode, reportDateStr);
		formData.put("jcsj24XzfyZs", jcsj24XzfyZs);
		formData.put("jcsj24SnzlZs", jcsj24SnzlZs);
		formData.put("jcsj24DkjgZs", jcsj24DkjgZs);
		formData.put("jcsj24YjZs", jcsj24Yj.size());
		formData.put("jcsj24EjZs", jcsj24Ej.size());
		formData.put("jcsj24SjZs", jcsj24Sj.size());
		subData.put("jcsj24YjSub", jcsj24Yj);
		subData.put("jcsj24EjSub", jcsj24Ej);
		subData.put("jcsj24SjSub", jcsj24Sj);
		formData.put("subData", subData);
		
		return formData;
    }
    
    /**
     * 构建基础数据
     * @param reportDate Date 报告日期
     * @return JSONObject
     */
    private JSONObject buildBasicData(Date reportDate) {
		JSONObject jsonData = new JSONObject();
		
		//时间变量
		Calendar c = Calendar.getInstance();
		c.setTime(reportDate);
		int dayOfYear = c.get(Calendar.DAY_OF_YEAR);
		jsonData.put("reportDayOfYear", dayOfYear);
		jsonData.put("reportDateEnd", DateUtil.doFormatDate("M月d日", c.getTime()));
		jsonData.put("reportDateC", DateUtil.doFormatDate("yyyy年M月d日", c.getTime()));
		c.add(Calendar.DAY_OF_YEAR, -1);
		jsonData.put("reportDateStart", DateUtil.doFormatDate("M月d日", c.getTime()));
		
		return jsonData;
    }
    
    /**
     * 构建在押人员数据
     * @param listMap List<Map<String, Object>> 结果集集合
     * @return String
     */
    private String buildZyryData(List<Map<String, Object>> listMap) {
    	StringBuffer sb = new StringBuffer();
    	if(CollectionUtil.isNotNull(listMap)) {
    		for(Map<String, Object> map : listMap) {
    			if(map.containsKey("xm")) {
    				if(StringUtil.isNotEmpty(sb.toString())) {
    					sb.append(",");
    				}
    				sb.append(map.get("xm"));
    				if(map.containsKey("room_name")) {
    					String roomName = (String)map.get("room_name");
    					if(StringUtil.isNotEmpty(roomName)) {
    						sb.append("(" + roomName + ")");
    					}
    				}
    			}
    		}
    	}
    	
    	return sb.toString();
    }
    
    /**
     * 构建外籍人员数据
     * @param listMap List<Map<String, Object>> 结果集集合
     * @param propName String 属性名称
     * @param zsPropName String 总数属性名称
     * @return String
     */
    private String buildWjryData(List<Map<String, Object>> listMap, String propName, String zsPropName) {
    	StringBuffer sb = new StringBuffer();
    	if(CollectionUtil.isNotNull(listMap)) {
    		for(Map<String, Object> map : listMap) {
    			if(map.containsKey(propName) && map.containsKey(zsPropName)) {
    				String propValue = (String)map.get(propName);
    				Long zs = (Long)map.get(zsPropName);
    				if(StringUtil.isNotEmpty(propValue)) {
    					if(propName.equalsIgnoreCase("gj")) {
    						propValue = DicUtils.translate("ZD_GABBZ_GJ", propValue);
    						if(StringUtil.isEmpty(propValue)) {
    							propValue = DicUtils.translate("ZD_GAJGSJHJBZ_GJ", propValue);
    						}
    					}
    					if(StringUtil.isNotEmpty(sb.toString())) {
        					sb.append(",");
        				}
    					sb.append(propValue + zs + "人");
    				}
    			}
    		}
    	}
    	
    	return sb.toString();
    }
    
    /**
     * 构建外籍人员总数
     * @param listMap List<Map<String, Object>> 结果集集合
     * @param propName String 属性名称
     * @return String
     */
    private Long buildWjryZs(List<Map<String, Object>> listMap, String propName) {
    	Long total = 0l;
    	if(CollectionUtil.isNotNull(listMap)) {
    		for(Map<String, Object> map : listMap) {
    			if(map.containsKey(propName)) {
    				Long zs = (Long)map.get(propName);
    				total += zs;
    			}
    		}
    	}
    	
    	return total;
    }
}
