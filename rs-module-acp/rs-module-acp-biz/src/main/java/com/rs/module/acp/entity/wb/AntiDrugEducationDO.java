package com.rs.module.acp.entity.wb;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 实战平台-窗口业务-禁毒教育 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_anti_drug_education")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_wb_anti_drug_education")
public class AntiDrugEducationDO extends BaseDO {
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 活动时间
     */
    private Date activityTime;
    /**
     * 活动类型
     */
    private String activityType;
    /**
     * 具体活动类型
     */
    private String specificActivityType;
    /**
     * 授课人身份证号
     */
    private String lecturerUserSfzh;
    /**
     * 授课人姓名
     */
    private String lecturerUserName;
    /**
     * 授课人单位
     */
    private String lecturerUnit;
    /**
     * 授课题目
     */
    private String lectureTopic;
    /**
     * 授课内容摘要
     */
    private String lectureContent;
    /**
     * 听课人数
     */
    private Integer attendeeCount;
    /**
     * 听课单位
     */
    private String attendeeUnit;
    /**
     * 活动地点
     */
    private String activityLocation;

}
