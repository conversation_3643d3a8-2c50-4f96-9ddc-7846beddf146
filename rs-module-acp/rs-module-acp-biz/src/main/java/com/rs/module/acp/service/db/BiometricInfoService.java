package com.rs.module.acp.service.db;

import java.util.*;
import javax.validation.*;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.entity.db.BiometricInfoDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 实战平台-收押业务-生物特征信息 Service 接口
 *
 * <AUTHOR>
 */
public interface BiometricInfoService extends IBaseService<BiometricInfoDO>{

    /**
     * 创建实战平台-收押业务-生物特征信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBiometricInfo(@Valid BiometricInfoSaveReqVO createReqVO);

    /**
     * 更新实战平台-收押业务-生物特征信息
     *
     * @param updateReqVO 更新信息
     */
    void updateBiometricInfo(@Valid BiometricInfoSaveReqVO updateReqVO);

    /**
     * 删除实战平台-收押业务-生物特征信息
     *
     * @param id 编号
     */
    void deleteBiometricInfo(String id);

    /**
     * 获得实战平台-收押业务-生物特征信息
     *
     * @param id 编号
     * @return 实战平台-收押业务-生物特征信息
     */
    BiometricInfoDO getBiometricInfo(String id);

    /**
    * 获得实战平台-收押业务-生物特征信息分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-收押业务-生物特征信息分页
    */
    PageResult<BiometricInfoDO> getBiometricInfoPage(BiometricInfoPageReqVO pageReqVO);

    /**
    * 获得实战平台-收押业务-生物特征信息列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-收押业务-生物特征信息列表
    */
    List<BiometricInfoDO> getBiometricInfoList(BiometricInfoListReqVO listReqVO);


    boolean updateWristbandInformation(@Valid BraceletBindingSaveReqVO updateReqVO);

    List<BiometricInfoDO> getBiometricInfoByRybh(String rybh);

    List<BiometricInfoRespVO> getBiometricVOByRybh(String rybh);

    boolean updateWristbandInfoQuick(@Valid BraceletBindingSaveReqVO updateReqVO);

    CollectedPersonDetailVO getCollectedPersonDetail(prisonerInfoReqVO prisonerInfoReqVO);

    void uploadCollectedData(Map<String, Object> request);
}
