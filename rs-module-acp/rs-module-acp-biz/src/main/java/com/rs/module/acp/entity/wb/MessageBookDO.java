package com.rs.module.acp.entity.wb;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-窗口业务-留言内容 DO
 *
 * <AUTHOR>
 */
@TableName("acp_wb_message_book")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_wb_message_book")
public class MessageBookDO extends BaseDO {
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 留言时间
     */
    private Date messageTime;
    /**
     * 留言人姓名
     */
    private String messageUserName;
    /**
     * 留言人联系电话
     */
    private String messageUserPhone;
    /**
     * 人员类型
     */
    private String userType;
    /**
     * 工作单位
     */
    private String workUnit;
    /**
     * 地址
     */
    private String address;
    /**
     * 留言内容
     */
    private String messageContent;

}
