package com.rs.module.acp.controller.admin.pm;

import com.rs.module.acp.controller.admin.db.vo.DetainRegKssRespVO;
import com.rs.module.acp.controller.admin.db.vo.InRecordJlsPreRespVO;
import com.rs.module.acp.controller.admin.db.vo.InRecordJlsRespVO;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.PrisonerPreInDO;
import com.rs.module.acp.service.pm.PrisonerPreInService;

@Api(tags = "嫌疑人（待入所）信息")
@RestController
@RequestMapping("/acp/pm/prisonerPreIn")
@Validated
public class PrisonerPreInController {

    @Resource
    private PrisonerPreInService prisonerPreInService;

    @PostMapping("/create")
    @ApiOperation(value = "创建嫌疑人（待入所）信息")
    public CommonResult<String> createPrisonerPreIn(@Valid @RequestBody PrisonerPreInSaveReqVO createReqVO) {
        return success(prisonerPreInService.createPrisonerPreIn(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新嫌疑人（待入所）信息")
    public CommonResult<Boolean> updatePrisonerPreIn(@Valid @RequestBody PrisonerPreInSaveReqVO updateReqVO) {
        prisonerPreInService.updatePrisonerPreIn(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除嫌疑人（待入所）信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deletePrisonerPreIn(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           prisonerPreInService.deletePrisonerPreIn(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得嫌疑人（待入所）信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<PrisonerPreInRespVO> getPrisonerPreIn(@RequestParam("id") String id) {
        PrisonerPreInDO prisonerPreIn = prisonerPreInService.getPrisonerPreIn(id);
        return success(BeanUtils.toBean(prisonerPreIn, PrisonerPreInRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得嫌疑人（待入所）信息分页")
    public CommonResult<PageResult<PrisonerPreInRespVO>> getPrisonerPreInPage(@Valid @RequestBody PrisonerPreInPageReqVO pageReqVO) {
        PageResult<PrisonerPreInDO> pageResult = prisonerPreInService.getPrisonerPreInPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PrisonerPreInRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得嫌疑人（待入所）信息列表")
    public CommonResult<List<PrisonerPreInRespVO>> getPrisonerPreInList(@Valid @RequestBody PrisonerPreInListReqVO listReqVO) {
        List<PrisonerPreInDO> list = prisonerPreInService.getPrisonerPreInList(listReqVO);
        return success(BeanUtils.toBean(list, PrisonerPreInRespVO.class));
    }

    /**
     * 根据身份证号和监所编号查询待入所信息-看守所
     */
    @GetMapping("/getRecordByzjhm")
    @ApiOperation(value = "根据身份证号和监所编号查询待入所信息")
    public CommonResult<DetainRegKssRespVO> getPrisonerPreInByIdCardAndPrisonCode(
            @ApiParam(value = "身份证号", required = true) @RequestParam("zjhm") String zjhm) {
        PrisonerPreInListReqVO listReqVO = new PrisonerPreInListReqVO();
        listReqVO.setZjh(zjhm);
//        listReqVO.setJsbh(prisonCode);
        DetainRegKssRespVO detainRegKssRespVO = prisonerPreInService.getPrisonerPreInByIdCardAndPrisonCode(listReqVO);

        return success(detainRegKssRespVO);

    }

    @GetMapping("/getRecordByzjhmJls")
    @ApiOperation(value = "根据身份证号和监所编号查询待入所信息")
    public CommonResult<InRecordJlsPreRespVO> getPrisonerPreInByZjhmJls(
            @ApiParam(value = "身份证号", required = true) @RequestParam("zjhm") String zjhm
    ) {
        PrisonerPreInListReqVO listReqVO = new PrisonerPreInListReqVO();
        listReqVO.setZjh(zjhm);
//        listReqVO.setJsbh(prisonCode);
        InRecordJlsPreRespVO inRecordJlsRespVO = prisonerPreInService.getPrisonerPreInByZjhmJls(listReqVO);

        return success(inRecordJlsRespVO);

    }
}
