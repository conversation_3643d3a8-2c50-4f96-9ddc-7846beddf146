package com.rs.module.acp.controller.admin.wb;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.wb.vo.MessageBookRespVO;
import com.rs.module.acp.controller.admin.wb.vo.MessageBookSaveReqVO;
import com.rs.module.acp.entity.wb.MessageBookDO;
import com.rs.module.acp.service.wb.MessageBookService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-窗口业务-留言内容")
@RestController
@RequestMapping("/acp/wb/messageBook")
@Validated
public class MessageBookController {

    @Resource
    private MessageBookService messageBookService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-窗口业务-留言内容")
    public CommonResult<String> createMessageBook(@Valid @RequestBody MessageBookSaveReqVO createReqVO) {
        return success(messageBookService.createMessageBook(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-窗口业务-留言内容")
    public CommonResult<Boolean> updateMessageBook(@Valid @RequestBody MessageBookSaveReqVO updateReqVO) {
        messageBookService.updateMessageBook(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-窗口业务-留言内容")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteMessageBook(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           messageBookService.deleteMessageBook(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-窗口业务-留言内容")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<MessageBookRespVO> getMessageBook(@RequestParam("id") String id) {
        MessageBookDO messageBook = messageBookService.getMessageBook(id);
        return success(BeanUtils.toBean(messageBook, MessageBookRespVO.class));
    }
}
