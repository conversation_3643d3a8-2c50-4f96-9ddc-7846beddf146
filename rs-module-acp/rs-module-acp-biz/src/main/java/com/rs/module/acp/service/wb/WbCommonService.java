package com.rs.module.acp.service.wb;

import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.wb.vo.WbInspectionResultsSaveReqVO;

import java.util.Date;
import java.util.List;

public interface WbCommonService {

    /**
     * 创建实战平台-窗口业务-附件上传
     *
     * @param objectId 对象ID
     * @param fileJson 附件JSON信息--数组
     * @return 对象ID
     */
    String saveFile(String objectId,String fileJson);

    /**
     * 创建实战平台-窗口业务-获取附件
     *
     * @param objectId 对象ID
     * @return
     */
    String getFile(String objectId);

    /**
     * 创建实战平台-窗口业务-附件删除
     *
     * @param objectId 对象ID
     * @return
     */
    boolean deleteFile(String objectId);

    /**
     * 实战平台-管教业务-出入登记存储
     * @param inspectionResultsVO 创建信息
     * @param businessType 业务类型()
     * @param type out：带出，in：带入
     * @param status 0：待处理，3：已完成
     * @param isAdditional 是否是补录
     * @return
     */
    void saveInOutRecord(WbInspectionResultsSaveReqVO inspectionResultsVO,String businessType,String type,String status,boolean isAdditional);

    /**
     * 创建实战平台-窗口业务-登记消息提醒（0：提讯、1：提询、2：提解、3：律师会见、4：家属会见(当面会见)、5：领事会见、6：家属单向视频会见）
     * @param type
     * @param targerObject
     */
    void registrationReminder(JSONObject targerObject,String type);

    /**
     * 创建实战平台-窗口业务-补录提醒（0：提讯、1：提询、2：提解、3：律师会见、4：家属会见(当面会见)、5：领事会见、6：家属单向视频会见）
     * @param type
     * @param targerObject
     */
    void additionalRecordingReminder(JSONObject targerObject,String type);

    /**
     * 获得实战平台-窗口业务-获取审讯室
     * @param isAll 是否获取全部（true：包含使用中，false：只获取空闲的）
     * @return 实战平台-窗口业务-审讯室
     */
    List<JSONObject> getIdleInterrogationRoom(boolean isAll);

    /**
     * 获得实战平台-窗口业务-获取空闲律师会见室
     * @param id 业务对象ID
     * @param type 类型（3：律师，5：领事会见）
     * @return 实战平台-窗口业务-空闲律师会见室
     */
    List<JSONObject> getIdleLawyerMeetingRoom(String id,String type);

    /**
     * 获得实战平台-窗口业务-获取家属审讯室
     * @param isAll 是否获取全部（true：包含使用中，false：只获取空闲的）
     * @return 实战平台-窗口业务-家属审讯室
     */
    List<JSONObject> getIdleFamilyRoom(boolean isAll);


    /**
     * 获得实战平台-窗口业务-获取业务轨迹
     * @param targetId 业务ID
     * @param type 类型 （0：提讯、1：提询、2：提解、3：律师会见、4：家属会见(当面会见)、5：领事会见、6：家属单向视频会见）
     * @return
     */
    List<JSONObject> getTrajectory(String targetId,String type);

    /**
     * 获得实战平台-窗口业务-校验被监管人员是否存在其他业务正在进行中
     *
     * @param jgrybm 被监管人员编码
     * @param type 类型 （0：提讯、1：提询、2：提解、3：律师会见、4：家属会见(当面会见)、5：领事会见、6：家属单向视频会见）
     */
    JSONObject verificationPersonnel(String jgrybm,String type);

    /**
     * 获得实战平台-窗口业务-根据被监管人员编码获取监管人员历史会见记录
     * @param jgrybm
     * @param pageNo
     * @param pageSize
     * @param type
     * @return
     */
    PageResult<JSONObject> getHistoryMeetingByJgrybm(String jgrybm,int pageNo,int pageSize,String type);

    /**
     * 根据会见室类型，获取会见室类型
     * @return
     */
    List<JSONObject> getMeetingRoomList(String type);


    /**
     * 窗口语音播报
     * @param type （0：提讯、1：提询、2：提解、3：律师会见、4：家属会见(当面会见)、5：领事会见、6：家属单向视频会见）
     * @param targetJson
     */
    void audioBroadcast(String type,JSONObject targetJson);
}
