package com.rs.module.acp.controller.app.gj;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice.RightsObligationsNoticeSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice.RightsObligationsNoticeViewInfoRespVO;
import com.rs.module.acp.service.gj.rightsobligationsnotice.RightsObligationsNoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-权力义务告知书")
@RestController
@RequestMapping("/app/acp/gj/rightsObligationsNotice")
@Validated
public class AppRightsObligationsNoticeController {

    @Resource
    private RightsObligationsNoticeService rightsObligationsNoticeService;

    @PostMapping("/sign")
    @ApiOperation(value = "App-签名")
    public CommonResult<String> sign(@Valid @RequestBody RightsObligationsNoticeSaveReqVO createReqVO) {
        createReqVO.setSignTime(new Date());
        return success(rightsObligationsNoticeService.sign(createReqVO));
    }

    @GetMapping("/getParams")
    @ApiOperation(value = "App-获取预览参数")
    @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true)
    public CommonResult<RightsObligationsNoticeViewInfoRespVO> getParams(@RequestParam("jgrybm") String jgrybm) {
        return success(rightsObligationsNoticeService.getParams(jgrybm));
    }

}
