package com.rs.module.acp.service.gj.equipment;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApprovalTraceVO;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.equipment.*;
import com.rs.module.acp.controller.app.gj.vo.AppEquipmentUseRespVO;
import com.rs.module.acp.entity.gj.EquipmentUseDO;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 实战平台-管教业务-械具使用 Service 接口
 *
 * <AUTHOR>
 */
public interface EquipmentUseService extends IBaseService<EquipmentUseDO>{

    /**
     * 创建实战平台-管教业务-械具使用
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createEquipmentUse(@Valid EquipmentUseSaveReqVO createReqVO);

    /**
     * 更新实战平台-管教业务-械具使用
     *
     * @param updateReqVO 更新信息
     */
    void updateEquipmentUse(@Valid EquipmentUseSaveReqVO updateReqVO);

    /**
     * 删除实战平台-管教业务-械具使用
     *
     * @param id 编号
     */
    void deleteEquipmentUse(String id);

    /**
     * 获得实战平台-管教业务-械具使用
     *
     * @param id 编号
     * @return 实战平台-管教业务-械具使用
     */
    EquipmentUseRespVO getEquipmentUse(String id);

    /**
     * 获得实战平台-管教业务-械具使用
     *
     * @param id 编号
     * @return 实战平台-管教业务-械具使用
     */
    AppEquipmentUseRespVO getAppEquipmentUse(String id);
    /**
     * 戒具使用流程
     * <AUTHOR>
     * @date 2025/6/3 19:03
     * @param [createReqVO]
     * @return void
     */
    String createStartFlow(EquipmentUseStartFlowReqVO createReqVO);

    /**
     * 戒具使用审批
     * <AUTHOR>
     * @date 2025/6/7 2:52
     * @param [approveReqVO]
     * @return void
     */
    void leaderApproveApply(GjApproveReqVO approveReqVO);

    /**
     * 提交流程
     * <AUTHOR>
     * @date 2025/6/3 20:40
     * @param [processCmd]
     * @return java.lang.Boolean
     */
    Boolean submitCreateInfo(String processCmd);

    /**
     * 待登记信息
     * @param createReqVO
     * @return
     */
    Boolean regInfo(EquipmentUseSaveRegInfoVO regInfoVO);

    /**
     * 人员信息
     * <AUTHOR>
     * @date 2025/6/4 21:35
     * @param [jgrybm]
     * @return java.util.List<com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseSaveReqVO>
     */
    List<EquipmentUseRespVO> getEquipmentUseListByJgrybm(String jgrybm);

    /**
     * 延长审批
     * <AUTHOR>
     * @date 2025/6/5 12:46
     * @param [createReqVO]
     * @return java.lang.Boolean
     */
    Boolean extendApproval(EquipmentExtendApprovalApplyReqVO createReqVO);

    /**
     * 领导审批-所领导延长审批
     * <AUTHOR>
     * @date 2025/6/5 14:37
     * @param [approveReqVO]
     * @return void
     */
    void leaderApproveExtendApply(GjApproveReqVO approveReqVO);

    /**
     * 提前解除审批申请
     * <AUTHOR>
     * @date 2025/6/5 15:31
     * @param [createReqVO]
     * @return void
     */
    Boolean removeApprovalApply(EquipmentUseRemoveApplyReqVO createReqVO);


    /**
     * 领导审批提前解除
     * <AUTHOR>
     * @date 2025/6/5 16:00
     * @param [approveReqVO]
     * @return void
     */
    void leaderApproveRemoveApply(GjApproveReqVO approveReqVO);

    /**
     * 戒具使用，流程状态
     * @param id
     * @return
     */
    List<GjApprovalTraceVO> getApproveTrack(String id);

    /**
     *戒具解除登记信息
     * <AUTHOR>
     * @date 2025/6/17 8:57
     * @param [approveReqVO]
     * @return void
     */
    void removeRegInfo(EquipmentUseRemoveApplyRegInfoVO regInfo);

    /**
     * 查询正在-佩戴戒具的人
     * <AUTHOR>
     * @date 2025/6/17 21:31
     * @param [startTime, endTime]
     * @return java.util.List<com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseRespVO>
     */
    List<EquipmentUseRespWearVO> getWearEquipmentUse(Date startTime,Date endTime);
}
