package com.rs.module.acp.service.db;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.entity.db.DetainRegKssDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-羁押业务-看守所收押登记 Service 接口
 *
 * <AUTHOR>
 */
public interface DetainRegKssService extends IBaseService<DetainRegKssDO>{

    /**
     * 创建实战平台-羁押业务-看守所收押登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDetainRegKss(@Valid DetainRegKssSaveReqVO createReqVO);

    /**
     * 更新实战平台-羁押业务-看守所收押登记
     *
     * @param updateReqVO 更新信息
     */
    void updateDetainRegKss(@Valid DetainRegKssSaveReqVO updateReqVO);

    /**
     * 删除实战平台-羁押业务-看守所收押登记
     *
     * @param id 编号
     */
    void deleteDetainRegKss(String id);

    /**
     * 获得实战平台-羁押业务-看守所收押登记
     *
     * @param id 编号
     * @return 实战平台-羁押业务-看守所收押登记
     */
    DetainRegKssDO getDetainRegKss(String id);

    /**
    * 获得实战平台-羁押业务-看守所收押登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-羁押业务-看守所收押登记分页
    */
    PageResult<DetainRegKssDO> getDetainRegKssPage(DetainRegKssPageReqVO pageReqVO);

    /**
    * 获得实战平台-羁押业务-看守所收押登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-羁押业务-看守所收押登记列表
    */
    List<DetainRegKssDO> getDetainRegKssList(DetainRegKssListReqVO listReqVO);


    CombineRespVO getCombineInfo(String id);

    void approval(GjApproveReqVO approveReqVO);

    void updateLeaderApprovalStatus(@Valid LeaderApprovalStatusReqVO updateReqVO);

    DetainRegKssDO getPrisonerInfo(String rybh);

    String ifExists(@Valid DetainRegKssSaveReqVO updateReqVO);

    InRecordStatusVO getInRecordStatus(String rybh);

    void updateStepInfo(String prison, String rybh, String status, String currentStep, String spzt, String actInstId);

}
