package com.rs.module.acp.service.db;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bsp.common.cache.DicUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.cons.DetainRegStatusEnum;
import com.rs.module.acp.cons.InProcessStageEnum;
import com.rs.module.acp.cons.LeaderApprovalStatusEnum;
import com.rs.module.acp.cons.ThirdBiometricTypeEnum;
import com.rs.module.acp.controller.admin.db.vo.*;
import com.rs.module.acp.controller.admin.db.vo.third.haixin.FingerVO;
import com.rs.module.acp.controller.admin.db.vo.third.haixin.IrisVO;
import com.rs.module.acp.controller.admin.db.vo.third.haixin.PhotoVO;
import com.rs.module.acp.controller.admin.pm.vo.LocalsenseTagPersonRsSaveReqVO;
import com.rs.module.acp.dao.db.BiometricInfoDao;
import com.rs.module.acp.dao.db.DetainRegKssDao;
import com.rs.module.acp.entity.db.BiometricInfoDO;
import com.rs.module.acp.entity.db.DetainRegKssDO;
import com.rs.module.acp.entity.db.PersonalEffectsDO;
import com.rs.module.acp.service.db.components.RegistrationInfoService;
import com.rs.module.acp.service.db.components.RegistrationInfoServiceContext;
import com.rs.module.acp.service.pm.LocalsenseTagPersonService;
import com.rs.module.oss.utils.UploadUtils;
import groovy.util.logging.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 实战平台-收押业务-生物特征信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class BiometricInfoServiceImpl extends BaseServiceImpl<BiometricInfoDao, BiometricInfoDO> implements BiometricInfoService {

    @Resource
    private BiometricInfoDao biometricInfoDao;

    @Resource
    private DetainRegKssDao detainRegKssDao;
    @Resource
    private RegistrationInfoServiceContext registrationInfoServiceContext;

    @Resource
    private PersonalEffectsService personalEffectsService;
    private static final ObjectMapper objectMapper = new ObjectMapper();
    @Resource
    private LocalsenseTagPersonService localsenseTagPersonService;
    /**
     * 创建生物特征信息。
     *
     * @param createReqVO 创建生物特征信息请求参数
     * @return 返回创建的生物特征信息ID
     */
    public String createBiometricInfo(BiometricInfoSaveReqVO createReqVO) {
        // 1. 查询是否存在相同人员编号和采集信息类型的生物特征信息
        BiometricInfoDO biometricInfo = BeanUtils.toBean(createReqVO, BiometricInfoDO.class);
        BiometricInfoDO biometricInfoDO = biometricInfoDao.selectOne(new LambdaQueryWrapper<BiometricInfoDO>()
                .eq(BiometricInfoDO::getRybh, createReqVO.getRybh())
                .eq(BiometricInfoDO::getCjxmlx, createReqVO.getCjxmlx()));
        // 2. 如果存在则提取biometricInfoDO
        if (ObjectUtil.isNotEmpty(biometricInfoDO)) {
            biometricInfo = biometricInfoDO;
            biometricInfo.setBz(createReqVO.getBz());
            biometricInfo.setSwtzfj(createReqVO.getSwtzfj());
        } else {
            // 3. 插入新的生物特征信息
            biometricInfo.setJgrybm(createReqVO.getRybh());
            biometricInfo.setStatus(DetainRegStatusEnum.DRAFT.getCode());
        }
        saveOrUpdate(biometricInfo);
        // 4. 返回新插入的生物特征信息ID
        return biometricInfo.getId();
    }

    @Override
    public void updateBiometricInfo(BiometricInfoSaveReqVO updateReqVO) {
        // 校验存在
        validateBiometricInfoExists(updateReqVO.getId());
        // 更新
        BiometricInfoDO updateObj = BeanUtils.toBean(updateReqVO, BiometricInfoDO.class);
        biometricInfoDao.updateById(updateObj);
    }

    @Override
    public void deleteBiometricInfo(String id) {
        // 校验存在
        validateBiometricInfoExists(id);
        // 删除
        biometricInfoDao.deleteById(id);
    }

    private void validateBiometricInfoExists(String id) {
        if (biometricInfoDao.selectById(id) == null) {
            throw new ServerException("实战平台-收押业务-生物特征信息数据不存在");
        }
    }

    @Override
    public BiometricInfoDO getBiometricInfo(String id) {
        return biometricInfoDao.selectById(id);
    }

    @Override
    public PageResult<BiometricInfoDO> getBiometricInfoPage(BiometricInfoPageReqVO pageReqVO) {
        return biometricInfoDao.selectPage(pageReqVO);
    }

    @Override
    public List<BiometricInfoDO> getBiometricInfoList(BiometricInfoListReqVO listReqVO) {
        return biometricInfoDao.selectList(listReqVO);
    }

    /**
     * 更新手环信息
     * 当羁押人员的手环状态发生变化时，同步更新生物信息和羁押人员的审批状态
     *
     * @param updateReqVO 包含手环更新信息的请求对象，包括人员编号(rybh)、手环状态(status)等信息
     */
    @Override
    public boolean updateWristbandInformation(BraceletBindingSaveReqVO updateReqVO) {
        boolean isApproval = false;
        //入所类型
        String rslx = updateReqVO.getRslx();
        // 获取生物信息列表并更新状态
        List<BiometricInfoDO> biometricInfoDOList = getBiometricInfoByRybh(updateReqVO.getRybh());
        if (biometricInfoDOList != null && !biometricInfoDOList.isEmpty()) {
            for (BiometricInfoDO biometricInfoDO : biometricInfoDOList) {
                biometricInfoDO.setStatus(updateReqVO.getStatus());
            }
            biometricInfoDao.updateBatch(biometricInfoDOList);
        } else if (updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.DRAFT.getCode())) {
            // 暂存时插入一条空数据
            BiometricInfoSaveReqVO createReqVO = new BiometricInfoSaveReqVO();
            createReqVO.setRybh(updateReqVO.getRybh());
            createReqVO.setCjxmlx("01");
            createBiometricInfo(createReqVO);
        }
        RegistrationInfoService registrationInfoService = registrationInfoServiceContext.getService(updateReqVO.getBusinessType());
        if(StringUtil.isNotEmpty(updateReqVO.getShid())) {
            updateReqVO.setShbdzt("02");//未绑定 01,已绑定 02 ,已解绑 03
            if(updateReqVO.getSdbdsj() ==null) updateReqVO.setSdbdsj(new Date());
            // 调用注册信息服务更新手环信息

            registrationInfoService.updateWristbandInfo(
                    updateReqVO.getRybh(),
                    updateReqVO.getShid(),
                    updateReqVO.getShbdzt(),
                    updateReqVO.getSdbdsj(),
                    updateReqVO.getStatus());
            LocalsenseTagPersonRsSaveReqVO localsenseTagPersonRsSaveReqVO = new LocalsenseTagPersonRsSaveReqVO();
            localsenseTagPersonRsSaveReqVO.setTagId(updateReqVO.getShid());
            localsenseTagPersonRsSaveReqVO.setBindPersonId(updateReqVO.getRybh());
            localsenseTagPersonRsSaveReqVO.setBindPersonName(updateReqVO.getRyxm());
            localsenseTagPersonRsSaveReqVO.setBindTime(updateReqVO.getSdbdsj());
            try {
                localsenseTagPersonService.rsCreateLocalsenseTagPerson(localsenseTagPersonRsSaveReqVO);
            } catch (Exception e) {
                log.error("关联手环异常：{}", e);
            }
        }else{
            updateReqVO.setStatus("01");//未绑定
        }
        // 结合物品登记状态，判断状态是否流转到领导审批并更新流程状态
        if (updateReqVO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())) {
            //已提交状态，需要确认生物信息采集是否已采集
            Integer count = biometricInfoDao.selectCount("rybh",updateReqVO.getRybh());
            if(count <= 0){
                throw new ServerException("生物信息未采集，不允许提交！！！");
            }
            PersonalEffectsDO personalEffectsDO = personalEffectsService.getPersonalEffectsByRybh(updateReqVO.getRybh());
            //物品登记状态若为已提交或者无需登记的
            if (personalEffectsDO != null && (personalEffectsDO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())
                    || personalEffectsDO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.NOT_NEED_REGISTER.getCode()))) {
                if(rslx!=null&&rslx.equalsIgnoreCase("02")) {
                    registrationInfoService.updateStateInfo(updateReqVO.getRybh(), null, InProcessStageEnum.COMPLETED.getCode());
                    isApproval = true;

                }else {
                    registrationInfoService.updateStateInfo(updateReqVO.getRybh(), LeaderApprovalStatusEnum.PENDING.getCode(), InProcessStageEnum.PENDING_LEADER_APPROVAL.getCode());
                    isApproval = true;
                }
            } else {
                registrationInfoService.updateStateInfo(updateReqVO.getRybh(), null, InProcessStageEnum.PENDING_ITEM_REGISTRATION.getCode());
            }
        }

        return isApproval;
    }

    /**
     * 更新主表状态信息
     * @param updateReqVO
     * @return
     */
    private boolean updateStateInfo(BraceletBindingSaveReqVO updateReqVO, String rslx) {
        boolean isApproval = false;
        RegistrationInfoService registrationInfoService = registrationInfoServiceContext.getService(updateReqVO.getBusinessType());

        PersonalEffectsDO personalEffectsDO = personalEffectsService.getPersonalEffectsByRybh(updateReqVO.getRybh());
        //物品登记状态若为已提交或者无需登记的
        if (personalEffectsDO != null && (personalEffectsDO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.SUBMITTED.getCode())
                || personalEffectsDO.getStatus().equalsIgnoreCase(DetainRegStatusEnum.NOT_NEED_REGISTER.getCode()))) {
            registrationInfoService.updateStateInfo(updateReqVO.getRybh(), LeaderApprovalStatusEnum.PENDING.getCode(), InProcessStageEnum.PENDING_LEADER_APPROVAL.getCode());
            isApproval = true;
        } else {
            registrationInfoService.updateStateInfo(updateReqVO.getRybh(), null, InProcessStageEnum.PENDING_ITEM_REGISTRATION.getCode());
        }
        return isApproval;
    }


    @Override
    public List<BiometricInfoDO> getBiometricInfoByRybh(String rybh) {
        BiometricInfoListReqVO reqVO = new BiometricInfoListReqVO();
        reqVO.setRybh(rybh);
        return biometricInfoDao.selectList(reqVO);
    }

    @Override
    public List<BiometricInfoRespVO> getBiometricVOByRybh(String rybh) {
        List<BiometricInfoDO> list = getBiometricInfoByRybh(rybh);
        List<BiometricInfoRespVO> respVOList = new ArrayList<>();
        for (BiometricInfoDO biometricInfoDO : list) {
            List<JSONObject> dataList = new ArrayList<>();
            BiometricInfoRespVO respVO = BeanUtils.toBean(biometricInfoDO, BiometricInfoRespVO.class);
            List<JSONObject> biometricList = new ArrayList<>();
            if (StringUtil.isNotEmpty(biometricInfoDO.getSwtz())) {
                ThirdBiometricTypeEnum dataType = ThirdBiometricTypeEnum.getByCode(biometricInfoDO.getCjxmlx());
                dataList = JSONObject.parseArray(biometricInfoDO.getSwtz(), JSONObject.class);
                switch (dataType) {
                    case FINGER:
                        for (JSONObject data : dataList) {
                            String zwdmName = DicUtil.translate("ZD_HX_ZWCJ_ZWDM", data.getString("zwdm"));
                            if (StringUtil.isNullBlank(zwdmName) || "null".equals(zwdmName))
                                continue;
                            data.put("forceFlagName", DicUtil.translate("ZD_TYSFDM", data.getString("forceFlag")));
                            data.put("qsqkdmName", DicUtil.translate("ZD_HX_ZWCJ_QZQK", data.getString("qsqkdm")));
                            data.put("zwdmName", DicUtil.translate("ZD_HX_ZWCJ_ZWDM", data.getString("zwdm")));
                            biometricList.add(data);
                        }
                        break;
                    case IRIS:
                        for (JSONObject data : dataList) {
                            String hmywdmName = DicUtil.translate("ZD_HX_HMCJ_YWLX", data.getString("hmywdm"));
                            if (StringUtil.isNullBlank(hmywdmName) || "null".equals(hmywdmName))
                                continue;
                            data.put("hmqsqkdmName", DicUtil.translate("ZD_HX_HMCJ_HMQSQK", data.getString("hmqsqkdm")));
                            data.put("hmywdmName", hmywdmName);
                            data.put("qzcjbsName", DicUtil.translate("ZD_TYSFDM", data.getString("qzcjbs")));
                            biometricList.add(data);
                        }
                        break;
                    case PHOTO:
                        for (JSONObject data : dataList) {
                            String ryzplxdmName = DicUtil.translate("ZD_HX_RXCJ_RXLX", data.getString("ryzplxdm"));
                            if ("31".equals(data.getString("ryzplxdm")) || StringUtil.isNullBlank(ryzplxdmName) || "null".equals(ryzplxdmName))
                                continue;
                            data.put("forceCodeName", DicUtil.translate("ZD_HX_RXCJ_QZTGLX", data.getString("forceCode")));
                            data.put("ryzplxdmName", ryzplxdmName);
                            biometricList.add(data);
                        }
                        break;
                }
            }
            respVO.setBiometric(biometricList);
            respVOList.add(respVO);
        }
        return respVOList;
    }

    @Override
    public boolean updateWristbandInfoQuick(BraceletBindingSaveReqVO updateReqVO) {
        updateWristbandInformation(updateReqVO);
        return false;
    }

    /**
     * 获取采集人员详情
     *
     * @param prisonerInfoReqVO
     * @return
     */
    @Override
    public CollectedPersonDetailVO getCollectedPersonDetail(prisonerInfoReqVO prisonerInfoReqVO) {
        String businessType = prisonerInfoReqVO.getBusinessType();

        RegistrationInfoService registrationInfoService = registrationInfoServiceContext.getService(businessType);

        return registrationInfoService.getCollectedPersonDetail(prisonerInfoReqVO);
    }

    @Override
    public void uploadCollectedData(Map<String, Object> request) {
        try {
            JSONObject req = JSONObject.parseObject(JSONObject.toJSONString(request));
            List<JSONObject> dataList = JSONObject.parseArray(req.getString("data"), JSONObject.class);
            log.warn("采集上传数据: "+request);
            String jgrybm = req.getString("JZRYBH");
            Date collectTime = req.getDate("collectTime");
            String swtz = "";
            ThirdBiometricTypeEnum dataType = ThirdBiometricTypeEnum.getByThirdCode(req.getString("dataType"));
            switch (dataType) {
                case FINGER:
                    List<FingerVO> fingerData = new ArrayList<>();
                    for (JSONObject data : dataList) {
                        FingerVO finger = new FingerVO();
                        String url = "";
                        if (StringUtil.isNotEmpty(data.getString("TXSJ"))) {
                            url = UploadUtils.uploadByBase(data.getString("TXSJ"), StringUtil.getGuid32() + ".jpg");
                        }
                        finger.setUrl(url);
                        finger.setForceFlag(data.getString("FORCE_FLAG"));
                        finger.setQsqkdm(data.getString("QSQKDM"));
                        finger.setTxzl(data.getString("TXZL"));
                        finger.setZwdm(data.getString("ZWDM"));
                        fingerData.add(finger);
                    }
                    swtz = JSONObject.toJSONString(fingerData);
                    break;
                case IRIS:
                    List<IrisVO> irisData = new ArrayList<>();
                    for (JSONObject data : dataList) {
                        IrisVO iris = new IrisVO();
                        String url = "";
                        if (StringUtil.isNotEmpty(data.getString("HMSJ"))) {
                            url = UploadUtils.uploadByBase(data.getString("HMSJ"), StringUtil.getGuid32() + ".bmp");
                        }
                        iris.setUrl(url);
                        iris.setHmqsqkdm(data.getString("HMQSQKDM"));
                        iris.setHmywdm(data.getString("HMYWDM"));
                        iris.setQzcjbs(data.getString("QZCJBS"));
                        iris.setRemark(data.getString("REMARK"));
                        iris.setXxzldf(data.getString("XXZLDF"));
                        irisData.add(iris);
                    }
                    swtz = JSONObject.toJSONString(irisData);
                    break;
                case PHOTO:
                    List<PhotoVO> photoData = new ArrayList<>();
                    for (JSONObject data : dataList) {
                        // 未裁剪的大图，不需要的数据
                        if ("31".equals(data.getString("ryzplxdm"))) {
                            continue;
                        }
                        PhotoVO photo = new PhotoVO();
                        String url = "";
                        if (StringUtil.isNotEmpty(data.getString("RYZPSJ"))) {
                            url = UploadUtils.uploadByBase(data.getString("RYZPSJ"), StringUtil.getGuid32() + ".jpg");
                        }
                        photo.setUrl(url);
                        photo.setForceCode(data.getString("FORCE_CODE"));
                        photo.setForceReason(data.getString("FORCE_REASON"));
                        photo.setRyzplxdm(data.getString("RYZPLXDM"));
                        photo.setXxzldf(data.getString("XXZLDF"));
                        photoData.add(photo);
                    }
                    swtz = JSONObject.toJSONString(photoData);
                    break;
            }

            BiometricInfoDO biometricInfoDO = getOne(new LambdaQueryWrapper<BiometricInfoDO>()
                    .eq(BiometricInfoDO::getJgrybm, jgrybm)
                    .eq(BiometricInfoDO::getCjxmlx, dataType.getCode()), false);
            if (ObjectUtil.isEmpty(biometricInfoDO)) {
                biometricInfoDO = new BiometricInfoDO();
                DetainRegKssDO detainRegKssDO = detainRegKssDao.selectOne(new LambdaQueryWrapper<DetainRegKssDO>()
                        .eq(DetainRegKssDO::getJgrybm, jgrybm));
                biometricInfoDO.setAddUser(detainRegKssDO.getAddUser());
                biometricInfoDO.setAddUserName(detainRegKssDO.getAddUserName());
                biometricInfoDO.setOrgCode(detainRegKssDO.getOrgCode());
                biometricInfoDO.setOrgName(detainRegKssDO.getOrgName());
                biometricInfoDO.setRybh(jgrybm);
                biometricInfoDO.setJgrybm(jgrybm);
                biometricInfoDO.setCjxmlx(dataType.getCode());
                biometricInfoDO.setStatus("02");
            }
            biometricInfoDO.setSwtz(swtz);
            this.saveOrUpdate(biometricInfoDO);

            //待对接数据明确后补充完善
//            String json = objectMapper.writeValueAsString(request);
//            log.warn("接收到上传数据: "+json);
//            System.out.println("###接收到上传数据: "+json);
        } catch (Exception e) {
            log.error("序列化 request 出错", e);
        }
        // 提取请求参数
        // 从请求中获取羁押人员编号（JZRYBH），优先大写键名，若为空则取小写键名
        String jzrybh = (String) Optional.ofNullable(request.get("JZRYBH")).orElse(request.get("jzrybh"));
        // 从请求中获取数据类型（dataType）
        String dataType = (String) request.get("dataType");
        Object data = request.get("data");
        String collectTimeStr = (String) request.get("collectTime");

    }
}
