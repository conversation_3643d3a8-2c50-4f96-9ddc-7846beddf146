package com.rs.module.acp.controller.app.db.vo.kss;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "app端 - 实战平台-羁押业务-看守所收押登记详情 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppDetainRegKssRespVO extends BaseVO implements TransPojo {
private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("姓名")
    private String xm;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监室号")
    private String jsh;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY,key = "ZD_XB")
    private String xb;
    @ApiModelProperty("出生日期")
    private Date csrq;
    @ApiModelProperty("证件号码")
    private String zjhm;
    @ApiModelProperty("文化程度")
    @Trans(type = TransType.DICTIONARY,key = "ZD_WHCD")
    private String whcd;
    @ApiModelProperty("人员管理类别")
    @Trans(type = TransType.DICTIONARY,key = "ZD_KSS_RYGLLB")
    private String gllb;
    @ApiModelProperty("民族")
    @Trans(type = TransType.DICTIONARY,key = "ZD_MZ")
    private String mz;
    @ApiModelProperty("籍贯")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GABBZ_JG")
    private String jg;
    @ApiModelProperty("现住址详址")
    private String xzzxz;
    @ApiModelProperty("特殊身份")
    @Trans(type = TransType.DICTIONARY,key = "ZD_TSSF")
    private String tssf;


    @ApiModelProperty("案件编号")
    private String ajbh;
    @ApiModelProperty("案由代码")
    @Trans(type = TransType.DICTIONARY,key = "ZD_AJLB")
    private String ajlbdm;
    @ApiModelProperty("入所原因")
    @Trans(type = TransType.DICTIONARY,key = "ZD_KSS_RSYY")
    private String rsyy;
    @ApiModelProperty("诉讼环节")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SSJD")
    private String sshj;
    @ApiModelProperty("送押机关类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SYYWSYDWLX")
    private String syjglx;
    @ApiModelProperty("送押机关名称")
    @Trans(type = TransType.DICTIONARY,key = "ZD_BADW_GAJG")
    private String syjgmc;
    @ApiModelProperty("羁押日期")
    private Date jyrq;
    @ApiModelProperty("简要案情")
    private String jyaq;
    @ApiModelProperty("送押人")
    private String syr1;
    @ApiModelProperty("送押人手机")
    private String syrsj1;


    @ApiModelProperty("重刑犯")
    @Trans(type = TransType.DICTIONARY,key = "ZD_TYSFDM")
    private String zxf;
    @ApiModelProperty("档案编号")
    private String dabh;
    @ApiModelProperty("收分配标识服")
    private String sfpbsf;
    @ApiModelProperty("收押民警姓名")
    private String symjxm;


}
