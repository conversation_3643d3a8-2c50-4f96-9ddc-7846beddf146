package com.rs.module.acp.service.dch;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.service.dch.context.DataChangeEventContext;
import com.rs.module.base.entity.pm.DataChangeEventLogDO;

import com.rs.module.base.enums.DataChangeEventStatusEnum;
import com.rs.module.base.vo.pm.DataChangeEventLogVO;

import java.util.List;
import java.util.Map;

/**
 * 数据变更事件日志服务接口
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
public interface DataChangeEventLogService {

    /**
     * 创建事件日志
     *
     * @param context 事件上下文
     * @return 事件ID
     */
    String createEventLog(DataChangeEventContext context);

    /**
     * 更新事件状态
     *
     * @param eventId 事件ID
     * @param status  状态
     * @return 是否成功
     */
    boolean updateEventStatus(String eventId, DataChangeEventStatusEnum status);

    int updateEventDealClass(String id, String className);

    /**
     * 更新事件处理信息
     *
     * @param eventId          事件ID
     * @param status           状态
     * @param processStartTime 开始处理时间
     * @param processEndTime   结束处理时间
     * @param errorMessage     错误信息
     * @param retryCount       重试次数
     * @param nextRetryTime    下次重试时间
     * @return 是否成功
     */
    boolean updateEventProcessInfo(String eventId, DataChangeEventStatusEnum status,
                                   java.util.Date processStartTime, java.util.Date processEndTime,
                                   String errorMessage, Integer retryCount, java.util.Date nextRetryTime);

    /**
     * 查询待处理的事件
     *
     * @param limit 限制数量
     * @return 事件列表
     */
    List<DataChangeEventLogDO> queryPendingEvents(int limit);

    /**
     * 查询需要重试的事件
     *
     * @param limit 限制数量
     * @return 事件列表
     */
    List<DataChangeEventLogDO> queryRetryEvents(int limit);

    /**
     * 根据状态查询事件
     *
     * @param status 状态
     * @param limit  限制数量
     * @return 事件列表
     */
    List<DataChangeEventLogDO> queryEventsByStatus(DataChangeEventStatusEnum status, int limit);

    /**
     * 根据业务类型查询事件
     *
     * @param businessType 业务类型
     * @param status       状态（可为空）
     * @param limit        限制数量
     * @return 事件列表
     */
    List<DataChangeEventLogDO> queryEventsByBusinessType(String businessType,
                                                         DataChangeEventStatusEnum status, int limit);

    /**
     * 获取事件详情
     *
     * @param eventId 事件ID
     * @return 事件详情
     */
    DataChangeEventLogVO getEventDetail(String eventId);

    /**
     * 删除过期的成功事件
     *
     * @param expireDays 过期天数
     * @return 删除数量
     */
    int deleteExpiredSuccessEvents(int expireDays);

    /**
     * 统计各状态的事件数量
     *
     * @return 统计结果
     */
    Map<String, Long> countEventsByStatus();

    /**
     * 重新处理失败的事件
     *
     * @param eventId 事件ID
     * @return 处理结果
     */
    CommonResult<String> retryFailedEvent(String eventId);

    /**
     * 批量重新处理失败的事件
     *
     * @param businessType 业务类型（可为空）
     * @param limit        限制数量
     * @return 处理结果
     */
    CommonResult<String> batchRetryFailedEvents(String businessType, int limit);
}
