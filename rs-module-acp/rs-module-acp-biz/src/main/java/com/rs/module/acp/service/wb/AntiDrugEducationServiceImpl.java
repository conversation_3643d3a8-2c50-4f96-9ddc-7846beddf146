package com.rs.module.acp.service.wb;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.AntiDrugEducationDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.acp.dao.wb.AntiDrugEducationDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 实战平台-窗口业务-禁毒教育 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AntiDrugEducationServiceImpl extends BaseServiceImpl<AntiDrugEducationDao, AntiDrugEducationDO> implements AntiDrugEducationService {

    @Resource
    private AntiDrugEducationDao antiDrugEducationDao;

    @Override
    public String createAntiDrugEducation(AntiDrugEducationSaveReqVO createReqVO) {
        // 插入
        AntiDrugEducationDO antiDrugEducation = BeanUtils.toBean(createReqVO, AntiDrugEducationDO.class);
        antiDrugEducationDao.insert(antiDrugEducation);
        // 返回
        return antiDrugEducation.getId();
    }

    @Override
    public void updateAntiDrugEducation(AntiDrugEducationSaveReqVO updateReqVO) {
        // 校验存在
        validateAntiDrugEducationExists(updateReqVO.getId());
        // 更新
        AntiDrugEducationDO updateObj = BeanUtils.toBean(updateReqVO, AntiDrugEducationDO.class);
        antiDrugEducationDao.updateById(updateObj);
    }

    @Override
    public void deleteAntiDrugEducation(String id) {
        // 校验存在
        validateAntiDrugEducationExists(id);
        // 删除
        antiDrugEducationDao.deleteById(id);
    }

    private void validateAntiDrugEducationExists(String id) {
        if (antiDrugEducationDao.selectById(id) == null) {
            throw new ServerException("实战平台-窗口业务-禁毒教育数据不存在");
        }
    }

    @Override
    public AntiDrugEducationDO getAntiDrugEducation(String id) {
        return antiDrugEducationDao.selectById(id);
    }

}
