package com.rs.module.acp.controller.app.db.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-随身物品登记子 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppPersonalEffectsSubRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("物品名称")
    @Trans(type = TransType.DICTIONARY,key = "ZD_FWGLWPZL")
    private String wpmc;
    @ApiModelProperty("数量")
    private String sl;
    @ApiModelProperty("物品特征")
    private String wptz;
    @ApiModelProperty("物品照片")
    private String wpzp;
    @ApiModelProperty("位置")
    private String wz;
    @ApiModelProperty("存入时间")
    private Date storageTime;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY,key = "ZD_WPDJBCFZT")
    private String status;
}
