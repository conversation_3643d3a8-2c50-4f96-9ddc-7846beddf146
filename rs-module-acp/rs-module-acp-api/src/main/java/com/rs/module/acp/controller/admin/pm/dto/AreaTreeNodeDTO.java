package com.rs.module.acp.controller.admin.pm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by Ace on 2017/6/12.
 */
@Data
public class AreaTreeNodeDTO implements Serializable {
    /**
     * ID
     */
    private String id;
    /**
     * 节点显示内容
     */
    private String title;
    /**
     * 父节点ID
     */
    private String parentId;

    /**
     * 节点类型:0：区域，1：设备
     */
    private String areaType;

    private Integer treeLevel;
    /**
     * 子节点集合
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<AreaTreeNodeDTO> children;
    
    @ApiModelProperty("每个监室的人员数量")
    private int personCunt;
}
