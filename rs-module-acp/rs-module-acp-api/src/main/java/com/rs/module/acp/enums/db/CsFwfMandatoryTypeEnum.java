package com.rs.module.acp.enums.db;

/**
 * 防误放强制类型枚举
 *
 * <AUTHOR>
 * @Date 2025/8/14 16:08
 */
public enum CsFwfMandatoryTypeEnum {

    FORCE_IN("1", "强制带入"),
    FORCE_OUT("2", "强制带出");
    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static CsFwfMandatoryTypeEnum getByCode(String code) {
        for (CsFwfMandatoryTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    CsFwfMandatoryTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
