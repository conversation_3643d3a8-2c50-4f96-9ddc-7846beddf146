package com.rs.module.acp.enums.gj;

public enum RightsObligationsNoticeSignTypeEnum {

    ONLINE_SIGN("1", "线上"),
    OFFLINE_SIGH("2", "线下");

    RightsObligationsNoticeSignTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;

    private String name;

    public static RightsObligationsNoticeSignTypeEnum getByCode(String code) {
        RightsObligationsNoticeSignTypeEnum[] enums = RightsObligationsNoticeSignTypeEnum.values();
        for (RightsObligationsNoticeSignTypeEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
