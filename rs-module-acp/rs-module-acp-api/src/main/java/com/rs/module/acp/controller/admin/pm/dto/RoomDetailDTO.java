package com.rs.module.acp.controller.admin.pm.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class RoomDetailDTO implements Serializable {

    @ApiModelProperty("标签ID")
    private String tagId;
    @ApiModelProperty("人员编码")
    private String personId;
    @ApiModelProperty("人员姓名")
    private String personName;
    /*@ApiModelProperty("x坐标")
    private String  x;
    @ApiModelProperty("y坐标")
    private String  y;
    @ApiModelProperty("z坐标")
    private String  z;*/
    @ApiModelProperty("电量百分比")
    private String batteryPercent;
    @ApiModelProperty("体征信息-体温")
    private JSONObject twVitalSignInfo;
    @ApiModelProperty("体征信息-血氧")
    private JSONObject xyVitalSignInfo;
    @ApiModelProperty("体征信息-心率")
    private JSONObject xlVitalSignInfo;
    @ApiModelProperty("位置信息")
    private JSONObject position;
    @ApiModelProperty("数据时间")
    private String datetime;
    @ApiModelProperty("最新告警信息")
    private JSONObject latestAlarmInfo;
}
