package com.rs.module.acp.enums.gj;

public enum XzfyssjlStatusEnum {

    DZD("01", "待转递"),
    YZD("02", "已转递");

    XzfyssjlStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;

    private String name;

    public static XzfyssjlStatusEnum getByCode(String code) {
        XzfyssjlStatusEnum[] enums = XzfyssjlStatusEnum.values();
        for (XzfyssjlStatusEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("无效状态类型: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
