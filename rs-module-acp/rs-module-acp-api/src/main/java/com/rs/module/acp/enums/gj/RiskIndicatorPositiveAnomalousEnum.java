package com.rs.module.acp.enums.gj;

public enum RiskIndicatorPositiveAnomalousEnum {

    ZX("01", "加分指标"),
    YC("02", "风险指标");

    RiskIndicatorPositiveAnomalousEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;

    private String name;

    public static RiskIndicatorPositiveAnomalousEnum getByCode(String code) {
        RiskIndicatorPositiveAnomalousEnum[] enums = RiskIndicatorPositiveAnomalousEnum.values();
        for (RiskIndicatorPositiveAnomalousEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("非法【指标类型】编码: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
