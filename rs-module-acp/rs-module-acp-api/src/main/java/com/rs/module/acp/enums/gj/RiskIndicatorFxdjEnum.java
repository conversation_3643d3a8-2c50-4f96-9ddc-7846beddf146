package com.rs.module.acp.enums.gj;

public enum RiskIndicatorFxdjEnum {

    GFX("1", "高风险"),
    ZFX("2", "中风险"),
    DFX("3", "低风险");

    RiskIndicatorFxdjEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;

    private String name;

    public static RiskIndicatorFxdjEnum getByCode(String code) {
        RiskIndicatorFxdjEnum[] enums = RiskIndicatorFxdjEnum.values();
        for (RiskIndicatorFxdjEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("非法【风险等级】编码: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
