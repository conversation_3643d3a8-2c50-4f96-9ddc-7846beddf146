package com.rs.module.acp.enums.db;

/**
 * 防误放业务类型枚举
 *
 * <AUTHOR>
 * @Date 2025/8/14 14:21
 */
public enum CsFwfBusinessTypeEnum {
    OUT("1", "出所"),
    IN("2", "入所");
    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static CsFwfBusinessTypeEnum getByCode(String code) {
        for (CsFwfBusinessTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    CsFwfBusinessTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
