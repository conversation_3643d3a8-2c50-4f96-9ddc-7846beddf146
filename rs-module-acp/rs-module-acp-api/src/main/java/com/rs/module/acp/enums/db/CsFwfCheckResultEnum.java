package com.rs.module.acp.enums.db;

/**
 * 防误放核验结果枚举
 *
 * <AUTHOR>
 * @Date 2025/8/14 14:23
 */
public enum CsFwfCheckResultEnum {
    WAIT_VERIFY("01", "待核验"),
    VERIFY_SUCCESS("02", "验证成功"),
    VERIFY_FAIL("03", "验证失败");
    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static CsFwfCheckResultEnum getByCode(String code) {
        for (CsFwfCheckResultEnum value : CsFwfCheckResultEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    CsFwfCheckResultEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
