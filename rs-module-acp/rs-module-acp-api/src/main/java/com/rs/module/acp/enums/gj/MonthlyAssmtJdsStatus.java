package com.rs.module.acp.enums.gj;

public enum MonthlyAssmtJdsStatus {

    DDJ("01", "待登记"),
    DJYQM("02", "待戒员签名"),
    YWC("03", "已完成");

    MonthlyAssmtJdsStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;

    private String name;

    public static MonthlyAssmtJdsStatus getByCode(String code) {
        MonthlyAssmtJdsStatus[] enums = MonthlyAssmtJdsStatus.values();
        for (MonthlyAssmtJdsStatus e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("无效状态类型: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
