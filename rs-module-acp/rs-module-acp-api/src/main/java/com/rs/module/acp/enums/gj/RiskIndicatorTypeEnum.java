package com.rs.module.acp.enums.gj;

public enum RiskIndicatorTypeEnum {

    FXZB("1", "风险指标"),
    JFZB("2", "加分指标"),
    WZBG("3", "完整报告");

    RiskIndicatorTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;

    private String name;

    public static RiskIndicatorTypeEnum getByCode(String code) {
        RiskIndicatorTypeEnum[] enums = RiskIndicatorTypeEnum.values();
        for (RiskIndicatorTypeEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
