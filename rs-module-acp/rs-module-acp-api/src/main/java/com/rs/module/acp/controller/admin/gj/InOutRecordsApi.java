package com.rs.module.acp.controller.admin.gj;


import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.db.dto.CasePersonnelRespVO;
import com.rs.module.acp.controller.admin.gj.dto.InOutStatisticDetailDTO;
import com.rs.module.acp.enums.db.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC服务-出入登记信息")
public interface InOutRecordsApi {

    String PREFIX = ApiConstants.PREFIX + "/inOutRecords";

    @GetMapping(PREFIX + "/selectBusinessTypeCount")
    @Operation(summary = "查询当前外出人数")
    @Parameter(name = "roomId", description = "roomId", example = "", required = true)
    List<InOutStatisticDetailDTO> selectBusinessTypeCount(@RequestParam("roomId") String roomId);

}
