package com.rs.module.acp.controller.admin.pm;

import cn.hutool.core.lang.tree.Tree;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.pm.dto.AreaTreeNode;
import com.rs.module.acp.controller.admin.pm.dto.RoomDetailDTO;
import com.rs.module.acp.controller.admin.pm.dto.TagTrackDTO;
import com.rs.module.acp.enums.db.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC服务-智能腕带")
public interface LocalsenseTagApi {
    String PREFIX = ApiConstants.PREFIX + "/localsensetag";

    /**
     * 1、提供监所下的监区和监室信息
     * 返回 ID，Name（区域名称），Type（区域类型）
     */
    @GetMapping(PREFIX + "/areaInfo")
    @Operation(summary = "提供监所下的监区和监室信息")
    @Parameter(name = "areaCode", description = "code", required = true)
    CommonResult<List<Tree<String>>> getAreaInfo(@RequestParam("areaCode") String areaCode);

    /**
     * 2、监所、监区、监室三类手环聚合数据
     * 返回某个监所下手环聚合总数（监所、监区、监室）——通过参数返回在押人员和民警的数据
     */
    @GetMapping(PREFIX + "/aggregateData")
    @Operation(summary = "监所、监区、监室三类手环聚合数据")
    @Parameters({
        @Parameter(name = "areaCode", description = "区域编码", required = true),
        @Parameter(name = "personType", description = "人员类型（01：在押人员，02：民警，不传则查询所有）", required = false)
    })
    CommonResult<List<Tree<String>>> getAggregateData(
        @RequestParam("areaCode") String areaCode,
        @RequestParam(value = "personType", required = false) String personType);

    /**
     * 3、提供某个监室下的所有手环定位数据
     * 返回某个监室下的所有手环定位数据，——通过参数返回在押人员和民警的手环或民警卡定位数据
     */
    @GetMapping(PREFIX + "/roomPositionData")
    @Operation(summary = "提供某个监室下的所有手环定位数据")
    @Parameters({
        @Parameter(name = "areaCode", description = "区域编码", required = true),
        @Parameter(name = "roomId", description = "监室ID", required = true),
        @Parameter(name = "personType", description = "人员类型（01：在押人员，02：民警，不传则查询所有）", required = false)
    })
    CommonResult<List<RoomDetailDTO>> getRoomPositionData(@RequestParam("areaCode") String areaCode,
        @RequestParam("roomId") String roomId,
        @RequestParam(value = "personType", required = false) String personType);

    /**
     * 4、提供某个手环对应的详情数据
     * 返回手环电量、心率、体温基础数据及绑定在押人员的基础数据；（民警卡无详情数据）
     */
    @GetMapping(PREFIX + "/tagDetail")
    @Operation(summary = "提供某个手环对应的详情数据")
    @Parameter(name = "tagId", description = "手环ID", required = true)
    CommonResult<RoomDetailDTO> getTagDetail(@RequestParam("tagId") String tagId);

    /**
     * 5、提供手环或民警卡的历史轨迹数据
     * 返回手环或民警卡的历史轨迹数据在地图上展示——通过参数返回在押人员和民警的手环或民警卡历史定位数据
     */
    @GetMapping(PREFIX + "/historyTrack")
    @Operation(summary = "提供手环或民警卡的历史轨迹数据")
    @Parameters({
        @Parameter(name = "num", description = "被监管人员编码", required = true),
        @Parameter(name = "startTime", description = "开始时间", required = true),
        @Parameter(name = "endTime", description = "结束时间", required = true),
        @Parameter(name = "personType", description = "人员类型（01：在押人员，02：民警，不传则查询所有）", required = false)
    })
    CommonResult<List<TagTrackDTO>> getHistoryTrack(
        @RequestParam("num") String num,
        @RequestParam("startTime") String startTime,
        @RequestParam("endTime") String endTime,
        @RequestParam(value = "personType", required = false) String personType);

    @PostMapping("/getDeviceTreeByArea")
    @Operation(summary = "监所区域-设备树")
    CommonResult<List<AreaTreeNode>> getDeviceTreeByArea(@RequestParam("orgCode") String orgCode,
                                                                @RequestParam(name = "deviceStatus", required = false) String deviceStatus,
                                                                @RequestParam(name = "roomId",required = false) String roomId,
                                                                @RequestParam(name = "deviceName",required = false) String deviceName,
                                                                @RequestParam(name = "deviceTypeId",required = false) String deviceTypeId);
}
