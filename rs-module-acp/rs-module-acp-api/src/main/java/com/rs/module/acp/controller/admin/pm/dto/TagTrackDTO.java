package com.rs.module.acp.controller.admin.pm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TagTrackDTO implements Serializable {
    @ApiModelProperty("手环绑定记录ID(被监管人员编号)")
    private String num;
    @ApiModelProperty("每张地图上对应的时间戳集合")
    private List<PostTime> postTime;
    @ApiModelProperty("位置数据集合")
    private List<PostData> postData;

    @Data
    public static class PostTime{
        @ApiModelProperty("产生坐标对应的时间戳")
        private List<Long> time;
        @ApiModelProperty("位置所在的地地图ID")
        private String mapid;
    }

    @Data
    public static class PostData{
        @ApiModelProperty("坐标数据集合，每个小集合中分两个数据分表代表x,y坐标")
        private List<List<Integer>> data;
        @ApiModelProperty("位置所在的地地图ID")
        private String mapid;
    }
}
