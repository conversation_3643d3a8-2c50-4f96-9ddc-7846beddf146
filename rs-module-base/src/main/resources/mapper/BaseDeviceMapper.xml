<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.rs.module.base.dao.pm.device.BaseDeviceDao">
<!--
 ,b.config_code
 LEFT JOIN map_area_config b on a.id = b.area_id
 -->
    <select id="areaTree" resultType="com.rs.module.base.vo.TreeNode">
        select a.*, a.area_name as title, 'area' as type, a.area_name as label
        from acp_pm_area a
        <where>
            <if test="form.orgCode != null and form.orgCode != ''">
                AND a.org_code = #{form.orgCode}
            </if>
            <if test="form.areaId != null and form.areaId != ''">
                and a.id IN
                <foreach item="item" index="index" collection="form.areaId.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by id
    </select>

    <select id="deviceTree" resultType="com.rs.module.base.vo.TreeNode">
        select a.id,a.area_id as parent_id,a.device_name as title, 'camera' as type, b.channel_id as data, b.device_ip
        as value,
        a.device_status as label,b.type as cameraType,a.org_code
        from acp_pm_device a
        inner join acp_pm_device_camera b on a.id = b.device_id
        <where>
            <if test="form.orgCode != null and form.orgCode != ''">
                AND a.org_code = #{form.orgCode}
            </if>
            <if test="form.areaId != null and form.areaId != ''">
                AND a.area_id = #{form.areaId}
            </if>
            <if test="form.deviceTypeId != null and form.deviceTypeId != ''">
                AND a.device_type_id = #{form.deviceTypeId}
            </if>
            <if test="form.deviceName != null and form.deviceName != ''">
                AND a.device_name like concat('%',#{form.deviceName},'%')
            </if>
        </where>
        order by id
    </select>

    <select id="meetRoomDeviceTree" resultType="com.rs.module.base.vo.TreeNode">
        select a.id,a.area_id as parent_id,a.device_name as title, 'camera' as type, b.channel_id as data, b.device_ip
        as value,
        a.device_status as label,b.type as cameraType,a.org_code
        from acp_pm_device a
        inner join acp_pm_device_camera b on a.id = b.device_id
        <where>
            (a.device_name like concat('%','会见','%') or a.device_name like concat('%','审讯','%'))
            <if test="form.orgCode != null and form.orgCode != ''">
                AND a.org_code = #{form.orgCode}
            </if>
            <if test="form.areaId != null and form.areaId != ''">
                AND a.area_id = #{form.areaId}
            </if>
            <if test="form.deviceTypeId != null and form.deviceTypeId != ''">
                AND a.device_type_id = #{form.deviceTypeId}
            </if>
            <if test="form.deviceName != null and form.deviceName != ''">
                AND a.device_name like concat('%',#{form.deviceName},'%')
            </if>
        </where>
    </select>

    <select id="getRoomName" resultType="java.lang.String">
		select a.room_name as roomName from acp_pm_area_prison_room a where a.id = #{roomId}
	</select>

    <select id="getMaxIdByType" resultType="String">
		select max(id) from acp_pm_device
		where id like concat(#{str},'%')
	</select>

    <select id="getAllDevices" resultType="com.rs.module.base.vo.TreeNode">
        select a.id,case when b.bd_area_code is null then a.room_id else b.bd_area_code end as parent_id,a.device_name as title,'camera' as type ,
               a.ip_address as value,a.device_status as label,a.org_code,a.channel_id as chanId
        from acp_pm_device a left JOIN acp_pm_area_mapping b on a.id=b.sp_area_code
        <where>
            <if test="form.orgCode != null and form.orgCode != ''">
                AND a.org_code = #{form.orgCode}
            </if>
            <if test="form.areaId != null and form.areaId != ''">
                AND a.area_id = #{form.areaId}
            </if>
            <if test="form.roomId != null and form.roomId != ''">
                AND a.room_id = #{form.roomId}
            </if>
            <if test="form.deviceTypeId != null and form.deviceTypeId != ''">
                AND a.device_type_id = #{form.deviceTypeId}
            </if>
            <if test="form.deviceName != null and form.deviceName != ''">
                AND a.device_name like concat('%',#{form.deviceName},'%')
            </if>
            <if test="form.dataSources != null and form.dataSources != ''">
                AND a.data_sources = #{form.dataSources}
            </if>
            <if test="form.dataTypes != null and form.dataTypes != ''">
                AND b.data_types = #{form.dataTypes}
            </if>
            <if test="form.deviceStatus != null and form.deviceStatus != ''">
                AND b.device_status = #{form.deviceStatus}
            </if>
        </where>
        order by id
    </select>


    <select id="getElectronicAllDevices" resultType="com.rs.module.base.vo.TreeNode">
        SELECT A
        .ID,
        A.area_id AS parent_id,
        A.device_name AS title,
        'camera' AS TYPE,
        A.device_type_id as device_type,
        A.ip_address AS
        VALUE
        ,
        A.device_status AS label,
        A.org_code,
        CASE

        WHEN ( SELECT COUNT ( 1 ) FROM device_surface ds WHERE ds.device_id = A.ID ) > 0 THEN
        1 ELSE 0
        END AS isPoint,
        ds.map_id,
        ba.area_name
        FROM
        acp_pm_device A LEFT JOIN device_surface ds on a.id = ds.device_id
        LEFT JOIN acp_pm_area ba on a.area_id = ba."id"
        <where>
            <if test="form.orgCode != null and form.orgCode != ''">
                AND a.org_code = #{form.orgCode}
            </if>
            <if test="form.deviceTypeId != null and form.deviceTypeId != ''">
                AND a.device_type_id = #{form.deviceTypeId}
            </if>
            <if test="form.deviceName != null and form.deviceName != ''">
                AND a.device_name like concat('%',#{form.deviceName},'%')
            </if>
        </where>
        order by id
    </select>

    <select id="getParentIdById" resultType="java.lang.String">
        select a.parent_id from acp_pm_area a where a.id = #{id} LIMIT 1
    </select>

    <select id="getParentAreaId" resultType="com.rs.module.base.vo.TreeNode">
        select a.parent_id,a.id from acp_pm_area a where a.org_code = #{id}
    </select>

    <select id="getFlowList" resultType="map">
        select a.id as id,a.level as "level"
        from acp_pm_area a
        where
        a.area_type = #{type}
        <if test="orgCode != null and orgCode != ''">
            AND a.org_code = #{orgCode}
        </if>
    </select>

    <select id="getParentNodeById" resultType="com.rs.module.base.vo.TreeNode">
        select a.*, a.area_name as title, 'area' as type, a.area_name as label,b.config_code,
        case when
        (a.area_type = '0001' or a.area_type = '0037')
        then 1
        else
        '0'
        end as isConfig
        from acp_pm_area a
        LEFT JOIN map_area_config b on a.id = b.area_id
        where a.id = #{id} LIMIT 1
    </select>

   <!-- <update id="updateDeviceStatus">
        update acp_pm_device set device_status=#{deviceStatus} where id=#{deviceId}
    </update>

    <update id="updateMacAddress">
        update acp_pm_device set mac_address=#{macAddress} where id=#{deviceId}
    </update>

    <update id="updateSerialNumber">
        update acp_pm_device_inscreen set serial_number=#{serialNumber} where device_id=#{deviceId}
    </update>-->

    <update id="updateDeviceStatusByChanId" >
        UPDATE acp_pm_device
        SET device_status = #{status}
        WHERE
                ID = ( SELECT device_id FROM acp_pm_device_camera A WHERE A.channel_id = #{chanId} LIMIT 1)
    </update>

    <update id="updateDeviceGbCodeByChanId" >
        UPDATE acp_pm_device
        SET gb_code = #{code}
        WHERE
                ID = ( SELECT device_id FROM acp_pm_device_camera A WHERE A.channel_id = #{chanId} LIMIT 1)
    </update>
    <update id="updateDeviceByChanId" >
        UPDATE acp_pm_device
        SET gb_code = #{code}, device_status = #{status}
        WHERE
                ID = ( SELECT device_id FROM acp_pm_device_camera A WHERE A.channel_id = #{chanId} LIMIT 1)
    </update>
</mapper>