<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.base.dao.pm.AreaDao">


    <select id="getAreaListByOrgCode" resultType="com.rs.module.base.controller.admin.pm.vo.AreaListRespVO">
        SELECT
        apa.area_name,
        apa.area_code,
        apapr.room_name,
        apapr.room_code
        FROM
        acp_pm_area apa INNER JOIN acp_pm_area_prison_room apapr ON apa.area_code = apapr.area_id
        AND apa.org_code = apapr.org_code
        WHERE
        apa.org_code = #{orgCode} AND apa.area_type = #{areaType}
        AND apa.is_del = 0 AND apapr.is_del = 0
        <if test="roomCodes != null">
            AND apapr.room_code in
            <foreach collection="roomCodes" item="roomCode" open="(" separator="," close=")">
                #{roomCode}
            </foreach>
        </if>
        ORDER BY apa.area_code, apapr.room_code
    </select>

    <select id="getAreaListByOrgCodeAndAreaType" resultType="com.rs.module.base.controller.admin.pm.vo.AreaInfoRespVO">
        WITH RECURSIVE cte AS (
        SELECT id, org_code, org_name, area_name, area_code, area_type, level, parent_id
        FROM acp_pm_area
        WHERE   is_del =0 and org_code = #{orgCode} AND area_type = #{areaType} -- 指定子节点
        UNION
        SELECT d.id, d.org_code, d.org_name, d.area_name, d.area_code,d.area_type, d.level, d.parent_id
        FROM acp_pm_area d
        INNER JOIN cte ON d.id = cte.parent_id and d.is_del  = 0
        )
        SELECT * FROM cte
    </select>
    <select id="queryArea" resultType="com.rs.module.base.entity.pm.AreaDO">
        SELECT
        apa.id,
        apa.org_code,
        apa.org_name,
        apa.area_name,
        apa.area_code,
        apa.area_type,
        apa.parent_id,
        apa.all_parent_id,
        apa.level,
        apa.is_del,
        apa.add_user,
        apa.add_user_name,
        apa.add_time,
        apa.update_user,
        apa.update_user_name,
        apa.update_time
        FROM
        acp_pm_area apa
        WHERE
        apa.id in
        <foreach collection="allParentId" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and apa.area_type = '0002'
        and apa.org_code = #{orgCode}
        and apa.is_del = '0'
    </select>

    <select id="selectByOrgCodeAndCondition"
            resultType="com.rs.module.base.controller.admin.pm.vo.AreaInfoRespVO">
        SELECT
        apa.id,
        apa.org_code,
        apa.org_name,
        apa.area_name,
        apa.area_code,
        apa.area_type,
        apa.parent_id,
        apa.all_parent_id,
        apa.level,
        apa.is_del,
        apa.add_user,
        apa.add_user_name,
        apa.add_time,
        apa.update_user,
        apa.update_user_name,
        apa.update_time
        FROM
        acp_pm_area apa
        WHERE
        apa.org_code = #{orgCode}
        <if test="areaName != null and areaName != ''">
            AND apa.area_name like concat('%', #{areaName},'%')
        </if>
        <if test="areaCode != null and areaCode != ''">
            AND apa.area_code like concat('%', #{areaCode},'%')
        </if>
        <if test="areaType != null and areaType != ''">
            AND apa.area_type = #{areaType}
        </if>
        order by id;
    </select>
    <select id="selecChildrentPage" resultType="com.rs.module.base.entity.pm.AreaDO">
    </select>
    <select id="getMaxIdByAreaType" resultType="string">
        select max(id) from    acp_pm_area  where id like concat(#{str},'%')
    </select>

    <select id="findParentByParentId" resultType="com.rs.module.base.vo.TreeNode">
        select a.id, a.id as value, a.area_name as title, a.area_type as type, a.area_name as data, a.area_name as label,org_code,parent_id
        from acp_pm_area a
        where a.id =#{parentId}
        order by id;
    </select>
</mapper>
