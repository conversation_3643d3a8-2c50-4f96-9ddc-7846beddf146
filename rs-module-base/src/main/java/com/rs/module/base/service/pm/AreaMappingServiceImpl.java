package com.rs.module.base.service.pm;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.dao.pm.BaseDeviceCameraDao;
import com.rs.module.acp.entity.pm.BaseDeviceCameraDO;
import com.rs.module.acp.service.pm.BaseDeviceCameraService;
import com.rs.module.base.controller.admin.pm.vo.*;
import com.rs.module.base.dao.pm.AreaMappingDao;
import com.rs.module.base.dao.pm.device.BaseDeviceDao;
import com.rs.module.base.dao.pm.device.SplwTreeDao;
import com.rs.module.base.entity.pm.AreaMappingDO;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;
import com.rs.module.base.entity.pm.device.SplwTreeDO;
import com.rs.module.base.service.pm.device.BaseDeviceService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;


import com.rs.framework.common.util.object.BeanUtils;



import com.rs.framework.common.exception.ServerException;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 实战平台-监管管理-区域映射 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AreaMappingServiceImpl extends BaseServiceImpl<AreaMappingDao, AreaMappingDO> implements AreaMappingService {

    @Resource
    private AreaMappingDao areaMappingDao;

    @Resource
    private SplwTreeDao splwTreeDao;

    @Resource
    private BaseDeviceService baseDeviceService;
    @Resource
    private BaseDeviceCameraService baseDeviceCameraService;

    @Resource
    private BaseDeviceDao baseDeviceDao;

    @Resource
    private BaseDeviceCameraDao baseDeviceCameraDao;
    @Override
    public String createAreaMapping(AreaMappingSaveReqVO createReqVO) {
        // 插入
        AreaMappingDO areaMapping = BeanUtils.toBean(createReqVO, AreaMappingDO.class);
        areaMappingDao.insert(areaMapping);
        // 返回
        return areaMapping.getId();
    }

    @Override
    public void updateAreaMapping(AreaMappingSaveReqVO updateReqVO) {
        // 校验存在
        validateAreaMappingExists(updateReqVO.getId());
        // 更新
        AreaMappingDO updateObj = BeanUtils.toBean(updateReqVO, AreaMappingDO.class);
        areaMappingDao.updateById(updateObj);
    }

    @Override
    public void deleteAreaMapping(String id) {
        // 校验存在
        validateAreaMappingExists(id);
        // 删除
        areaMappingDao.deleteById(id);
    }

    private void validateAreaMappingExists(String id) {
        if (areaMappingDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-区域映射数据不存在");
        }
    }

    @Override
    public AreaMappingDO getAreaMapping(String id) {
        return areaMappingDao.selectById(id);
    }

    @Override
    public String getSpAreaCodeByBdAreaCode(String bdAreaCode) {
        return areaMappingDao.selectOne(new LambdaQueryWrapper<AreaMappingDO>()
                        .select(AreaMappingDO::getSpAreaCode)
                        .eq(AreaMappingDO::getTypes, "01")
                        .eq(AreaMappingDO::getBdAreaCode, bdAreaCode))
                .getSpAreaCode();
    }
    @Override
    public String getBdAreaCodeBySpAreaCode(String spAreaCode) {
        return areaMappingDao.selectOne(new LambdaQueryWrapper<AreaMappingDO>()
                        .select(AreaMappingDO::getBdAreaCode)
                        .eq(AreaMappingDO::getTypes, "01")
                        .eq(AreaMappingDO::getSpAreaCode, spAreaCode))
                .getBdAreaCode();
    }

    @Override
    public List<Tree<String>> getAreaTree(String orgCode, String areaName) {
        // 查询匹配条件的区域列表
        LambdaQueryWrapper<SplwTreeDO> query = new LambdaQueryWrapper<SplwTreeDO>().eq(SplwTreeDO::getOrgCode, orgCode);
        if(StringUtil.isNotEmpty(areaName)) query.like(SplwTreeDO::getName, areaName);
        List<SplwTreeDO> areaList = splwTreeDao.selectList(query);

        // 如果 areaName 没传，则返回完整树
        if (StrUtil.isBlank(areaName)) {
            // 查询所有节点构建完整树
            List<SplwTreeDO> allList = splwTreeDao.selectList(new LambdaQueryWrapper<SplwTreeDO>().eq(SplwTreeDO::getOrgCode, orgCode));
            return buildTree(allList);
        }

        // 2. 组合匹配节点和所有祖先节点
        Set<String> requiredIds = new HashSet<>();
        for (SplwTreeDO matched : areaList) {
            if (StrUtil.isNotBlank(matched.getParentPathId())) {
                requiredIds.addAll(Arrays.asList(matched.getParentPathId().split("/")));
            }
            requiredIds.add(matched.getId()+"");
        }

        // 3. 查询所有数据
        List<SplwTreeDO> allList = splwTreeDao.selectList(new LambdaQueryWrapper<SplwTreeDO>().eq(SplwTreeDO::getOrgCode, orgCode));

        // 4. 过滤出所需的所有节点
        List<SplwTreeDO> filtered = allList.stream()
                .filter(area -> requiredIds.contains(area.getId()+""))
                .collect(Collectors.toList());

        // 构建并返回过滤后的区域树
        return buildTree(filtered);
    }

    private List<Tree<String>> buildTree(List<SplwTreeDO> areaList) {
        // 使用TreeUtil工具类构建树结构，根节点的父ID为"1"（根据数据库中的实际根节点ID）
        return TreeUtil.build(areaList, "1", (area, treeNode) -> {
            // 将地区数据对象的属性值赋给树节点
            treeNode.setId(area.getId()+"");
            treeNode.setParentId(area.getPid() != null ? area.getPid()+"" : "1");
            treeNode.setName(area.getName());
            treeNode.setWeight(area.getOrderNo() != null ? area.getOrderNo() : 1);
            // 将额外的地区信息作为节点的扩展属性
            treeNode.putExtra("orgCode", area.getOrgCode());
            treeNode.putExtra("areaName", area.getName());
            treeNode.putExtra("areaCode", area.getTreeCode());
            treeNode.putExtra("areaType", area.getType());
        });
    }
    /*
    保存映射关系 映射数据类型 area 区域映射,camera 摄像机
    dataTypes = area 区域映射
    保存映射关系即可
    dataTypes = camera 摄像机
    保存映射关系 一个区域可关联多个摄像机 摄像机标识为通道ID

    判断设备是否存在
        如不存在 写入设备表，设备摄像机表
        如存在 更新设备表、设备摄像机表

    * */
    @Override
    public boolean saveAreaMappingCamera(AreaMappingCameraSaveReqVO saveReqVO) {
        for (AreaMappingCameraSaveReqVO.ChanInfo chanInfo: saveReqVO.getChanInfoList()){
            AreaMappingDO areaMapping = BeanUtils.toBean(saveReqVO, AreaMappingDO.class);
            //判断设备是否存在
            BaseDeviceDO deviceDO = baseDeviceDao.selectOne(new LambdaQueryWrapper<BaseDeviceDO>().eq(BaseDeviceDO::getChannelId, chanInfo.getChanId()));
            String deviceId = StringUtil.getGuid();
            //当前设备不存在
            if(null == deviceDO || StringUtil.isEmpty(deviceDO.getId())){
                BaseDeviceDO baseDevice = initBaseDeviceDOData(chanInfo,null, SessionUserUtil.getSessionUser().getOrgCode(),areaMapping.getBdAreaCode());
                baseDevice.setId(deviceId);
                baseDeviceDao.insert(baseDevice);
            }else{
                deviceId = deviceDO.getId();
                deviceDO = initBaseDeviceDOData(chanInfo,deviceDO, SessionUserUtil.getSessionUser().getOrgCode(),areaMapping.getBdAreaCode());
                baseDeviceDao.updateById(deviceDO);
            }
            BaseDeviceCameraDO deviceCameraDO =  baseDeviceCameraDao.selectOne(new LambdaQueryWrapper<BaseDeviceCameraDO>().eq(BaseDeviceCameraDO::getChannelId, chanInfo.getChanId()));
            //当前设备不存在
            if(null == deviceCameraDO || StringUtil.isEmpty(deviceCameraDO.getId())){
                BaseDeviceCameraDO baseDeviceCameraDO = initBaseDeviceCameraDOData(chanInfo,deviceCameraDO, SessionUserUtil.getSessionUser().getOrgCode(),deviceId);
                baseDeviceCameraDao.insert(baseDeviceCameraDO);
            }else{
                deviceCameraDO = initBaseDeviceCameraDOData(chanInfo,deviceCameraDO, SessionUserUtil.getSessionUser().getOrgCode(),deviceId);
                baseDeviceCameraDao.updateById(deviceCameraDO);
            }

            areaMapping.setSpAreaCode(deviceId);
            areaMapping.setSpAreaName(chanInfo.getChanName());
            areaMapping.setSpParentId(chanInfo.getSpParentId());
            areaMapping.setSpParentName(chanInfo.getSpParentName());
            areaMapping.setTypes("01");
            areaMapping.setDataTypes("camera");
            //查询当前是否存在映射关系
            AreaMappingDO areaMappingDO = areaMappingDao.selectOne(new LambdaQueryWrapper<AreaMappingDO>()
                    .eq(AreaMappingDO::getSpAreaCode, areaMapping.getSpAreaCode())
                    .eq(AreaMappingDO::getTypes, "01")
                    .eq(AreaMappingDO::getDataTypes, "camera")
                    .eq(AreaMappingDO::getBdAreaName, areaMapping.getBdAreaCode()));
            if(null == areaMappingDO || StringUtil.isEmpty(areaMappingDO.getId())){
                areaMappingDao.insert(areaMapping);
            }else{
                areaMapping.setId(areaMappingDO.getId());
                areaMappingDao.updateById(areaMapping);
            }
        }
        return true;
    }
    private BaseDeviceDO initBaseDeviceDOData(AreaMappingCameraSaveReqVO.ChanInfo chanInfo,BaseDeviceDO entity,String orgCode,String bdAreaId){
        if(entity == null) entity = new BaseDeviceDO();
        entity.setGbCode(chanInfo.getGb28181Code());
        entity.setDeviceName(chanInfo.getChanName());
        entity.setChannelId(chanInfo.getChanId());
        entity.setChannelName(chanInfo.getChanName());
        entity.setDeviceStatus(chanInfo.getStatus().equals("0") ? "001" : "002");
        entity.setDeviceTypeId("0001");
        entity.setPointName(chanInfo.getChanName());
        entity.setOrgCode(orgCode);
        entity.setAreaId(bdAreaId);
        entity.setRoomId(bdAreaId);
        entity.setIpAddress(chanInfo.getIp());
        entity.setIsEnabled(1);
        entity.setDeviceCode(chanInfo.getTreeCode());
        entity.setIsDel(false);
        entity.setDataSources("4");
        return entity;
    }
    private BaseDeviceCameraDO initBaseDeviceCameraDOData(AreaMappingCameraSaveReqVO.ChanInfo chanInfo,BaseDeviceCameraDO entity,String orgCode,String deviceId){
        if(entity == null) entity = new BaseDeviceCameraDO();
        entity.setDeviceId(deviceId);
        entity.setChannelId(chanInfo.getChanId());
        entity.setDeviceIp(chanInfo.getIp());
        entity.setChannelName(chanInfo.getChanName());
        entity.setOrgCode(orgCode);
        entity.setIsVideo(1);
        entity.setDataSources("04");
        entity.setIsDel(false);
        return entity;
    }
    @Override
    public boolean batchSaveAreaMappingCamera(List<AreaMappingCameraSaveReqVO> saveReqVOList) {
        for (AreaMappingCameraSaveReqVO saveReqVO: saveReqVOList) {
            saveAreaMappingCamera(saveReqVO);
        }
        return true;
    }


    /**
     * 删除映射关系
     * @param bdAreaCode 实战平台区域code
     * @param spAreaCode 视频联网摄像头区域code
     * @param isDeleteDevice 是否同步删除设备
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAreaMappingCamera(String bdAreaCode, String spAreaCode, Boolean isDeleteDevice) {
        if(isDeleteDevice == null ) isDeleteDevice = false;
        //根据通道ID查询 是否还有绑定数据
        List<AreaMappingDO> list = areaMappingDao.selectList(new LambdaQueryWrapper<AreaMappingDO>()
                //.eq(AreaMappingDO::getSpAreaCode, bdAreaCode)
                .eq(AreaMappingDO::getTypes, "01")
                .eq(AreaMappingDO::getDataTypes, "camera")
                .eq(AreaMappingDO::getBdAreaName, spAreaCode));
        boolean hasOtherRelation= false;
        StringBuffer stringBuffer = new StringBuffer();
        if(CollectionUtil.isNotNull(list)){
            for (AreaMappingDO areaMappingDO : list) {
                if(!bdAreaCode.equals(areaMappingDO.getBdAreaName())){
                    hasOtherRelation = true;
                    if(stringBuffer.length() > 0) stringBuffer.append(",");
                    stringBuffer.append(areaMappingDO.getBdAreaName());
                }
            }
        }
        areaMappingDao.deleteByBdSpAreaCode(bdAreaCode,spAreaCode);
        if(isDeleteDevice ){
            if(hasOtherRelation)
            throw new ServerException("当前视频联网设备下有绑定到其他区域[" + stringBuffer + "]，请先解除绑定关系");
            //删除设备
            baseDeviceDao.delete(new LambdaQueryWrapper<BaseDeviceDO>().eq(BaseDeviceDO::getChannelId, spAreaCode));
            //删除设备通道
            baseDeviceCameraDao.delete(new LambdaQueryWrapper<BaseDeviceCameraDO>().eq(BaseDeviceCameraDO::getChannelId, spAreaCode));
        }
        return true;
    }
}
