package com.rs.module.base.service.pm.device;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenDO;
import com.rs.module.acp.service.pm.BaseDeviceCameraService;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceSaveReqVO;
import com.rs.module.base.controller.admin.video.vo.VideoTreeVO;
import com.rs.module.base.dao.pm.device.BaseDeviceDao;
import com.rs.module.base.dao.pm.device.SplwTreeDao;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;
import com.rs.module.base.entity.pm.device.SplwTreeDO;
import com.rs.module.base.service.video.VideoRouteService;
import com.rs.module.base.util.SplwTreeUtil;
import com.rs.module.base.vo.TreeNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;


/**
 * 实战平台-监管管理-设备信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class SplwTreeServiceImpl extends BaseServiceImpl<SplwTreeDao, SplwTreeDO> implements SplwTreeService {
    @Resource
    private SplwTreeDao splwTreeDao;
    @Resource
    private BaseDeviceService baseDeviceService;
    @Resource
    private BaseDeviceDao baseDeviceDao;
    @Resource
    private BaseDeviceCameraService baseDeviceCameraService;
    @Value("${sply.treeCode:}")
    public String rootCode;
    @Autowired
    private VideoRouteService videoRouteService;
    @Override
    public JSONObject sysSplwData(String orgCode) throws Exception{
        log.info("从视频联网对应区域名字及设备>>>>>>>>>>>>>>>开始");
        JSONObject result = new JSONObject();
        try {
            // 清空原有数据
            splwTreeDao.clearTable(orgCode);

            // 先创建根节点（如果不存在）
            createRootNodeIfNotExists(orgCode);

            JSONObject rootTree = videoRouteService.getSplwAreaByTreeCode(rootCode.equals("001") ? "" : "001");
            JSONArray rootNodes = Optional.ofNullable(rootTree)
                    .map(j -> j.getJSONArray("treeNodeList"))
                    .filter(arr -> arr.size() > 0)
                    .orElseThrow(() -> new ServerException("根节点数据为空"));
            log.info("获取根节点信息{}", rootNodes);

            // 获取当前最大ID作为起始ID
            Integer maxId = splwTreeDao.getMaxCodeAll();
            if (maxId == null) {
                maxId = 1;
            }

            // 递归处理完整的树结构，从根节点开始
            processAndSaveTreeStructure(rootNodes, orgCode, 1, "/1", maxId, new HashSet<>());

        } catch (Exception e) {
            log.error("同步视频树失败，orgCode: {}, areaName: {}", orgCode, e);
            throw e;
        }
        log.info("从视频联网对应区域名字及设备>>>>>>>>>>>>>>>结束");
        return result;
    }

    /**
     * 递归处理并保存树结构
     * @param treeNodeList 节点列表
     * @param orgCode 监狱编码
     * @param parentId 父节点ID
     * @param parentPath 父节点路径
     * @param currentId 当前ID计数器（引用传递）
     * @param processedTreeCodes 已处理的treeCode集合
     */
    private Integer processAndSaveTreeStructure(JSONArray treeNodeList, String orgCode, Integer parentId,
                                                String parentPath, Integer currentId, Set<String> processedTreeCodes) throws Exception {
        if (treeNodeList == null || treeNodeList.isEmpty()) {
            return currentId;
        }

        // 处理当前层级的所有节点
        for (int i = 0; i < treeNodeList.size(); i++) {
            JSONObject node = treeNodeList.getJSONObject(i);
            String treeCode = node.getString("treeCode");

            // 避免重复处理
            if (processedTreeCodes.contains(treeCode)) {
                continue;
            }

            // 检查数据库中是否已存在该节点
            Integer checkNum = splwTreeDao.getCheckNum(node.getString("name"), treeCode);
            if (checkNum != null && checkNum > 0) {
                continue;
            }

            // 更新当前ID
            currentId++;
            processedTreeCodes.add(treeCode);

            // 创建节点对象
            SplwTreeDO treeDO = new SplwTreeDO();
            treeDO.setId(currentId);
            treeDO.setOrderNo(currentId);
            treeDO.setTreeCode(treeCode);
            treeDO.setName(node.getString("name"));
            treeDO.setType(node.getInteger("type"));
            treeDO.setPlatformId(node.getInteger("platformId"));
            treeDO.setGb28181Code(node.getString("gb28181Code"));
            treeDO.setIsLocked(node.getInteger("isLocked"));
            treeDO.setTotalNum(node.getInteger("totalNum"));
            treeDO.setOnlineNum(node.getInteger("onlineNum"));
            treeDO.setAllNum(node.getInteger("allNum"));
            treeDO.setPrisonId(orgCode);

            // 计算层级
            int treeLevel = calculateTreeLevel(treeCode);
            treeDO.setTreeLevel(treeLevel);

            // 设置父节点信息
            treeDO.setPid(parentId);
            treeDO.setParentPathId(parentPath);
            treeDO.setPathName(buildPathName(treeCode, node.getString("name"), parentPath));

            // 保存到数据库
            splwTreeDao.insert(treeDO);

            // 递归获取并处理子节点数据
            JSONObject childTree = videoRouteService.getSplwAreaByTreeCode(treeCode);
            JSONArray childTreeNodeList = childTree != null ? childTree.getJSONArray("treeNodeList") : null;

            // 如果有子节点，递归处理
            if (childTreeNodeList != null && !childTreeNodeList.isEmpty()) {
                String childParentPath = parentPath + "/" + currentId;
                currentId = processAndSaveTreeStructure(childTreeNodeList, orgCode, currentId, childParentPath, currentId, processedTreeCodes);
            }
        }

        return currentId;
    }

    /**
     * 构建路径名称
     * @param treeCode 节点treeCode
     * @param nodeName 节点名称
     * @param parentPath 父节点路径
     * @return 完整路径名称
     */
    private String buildPathName(String treeCode, String nodeName, String parentPath) {
        if ("/1".equals(parentPath)) {
            // 根节点下的第一级节点
            return "根节点/" + nodeName;
        } else {
            // 获取父节点名称
            String[] pathIds = parentPath.split("/");
            if (pathIds.length > 1) {
                // 构造路径名称
                StringBuilder pathName = new StringBuilder();
                pathName.append("根节点");

                // 从路径ID中获取每个节点的名称
                for (int i = 1; i < pathIds.length; i++) {
                    Integer nodeId = Integer.valueOf(pathIds[i]);
                    // 这里可以查询数据库获取节点名称，但为了简化处理，我们只构建ID路径
                    // 在实际应用中，如果需要完整路径名称，可以从数据库查询
                    // 这里我们只构建到当前节点的路径
                }
                pathName.append("/").append(nodeName);
                return pathName.toString();
            }
        }
        return nodeName;
    }

    /**
     * 如果根节点不存在则创建
     * @param orgCode 监狱编码
     */
    private void createRootNodeIfNotExists(String orgCode) {
        List<String> hasCode = splwTreeDao.isHasCode();
        if (hasCode.isEmpty()) {
            SplwTreeDO rootDO = new SplwTreeDO();
            rootDO.setId(1);
            rootDO.setOrderNo(1);
            rootDO.setName("根节点");
            rootDO.setType(0);
            rootDO.setTreeCode("001");
            rootDO.setTreeLevel(0);
            rootDO.setPid(-1);
            rootDO.setParentPathId("/");
            rootDO.setPathName("根节点");
            rootDO.setPrisonId(orgCode);
            splwTreeDao.insert(rootDO);
        }
    }

    /**
     * 计算treeCode的层级
     * @param treeCode 树编码
     * @return 层级
     */
    private int calculateTreeLevel(String treeCode) {
        if (treeCode == null || treeCode.length() < 3) {
            return 0;
        }
        // 每3位为一级
        return treeCode.length() / 3;
    }






    private Integer handleRootNode(JSONObject rootNode, String orgCode) {
        List<String> hasCode = splwTreeDao.isHasCode();
        Integer id;

        if (hasCode.isEmpty()) {
            id = 2; // 初始ID为2

            SplwTreeDO baseSplwTreeEntity = new SplwTreeDO();
            baseSplwTreeEntity.setId(1); // 根节点ID为1
            baseSplwTreeEntity.setOrderNo(1);
            baseSplwTreeEntity.setTreeCode(rootNode.getString("treeCode"));
            baseSplwTreeEntity.setType(1);
            baseSplwTreeEntity.setTreeLevel(0);
            baseSplwTreeEntity.setPid(-1);
            baseSplwTreeEntity.setPrisonId(orgCode);
            baseSplwTreeEntity.setParentPathId("1");
            baseSplwTreeEntity.setName(rootNode.getString("name"));
            baseSplwTreeEntity.setAllNum(rootNode.getIntValue("allNum"));
            baseSplwTreeEntity.setIsLocked(rootNode.getIntValue("isLocked"));
            baseSplwTreeEntity.setOnlineNum(rootNode.getIntValue("onlineNum"));
            baseSplwTreeEntity.setPlatformId(rootNode.getIntValue("platformId"));
            baseSplwTreeEntity.setTotalNum(rootNode.getIntValue("totalNum"));
            baseSplwTreeEntity.setGb28181Code(rootNode.getString("gb28181Code"));
            splwTreeDao.insert(baseSplwTreeEntity);
        } else {
            Integer maxCode = splwTreeDao.getMaxCodeAll();
            id = maxCode + 1;
        }

        return id;
    }
    private void processAreaTree(List<TreeNode> areaTree, String rootCode, String orgCode, Integer initialId) throws Exception {
        log.info("处理区域树 processAreaTree param{},{}", areaTree, rootCode);
        for (TreeNode treeNode : areaTree) {
            if (orgCode.equals(treeNode.getOrgCode())) {
                JSONObject splwTree = videoRouteService.getSplwAreaByTreeCode("001001");
                JSONArray treeNodeList = Optional.ofNullable(splwTree)
                        .map(j -> j.getJSONArray("treeNodeList"))
                        .orElseThrow(() -> new ServerException("平台父节点数据为空"));
                log.info("=================>处理区域树 processAreaTree param{},{}", treeNodeList, treeNodeList.size());
                for (Object obj : treeNodeList) {
                    JSONObject node = (JSONObject) obj;

                    // 处理区域节点
                    Integer currentId = processAreaNode(node, rootCode, orgCode, initialId);

                    // 获取通道信息列表
                    JSONObject areaByTreeCode = videoRouteService.getSplwAreaByTreeCode(node.getString("treeCode"));
                    JSONArray chanInfoList = Optional.ofNullable(areaByTreeCode)
                            .map(j -> j.getJSONArray("chanInfoList"))
                            .orElse(new JSONArray());

                    log.info("=================>开始循环同步设备，共 {} 个通道", chanInfoList.size());

                    // 批量保存通道设备信息
                    batchSaveChannelDevices(chanInfoList, treeNode, orgCode, currentId, node.getString("treeCode"));

                    // 递归处理子节点
                    getChildren2(treeNode.getChildren(), currentId, node.getString("treeCode"));

                }
            }
        }
    }
    public List<TreeNode> getChildren2(List<TreeNode> children, Integer id, String treeCode) throws Exception {
        if (CollectionUtil.isEmpty(children)) {
            return Collections.emptyList();
        }

        String orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        String rootCode = videoRouteService.getRootCode();

        List<SplwTreeDO> splwTreeList = new ArrayList<>();
        List<BaseDeviceSaveReqVO> deviceList = new ArrayList<>();

        for (TreeNode treeNode : children) {
            if (treeNode != null && "area".equals(treeNode.getType())) {
                JSONObject splwTree = videoRouteService.getSplwAreaByTreeCode(treeCode);
                JSONArray treeNodeList = splwTree.getJSONArray("treeNodeList");

                if (treeNodeList == null || treeNodeList.isEmpty()) {
                    continue;
                }

                for (int i = 0; i < treeNodeList.size(); i++) {
                    JSONObject treeJsonObject = treeNodeList.getJSONObject(i);
                    String code = treeJsonObject.getString("treeCode");
                    // 检查是否需要处理
                    Integer checkNum = splwTreeDao.getCheckNum(treeJsonObject.getString("name"), treeJsonObject.getString("treeCode"));
                    if (StringUtil.isEmpty(treeNode.getAreaName()) || !treeNode.getAreaName().equals(treeJsonObject.getString("name")) || checkNum >= 1) {
                        continue;
                    }

                    id = splwTreeDao.getMaxCode(orgCode) + 1;
                    JSONObject chanTree = videoRouteService.getSplwAreaByTreeCode(treeJsonObject.getString("treeCode"));
                    JSONArray chanInfoList = chanTree.getJSONArray("chanInfoList");

                    // 创建SplwTreeDO对象
                    SplwTreeDO splwTreeDO = buildSplwTreeDO(treeJsonObject, id, rootCode, orgCode);
                    //splwTreeList.add(splwTreeDO);
                    splwTreeDao.insert(splwTreeDO);
                    // 处理通道信息
                    if (!chanInfoList.isEmpty()) {
                        for (int j = 0; j < chanInfoList.size(); j++) {
                            JSONObject chanInfoJsonObject = chanInfoList.getJSONObject(j);

                            // 更新设备状态
                            String status = chanInfoJsonObject.get("status").toString().equals("0") ? "001" : "002";
                            String chanId = chanInfoJsonObject.get("chanId").toString();
                            baseDeviceDao.updateDeviceByChanId(chanId, status, chanInfoJsonObject.get("gb28181Code").toString());
                            baseDeviceCameraService.updateChanDeviceStatus(chanId, Integer.valueOf(chanInfoJsonObject.get("facade").toString()));

                            // 创建设备信息
                            BaseDeviceSaveReqVO baseDeviceDto = createBaseDeviceSaveReqVO(chanInfoJsonObject, treeNode, orgCode);
                            deviceList.add(baseDeviceDto);

                            // 清空之前设备再新增
                            clearDeviceTable(chanInfoJsonObject.get("chanId").toString(), orgCode);
                            baseDeviceCameraService.clearDeviceCameraTable(chanInfoJsonObject.get("chanId").toString(), orgCode);
                        }
                    }
                    if (CollectionUtil.isNotEmpty(treeNode.getChildren())) {
                        getChildren2(treeNode.getChildren(), id, code);
                    }
                }
            }
        }

        // 批量插入SplwTree数据
        if (!splwTreeList.isEmpty()) {
            //splwTreeDao.insertBatch(splwTreeList);
        }

        // 批量新增设备
        for (BaseDeviceSaveReqVO device : deviceList) {
            baseDeviceService.addDevice(device);
        }
        return children;
    }
    private SplwTreeDO buildSplwTreeDO(JSONObject jsonObject, Integer id, String rootCode, String orgCode) {
        SplwTreeDO splwTreeDO = new SplwTreeDO();
        splwTreeDO.setId(id);
        splwTreeDO.setOrderNo(id);
        splwTreeDO.setTreeCode(jsonObject.getString("treeCode"));
        splwTreeDO.setAllNum(Integer.valueOf(jsonObject.get("allNum").toString()));
        splwTreeDO.setGb28181Code(jsonObject.get("gb28181Code") != null ? jsonObject.get("gb28181Code").toString() : "");
        splwTreeDO.setIsLocked(Integer.valueOf(jsonObject.get("isLocked").toString()));
        splwTreeDO.setOnlineNum(Integer.valueOf(jsonObject.get("onlineNum").toString()));
        splwTreeDO.setPlatformId(Integer.valueOf(jsonObject.get("platformId").toString()));
        splwTreeDO.setTotalNum(Integer.valueOf(jsonObject.get("totalNum").toString()));
        splwTreeDO.setType(1);
        splwTreeDO.setPrisonId(orgCode);

        // 获取路径信息
        Map<String, String> map1 = new HashMap<>();
        if ("001".equals(rootCode)) {
            Integer level = videoRouteService.getTreeLevel(jsonObject.getString("treeCode").toString(), 1);
            map1 = videoRouteService.getTreePathIds(jsonObject.getString("treeCode").toString(), level);
        } else {
            Integer level = videoRouteService.getTreeLevel(jsonObject.getString("treeCode").toString(), 3);
            map1 = videoRouteService.getTreePathIds3(jsonObject.getString("treeCode").toString(), level);
            if (map1 == null) {
                return null;
            }
        }

        splwTreeDO.setTreeLevel(videoRouteService.getTreeLevel(jsonObject.getString("treeCode").toString(), 1));
        splwTreeDO.setParentPathId(map1.get("pathId"));
        splwTreeDO.setPathName(map1.get("pathName"));
        splwTreeDO.setPid(Integer.valueOf(map1.get("pathId").substring(map1.get("pathId").lastIndexOf("/") + 1)));
        splwTreeDO.setName(jsonObject.getString("name"));

        return splwTreeDO;
    }
    private Integer processAreaNode(JSONObject node, String rootCode, String orgCode, Integer initialId) {
        // 获取路径信息
        Map<String, String> pathInfo = getTreePathInfo(node, rootCode);
        if (pathInfo == null) {
            return initialId;
        }
        Integer checkNum = splwTreeDao.getCheckNum(node.getString("name"), node.getString("treeCode"));
        // if(checkNum == 0){
        // 构建SplwTreeDO对象
        SplwTreeDO treeDO = SplwTreeUtil.buildSplwTreeDO(node, initialId, pathInfo, orgCode);

        // 插入数据库
        splwTreeDao.insert(treeDO);
        //  }
        return initialId + 1;
    }
    /**
     * 获取树路径信息
     */
    private Map<String, String> getTreePathInfo(JSONObject node, String rootCode) {
        String treeCode = node.getString("treeCode");
        Map<String, String> map = new HashMap<>();
        Integer level = 1;
        if ("001".equals(rootCode)) {
            level = videoRouteService.getTreeLevel(treeCode, 1);
            map = videoRouteService.getTreePathIds(treeCode, level);
        } else {
            level = videoRouteService.getTreeLevel(treeCode, 3);
            map = videoRouteService.getTreePathIds3(treeCode, level);
        }

        if (map == null) {
            log.warn("获取树路径信息失败，treeCode: {}", treeCode);
            return null;
        }

        map.put("level", level.toString());
        return map;
    }
    /**
     * 批量保存通道设备信息
     */
    private void batchSaveChannelDevices(JSONArray chanInfoList, TreeNode treeNode, String orgCode, Integer currentId, String code) {
        if (chanInfoList == null || chanInfoList.isEmpty()) {
            return;
        }

        List<SplwTreeDO> splwTreeList = new ArrayList<>();
        List<BaseDeviceSaveReqVO> deviceList = new ArrayList<>();

        for (int i = 0; i < chanInfoList.size(); i++) {
            JSONObject item = chanInfoList.getJSONObject(i);

            // 更新设备状态
            updateDeviceStatus(item, orgCode);
            Map<String, String> pathInfo = getPathInfoForChan(item, code, rootCode);
            // 创建SplwTreeDO对象
            SplwTreeDO splwTreeDO = SplwTreeUtil.createSplwTreeDO(rootCode,item, currentId, code, treeNode, orgCode,pathInfo);
            if (splwTreeDO != null) {
                splwTreeList.add(splwTreeDO);
                splwTreeDao.insert(splwTreeDO);
                // 创建设备信息
                BaseDeviceSaveReqVO deviceDto = createBaseDeviceSaveReqVO(item, treeNode, orgCode);
                deviceList.add(deviceDto);

                // 清空旧数据
                clearOldData(item, orgCode);

                currentId = splwTreeDao.getMaxCode(orgCode) + 1;
            }
        }

        // 批量插入SplwTree数据
        if (!splwTreeList.isEmpty()) {
            //splwTreeDao.insertBatch(splwTreeList);
        }

        // 批量新增设备
        for (BaseDeviceSaveReqVO device : deviceList) {
            baseDeviceService.addDevice(device);
        }
    }
    public Integer clearDeviceTable(String channelId, String orgCode) {
        LambdaQueryWrapper<BaseDeviceDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseDeviceDO::getChannelId, channelId)
                .eq(BaseDeviceDO::getOrgCode, orgCode);

        return baseDeviceDao.delete(wrapper);

    }
    /**
     * 清除旧数据
     */
    private void clearOldData(JSONObject item, String orgCode) {
        String chanId = item.getString("chanId");
        clearDeviceTable(chanId, orgCode);
        baseDeviceCameraService.clearDeviceCameraTable(chanId, orgCode);
    }
    /**
     * 创建BaseDeviceSaveReqVO对象
     */
    private BaseDeviceSaveReqVO createBaseDeviceSaveReqVO(JSONObject item, TreeNode treeNode, String orgCode) {
        BaseDeviceSaveReqVO baseDeviceDto = new BaseDeviceSaveReqVO();
        String bdAreaCode = splwTreeDao.getBDAreaCode(treeNode.getId());
        baseDeviceDto.setAreaId(StringUtil.isNotEmpty(bdAreaCode) ? bdAreaCode : treeNode.getId());
        baseDeviceDto.setGbCode(item.getString("gb28181Code"));
        baseDeviceDto.setDeviceName(item.getString("chanName"));
        baseDeviceDto.setChannelId(item.getString("chanId"));
        baseDeviceDto.setChannelName(item.getString("chanName"));
        baseDeviceDto.setDeviceStatus(item.getString("status").equals("0") ? "001" : "002");
        baseDeviceDto.setDeviceTypeId("0001");
        baseDeviceDto.setPointName(item.getString("chanName"));
        baseDeviceDto.setOrgCode(orgCode);
        baseDeviceDto.setDeviceInscreen(new BaseDeviceInscreenDO());
        return baseDeviceDto;
    }
    /**
     * 更新设备状态
     */
    private void updateDeviceStatus(JSONObject item, String orgCode) {
        String chanId = item.getString("chanId");
        String status = item.getString("status").equals("0") ? "001" : "002";
        String gb28181Code = item.getString("gb28181Code");

        baseDeviceDao.updateDeviceByChanId(chanId, status, gb28181Code);
        baseDeviceCameraService.updateChanDeviceStatus(chanId, Integer.valueOf(item.getString("facade")));
    }

    /**
     * 获取通道项的路径信息
     */
    private Map<String, String> getPathInfoForChan(JSONObject item, String code, String rootCode) {
        String treeCode = item.getString("treeCode");
        Map<String, String> stringMap;
        Integer level;
        if ("001".equals(rootCode)) {
            level = videoRouteService.getTreeLevel(treeCode, 2);
            stringMap = videoRouteService.getTreePathIds(treeCode, level);
        } else {
            level = videoRouteService.getTreeLevel(treeCode, 1);
            stringMap = videoRouteService.getTreePathIds3(treeCode, level);
        }

        if (stringMap == null) {
            log.warn("获取树路径信息失败，treeCode: {}", treeCode);
            return null;
        }
        stringMap.put("level", level.toString());
        return stringMap;
    }

}
