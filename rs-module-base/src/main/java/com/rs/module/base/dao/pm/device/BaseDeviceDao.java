package com.rs.module.base.dao.pm.device;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceListReqVO;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDevicePageReqVO;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceTreeReqVO;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;
import com.rs.module.base.vo.TreeNode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* 实战平台-监管管理-设备信息 Dao
*
* <AUTHOR>
*/
@Mapper
public interface BaseDeviceDao extends IBaseDao<BaseDeviceDO> {


    default PageResult<BaseDeviceDO> selectPage(BaseDevicePageReqVO reqVO) {
        Page<BaseDeviceDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<BaseDeviceDO> wrapper = new LambdaQueryWrapperX<BaseDeviceDO>()
            .eqIfPresent(BaseDeviceDO::getDeviceCode, reqVO.getDeviceCode())
            .likeIfPresent(BaseDeviceDO::getDeviceName, reqVO.getDeviceName())
            .eqIfPresent(BaseDeviceDO::getDeviceTypeId, reqVO.getDeviceTypeId())
            .eqIfPresent(BaseDeviceDO::getFactory, reqVO.getFactory())
            .eqIfPresent(BaseDeviceDO::getModel, reqVO.getModel())
            .eqIfPresent(BaseDeviceDO::getProtocol, reqVO.getProtocol())
            .eqIfPresent(BaseDeviceDO::getIpAddress, reqVO.getIpAddress())
            .eqIfPresent(BaseDeviceDO::getPort, reqVO.getPort())
            .likeIfPresent(BaseDeviceDO::getDevUserName, reqVO.getDevUserName())
            .eqIfPresent(BaseDeviceDO::getDevPassword, reqVO.getDevPassword())
            .likeIfPresent(BaseDeviceDO::getPointName, reqVO.getPointName())
            .eqIfPresent(BaseDeviceDO::getChannelId, reqVO.getChannelId())
            .likeIfPresent(BaseDeviceDO::getChannelName, reqVO.getChannelName())
            .eqIfPresent(BaseDeviceDO::getGbCode, reqVO.getGbCode())
            .eqIfPresent(BaseDeviceDO::getMacAddress, reqVO.getMacAddress())
            .betweenIfPresent(BaseDeviceDO::getOnlineTime, reqVO.getOnlineTime())
            .eqIfPresent(BaseDeviceDO::getAreaId, reqVO.getAreaId())
            .eqIfPresent(BaseDeviceDO::getRoomId, reqVO.getRoomId())
            .eqIfPresent(BaseDeviceDO::getIsEnabled, reqVO.getIsEnabled())
            .eqIfPresent(BaseDeviceDO::getDeviceStatus, reqVO.getDeviceStatus())
;
        if(reqVO.getOrderFields() != null) {
           page.setOrders(reqVO.getOrderFields());
        }else {
           wrapper.orderByDesc(BaseDeviceDO::getAddTime);
        }
        Page<BaseDeviceDO> baseDevicePage = selectPage(page, wrapper);
        return new PageResult<>(baseDevicePage.getRecords(), baseDevicePage.getTotal());
    }
    default List<BaseDeviceDO> selectList(BaseDeviceListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BaseDeviceDO>()
            .eqIfPresent(BaseDeviceDO::getDeviceCode, reqVO.getDeviceCode())
            .likeIfPresent(BaseDeviceDO::getDeviceName, reqVO.getDeviceName())
            .eqIfPresent(BaseDeviceDO::getDeviceTypeId, reqVO.getDeviceTypeId())
            .eqIfPresent(BaseDeviceDO::getFactory, reqVO.getFactory())
            .eqIfPresent(BaseDeviceDO::getModel, reqVO.getModel())
            .eqIfPresent(BaseDeviceDO::getProtocol, reqVO.getProtocol())
            .eqIfPresent(BaseDeviceDO::getIpAddress, reqVO.getIpAddress())
            .eqIfPresent(BaseDeviceDO::getPort, reqVO.getPort())
            .likeIfPresent(BaseDeviceDO::getDevUserName, reqVO.getDevUserName())
            .eqIfPresent(BaseDeviceDO::getDevPassword, reqVO.getDevPassword())
            .likeIfPresent(BaseDeviceDO::getPointName, reqVO.getPointName())
            .eqIfPresent(BaseDeviceDO::getChannelId, reqVO.getChannelId())
            .likeIfPresent(BaseDeviceDO::getChannelName, reqVO.getChannelName())
            .eqIfPresent(BaseDeviceDO::getGbCode, reqVO.getGbCode())
            .eqIfPresent(BaseDeviceDO::getMacAddress, reqVO.getMacAddress())
            .betweenIfPresent(BaseDeviceDO::getOnlineTime, reqVO.getOnlineTime())
            .eqIfPresent(BaseDeviceDO::getAreaId, reqVO.getAreaId())
            .eqIfPresent(BaseDeviceDO::getRoomId, reqVO.getRoomId())
            .eqIfPresent(BaseDeviceDO::getIsEnabled, reqVO.getIsEnabled())
            .eqIfPresent(BaseDeviceDO::getDeviceStatus, reqVO.getDeviceStatus())
        .orderByDesc(BaseDeviceDO::getAddTime));    }

    /**
     * 楼层分布的设备设施
     * @param areaList 节点列表
     * @return
     */
    //List<BaseDeviceByAreaVO> getDeviceByArea(@Param("areaList") List<BaseAreaVO> areaList);

    /**
     * 设备列表
     * @param areaList 节点列表
     * @return
     */
    //List<BaseDeviceVO> getList(@Param("areaList") List<BaseAreaVO> areaList);

    /**
     * 区域树
     * @param treeDto
     * @return
     */
    List<TreeNode> areaTree(@Param("form") BaseDevicePageReqVO treeDto);

    TreeNode getParentNodeById(@Param("id") String id);

    String getParentIdById(@Param(value = "id") String id);

    /**
     * 设备树
     * @param treeDto
     * @return
     */
    List<TreeNode> deviceTree(@Param("form") BaseDevicePageReqVO treeDto);

    /**
     * 会见室审讯室设备树
     * @param treeDto
     * @return
     */
    List<TreeNode> meetRoomDeviceTree(@Param("form") BaseDevicePageReqVO treeDto);

    String getRoomName(@Param("roomId") String roomId);

    String getMaxIdByType(String str);

    List<TreeNode> getAllDevices(@Param("form") BaseDeviceTreeReqVO treeReqVO);

    List<TreeNode> getElectronicAllDevices(@Param("form") BaseDevicePageReqVO treeDto);

    List<TreeNode> getParentAreaId(@Param(value = "id") String id);

    List<Map<String,Object>> getFlowList(@Param(value = "type") String type, @Param(value = "prisonId") String prisonId);

    Integer updateDeviceStatusByChanId(@Param(value = "chanId") String chanId,@Param(value = "status") String status);
    Integer updateDeviceGbCodeByChanId(@Param(value = "chanId") String chanId,@Param(value = "code") String code);
    Integer updateDeviceByChanId(@Param(value = "chanId") String chanId,@Param(value = "status") String status,@Param(value = "code") String code);

}
