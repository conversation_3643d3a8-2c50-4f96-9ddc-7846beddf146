package com.rs.module.base.service.pm;

import cn.hutool.core.lang.tree.Tree;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.base.controller.admin.pm.vo.AreaMappingCameraSaveReqVO;
import com.rs.module.base.controller.admin.pm.vo.AreaMappingSaveReqVO;
import com.rs.module.base.entity.pm.AreaMappingDO;

import javax.validation.*;
import java.util.List;

/**
 * 实战平台-监管管理-区域映射 Service 接口
 *
 * <AUTHOR>
 */
public interface AreaMappingService extends IBaseService<AreaMappingDO> {

    /**
     * 创建实战平台-监管管理-区域映射
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createAreaMapping(@Valid AreaMappingSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-区域映射
     *
     * @param updateReqVO 更新信息
     */
    void updateAreaMapping(@Valid AreaMappingSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-区域映射
     *
     * @param id 编号
     */
    void deleteAreaMapping(String id);

    /**
     * 获得实战平台-监管管理-区域映射
     *
     * @param id 编号
     * @return 实战平台-监管管理-区域映射
     */
    AreaMappingDO getAreaMapping(String id);


    //根据bdAreaCode查询 映射spAreaCode
    String getSpAreaCodeByBdAreaCode(String bdAreaCode);

    String getBdAreaCodeBySpAreaCode(String spAreaCode);

    List<Tree<String>> getAreaTree(String orgCode, String areaName);

    /*保存映射关系 映射数据类型 area 区域映射,camera 摄像机
        dataTypes = area 区域映射
        保存映射关系即可
        dataTypes = camera 摄像机
        保存映射关系 一个区域可关联多个摄像机 摄像机标识为通道ID

        判断设备是否存在
            如不存在 写入设备表，设备摄像机表
            如存在 更新设备表、设备摄像机表

        * */
    boolean saveAreaMappingCamera(AreaMappingCameraSaveReqVO saveReqVO);

    boolean batchSaveAreaMappingCamera(List<AreaMappingCameraSaveReqVO> saveReqVOList);

    boolean deleteAreaMappingCamera(String bdAreaCode, String spAreaCode, Boolean isDeleteDevice) throws Exception;
}
