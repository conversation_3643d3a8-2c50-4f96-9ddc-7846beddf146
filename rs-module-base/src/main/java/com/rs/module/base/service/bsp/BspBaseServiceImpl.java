package com.rs.module.base.service.bsp;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BspApi;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.Nullable;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @ClassName BspBaseService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/8/12 17:31
 * @Version 1.0
 */
@Log4j2
public abstract class BspBaseServiceImpl<DAO extends IBaseDao<DO>, DO> extends BaseServiceImpl<DAO, DO> implements BspBaseService {
    @Resource
    public BspApi bspApi;

    @Override
    public String getSerialNumber(Map<String, Object> formData) {
        JSONObject formDataTemp = getEntries(formData);
        try {
            if (formDataTemp != null) {
                return bspApi.executeByRuleCode(getRuleCode(), formDataTemp.toJSONString(4));
            }
            return bspApi.executeByRuleCode(getRuleCode(), null);
        } catch (Exception e) {
            log.error("获取序列号失败", e);
            return System.currentTimeMillis() + "";
        }
    }

    @Nullable
    private static JSONObject getEntries(Map<String, Object> formData) {
        JSONObject formDataTemp = null;
        if (formData != null && formData.get("orgCode") != null) {
            formDataTemp = JSONUtil.createObj().set("orgCode", formData.get("orgCode").toString());
        } else {
            try {
                formDataTemp = JSONUtil.createObj().set("orgCode", SessionUserUtil.getSessionUser().getOrgCode());
            } catch (Exception e) {

            }
        }
        return formDataTemp;
    }

    @Override
    public String getSerialNumberByRuleCode(Map<String, Object> formData, String ruleCode) {
        JSONObject formDataTemp = getEntries(formData);
        try {
            if (formDataTemp != null) {
                return bspApi.executeByRuleCode(ruleCode, formDataTemp.toJSONString(4));
            }
            return bspApi.executeByRuleCode(ruleCode, null);
        } catch (Exception e) {
            log.error("获取序列号失败", e);
            return System.currentTimeMillis() + "";
        }
    }

    public abstract String getRuleCode();
}
