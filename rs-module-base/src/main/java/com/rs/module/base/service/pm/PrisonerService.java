package com.rs.module.base.service.pm;

import cn.hutool.core.collection.CollUtil;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.base.controller.admin.pm.vo.ForeignPersonnelListRespVO;
import com.rs.module.base.controller.admin.pm.vo.PoreginPersonnelPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerListVwRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.entity.pm.PrisonerListDO;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;

import java.util.Collection;
import java.util.List;

/**
 * 在所人员 Service 接口
 *
 * <AUTHOR>
 */
public interface PrisonerService {

    /**
     * 获得VIEW分页
     *
     * @param pageReqVO 分页查询
     * @return VIEW分页
     */
    PageResult<PrisonerListVwRespVO> getPrisonerSelectCompomentList(PrisonerVwPageReqVO pageReqVO);

    PrisonerVwRespVO getPrisonerOne(String orgCode, String jgrybm, PrisonerQueryRyztEnum ryzt);

    PrisonerVwRespVO getPrisonerByJgrybm(String jgrybm);

    PrisonerVwRespVO getPrisonerByZjhm(String orgCode, String zjhm);

    default PrisonerVwRespVO getPrisonerSelectCompomenOne(String jgrybm, PrisonerQueryRyztEnum ryzt) {
        return getPrisonerOne(null, jgrybm, ryzt);
    }

    /**
     * 查询在所人员,多个有逗号隔开
     *
     * @param jgrybms
     * @return
     */
    default PrisonerInDO getPrisonerInOne(String jgrybms) {
        List<PrisonerInDO> prisonerInOneList = getPrisonerInOneList(CollUtil.toList(jgrybms));
        return prisonerInOneList.size() > 0 ? prisonerInOneList.get(0) : null;
    }

    List<PrisonerInDO> getPrisonerInOneList(Collection<String> jgrybms);

    /**
     * 查询在所人员
     *
     * @param orgCode  机构代码
     * @param roomCode 监室代码
     * @return
     */
    List<PrisonerInDO> getPrisonerInList(String orgCode, String roomCode);

    /**
     * 查询人员
     *
     * @param jgrybm
     * @param ryzt
     * @return
     */
    List<PrisonerVwRespVO> getPrisonerListByJgrybm(String jgrybm, PrisonerQueryRyztEnum ryzt);

    /**
     * 查询在所人员数量
     *
     * @param orgCode  机构代码
     * @param roomCode 监室代码
     * @return
     */
    Integer getPrisonerCount(String orgCode, String roomCode);

    /**
     * 查询今日入所人员数量
     *
     * @param orgCode  机构代码
     * @param roomCode 监室代码
     * @return
     */
    Integer getPrisonerToDayInCount(String orgCode, String roomCode);

    /**
     * 获取在所人员
     *
     * @param jsh 监室号
     * @return
     */
    List<PrisonerVwRespVO> getPrisonerListByJsh(String jsh);

    /**
     * 获取所有在所人员
     *
     * @param jsh 监室号
     * @return
     */
    List<PrisonerVwRespVO> getPrisonerListByOrgCode(String orgCode);

    /**
     * 获取人员,带有医疗信息
     *
     * @param jgrybm
     * @return
     */
    PrisonerVwRespVO getPrisonerWithMedicalInformationByJgrybm(String jgrybm);

    PageResult<PrisonerListVwRespVO> getCivilizedPersonalPrisonerSelectCompomentList(PrisonerVwPageReqVO pageReqVO);

    /**
     * 根据人员id获取监管人员编码
     *
     * @param ryId
     * @return
     */
    String getJgrybm(String ryId);

    /**
     * 外来人员分页查询
     * @param pageReqVO
     * @return
     */
    PageResult<ForeignPersonnelListRespVO> getForeinPersionnelSelectCompoment(PoreginPersonnelPageReqVO pageReqVO);

    /**
     * 获得vw_acp_pm_prisoner_list 视图
     *
     * @param jgrybms 分页查询
     * @return VIEW分页
     */
    List<PrisonerListDO> getPrisonerListVm(List<String> jgrybms);

}
