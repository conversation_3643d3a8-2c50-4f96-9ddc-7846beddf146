package com.rs.module.base.service.pm.device;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceListReqVO;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDevicePageReqVO;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceRespVO;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceSaveReqVO;
import com.rs.module.base.entity.pm.DeviceData;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;
import com.rs.module.base.entity.pm.device.SplwTreeDO;
import com.rs.module.base.vo.TreeNode;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-监管管理-设备信息 Service 接口
 *
 * <AUTHOR>
 */
public interface SplwTreeService extends IBaseService<SplwTreeDO>{

    //从视频联网平台同步数据
    JSONObject sysSplwData(String orgCode) throws Exception;
}
