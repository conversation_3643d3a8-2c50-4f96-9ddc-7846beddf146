package com.rs.module.base.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-监管管理-区域映射 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AreaMappingRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("大数据平台区域编码")
    private String bdAreaCode;
    @ApiModelProperty("大数据平台区域名称")
    private String bdAreaName;
    @ApiModelProperty("大数据平台区域父类ID(acp_area_area.parent_id)")
    private String bdParentId;
    @ApiModelProperty("视频联网平台区域父类名称")
    private String bdParentName;
    @ApiModelProperty("视频联网平台区域编码")
    private String spAreaCode;
    @ApiModelProperty("视频联网平台区域名称")
    private String spAreaName;
    @ApiModelProperty("视频联网平台区域父类ID(acp_area_area.parent_id)")
    private String spParentId;
    @ApiModelProperty("视频联网平台区域父类名称")
    private String spParentName;
}
