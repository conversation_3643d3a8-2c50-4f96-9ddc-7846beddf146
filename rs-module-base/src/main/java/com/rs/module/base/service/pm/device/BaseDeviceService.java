package com.rs.module.base.service.pm.device;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.base.controller.admin.pm.vo.device.*;
import com.rs.module.base.entity.pm.DeviceData;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;
import com.rs.module.base.vo.TreeNode;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-监管管理-设备信息 Service 接口
 *
 * <AUTHOR>
 */
public interface BaseDeviceService extends IBaseService<BaseDeviceDO>{

    /**
     * 创建实战平台-监管管理-设备信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createBaseDevice(@Valid BaseDeviceSaveReqVO createReqVO);

    /**
     * 更新实战平台-监管管理-设备信息
     *
     * @param updateReqVO 更新信息
     */
    void updateBaseDevice(@Valid BaseDeviceSaveReqVO updateReqVO);

    /**
     * 删除实战平台-监管管理-设备信息
     *
     * @param id 编号
     */
    void deleteBaseDevice(String id);

    /**
     * 获得实战平台-监管管理-设备信息
     *
     * @param id 编号
     * @return 实战平台-监管管理-设备信息
     */
    BaseDeviceDO getBaseDevice(String id);

    /**
    * 获得实战平台-监管管理-设备信息分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-监管管理-设备信息分页
    */
    PageResult<BaseDeviceDO> getBaseDevicePage(BaseDevicePageReqVO pageReqVO);

    /**
    * 获得实战平台-监管管理-设备信息列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-监管管理-设备信息列表
    */
    List<BaseDeviceDO> getBaseDeviceList(BaseDeviceListReqVO listReqVO);

    /**
     * 导入设备信息
     * <AUTHOR>
     * @date 2025/5/21 10:17
     * @param [dataList]
     * @return void
     */
    void importDeviceData(List<DeviceData> dataList);

    /**
     * 根据监室号ID，获取摄像头信息
     * <AUTHOR>
     * @date 2025/6/11 22:14
     * @param [roomId]
     * @return java.util.List<com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceRespVO>
     */
    List<BaseDeviceRespVO> getCameraListByRoomId(String roomId);

    JSONObject sysVideoTreeByAreaName(String areaName, String orgCode) throws Exception;

    JSONObject sysVideoTree(String areaName, String orgCode) throws Exception;

    List<TreeNode> getAllAreaTree(BaseDevicePageReqVO treeDto);

    @Transactional(propagation = Propagation.SUPPORTS,rollbackFor = Exception.class)
    Integer addDevice(BaseDeviceSaveReqVO baseDeviceDO);

    //@Async
    void sysDeviceTree(String orgCode);

    List<TreeNode> getDeviceTreeByArea(BaseDeviceTreeReqVO treeReqVO);
}
