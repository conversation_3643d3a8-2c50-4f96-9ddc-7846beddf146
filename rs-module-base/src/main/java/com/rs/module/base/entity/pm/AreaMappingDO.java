package com.rs.module.base.entity.pm;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-监管管理-区域映射 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_area_mapping")
@KeySequence("acp_pm_area_mapping_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_area_mapping")
public class AreaMappingDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 大数据平台区域编码
     */
    private String bdAreaCode;
    /**
     * 大数据平台区域名称
     */
    private String bdAreaName;
    /**
     * 大数据平台区域父类ID(acp_area_area.parent_id)
     */
    private String bdParentId;
    /**
     * 大数据平台区域父类名称
     */
    private String bdParentName;
    /**
     * 视频联网平台区域编码 camera数据类型时为设备表ID
     */
    private String spAreaCode;
    /**
     * 视频联网平台区域名称
     */
    private String spAreaName;
    /**
     * 视频联网平台区域父类ID(acp_area_area.parent_id)
     */
    private String spParentId;
    /**
     * 视频联网平台区域父类名称
     */
    private String spParentName;
    /**
     * 映射平台类型 01 大数据平台区域映射视频联网平台
     */
    private String types;
    /**
     * 映射数据类型 area 区域映射,camera 摄像机
     */
    private String dataTypes;
}
