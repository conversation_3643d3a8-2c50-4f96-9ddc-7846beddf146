package com.rs.module.base.entity.pm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.rs.module.base.enums.DataChangeEventStatusEnum;
import com.rs.module.base.enums.DataChangeEventTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 数据变更事件日志实体
 *
 * <AUTHOR>
 * @date 2025/01/05
 */
@Data
@TableName("acp_pm_data_change_event_log")
public class DataChangeEventLogDO  {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 事件类型（INSERT/UPDATE/DELETE）
     */
    private DataChangeEventTypeEnum eventType;

    /**
     * 业务表名
     */
    private String tableName;

    /**
     * 业务类型（用于区分不同的业务场景）
     */
    private String businessType;

    /**
     * 主键ID
     */
    private String primaryKeyId;

    /**
     * 变更前数据（JSON格式）
     */
    private String oldData;

    /**
     * 变更后数据（JSON格式）
     */
    private String newData;

    /**
     * 事件状态
     */
    private DataChangeEventStatusEnum status;

    /**
     * 处理器类名
     */
    private String handlerClass;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 事件触发时间
     */
    private Date eventTime;

    /**
     * 开始处理时间
     */
    private Date processStartTime;

    /**
     * 处理完成时间
     */
    private Date processEndTime;

    /**
     * 下次重试时间
     */
    private Date nextRetryTime;

    /**
     * 扩展信息（JSON格式）
     */
    private String extendInfo;

    /**
     * 期望执行时间
     */
    private Date expectationExecuteDate;
}
