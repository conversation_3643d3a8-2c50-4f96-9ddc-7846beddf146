package com.rs.module.base.util;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.base.entity.pm.device.SplwTreeDO;
import com.rs.module.base.vo.TreeNode;

import java.util.Map;

public class SplwTreeUtil {

    /**
     * 构建SplwTreeDO对象
     */
    public static SplwTreeDO buildSplwTreeDO(JSONObject node, Integer id, Map<String, String> pathInfo, String orgCode) {
        SplwTreeDO treeDO = new SplwTreeDO();
        treeDO.setId(id);
        treeDO.setOrderNo(id);
        treeDO.setTreeCode(node.getString("treeCode"));
        treeDO.setType(1);
        treeDO.setPrisonId(orgCode);

        // 设置数值型属性
        setNumericProperties(treeDO, node);

        // 设置路径相关信息
        treeDO.setTreeLevel(Integer.parseInt(pathInfo.get("level")));
        treeDO.setParentPathId(pathInfo.get("pathId"));
        treeDO.setPathName(pathInfo.get("pathName"));

        String pathId = pathInfo.get("pathId");
        treeDO.setPid(Integer.valueOf(pathId.substring(pathId.lastIndexOf("/") + 1)));
        treeDO.setName(node.getString("name"));

        return treeDO;
    }
    /**
     * 设置SplwTreeDO的数值型属性
     */
    public static void setNumericProperties(SplwTreeDO treeDO, JSONObject node) {
        treeDO.setAllNum(node.getIntValue("allNum"));
        treeDO.setIsLocked(node.getIntValue("isLocked"));
        treeDO.setOnlineNum(node.getIntValue("onlineNum"));
        treeDO.setPlatformId(node.getIntValue("platformId"));
        treeDO.setTotalNum(node.getIntValue("totalNum"));
    }
    /**
     * 创建SplwTreeDO对象
     */
    public static SplwTreeDO createSplwTreeDO(String rootCode, JSONObject item, Integer currentId, String code,
                                        TreeNode treeNode, String orgCode,Map<String, String> pathInfo) {
        if (pathInfo == null) {
            return null;
        }

        SplwTreeDO treeVO = new SplwTreeDO();
        treeVO.setId(currentId);
        treeVO.setOrderNo(currentId);
        treeVO.setTreeCode(item.getString("treeCode"));
        treeVO.setType(2);
        treeVO.setTreeLevel(Integer.parseInt(pathInfo.get("level")));
        treeVO.setParentPathId(pathInfo.get("pathId"));
        treeVO.setPathName(pathInfo.get("pathName"));

        String pathId = pathInfo.get("pathId");
        treeVO.setPid(Integer.valueOf(pathId.substring(pathId.lastIndexOf("/") + 1)));
        treeVO.setName(item.getString("chanName"));
        treeVO.setChanId(Long.valueOf(item.getString("chanId")));

        // 设置其他属性
        setChanItemProperties(treeVO, item);

        return treeVO;
    }

    /**
     * 设置通道项属性
     */
    public static void setChanItemProperties(SplwTreeDO treeVO, JSONObject item) {
        treeVO.setChanTypeId(Integer.valueOf(item.getString("chanTypeId")));
        treeVO.setChnAbility(item.getString("chnAbility"));
        treeVO.setControlType(Integer.valueOf(item.getString("controlType")));
        treeVO.setDefaultStreamType(Integer.valueOf(item.getString("defaultStreamType")));
        treeVO.setDevId(Long.valueOf(item.getString("devId")));
        treeVO.setDevLockStatus(Integer.valueOf(item.getString("devLockStatus")));
        treeVO.setFacade(Integer.valueOf(item.getString("facade")));
        treeVO.setPtzLockStatus(Integer.valueOf(item.getString("ptzLockStatus")));
        treeVO.setGb28181Code(item.getString("gb28181Code"));
        treeVO.setIsFocus(Integer.valueOf(item.getString("isFocus")));
        treeVO.setPlatformId(Integer.valueOf(item.getString("platformId")));
        treeVO.setStatus(Integer.valueOf(item.getString("status")));
        treeVO.setStorageType(Integer.valueOf(item.getString("storageType")));
        treeVO.setUsageType(Integer.valueOf(item.getString("usageType")));
        treeVO.setPrisonId(item.getString("prisonId"));
    }
}
