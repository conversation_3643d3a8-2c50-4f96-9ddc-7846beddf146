package com.rs.module.base.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BpmApi;
import com.rs.adapter.bsp.api.dto.bpm.DefinitionDTO;
import com.rs.adapter.bsp.api.dto.bpm.ProcessCmdDTO;
import com.rs.adapter.bsp.enums.BspApproceBackTypeEnum;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.cons.CommonConstants;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.base.vo.ApproveReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class BspApprovalUtil implements ApplicationContextAware {
    public static final String APPROVE_RESULT_KEY_TASKID = "taskId";
    public static final String APPROVE_RESULT_KEY_ACTINSTID = "actInstId";
    public static final String APPROVE_RESULT_KEY_CANDIDATE_USERS = "candidateUsers";
    private static BpmApi bpmApi;

    public static BpmApi getBpmApi() {
        return bpmApi;
    }

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        bpmApi = context.getBean(BpmApi.class);
    }

    /**
     * 启动流程，自动选择符合配置的审批人员
     *
     * @param defKey     流程定义key
     * @param businessId 业务编号
     * @param msgTit     消息标题
     * @param msgUrl     消息地址
     * @return JSONObject
     */
    public static JSONObject defaultStartProcess(String defKey, String businessId, String msgTit, String msgUrl,
                                                 Map<String, Object> variables) {
        return commonStartProcess(defKey, businessId, msgTit, msgUrl, null, variables, HttpUtils.getAppCode());
    }
    public static String getAppDefKey(String fApp,String defKey) {
        if(defKey.contains(fApp+"-")){
            return defKey;
        }
        return fApp + "-" + defKey;
    }

    public static JSONObject commonStartProcess(String defKey, String businessId, String msgTit, String msgUrl,
                                                Map<String, Object> variables, String fApp) {
        return commonStartProcess(defKey, businessId, msgTit, msgUrl, null, variables, fApp);
    }

    public static JSONObject commonStartProcess(String defKey, String businessId, String msgTit, String msgUrl, String mobileUrl,
                                                Map<String, Object> variables, String fApp) {
        defKey = getAppDefKey(fApp, defKey);
        validateParams(defKey, businessId, msgTit, msgUrl);

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String formProcessVarStr = new JSONObject().toJSONString();
        if(variables != null) {
        	formProcessVarStr = JSONObject.toJSONString(variables);
        }

        JSONObject startApproveUser = bpmApi.getStartApproveUser(defKey, sessionUser.getId(), formProcessVarStr);
        if (startApproveUser == null || !startApproveUser.getBoolean("success")) {
            throw new RuntimeException("启动流程时未获取到下一级审批用户！流程标识：" + defKey);
        }

        List<ReceiveUser> candidateUsers = new ArrayList<>();
        List<DefinitionDTO> definition = new ArrayList<>();

        JSONArray data = startApproveUser.getJSONArray("data");
        for (Object obj : data) {
            parseApprovalNodesSingleNode(JSON.parseObject(JSON.toJSONString(obj)), defKey, candidateUsers, definition);
        }

        if (CollUtil.isEmpty(candidateUsers)) {
            throw new RuntimeException("启动流程时未获取到下一级审批用户！流程标识：" + defKey);
        }

        ProcessCmdDTO processCmdDTO = buildBaseProcessCmdDTO(sessionUser, defKey, businessId, msgTit, msgUrl, variables, fApp);
        processCmdDTO.setMobileUrl(mobileUrl);
        processCmdDTO.setCandidateUsers(candidateUsers);
        processCmdDTO.setDefinition(definition);
        processCmdDTO.setApprovalContent("首次填报");

        // 返回当前节点审批用户
        JSONObject processResult = bpmApi.startProcess(processCmdDTO);
        processResult.put(APPROVE_RESULT_KEY_CANDIDATE_USERS, candidateUsers);
        return processResult;
    }

    private static void validateParams(String... params) {
//        for (String param : params) {
//            if (param == null || param.trim().isEmpty()) {
//                throw new IllegalArgumentException("参数不能为空");
//            }
//        }
    }

    private static ProcessCmdDTO buildBaseProcessCmdDTO(SessionUser sessionUser, String defKey, String businessId,
                                                        String msgTit, String msgUrl, Map<String, Object> variables,
                                                        String fApp) {
        ProcessCmdDTO dto = new ProcessCmdDTO();
        dto.setActDefKey(defKey);
        dto.setBusinessId(businessId);
        dto.setMsgTit(msgTit);
        dto.setMsgUrl(msgUrl);
        dto.setVariables(variables);
        dto.setFApp(fApp);
        dto.setFXxpt("pc");
        dto.setAssigneeOrgId(sessionUser.getOrgCode());
        dto.setAssigneeOrgName(sessionUser.getOrgName());
        dto.setAssigneeUserId(sessionUser.getIdCard());
        dto.setAssigneeUserName(sessionUser.getName());
        return dto;
    }

    public static Map<String, String> commonStartProcessMap(String defKey, String businessId, String msgTit, String msgUrl,
                                                            Map<String, Object> variables, String fApp) throws RuntimeException {
        return commonStartProcessMap(defKey, businessId, msgTit, msgUrl, null, variables, fApp);
    }

    public static Map<String, String> commonStartProcessMap(String defKey, String businessId, String msgTit, String msgUrl,
                                                            String mobileUrl, Map<String, Object> variables, String fApp) throws RuntimeException {
        Map<String, String> map = new HashMap<>();
        try {
            JSONObject startResult = commonStartProcess(defKey, businessId, msgTit, msgUrl, mobileUrl, variables, fApp);
            JSONObject bpmTrail = validBPMResult(startResult, true);
            String actInstId = bpmTrail.getString(APPROVE_RESULT_KEY_ACTINSTID);
            String taskId = bpmTrail.getString(APPROVE_RESULT_KEY_TASKID);

            if (actInstId == null || taskId == null) {
                log.error("流程启动失败：返回缺少 actInstId 或 taskId");
                throw new RuntimeException("流程审批失败：返回缺少 actInstId 或 taskId");
            } else {
                log.info("启动流程成功：actInstId={}, taskId={}", actInstId, taskId);
                map.put(APPROVE_RESULT_KEY_ACTINSTID, actInstId);
                map.put(APPROVE_RESULT_KEY_TASKID, taskId);
                map.put(APPROVE_RESULT_KEY_CANDIDATE_USERS, JSONObject.toJSONString(startResult.get(APPROVE_RESULT_KEY_CANDIDATE_USERS)));
            }
        } catch (RuntimeException e) {
            log.error("流程启动异常: {}", e.getMessage(), e);
            throw new RuntimeException("流程启动异常：" + e.getMessage());
        }
        return map;
    }

    public static JSONObject defaultApprovalProcess(String defKey, String actInstId, String taskId, String businessId,
                                                    BspApproceStatusEnum isApprove, String approveContent, String msgTit,
                                                    String msgUrl, Map<String, Object> variables) {
        return commonApprovalProcess(defKey, actInstId, taskId, businessId, isApprove, approveContent, msgTit, msgUrl,
                null, null, variables, null, "dam");
    }

    public static JSONObject defaultApprovalProcess(String defKey, String actInstId, String taskId, String businessId,
                                                    BspApproceStatusEnum isApprove, String approveContent, String msgTit,
                                                    String msgUrl, Boolean terminateTask, Map<String, Object> variables) {
        return commonApprovalProcess(defKey, actInstId, taskId, businessId, isApprove, approveContent, msgTit, msgUrl,
                null, terminateTask, variables, null, "dam");
    }

    public static JSONObject defaultApprovalProcessPass(ApproveReqVO approveReqVO, String msgTit, String msgUrl, Boolean terminateTask,
                                                        Map<String, Object> variables, String fApp) {
        return commonApprovalProcess(approveReqVO.getDefKey(), approveReqVO.getActInstId(), approveReqVO.getTaskId(),
                approveReqVO.getId(), BspApproceStatusEnum.PASSED, approveReqVO.getApprovalComments(), msgTit, msgUrl,
                null, terminateTask, variables, null, fApp);
    }

    public static JSONObject commonApprovalProcess(String defKey, String actInstId, String taskId, String businessId,
                                                   BspApproceStatusEnum isApprove, String approveContent, String msgTit,
                                                   String msgUrl, String mobileUrl, Boolean terminateTask, Map<String, Object> variables,
                                                   JSONObject nowApproveUser, String fApp) {
        defKey = getAppDefKey(fApp, defKey);
        validateParams(defKey, actInstId, taskId, businessId);

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        ProcessCmdDTO processCmdDTO = buildBaseProcessCmdDTO(sessionUser, defKey, businessId, msgTit, msgUrl, variables, fApp);

        processCmdDTO.setTaskId(taskId);
        processCmdDTO.setMobileUrl(mobileUrl);
        processCmdDTO.setIsApprove(isApprove.getCode());
        processCmdDTO.setIsApproveStr(isApprove.getName());
        // 2025-06-07 丘文新提的，默认为通过。
        if(StrUtil.isBlank(approveContent)){
            approveContent = CommonConstants.DEFAULT_APPROVAL_VALUE_PASS;
        }
        processCmdDTO.setApprovalContent(approveContent);
        processCmdDTO.setApprovalDate(new Date());

        if (nowApproveUser != null) {
            processCmdDTO.setAssigneeOrgId(nowApproveUser.getString("orgCode"));
            processCmdDTO.setAssigneeOrgName(nowApproveUser.getString("orgName"));
            processCmdDTO.setAssigneeUserId(nowApproveUser.getString("idCard"));
            processCmdDTO.setAssigneeUserName(nowApproveUser.getString("name"));
        }
        String formProcessVarStr = JSONObject.toJSONString(variables); // 转成字符串
        JSONObject approveUser = bpmApi.getApproveUser(actInstId, sessionUser.getId(), formProcessVarStr);
        if (approveUser == null || !approveUser.getBoolean("success")) {
            throw new RuntimeException("未获取到下一级审批用户");
        }
        if (terminateTask != null) {
            processCmdDTO.setTerminateTask(terminateTask);
        }
        List<ReceiveUser> candidateUsers = new ArrayList<>();
        List<DefinitionDTO> definition = new ArrayList<>();

        boolean isEndNode = false;
        JSONArray data = approveUser.getJSONArray("data");
        for (Object obj : data) {
            JSONObject node = JSON.parseObject(JSON.toJSONString(obj));
            String currentNodeType = node.getString("nodeType");

            if ("userTask".equals(currentNodeType) || "exclusiveGateway".equals(currentNodeType)) {
                parseApprovalNodesSingleNode(node, defKey, candidateUsers, definition);
            }

            if ("endEvent".equals(currentNodeType)) {
                isEndNode = true;
                processCmdDTO.setTerminateTask(true);
                processCmdDTO.setBackNodeId("node_mjtb");
                processCmdDTO.setBackType(BspApproceBackTypeEnum.START.getCode());
                if (BspApproceStatusEnum.PASSED.equals(isApprove)) {
                    isApprove = BspApproceStatusEnum.PASSED_END;
                }
                break;
            }
        }

        //processCmdDTO.isTerminateTask() 是否结束流程
        if (CollUtil.isEmpty(candidateUsers) && !isEndNode && !processCmdDTO.isTerminateTask()) {
            throw new RuntimeException("未获取到下一级审批用户");
        }

        if (BspApproceStatusEnum.REJECT.getCode() == isApprove.getCode()) {
            processCmdDTO.setBack(true);
        }

        processCmdDTO.setCandidateUsers(candidateUsers);
        processCmdDTO.setDefinition(definition);

        // 返回当前节点审批用户
        JSONObject processResult = bpmApi.approvalProcess(processCmdDTO);
        processResult.put(APPROVE_RESULT_KEY_CANDIDATE_USERS, candidateUsers);
        return processResult;
    }

    private static void parseUserTaskNode(JSONObject node, List<ReceiveUser> candidateUsers,
                                          List<DefinitionDTO> definition, String defKey, String keys) {
        String nodeName = node.getString("nodeName");
        JSONArray orgUserList = node.getJSONArray(keys);

        for (Object obj : orgUserList) {
            JSONObject orgUser = (JSONObject) obj;
            JSONArray userList = orgUser.getJSONArray("user");
            for (Object userObj : userList) {
                JSONObject user = (JSONObject) userObj;
                ReceiveUser receiveUser = new ReceiveUser();
                receiveUser.setIdCard(user.getString("userIdCard"));
                receiveUser.setOrgCode(user.getString("orgCode"));
                receiveUser.setName(user.getString("userName"));
                candidateUsers.add(receiveUser);
            }
        }

        DefinitionDTO dto = new DefinitionDTO();
        dto.setDefKey(defKey);
        dto.setName(nodeName);
        definition.add(dto);
    }

    private static void parseApprovalNodesSingleNode(JSONObject node, String defKey,
                                                     List<ReceiveUser> candidateUsers,
                                                     List<DefinitionDTO> definition) {
        String currentNodeType = node.getString("nodeType");

        if ("userTask".equals(currentNodeType)) {
            parseUserTaskNode(node, candidateUsers, definition, defKey, "orgUserList");
        } else if ("exclusiveGateway".equals(currentNodeType)) {
            JSONArray gatewayChildren = node.getJSONArray("orgUserList");
            for (Object childObj : gatewayChildren) {
                JSONObject child = (JSONObject) childObj;
                String childNodeType = child.getString("nodeType");
                if ("userTask".equals(childNodeType)) {
                    parseUserTaskNode(child, candidateUsers, definition, defKey, "orgUser");
                }
            }
        }
    }

    public static JSONObject approvalProcess(String defKey, String actInstId, String taskId, String businessId,
                                             BspApproceStatusEnum isApprove, String approveContent, String msgTit, String msgUrl, String mobileUrl,
                                             Boolean terminateTask, Map<String, Object> variables, JSONObject nowApproveUser, String fApp) {
        return commonApprovalProcess(defKey, actInstId, taskId, businessId, isApprove, approveContent, msgTit, msgUrl,
                mobileUrl, terminateTask, variables, nowApproveUser, fApp);
    }


    public static JSONObject approvalProcess(String defKey, String actInstId, String taskId, String businessId,
                                             BspApproceStatusEnum isApprove, String approveContent, String msgTit, String msgUrl,
                                             Boolean terminateTask, Map<String, Object> variables, JSONObject nowApproveUser, String fApp) {
        return commonApprovalProcess(defKey, actInstId, taskId, businessId, isApprove, approveContent, msgTit, msgUrl, null,
                terminateTask, variables, nowApproveUser, fApp);
    }


    /**
     * 审批信息
     * <AUTHOR>
     * @date 2025/6/10 17:06
     * @param defKey, actInstId, taskId, businessId, isApprove, approveContent, msgTit, msgUrl, terminateTask, variables, nowApproveUser, fApp]
     * @return com.alibaba.fastjson.JSONObject
     */
    public static JSONObject approvalProcessAcp(String defKey, String actInstId, String taskId, String businessId,
                                             BspApproceStatusEnum isApprove, String approveContent) {

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", businessId);
        boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == isApprove.getCode() ||
                BspApproceStatusEnum.NOT_PASSED_END.getCode() == isApprove.getCode();
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getIdCard());
        nowApproveUser.put("name", sessionUser.getName());



        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(taskId, sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }

        String facp = StrUtil.isNotBlank(HttpUtils.getAppCode()) ? HttpUtils.getAppCode(): "acp";
        return commonApprovalProcess(defKey, actInstId, taskId, businessId, isApprove, approveContent, null,
                null, null, terminateTask, variables, nowApproveUser,  facp);
    }

    /**
     * 流程审批(已做完请求结果处理及日志输出)
     *
     * @param approveReqVO   审批参数
     * @param isApprove      审批结果
     * @param msgTit
     * @param msgUrl
     * @param terminateTask
     * @param variables
     * @param nowApproveUser
     * @param fApp
     * @return 审批下一个任务taskId
     */
    public static Map<String, String> approvalProcessMap(ApproveReqVO approveReqVO, BspApproceStatusEnum isApprove, String msgTit, String msgUrl, Boolean terminateTask,
                                                         Map<String, Object> variables, JSONObject nowApproveUser, String fApp) throws RuntimeException{
        Map<String, String> map = new HashMap<>();
        JSONObject approvalResult = approvalProcess(approveReqVO.getDefKey(), approveReqVO.getActInstId(), approveReqVO.getTaskId(),
                approveReqVO.getId(), isApprove, approveReqVO.getApprovalComments(), msgTit, msgUrl, terminateTask, variables, nowApproveUser, fApp);
        log.info("审批结果：{}", approvalResult);

        try {
            JSONObject bpmTrail = validBPMResult(approvalResult, false);
            String nextTaskId = bpmTrail.getString(APPROVE_RESULT_KEY_TASKID);
            String actInstId = bpmTrail.getString(APPROVE_RESULT_KEY_ACTINSTID);
            if (actInstId == null || nextTaskId == null) {
                log.error("流程审批失败：返回缺少 actInstId 或 taskId");
                throw new RuntimeException("流程审批失败：返回缺少 actInstId 或 taskId");
            } else {
                log.info("流程审批成功：actInstId={}, taskId={}", actInstId, nextTaskId);
                map.put(APPROVE_RESULT_KEY_ACTINSTID, actInstId);
                map.put(APPROVE_RESULT_KEY_TASKID, nextTaskId);
            }
        } catch (RuntimeException e) {
            log.error("流程审批异常: {}", e.getMessage(), e);
            throw new RuntimeException("流程审批失败：" + e.getMessage());
        }
        return map;
    }

    public List<JSONObject> batchDefaultApprovalProcess(String defKey,
                                                        List<JSONObject> taskItems,
                                                        BspApproceStatusEnum isApprove,
                                                        String approveContent,
                                                        String msgTit,
                                                        String msgUrl,
                                                        Map<String, Object> variables) {
        List<JSONObject> results = new ArrayList<>();
        for (JSONObject item : taskItems) {
            JSONObject result = defaultApprovalProcess(
                    defKey,
                    item.getString(APPROVE_RESULT_KEY_ACTINSTID),
                    item.getString(APPROVE_RESULT_KEY_TASKID),
                    item.getString("businessId"),
                    isApprove,
                    approveContent,
                    msgTit,
                    msgUrl,
                    variables
            );
            results.add(result);
        }
        return results;
    }

    /**
     * 批量审批
     * @param defKey
     * @param taskItems
     * @param isApprove
     * @param approveContent
     * @param msgTit
     * @param msgUrl
     * @param variables
     * @return
     */
    public static List<JSONObject> batchApprovalProcess(String defKey,
                                                        List<JSONObject> taskItems,
                                                        BspApproceStatusEnum isApprove,
                                                        String approveContent,
                                                        String msgTit,
                                                        String msgUrl,
                                                        Map<String, Object> variables) {
        List<JSONObject> results = new ArrayList<>();
        for (JSONObject item : taskItems) {
            JSONObject result = commonApprovalProcess(defKey,
                    item.getString(APPROVE_RESULT_KEY_ACTINSTID),
                    item.getString(APPROVE_RESULT_KEY_TASKID),
                    item.getString("businessId"),
                    isApprove,
                    approveContent,
                    msgTit,
                    msgUrl,
                    null,
                    null,
                    variables,
                    null,
                    HttpUtils.getAppCode());
            results.add(result);
            results.add(result);
        }
        return results;
    }

    public static JSONObject extractFirstApprover(JSONObject result) {
        if (result == null || !result.getBoolean("success")) {
            throw new RuntimeException("获取审批人信息失败");
        }

        JSONObject data = result.getJSONObject("data");
        JSONArray orgUserList = data.getJSONArray("orgUserList");

        if (orgUserList == null || orgUserList.isEmpty()) {
            return null;
        }

        for (Object obj : orgUserList) {
            JSONObject orgUser = (JSONObject) obj;
            JSONArray users = orgUser.getJSONArray("user");

            if (users != null && !users.isEmpty()) {
                JSONObject firstUser = users.getJSONObject(0);
                JSONObject nowApprroveUser = new JSONObject();
                nowApprroveUser.put("orgCode", firstUser.getString("orgCode"));
                nowApprroveUser.put("idCard", firstUser.getString("userIdCard"));
                nowApprroveUser.put("name", firstUser.getString("userName"));
                return nowApprroveUser;
            }
        }

        throw new RuntimeException("未找到可用审批人");
    }

    public static CommonResult autoCompleteProcess(String defKey, String businessId, String msgTit, String msgUrl,
                                                   Map<String, Object> variables, String fApp) {
        return autoCompleteProcess(defKey, businessId, msgTit, msgUrl, null, variables, fApp);
    }

    public static CommonResult autoCompleteProcess(String defKey, String businessId, String msgTit, String msgUrl,
                                                   String mobileUrl, Map<String, Object> variables, String fApp) {
        String actInstId = "";
        String taskId = "";
        try {
            Map<String, String> startResultMap = commonStartProcessMap(defKey, businessId, msgTit, msgUrl, mobileUrl, variables, fApp);
            actInstId = startResultMap.get(APPROVE_RESULT_KEY_ACTINSTID);
            taskId = startResultMap.get(APPROVE_RESULT_KEY_TASKID);

            if (actInstId == null || taskId == null) {
                log.error("流程启动失败：返回缺少 actInstId 或 taskId");
                return CommonResult.error("流程启动失败");
            }
        } catch (RuntimeException e) {
            log.error(e.getMessage());
        }

        int maxAttempts = 10; // 防止死循环
        for (int attempt = 0; attempt < maxAttempts; attempt++) {
            JSONObject approveUser = bpmApi.taskIdentityLinks(actInstId, "");
            if (approveUser == null || !approveUser.getBoolean("success")) {
                throw new RuntimeException("未获取到下一级审批用户");
            }

            JSONObject nowApprroveUser = extractFirstApprover(approveUser);
            if (nowApprroveUser == null) {
                break;
            }

            log.info("下一级审批用户：{}", nowApprroveUser);
            ApproveReqVO approveReqVO = new ApproveReqVO();
            approveReqVO.setDefKey(defKey);
            approveReqVO.setActInstId(actInstId);
            approveReqVO.setTaskId(taskId);
            approveReqVO.setId(businessId);
            Map<String, String> approvalResult = approvalProcessMap(approveReqVO,
                    BspApproceStatusEnum.PASSED,
                    "自动审批通过",
                    "",
                    false,
                    variables,
                    nowApprroveUser,
                    fApp
            );

            taskId = approvalResult.get(APPROVE_RESULT_KEY_TASKID); // 更新任务ID
        }
        JSONObject result = new JSONObject();
        result.put(APPROVE_RESULT_KEY_ACTINSTID, actInstId);
        result.put(APPROVE_RESULT_KEY_TASKID, taskId);

        return CommonResult.success(result, defKey + "流程自动审批完成");
    }

    public static JSONObject validBPMResult(JSONObject result, boolean isStartProcess) throws RuntimeException {
        String exceptionTitle = isStartProcess ? "流程启动失败：" : "流程审批失败：";
        if (result == null || !result.getBooleanValue("success")) {
            throw new RuntimeException(exceptionTitle + result.getString("msg"));
        }

        JSONObject resultData = result.getJSONObject("data");
        if (resultData == null) {
            throw new RuntimeException(exceptionTitle + "返回数据为空");
        }

        JSONObject resultBpmTrail = resultData.getJSONObject("bpmTrail");
        if (resultBpmTrail == null) {
            throw new RuntimeException(exceptionTitle + "未获取到 bpmTrail 数据");
        }

        return resultBpmTrail;
    }
}
