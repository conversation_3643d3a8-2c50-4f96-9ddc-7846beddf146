package com.rs.module.base.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-监管管理-区域映射新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AreaMappingSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("大数据平台区域编码")
    @NotEmpty(message = "大数据平台区域编码不能为空")
    private String bdAreaCode;

    @ApiModelProperty("大数据平台区域名称")
    @NotEmpty(message = "大数据平台区域名称不能为空")
    private String bdAreaName;

    @ApiModelProperty("大数据平台区域父类ID(acp_area_area.parent_id)")
    @NotEmpty(message = "大数据平台区域父类ID(acp_area_area.parent_id)不能为空")
    private String bdParentId;

    @ApiModelProperty("视频联网平台区域父类名称")
    @NotEmpty(message = "视频联网平台区域父类名称不能为空")
    private String bdParentName;

    @ApiModelProperty("视频联网平台区域编码")
    @NotEmpty(message = "视频联网平台区域编码不能为空")
    private String spAreaCode;

    @ApiModelProperty("视频联网平台区域名称")
    @NotEmpty(message = "视频联网平台区域名称不能为空")
    private String spAreaName;

    @ApiModelProperty("视频联网平台区域父类ID(acp_area_area.parent_id)")
    @NotEmpty(message = "视频联网平台区域父类ID(acp_area_area.parent_id)不能为空")
    private String spParentId;

    @ApiModelProperty("视频联网平台区域父类名称")
    @NotEmpty(message = "视频联网平台区域父类名称不能为空")
    private String spParentName;
    /**
     * 映射数据类型 area 区域映射,camera 摄像机
     */
    @ApiModelProperty("映射数据类型 area 区域映射,camera 摄像机")
    @NotEmpty(message = "映射数据类型")
    private String dataTypes;
}
