package com.rs.module.base.service.pm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderListReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderSaveReqVO;
import com.rs.module.base.dao.pm.PrisonRoomWarderDao;
import com.rs.module.base.entity.pm.PrisonRoomWarderDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 实战平台-监管管理-监室主协管人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrisonRoomWarderServiceImpl extends BaseServiceImpl<PrisonRoomWarderDao, PrisonRoomWarderDO> implements PrisonRoomWarderService {

    @Resource
    private PrisonRoomWarderDao prisonRoomWarderDao;

    @Override
    public String createPrisonRoomWarder(PrisonRoomWarderSaveReqVO createReqVO) {
        // 插入
        PrisonRoomWarderDO prisonRoomWarder = BeanUtils.toBean(createReqVO, PrisonRoomWarderDO.class);
        prisonRoomWarderDao.insert(prisonRoomWarder);
        // 返回
        return prisonRoomWarder.getId();
    }

    @Override
    public void updatePrisonRoomWarder(PrisonRoomWarderSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonRoomWarderExists(updateReqVO.getId());
        // 更新
        PrisonRoomWarderDO updateObj = BeanUtils.toBean(updateReqVO, PrisonRoomWarderDO.class);
        prisonRoomWarderDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonRoomWarder(String id) {
        // 校验存在
        validatePrisonRoomWarderExists(id);
        // 删除
        prisonRoomWarderDao.deleteById(id);
    }

    private void validatePrisonRoomWarderExists(String id) {
        if (prisonRoomWarderDao.selectById(id) == null) {
            throw new ServerException("实战平台-监管管理-监室主协管人员数据不存在");
        }
    }

    @Override
    public PrisonRoomWarderDO getPrisonRoomWarder(String id) {
        return prisonRoomWarderDao.selectById(id);
    }

    @Override
    public PageResult<PrisonRoomWarderDO> getPrisonRoomWarderPage(PrisonRoomWarderPageReqVO pageReqVO) {
        return prisonRoomWarderDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonRoomWarderDO> getPrisonRoomWarderList(PrisonRoomWarderListReqVO listReqVO) {
        return prisonRoomWarderDao.selectList(listReqVO);
    }

    @Override
    public Map<String,List<PrisonRoomWarderRespVO>> getPrisonRoomWarderListByRoomId(String roomId) {
        if (StringUtil.isNullBlank(roomId)) {
            return new HashMap<>();
        }
        List<PrisonRoomWarderDO> selectObjs = prisonRoomWarderDao.selectList(new LambdaQueryWrapperX<PrisonRoomWarderDO>()
                .in(PrisonRoomWarderDO::getRoomId, roomId.split(",")));
        if (CollUtil.isNotEmpty(selectObjs)) {
            Map<String,List<PrisonRoomWarderRespVO>> map = selectObjs.stream().map(prisonRoomWarderDO -> BeanUtils.toBean(prisonRoomWarderDO, PrisonRoomWarderRespVO.class))
                    .collect(Collectors.groupingBy(PrisonRoomWarderRespVO::getRoomId, Collectors.toList()));
            return map;
        }
        return new HashMap<>();
    }

    @Override
    public List<PrisonRoomWarderRespVO> getListByRoomId(String orgCode, String roomId) {
        List<PrisonRoomWarderDO> list = prisonRoomWarderDao.selectList(new LambdaQueryWrapperX<PrisonRoomWarderDO>()
                .eq(PrisonRoomWarderDO::getOrgCode, orgCode)
                .eq(PrisonRoomWarderDO::getRoomId, roomId)
                .eq(PrisonRoomWarderDO::getStatus, "1"));
        if (CollUtil.isNotEmpty(list)) {
            return BeanUtils.toBean(list, PrisonRoomWarderRespVO.class);
        }
        return new ArrayList<>();
    }

    @Override
    public List<PrisonRoomWarderDO> getByRoomId(String orgCode, String roomId, String type) {
        return prisonRoomWarderDao.selectList(new LambdaQueryWrapperX<PrisonRoomWarderDO>()
                .eq(PrisonRoomWarderDO::getOrgCode, orgCode)
                .eq(PrisonRoomWarderDO::getRoomId, roomId)
                .eqIfPresent(PrisonRoomWarderDO::getUserType, type)
                .eq(PrisonRoomWarderDO::getStatus, "1"));
    }

    @Override
    public void deletePrisonRoomWarderByRoomId(String roomId) {
        prisonRoomWarderDao.deletePrisonRoomWarderByRoomId(roomId);
    }


}
