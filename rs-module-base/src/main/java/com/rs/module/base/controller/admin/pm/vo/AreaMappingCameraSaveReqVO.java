package com.rs.module.base.controller.admin.pm.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-监管管理-区域关联视频联网平台摄像头新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AreaMappingCameraSaveReqVO extends BaseVO{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("大数据平台区域编码")
    @NotEmpty(message = "大数据平台区域编码不能为空")
    private String bdAreaCode;

    @ApiModelProperty("大数据平台区域名称")
    @NotEmpty(message = "大数据平台区域名称不能为空")
    private String bdAreaName;

    @ApiModelProperty("大数据平台区域父类ID(acp_area_area.parent_id)")
    @NotEmpty(message = "大数据平台区域父类ID(acp_area_area.parent_id)不能为空")
    private String bdParentId;

    @ApiModelProperty("视频联网平台区域父类名称")
    //@NotEmpty(message = "视频联网平台区域父类名称不能为空")
    private String bdParentName;

    @ApiModelProperty("视频联网平台摄像头信息")
    @NotEmpty(message = "视频联网平台摄像头信息不能为空")
    private List<ChanInfo> chanInfoList;
    @Data
    public static class ChanInfo{
        @ApiModelProperty(value = "通道ID")
        private String chanId;

        @ApiModelProperty(value = "设备ID")
        private Long devId;

        @ApiModelProperty(value = "通道号")
        private Integer chanNo;

        @ApiModelProperty(value = "平台ID")
        private Integer platformId;

        @ApiModelProperty(value = "通道类型ID")
        private Integer chanTypeId;

        @ApiModelProperty(value = "通道名称")
        private String chanName;

        @ApiModelProperty(value = "树编码")
        private String treeCode;

        @ApiModelProperty(value = "树名称")
        private String treeName;

        @ApiModelProperty(value = "GB28181编码")
        private String gb28181Code;

        @ApiModelProperty(value = "默认码流类型")
        private Integer defaultStreamType;

        @ApiModelProperty(value = "正面/背面")
        private Integer facade;

        @ApiModelProperty(value = "通道名称缩写")
        private String chanNameAbbr;

        @ApiModelProperty(value = "通道名称拼音")
        private String chanNameSpell;

        @ApiModelProperty(value = "备注")
        private String remark;

        @ApiModelProperty(value = "存储类型")
        private Integer storageType;

        @ApiModelProperty(value = "用途类型")
        private Integer usageType;

        @ApiModelProperty(value = "控制类型")
        private Integer controlType;

        @ApiModelProperty(value = "状态")
        private Integer status;

        @ApiModelProperty(value = "是否重点关注")
        private Integer isFocus;

        @ApiModelProperty(value = "GPS经度")
        private String gpsX;

        @ApiModelProperty(value = "GPS纬度")
        private String gpsY;

        @ApiModelProperty(value = "海拔")
        private String altitude;

        @ApiModelProperty(value = "通道能力")
        private String chnAbility;

        @ApiModelProperty(value = "设备锁定状态")
        private Integer devLockStatus;

        @ApiModelProperty(value = "云台锁定状态")
        private Integer ptzLockStatus;

        @ApiModelProperty(value = "父路径名称")
        private String parentPathName;

        @ApiModelProperty(value = "通道ID（备用字段）")
        private Long channelId;

        @ApiModelProperty(value = "通道类型ID（备用字段）")
        private Integer chntypeId;

        @ApiModelProperty(value = "通道名称（备用字段）")
        private String chanNameBackup;

        @ApiModelProperty(value = "树编码（备用字段）")
        private String treeCodeBackup;

        @ApiModelProperty(value = "GB28181编码（备用字段）")
        private String gb28181CodeBackup;

        @ApiModelProperty(value = "默认码流类型（备用字段）")
        private Integer defaultStreamTypeBackup;

        @ApiModelProperty(value = "存储类型（备用字段）")
        private Integer storageTypeBackup;

        @ApiModelProperty(value = "用途类型（备用字段）")
        private Integer usageTypeBackup;

        @ApiModelProperty(value = "控制类型（备用字段）")
        private Integer controlTypeBackup;

        @ApiModelProperty(value = "平台名称")
        private String platformName;

        @ApiModelProperty(value = "IP地址")
        private String ip;

        @ApiModelProperty(value = "一对一编码")
        private String oneForOneCode;

        @ApiModelProperty(value = "固定实况URL")
        private String fixRealplayUrl;

        @ApiModelProperty(value = "移动类型")
        private Integer mobileType;

        @ApiModelProperty(value = "地图上显示")
        private String showOnMap;

        @ApiModelProperty(value = "警用设备ID")
        private String policeDevId;
        @ApiModelProperty("视频联网平台区域父类ID(acp_area_area.parent_id)")
        @NotEmpty(message = "视频联网平台区域父类ID(acp_area_area.parent_id)不能为空")
        private String spParentId;

        @ApiModelProperty("视频联网平台区域父类名称")
        @NotEmpty(message = "视频联网平台区域父类名称不能为空")
        private String spParentName;
    }
   /* @ApiModelProperty("视频联网平台区域编码")
    @NotEmpty(message = "视频联网平台区域编码不能为空")
    private String spAreaCode;

    @ApiModelProperty("视频联网平台区域名称")
    @NotEmpty(message = "视频联网平台区域名称不能为空")
    private String spAreaName;*/


    @ApiModelProperty("映射数据类型 area 区域映射,camera 摄像机")
    private String dataTypes="camera";
}
