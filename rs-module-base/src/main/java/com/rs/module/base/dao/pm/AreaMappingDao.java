package com.rs.module.base.dao.pm;

import com.rs.module.base.entity.pm.AreaMappingDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
* 实战平台-监管管理-区域映射 Dao
*
* <AUTHOR>
*/
@Mapper
public interface AreaMappingDao extends IBaseDao<AreaMappingDO> {


    @Delete("delete from acp_pm_area_mapping where bd_area_code=#{bdAreaCode} and sp_area_code=#{spAreaCode}")
    boolean deleteByBdSpAreaCode(@Param("bdAreaCode") String bdAreaCode, @Param("spAreaCode") String spAreaCode);
}
