package com.rs.module.acp.enums;

public enum DeviceTerminalTypeEnum {

    CNP(1,"仓内屏"),
    CWP(2,"仓外屏"),
    DJFJ(3,"对讲分机"),
    DJZJ(4,"对讲主机"),
    DPLDZJ(5,"大屏联动主机"),
    FWFZD(6,"防误放终端"),
    TDAJP(7,"筒道安检屏"),
    ;

    DeviceTerminalTypeEnum(Integer key, String value){
        this.key=key;
        this.value=value;
    }

    private Integer key;
    private String value;

    // 普通方法
    public static String getValue(Integer index) {
        for (DeviceTerminalTypeEnum c : DeviceTerminalTypeEnum.values()) {
            if (index.equals(c.getKey())) {
                return c.value;
            }
        }
        return null;
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

}
