package com.rs.module.acp.entity.pm;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 实战平台-监管管理-摄像机设备 DO
 *
 * <AUTHOR>
 */
@TableName("acp_pm_device_camera")
@KeySequence("acp_pm_device_camera_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "acp_pm_device_camera")
public class BaseDeviceCameraDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 设备编号
     */
    private String deviceId;
    /**
     * 通道编号
     */
    private String channelId;
    /**
     * 设备IP
     */
    private String deviceIp;
    /**
     * 摄像机类型（0枪机，1球机）
     */
    private Integer type;
    /**
     * 通道名称
     */
    private String channelName;
    /**
     * 是否视频联网同步过来 1-是
     */
    private Integer isVideo;
    /**
     * 数据来源 1 实战平台,4视频联网平台
     */
    private String dataSources;
}
