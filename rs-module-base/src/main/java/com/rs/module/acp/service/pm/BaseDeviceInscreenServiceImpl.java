package com.rs.module.acp.service.pm;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.module.acp.dao.pm.RsBaseDeviceInscreenDao;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenDO;
import com.rs.module.acp.enums.DeviceTerminalTypeEnum;
import com.rs.module.base.controller.admin.pm.vo.device.BaseDeviceSaveReqVO;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;
import com.rs.module.base.enums.DeviceTypeEnum;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.base.service.pm.AreaService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 实战平台-监管管理-仓内屏设备 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BaseDeviceInscreenServiceImpl extends BaseServiceImpl<RsBaseDeviceInscreenDao, BaseDeviceInscreenDO> implements BaseDeviceInscreenService {

    @Resource
    private RsBaseDeviceInscreenDao rsBaseDeviceInscreenDao;

    @Resource
    private AreaService areaService;

    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;

    @Override
    public BaseDeviceInscreenDO getBaseDeviceInscreen(String id) {
        return rsBaseDeviceInscreenDao.selectById(id);
    }

    @Override
    public List<BaseDeviceInscreenDO> getByRoomId(String roomId) {
        List<BaseDeviceInscreenDO> list = list(new LambdaQueryWrapper<BaseDeviceInscreenDO>().eq(BaseDeviceInscreenDO::getRoomId, roomId));
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public List<BaseDeviceInscreenDO> getByOrgCode(String orgCode) {
        List<BaseDeviceInscreenDO> list = list(new LambdaQueryWrapper<BaseDeviceInscreenDO>().eq(BaseDeviceInscreenDO::getOrgCode, orgCode));
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public BaseDeviceInscreenDO getCnpByRoomId(String roomId) {
        return lambdaQuery().eq(BaseDeviceInscreenDO::getRoomId, roomId)
                .eq(BaseDeviceInscreenDO::getDeviceType, DeviceTerminalTypeEnum.CNP.getKey())
                .one();
    }

    /**
     * 代码的搬运工，搬广州实战平台的
     * @param createReqVO
     * @param baseDevice
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateDeviceInfo(BaseDeviceSaveReqVO createReqVO, BaseDeviceDO baseDevice) {

        BaseDeviceInscreenDO terminalEntity = new BaseDeviceInscreenDO();
        setBaseDeviceInscreenInfo(createReqVO, baseDevice,  terminalEntity);

        // 先查询是否存在，存在更新，否则插入
        List<BaseDeviceInscreenDO> terminalEntityList =  this.list(new LambdaQueryWrapper<BaseDeviceInscreenDO>()
                .eq(BaseDeviceInscreenDO::getDeviceId, baseDevice.getId()));
        if (!terminalEntityList.isEmpty()) {

//            terminalEntity2 = terminalEntityList.get(0);
            // 对多余数据进行清理
//            List<String> collect = terminalEntityList.stream().skip(1).map(BaseDeviceInscreenDO::getId).collect(Collectors.toList());
//            if (CollectionUtil.isNotEmpty( collect)) {
//                super.removeByIds(collect);
//            }
            //更新数据
            for(BaseDeviceInscreenDO inscreenDO:terminalEntityList){
                setBaseDeviceInscreenInfo(createReqVO, baseDevice,  inscreenDO);
            }
            updateBatchById(terminalEntityList);

        }else {
            BaseDeviceInscreenDO terminalEntity2 = new BaseDeviceInscreenDO();
            setBaseDeviceInscreenInfo(createReqVO, baseDevice,  terminalEntity2);
            this.save(terminalEntity2);
        }

    }

    /**
     * 构建设备对象
     * @param createReqVO
     * @param baseDevice
     * @param terminalEntity
     */
    private void setBaseDeviceInscreenInfo(BaseDeviceSaveReqVO createReqVO, BaseDeviceDO baseDevice,  BaseDeviceInscreenDO terminalEntity){
        if (StrUtil.isNotBlank(createReqVO.getSerialNumber())) {
            terminalEntity.setSerialNumber(createReqVO.getSerialNumber().trim());
        }
        terminalEntity.setDeviceId(baseDevice.getId());
        terminalEntity.setDeviceIp(baseDevice.getIpAddress());
        terminalEntity.setDeviceNum(createReqVO.getDeviceNum());
        terminalEntity.setHostNum(createReqVO.getHostNum());
        //TODO 有待商榷，不知道哪里填充的，旧版也没看到前端传参
        terminalEntity.setAddressIp(createReqVO.getIpAddress());

        //仓内屏  仓外屏  对讲主机，对讲分机
        terminalEntity.setDeviceName(createReqVO.getDeviceName());
        terminalEntity.setDevUserName(createReqVO.getDevUserName());
        terminalEntity.setDevPassword(createReqVO.getDevPassword());
        if(ObjectUtil.isEmpty(terminalEntity.getRoomId()) && ObjectUtil.isNotEmpty(baseDevice.getRoomId())){
            terminalEntity.setRoomId(baseDevice.getRoomId());
        }


        if (DeviceTypeEnum.INDOOR_TERMINAL.getCode().equals(baseDevice.getDeviceTypeId())) {
            terminalEntity.setDeviceType(DeviceTerminalTypeEnum.CNP.getKey());
        } else if (DeviceTypeEnum.SCREEN_OUTSIDE_THE_WAREHOUSE.getCode().equals(baseDevice.getDeviceTypeId())) {
            terminalEntity.setDeviceType(DeviceTerminalTypeEnum.CWP.getKey());
        } else if (DeviceTypeEnum.INTERCOM_EXTENSION.getCode().equals(baseDevice.getDeviceTypeId())) {
            terminalEntity.setDeviceType(DeviceTerminalTypeEnum.DJFJ.getKey());
        } else if (DeviceTypeEnum.INTERCOM_HOST.getCode().equals(baseDevice.getDeviceTypeId())) {
            terminalEntity.setDeviceType(DeviceTerminalTypeEnum.DJZJ.getKey());
        } else if (DeviceTypeEnum.ANTI_MISOPERATION.getCode().equals(baseDevice.getDeviceTypeId())) {
            terminalEntity.setDeviceType(DeviceTerminalTypeEnum.FWFZD.getKey());
        } else if (DeviceTypeEnum.INTERCOM_TUODAO.getCode().equals(baseDevice.getDeviceTypeId())) {
            terminalEntity.setDeviceType(DeviceTerminalTypeEnum.TDAJP.getKey());
        }
    }

    /**
     * 根据条件获取仓内屏设备
     * @param condition String 查询条件(二维数组)
     * @param deviceType Integer 设备类型
     * @return List<BaseDeviceInscreenDO>
     */
    public List<BaseDeviceInscreenDO> getInscreenByCondition(String condition, Integer deviceType) {
    	List<BaseDeviceInscreenDO> inscreenList = new ArrayList<>();
    	String[][] condArr = JSON.parseObject(condition, String[][].class);
    	for(String[] conds : condArr) {
    		List<BaseDeviceInscreenDO> singleList = null;

    		//多层结构处理
    		if(conds.length > 2) {
    			singleList = rsBaseDeviceInscreenDao.getInscreenByCondition(null, conds[conds.length - 1], deviceType);
    		}

    		//其它结构处理
    		else {
    			String currentAreaId = conds[conds.length - 1];
    			List<String> areaIds = areaService.getAllChildAreaIds(currentAreaId);
    			areaIds.add(currentAreaId);
    			singleList = rsBaseDeviceInscreenDao.getInscreenByCondition(areaIds, null, deviceType);
    		}

    		inscreenList.addAll(singleList);
    	}

    	return inscreenList;
    }

    /**
     * 根据设备序列号获取仓内屏设备
     * @param serialNumber String 设备序列号
     * @return BaseDeviceInscreenDO
     */
    public BaseDeviceInscreenDO getInscreenBySeiralNumber(String serialNumber) {
    	return getOne(new LambdaQueryWrapper<BaseDeviceInscreenDO>()
    			.eq(BaseDeviceInscreenDO::getSerialNumber, serialNumber));
    }
}
