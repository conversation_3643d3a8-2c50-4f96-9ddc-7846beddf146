package com.rs.module.ihc.service.ipm;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.BizAssert;
import com.rs.module.base.service.bsp.BspBaseServiceImpl;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.dao.ipm.PrescribeDao;
import com.rs.module.ihc.dao.ipm.PrescribeMedicineDao;
import com.rs.module.ihc.entity.ipm.PrescribeDO;
import com.rs.module.ihc.entity.ipm.PrescribeMedicineDO;
import com.rs.module.ihc.enums.InternalMedicalPrescribeStatusEnum;
import com.rs.module.ihc.service.pm.MedicineService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * 所内就医-处方 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Log4j2
public class PrescribeServiceImpl extends BspBaseServiceImpl<PrescribeDao, PrescribeDO> implements PrescribeService {

    @Resource
    private PrescribeDao prescribeDao;
    @Resource
    private PrescribeMedicineDao prescribeMedicineDao;
    @Resource
    private MedicineService medicineService;


    @Autowired
    BspApi bspApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPrescribe(PrescribeSaveReqVO createReqVO) {
        // 插入
        createReqVO.setOutNum(0);
        createReqVO.setDispenseNum(0);
        createReqVO.setDoseNum(0);

        if (StringUtils.isBlank(createReqVO.getPrescribeNum())) {
            createReqVO.setPrescribeNum(getSerialNumber(null));
        }
        if (StringUtils.isBlank(createReqVO.getDoctorAdviceNum())) {
            createReqVO.setDoctorAdviceNum(getSerialNumberByRuleCode(null, "ihc_yz_no"));
        }

        PrescribeDO prescribe = BeanUtils.toBean(createReqVO, PrescribeDO.class);
        prescribeDao.insert(prescribe);


        // 插入子表
        createPrescribeMedicineList(prescribe.getId(), createReqVO.getMedicineList());
        // 返回
        return prescribe.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrescribe(PrescribeSaveReqVO updateReqVO) {
        // 校验存validatePrescribeExists(updateReqVO.getId());
        // 更新
        PrescribeDO updateObj = BeanUtils.toBean(updateReqVO, PrescribeDO.class);
        prescribeDao.updateById(updateObj);

        // 更新子表
        updatePrescribeMedicineList(updateReqVO.getId(), updateReqVO.getMedicineList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePrescribe(String id) {
        // 校验存在
        validatePrescribeExists(id);
        // 删除
        prescribeDao.deleteById(id);

        // 删除子表
        deletePrescribeMedicineByMlh(id);
    }

    private void validatePrescribeExists(String id) {
        if (prescribeDao.selectById(id) == null) {
            throw new ServerException("所内就医-处方数据不存在");
        }
    }

    @Override
    public PrescribeDO getPrescribe(String id) {
        return prescribeDao.selectById(id);
    }

    @Override
    public PageResult<PrescribeDO> getPrescribePage(PrescribePageReqVO pageReqVO) {
        return prescribeDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrescribeDO> getPrescribeList(PrescribeListReqVO listReqVO) {
        return prescribeDao.selectList(listReqVO);
    }


    // ==================== 子表（所内就医-处方-药方信息） ====================

    @Override
    public List<PrescribeMedicineDO> getPrescribeMedicineListByMlh(String mlh) {
        return prescribeMedicineDao.selectListByMlh(mlh);
    }

    @Override
    public void saveOrUpdatePrescribeFromType(Integer type, String businessId, String jgrybm, PrescribeSaveReqVO prescribeSaveReqVO) {

        int count = count(new LambdaQueryWrapper<PrescribeDO>()
                .eq(PrescribeDO::getBusinessId, businessId)
                .eq(PrescribeDO::getPrescribeType, type)
                .eq(PrescribeDO::getJgrybm, jgrybm));
        if (count > 0) {
            updatePrescribe(prescribeSaveReqVO);
        } else {
            prescribeSaveReqVO.setPrescribeType(type);
            prescribeSaveReqVO.setBusinessId(businessId);
            prescribeSaveReqVO.setJgrybm(jgrybm);
            createPrescribe(prescribeSaveReqVO);
        }
    }

    @Override
    public void prescribeStatusChange(PrescribeStatusChangeReqVO prescribeStatusChangeDTO) {
        String updateStatus = prescribeStatusChangeDTO.getPrescribeStatus();
        String prescribeId = prescribeStatusChangeDTO.getPrescribeId();
        PrescribeDO ihcsInternalMedicalPrescribe = this.getById(prescribeId);
        BizAssert.notNull(ihcsInternalMedicalPrescribe, "处方不存在");
        // 判断状态是否可以转换
        String sourceStatus = ihcsInternalMedicalPrescribe.getPrescribeStatus();
        InternalMedicalPrescribeStatusEnum.checkIsCanStatusChange(sourceStatus, updateStatus);
        // 特殊情况，当发药次数大于0，则不能修改状态为未使用
        Integer dispenseNum = Optional.ofNullable(ihcsInternalMedicalPrescribe.getDispenseNum()).orElse(0);
        if (dispenseNum > 0) {
            BizAssert.isFalse(InternalMedicalPrescribeStatusEnum.NO_USE.getCode().equals(updateStatus),
                    "处方已使用，状态无法变更为未使用");
        }
        this.lambdaUpdate()
                .set(PrescribeDO::getPrescribeStatus, updateStatus)
                .eq(PrescribeDO::getId, prescribeId)
                .update();
    }

    @Override
    public void autoClosePrisonerOutPrescribe() {
        List<String> filterPrescribeStatusList = InternalMedicalPrescribeStatusEnum.getTerminateStatusList();
        List<String> prescribeIds = this.getBaseMapper().listPrisonerOutPrescribe(filterPrescribeStatusList);
        if (CollectionUtils.isEmpty(prescribeIds)) {
            log.info("查询人员出所需要结束的处方为空");
            return;
        }
        log.info("查询人员出所需要结束的处方id：{}", prescribeIds);
        this.lambdaUpdate()
                .set(PrescribeDO::getPrescribeStatus, InternalMedicalPrescribeStatusEnum.FINISH.getCode())
                .in(PrescribeDO::getId, prescribeIds)
                .update();
    }

    @Override
    public void addPrescribe(PrescribeSaveReqVO saveInternalMedicalPrescribeDTO, String supervisedUserCode, String businessId, Integer type) {
        this.checkPrescribe(saveInternalMedicalPrescribeDTO, supervisedUserCode);
        SessionUser user = SessionUserUtil.getSessionUser();
        PrescribeDO prescribe = new PrescribeDO();
        BeanUtil.copyProperties(saveInternalMedicalPrescribeDTO, prescribe, "id");
        prescribe.setJgrybm(supervisedUserCode);
        prescribe.setPrescribeType(type);
        prescribe.setBusinessId(businessId);
        if (StringUtils.isBlank(saveInternalMedicalPrescribeDTO.getPrescribeNum())) {
            String cfNo = bspApi.executeByRuleCode("ihc_cf_no", null);
            if (StringUtils.isBlank(cfNo)) {
                cfNo = "CF" + System.currentTimeMillis();
            }
            prescribe.setPrescribeNum(cfNo);
        }
        if (StringUtils.isBlank(saveInternalMedicalPrescribeDTO.getDoctorAdviceNum())) {
            String yzNo = bspApi.executeByRuleCode("ihc_yz_no", null);
            if (StringUtils.isBlank(yzNo)) {
                yzNo = "YZ" + System.currentTimeMillis();
            }
            prescribe.setDoctorAdviceNum(yzNo);
        }
        prescribe.setPrescribeTime(new Date());
        prescribe.setPrescribeUserid(user.getId());
        prescribe.setPrescribeUserName(user.getName());
        prescribe.setPrescribeStatus(InternalMedicalPrescribeStatusEnum.NO_USE.getCode());
        prescribe.setDispenseNum(0);
        prescribe.setDoseNum(0);
        save(prescribe);
        String prescribeId = prescribe.getId();
        List<PrescribeMedicineSaveReqVO> medicineList = saveInternalMedicalPrescribeDTO.getMedicineList();
        if (CollectionUtil.isNotEmpty(medicineList)) {
            medicineList.stream()
                    .map(dto -> {
                        PrescribeMedicineDO medicine = new PrescribeMedicineDO();
                        medicine.setMlh(prescribeId);
                        medicine.setMedicineId(dto.getMedicineId());
                        medicine.setTotalInventoryNum(dto.getTotalInventoryNum());
                        medicine.setOneDosageNum(dto.getOneDosageNum());
                        medicine.setUnit(dto.getUnit());
                        medicine.setUseFrequency(dto.getUseFrequency());
                        medicine.setUseDay(dto.getUseDay());
                        medicine.setNum(dto.getNum());
                        medicine.setUseMedicineMethod(dto.getUseMedicineMethod());
                        medicine.setEntrust(dto.getEntrust());
                        return medicine;
                    }).forEach(prescribeMedicineDao::insert);
        }

    }

    @Override
    public List<PrescribeRespVO> getPrescribeList(String roomId) {
        return prescribeDao.getPrescribeList(roomId);
    }

    @Override
    public List<PrescribeRespVO> getPrescribeListByJgrybm(String jgrybm, String doctorAdviceType) {
        return prescribeDao.getPrescribeListByJgrybm(jgrybm, doctorAdviceType);
    }


    private void createPrescribeMedicineList(String mlh, List<PrescribeMedicineSaveReqVO> list) {
        list.forEach(o -> o.setMlh(mlh));
        list.forEach(o -> {
            prescribeMedicineDao.insert(BeanUtils.toBean(o, PrescribeMedicineDO.class));
        });
    }

    private void updatePrescribeMedicineList(String mlh, List<PrescribeMedicineSaveReqVO> list) {
        deletePrescribeMedicineByMlh(mlh);
        list.forEach(o -> o.setId(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createPrescribeMedicineList(mlh, list);
    }

    private void deletePrescribeMedicineByMlh(String mlh) {
        prescribeMedicineDao.deleteByMlh(mlh);
    }


    /**
     * 验证处方信息
     *
     * @param saveInternalMedicalPrescribeDTO 处方信息
     * @param supervisedUserCode              监管人员
     */
    private void checkPrescribe(PrescribeSaveReqVO saveInternalMedicalPrescribeDTO, String supervisedUserCode) {
        List<String> medicineIds = Optional.ofNullable(saveInternalMedicalPrescribeDTO.getMedicineList())
                .orElse(Collections.emptyList())
                .stream()
                .map(PrescribeMedicineSaveReqVO::getMedicineId)
                .collect(Collectors.toList());
        medicineService.checkMedicineHasNotBelongUser(medicineIds, supervisedUserCode);
    }

    @Override
    public String getRuleCode() {
        return "ihc_cf_no";
    }
}
