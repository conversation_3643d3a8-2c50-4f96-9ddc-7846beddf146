package com.rs.module.ihc.service.pm;

import cn.hutool.core.date.DateUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.BizAssert;
import com.rs.module.ihc.constant.IhcsDictionaryConstant;
import com.rs.module.ihc.controller.admin.ipm.bo.UpdateMedicineInInventoryNumBO;
import com.rs.module.ihc.controller.admin.ipm.dto.SaveMedicineOutDTO;
import com.rs.module.ihc.controller.admin.ipm.vo.PrescribeMedicineOutReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineOutListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineOutPageReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineOutSaveReqVO;
import com.rs.module.ihc.dao.pm.MedicineDao;
import com.rs.module.ihc.dao.pm.MedicineOutDao;
import com.rs.module.ihc.entity.pm.MedicineDO;
import com.rs.module.ihc.entity.pm.MedicineInDO;
import com.rs.module.ihc.entity.pm.MedicineOutDO;
import lombok.Data;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 药房管理-药品出库 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MedicineOutServiceImpl extends BaseServiceImpl<MedicineOutDao, MedicineOutDO> implements MedicineOutService {

    @Resource
    private MedicineOutDao medicineOutDao;
    @Resource
    private MedicineInService medicineInService;
    @Resource
    private MedicineService medicineService;
    @Resource
    private MedicineDao medicineDao;


    @Override
    public String createMedicineOut(MedicineOutSaveReqVO createReqVO) {
        // 插入
        MedicineOutDO medicineOut = BeanUtils.toBean(createReqVO, MedicineOutDO.class);
        medicineOutDao.insert(medicineOut);
        // 返回
        return medicineOut.getId();
    }

    @Override
    public void updateMedicineOut(MedicineOutSaveReqVO updateReqVO) {
        // 校验存在
        validateMedicineOutExists(updateReqVO.getId());
        // 更新
        MedicineOutDO updateObj = BeanUtils.toBean(updateReqVO, MedicineOutDO.class);
        medicineOutDao.updateById(updateObj);
    }

    @Override
    public void deleteMedicineOut(String id) {
        // 校验存在
        validateMedicineOutExists(id);
        // 删除
        medicineOutDao.deleteById(id);
    }

    private void validateMedicineOutExists(String id) {
        if (medicineOutDao.selectById(id) == null) {
            throw new ServerException("药房管理-药品出库数据不存在");
        }
    }

    @Override
    public MedicineOutDO getMedicineOut(String id) {
        return medicineOutDao.selectById(id);
    }

    @Override
    public PageResult<MedicineOutDO> getMedicineOutPage(MedicineOutPageReqVO pageReqVO) {
        return medicineOutDao.selectPage(pageReqVO);
    }

    @Override
    public List<MedicineOutDO> getMedicineOutList(MedicineOutListReqVO listReqVO) {
        return medicineOutDao.selectList(listReqVO);
    }

/**
 * 批量保存药品出库（基于外部传入的批次源数据）
 *
 * 关键逻辑说明：
 * 1) 校验：药品存在；每条明细的入库批次存在、归属当前药品、且未过期。
 * 2) 扣减：按明细累积扣减“药品总库存”和“对应批次库存”。
 *    - 同一批次在同一次请求中可能出现多条明细，使用 batchInventoryNumMap 对每个批次进行聚合扣减。
 * 3) 并发与一致性：先批次CAS更新，再药品CAS更新，最后落库出库记录；任一步失败抛错并回滚事务。
 *    - 批次CAS：where id=? and inventory_num=源值
 *    - 药品CAS：where id=? and total_inventory_num=源值
 * 4) 记录：为每条明细生成出库记录，记录出库后批次库存与药品总库存以及操作者。
 *
 * 注意：
 * - 本方法本身未加事务注解，但由上层 batchAutoMedicalOut、batchSaveMedicineOut 等带事务方法调用，整体在同一事务中执行。
 * - 若存在历史数据导致药品总库存与批次之和不一致，建议在此处补充 targetInventoryNum >= 0 的健壮性校验。
 *
 * @param medicineOutList 出库明细列表（包含 medicineInId、outNum 等）
 * @param medicineId      药品ID（所有明细必须属于该药品）
 * @param dataSourceIhcsMedicineIn 出库涉及的批次源数据快照（由上层预查询并传入）
 */

    @Override
    public void batchSaveMedicineOutBySourceData(List<SaveMedicineOutDTO> medicineOutList, String medicineId, List<MedicineInDO> dataSourceIhcsMedicineIn) {
        SessionUser user = SessionUserUtil.getSessionUser();
        MedicineDO ihcsMedicine = medicineService.getBaseMapper().selectById(medicineId);
        BizAssert.notNull(ihcsMedicine, String.format("药品不存在，id：%s", medicineId));
        Map<String, MedicineInDO> ihcsMedicineInMap = dataSourceIhcsMedicineIn.stream()
                .collect(Collectors.toMap(MedicineInDO::getId, Function.identity()));
        // 药品库存数量
        final BigDecimal sourceInventoryNum = ihcsMedicine.getTotalInventoryNum();
        BigDecimal targetInventoryNum = sourceInventoryNum;
        // 记录每个批次的扣减记录
        Map<String, UpdateMedicineInInventoryNumBO> batchInventoryNumMap = new LinkedHashMap<>();
        List<MedicineOutDO> ihcsMedicineOutList = new ArrayList<>();
        Date nowDate = new Date();
        for (SaveMedicineOutDTO saveMedicineOutDTO : medicineOutList) {
            String medicineInId = saveMedicineOutDTO.getMedicineInId();
            MedicineInDO ihcsMedicineIn = ihcsMedicineInMap.get(medicineInId);
            BizAssert.notNull(ihcsMedicineIn, "入库批次id[" + medicineInId + "]不存在");
            String batchCode = ihcsMedicineIn.getBatchCode();
            BizAssert.isTrue(Objects.equals(ihcsMedicineIn.getMedicineId(), medicineId),
                    "入库批次[" + batchCode + "]不属于该药品");
            long l = DateUtil.betweenDay(nowDate, ihcsMedicineIn.getExpireDate(), true);
            BizAssert.isTrue(l >= 0, "入库批次[" + batchCode + "]已过期，无法出库");
            // 出库数量（本次明细要扣减的数量）
            BigDecimal outNum = saveMedicineOutDTO.getOutNum();
            // 批次原库存数量（快照值，用于后续CAS的源库存对比）
            BigDecimal batchSourceInventoryNum = ihcsMedicineIn.getInventoryNum();
            // 药品总库存扣减（在内存中累计，最终再做CAS到数据库）
            targetInventoryNum = targetInventoryNum.subtract(outNum);
            // 这里是由于可能出现批量出库时，一个批次在一次请求中分两个记录来出
            // 为避免对同一批次多次相减导致判断困难，先在内存中以批次为Key聚合扣减
            UpdateMedicineInInventoryNumBO updateMedicineInInventoryNumBO =
                    batchInventoryNumMap.computeIfAbsent(medicineInId, key -> UpdateMedicineInInventoryNumBO.builder()
                            .id(medicineInId)
                            .sourceInventoryNum(batchSourceInventoryNum)
                            .targetInventoryNum(batchSourceInventoryNum)
                            .build());
            // 批次库存数量扣减（内存聚合后的目标库存，稍后通过CAS一次性落库）
            BigDecimal batchTargetInventoryNum = updateMedicineInInventoryNumBO.getTargetInventoryNum().subtract(outNum);
            BizAssert.isTrue(batchTargetInventoryNum.signum() >= 0, "批次[" + batchCode + "]出库数量不能大于库存数量");
            updateMedicineInInventoryNumBO.setTargetInventoryNum(batchTargetInventoryNum);
            // 组装出库记录：记录该条明细处理后批次/药品的“目标库存”与操作人
            MedicineOutDO ihcsMedicineOut = this.convertToIhcsMedicineOut(saveMedicineOutDTO);
            ihcsMedicineOut.setMedicineId(medicineId);
            ihcsMedicineOut.setInventoryNum(batchTargetInventoryNum);
            ihcsMedicineOut.setTotalInventoryNum(targetInventoryNum);
            ihcsMedicineOut.setUserid(user.getIdCard());
            ihcsMedicineOut.setUserName(user.getName());
            ihcsMedicineOutList.add(ihcsMedicineOut);
        }
        // 批次库存更新（先更新批次，避免药品总库存已改而批次失败导致数据不一致）
        // 使用CAS：仅当当前库存等于源库存时，才更新为目标库存，否则抛错回滚
        medicineInService.batchCompareAndSetInventoryNum(batchInventoryNumMap.values());
        // 药品库存更新（CAS：仅当总库存等于源值时，才更新为目标值；否则抛错回滚）
        medicineDao.compareAndSetMedicineInventoryNum(medicineId, sourceInventoryNum, targetInventoryNum);

        // 出库记录保存（在库存成功扣减后再落库，避免出现“只记账不扣量”的脏数据）
        this.saveBatch(ihcsMedicineOutList);
    }


    private MedicineOutDO convertToIhcsMedicineOut(SaveMedicineOutDTO saveMedicineOutDTO) {
        return MedicineOutDO.builder()
                .medicineInId(saveMedicineOutDTO.getMedicineInId())
                .outStorageReason(saveMedicineOutDTO.getOutStorageReason())
                .outNum(saveMedicineOutDTO.getOutNum())
                .dispensaryUnit(saveMedicineOutDTO.getDispensaryUnit())
                .dispensaryTime(saveMedicineOutDTO.getDispensaryTime())
                .remark(saveMedicineOutDTO.getRemark())
                .build();
    }

    /**
     * 获取药品的批次自动扣减批次信息
     * 每个药品都有对应的入库批次，按照先进先出的原则排列批次，
     * 扣减的数量可能有一个批次出库就够了，可能需要多个批次出库，这样就需要组装药品出库数量和批次的关系
     *
     * @param prescribeMedicineOutInfoList 药品扣减信息
     * @param remark                       备注信息
     */
    private List<MedicineOutInfo> getBatchAutoMedicineOutInfo(Collection<PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo> prescribeMedicineOutInfoList, String remark) {
        // 出现同一种药分两个记录出的话需要把出库数量合并比较批次的数量
        Collection<PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo> noDuplicateMedicineOutInfoList =
                prescribeMedicineOutInfoList.stream()
                        .collect(Collectors.toMap(PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo::getMedicineId,
                                Function.identity(), (a, b) -> {
                                    a.setOutNum(a.getOutNum().add(b.getOutNum()));
                                    return a;
                                }, LinkedHashMap::new))
                        .values();
        // 先确保药品的库存足够
        this.checkMedicineInventory(noDuplicateMedicineOutInfoList);

        // 说明：此处获取的是“每种药最早的未过期且有库存的批次”的快照映射，不会随着下方分配过程而动态扣减剩余量。
        // 若同一药品在本次处方中出现多条记录，且这些记录都命中同一个“最早批次”，在未维护本地批次剩余量的情况下，
        // 可能导致该批次被重复计划分配（多条记录分别判断都足够），从而出现“计划分配量之和”超过实际库存的风险（超分配）。
        // 建议：实际分配应基于“合并后的需求列表”驱动，或在内存维护 Map<批次ID, 剩余量> 随分配即时递减，避免重复分配同一批次。
        List<MedicineOutInfo> medicineOutInfoList = new ArrayList<>();
        // 取药时间
        Date nowDateTime = new Date();
        // 注意：这里遍历的是原始的 prescribeMedicineOutInfoList（可能包含同一药品的多条记录），与上方用于库存校验的“合并列表”不同。
        // 若不在内存中维护批次剩余量，且多条记录均先命中相同的最早批次，则存在将该批次重复分配的风险。
        for (PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo prescribeMedicineOutInfo : noDuplicateMedicineOutInfoList) {
            String medicineId = prescribeMedicineOutInfo.getMedicineId();
            // 获取该药“未过期且有库存”的批次，按过期时间升序（先进先出）
            List<MedicineInDO> ihcsMedicineIns = medicineInService.lambdaQuery()
                    .eq(MedicineInDO::getMedicineId, medicineId)
                    .ge(MedicineInDO::getExpireDate, LocalDate.now())
                    .gt(MedicineInDO::getInventoryNum, 0)
                    .orderByAsc(MedicineInDO::getExpireDate)
                    .list();
            // 本药需扣减总量（若前置已合并，同种药只会出现一次）
            BigDecimal needOutNum = prescribeMedicineOutInfo.getOutNum();
            final List<MedicineInDO> ihcsMedicineInSourceData = new ArrayList<>();
            final List<SaveMedicineOutDTO> saveMedicineOutDTOList = new ArrayList<>();
            for (MedicineInDO medicineIn : ihcsMedicineIns) {
                if (needOutNum.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }
                BigDecimal inventoryNum = medicineIn.getInventoryNum();
                String medicineInId0 = medicineIn.getId();
                ihcsMedicineInSourceData.add(medicineIn);
                // 当前批次库存足够：本批次即可满足余量
                if (inventoryNum.compareTo(needOutNum) >= 0) {
                    saveMedicineOutDTOList.add(this.createSaveMedicineOutDTO(needOutNum, nowDateTime,
                            medicineInId0, remark));
                    needOutNum = BigDecimal.ZERO;
                } else {
                    // 当前批次库存不足：先用完本批次，再继续下一个批次
                    saveMedicineOutDTOList.add(this.createSaveMedicineOutDTO(inventoryNum, nowDateTime,
                            medicineInId0, remark));
                    // 剩余待扣减数量
                    needOutNum = needOutNum.subtract(inventoryNum);
                }
            }
            // 组装自动分配得到的出库批次与对应的批次源数据（用于后续CAS）
            MedicineOutInfo medicineOutInfo = new MedicineOutInfo();
            medicineOutInfo.setMedicineId(medicineId);
            medicineOutInfo.setMedicineInList(ihcsMedicineInSourceData);
            medicineOutInfo.setMedicineOutList(saveMedicineOutDTOList);
            medicineOutInfoList.add(medicineOutInfo);
        }
        return medicineOutInfoList;
    }

    /**
     * 检测药品库存
     *
     * @param prescribeMedicineOutInfoList 出库药品信息
     */
    private void checkMedicineInventory(Collection<PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo> prescribeMedicineOutInfoList) {
        Map<String, MedicineDO> medicineInventoryMap = this.medicineService.getBaseMapper().selectBatchIds(
                        prescribeMedicineOutInfoList.stream()
                                .map(PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo::getMedicineId)
                                .collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(MedicineDO::getId, Function.identity()));

        for (PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo prescribeMedicineOutInfo : prescribeMedicineOutInfoList) {
            String id = prescribeMedicineOutInfo.getMedicineId();
            MedicineDO ihcsMedicine = medicineInventoryMap.get(id);
            BizAssert.notNull(ihcsMedicine, String.format("药品id[%s]不存在或者已删除", id));
            BigDecimal totalInventoryNum = ihcsMedicine.getTotalInventoryNum();
            BizAssert.isTrue(totalInventoryNum.compareTo(prescribeMedicineOutInfo.getOutNum()) >= 0, String.format("药品[%s]库存不足", ihcsMedicine.getMedicineName()));
        }
    }

    private SaveMedicineOutDTO createSaveMedicineOutDTO(BigDecimal outNum,
                                                        Date nowDateTime,
                                                        String medicineInId,
                                                        String remark) {
        // 取药单位
        String prisonName = SessionUserUtil.getSessionUser().getOrgName();
        return SaveMedicineOutDTO.builder()
                .outNum(outNum)
                .outStorageReason(IhcsDictionaryConstant.PRESCRIBE_USE_MEDICINE)
                .dispensaryTime(nowDateTime)
                .dispensaryUnit(prisonName)
                .medicineInId(medicineInId)
                .remark(remark)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAutoMedicalOut(Collection<PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo> prescribeMedicineOutInfoList,
                                    String remark) {
        // 判断并且获取药品的出库批次信息
        List<MedicineOutInfo> medicineOutInfoList = this.getBatchAutoMedicineOutInfo(prescribeMedicineOutInfoList, remark);
        // 进行库存扣减
        medicineOutInfoList.forEach(medicineOutInfo ->
                this.batchSaveMedicineOutBySourceData(medicineOutInfo.getMedicineOutList(),
                        medicineOutInfo.getMedicineId(), medicineOutInfo.getMedicineInList()));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveMedicineOut(List<SaveMedicineOutDTO> medicineOutList, String medicineId) {
        List<MedicineInDO> ihcsMedicineIns = medicineInService.lambdaQuery()
                .in(MedicineInDO::getId, medicineOutList.stream()
                        .map(SaveMedicineOutDTO::getMedicineInId)
                        .collect(Collectors.toList()))
                .list();
        this.batchSaveMedicineOutBySourceData(medicineOutList, medicineId, ihcsMedicineIns);
    }


    @Data
    private static class MedicineOutInfo {
        /**
         * 出库批次数据
         */
        private List<SaveMedicineOutDTO> medicineOutList;

        /**
         * 药品id
         */
        private String medicineId;

        /**
         * 药品入库信息
         */
        private List<MedicineInDO> medicineInList;
    }
}
