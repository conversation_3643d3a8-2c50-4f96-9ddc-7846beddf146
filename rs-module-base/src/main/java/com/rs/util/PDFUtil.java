package com.rs.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.module.pdf.enums.PdfTemplateEnum;
import lombok.extern.log4j.Log4j2;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName PDFUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/5 18:12
 * @Version 1.0
 */
@Log4j2
public class PDFUtil {

    public static byte[] getPdf(PdfTemplateEnum pdfTemplateEnum, Object json) {
        String templateName = pdfTemplateEnum.getFileName();
        String url = BspDbUtil.getParam("BSP_PRINT");
        log.info("url:{}", url);
//        String url = "http://*************:1912/com/form/pdf/getCustomPdf";
        Map<String, Object> paramMap = new HashMap<>();
        // 先尝试从本地 config/template 目录读取
        String filename = System.getProperty("user.dir") + File.separator + "config" + File.separator + "template" + File.separator + templateName;
        File fileToSend = new File(filename);
        if (!fileToSend.exists() || !fileToSend.isFile()) {
            // 本地不存在则回退到从 JAR 内 resources/template 读取
            try (java.io.InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/" + templateName)) {
                if (in != null) {
                    java.nio.file.Path tmp = java.nio.file.Files.createTempFile("pdf-template-", "-" + templateName);
                    java.nio.file.Files.copy(in, tmp, java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                    fileToSend = tmp.toFile();
                    fileToSend.deleteOnExit();
                    log.info("模板未在本地找到，已从classpath加载: {}", templateName);
                } else {
                    throw new RuntimeException("未找到模板文件: " + templateName + "（本地和classpath均不存在）");
                }
            } catch (Exception e) {
                throw new RuntimeException("读取模板失败: " + templateName, e);
            }
        }
        paramMap.put("file", fileToSend);
        JSONConfig jsonConfig = new JSONConfig();
        jsonConfig.setDateFormat("yyyy-MM-dd HH:mm:ss");
        String jsonStr = JSONUtil.toJsonStr(json, jsonConfig);
        log.info(jsonStr);
        paramMap.put("json", jsonStr);
        return HttpRequest.post(url)
                .form(paramMap)
                .execute()
                .bodyBytes();

    }
}
