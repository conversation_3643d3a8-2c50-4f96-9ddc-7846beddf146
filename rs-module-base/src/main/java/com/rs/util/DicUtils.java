package com.rs.util;

import com.bsp.common.cache.DicUtil;
import com.bsp.common.cache.RedisClient;
import com.bsp.common.cache.RedisEnum;
import com.bsp.common.cache.RedisUtils;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.util.http.HttpUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName DicUtils
 * <AUTHOR>
 * @Date 2025/6/24 15:39
 * @Version 1.0
 */
public class DicUtils {
    public static List<String> translate(String dicName, List<String> codes) {
        List<String> translate = DicUtil.translate(HttpUtils.getAppCode(), dicName, codes);
        if (translate == null || translate.isEmpty()) {
            translate = DicUtil.translate("bsp", dicName, codes);
        }
        return translate;
    }


    public static String translate(String dicName, String code) {
        String translate = DicUtil.translate(HttpUtils.getAppCode(), dicName, code);
        if (StringUtil.isEmpty(translate) || "null".equals(translate)) {
            translate = DicUtil.translate("bsp", dicName, code);
        }
        return translate;
    }


    public static <T> List<Map<String, Object>> translate(List<T> list, String[] dicNames, String[] dicFields) {
        List<Map<String, Object>> translate = DicUtil.translate(HttpUtils.getAppCode(), list, dicNames, dicFields);
        if (translate == null || translate.isEmpty()) {
            translate = DicUtil.translate("bsp", list, dicNames, dicFields);
        }
        return translate;
    }


    public static <T> List<Map<String, Object>> translate(List<T> list, String dicName, String dicField) {
        List<Map<String, Object>> translate = DicUtil.translate(list, HttpUtils.getAppCode(), dicName, dicField);
        if (translate == null || translate.isEmpty()) {
            translate = DicUtil.translate(list, "bsp", dicName, dicField);
        }
        return translate;
    }

    /**
     * 通过字典名称获取字典缓存数据
     * @param dicName String 字典名称
     * @return Map<String, String>
     */
    public static Map<String, String> getMap(String dicName) {
        String appCode = HttpUtils.getAppCode();
        String dicKey = RedisUtils.buildKey(RedisEnum.COM_DIC, StringUtils.isEmpty(appCode) ? "bsp" : appCode, dicName.toUpperCase());
    	if(!RedisClient.exists(dicKey)) {
    		dicKey = RedisUtils.buildKey(RedisEnum.COM_DIC, "bsp", dicName.toUpperCase());
    	}
    	return RedisClient.hgetAll(dicKey);
    }

    /**
     * 通过字典名称数组获取字典缓存数据
     * @param dicNames String[] 字典名称数组
     * @return List<Map<String, String>>
     */
    public static List<Map<String, String>> getMaps(String[] dicNames) {
    	List<Map<String, String>> result = new ArrayList<>();
    	for(String dicName : dicNames) {
    		result.add(getMap(dicName));
    	}
    	return result;
    }
}
