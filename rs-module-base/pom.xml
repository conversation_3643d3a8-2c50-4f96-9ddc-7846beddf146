<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.rs</groupId>
		<artifactId>rs-master</artifactId>
		<version>${rs.version}</version>
	</parent>

	<artifactId>rs-module-base</artifactId>
	<packaging>jar</packaging>
	<name>${project.artifactId}</name>
	<description>监所管理-基础模块，实现对人员、区域等基础数据的管理</description>

	<dependencies>

		<!-- 技术组件 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-starter-mybatis</artifactId>
		</dependency>
		<!-- 技术组件 end -->

		<!-- web 相关 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-starter-web</artifactId>
		</dependency>
		<!-- web 相关 end -->

		<!-- bsp 相关 begin -->
		<dependency>
			<groupId>com.bsp</groupId>
			<artifactId>bsp-common</artifactId>
			<exclusions>
				<exclusion>
					<groupId>cn.hutool</groupId>
		  			<artifactId>hutool-all-u</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.bsp</groupId>
			<artifactId>bsp-security</artifactId>
		</dependency>
		<dependency>
			<groupId>com.bsp</groupId>
			<artifactId>bsp-plus-sdk</artifactId>
		</dependency>
		<!-- bsp 相关 end -->


		<!-- DB 相关 begin -->
		<dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
		</dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId> <!-- 多数据源 -->
            <exclusions>
		        <exclusion>
		             <groupId>org.springframework.boot</groupId>
      				 <artifactId>spring-boot-starter</artifactId>
		        </exclusion>
		    </exclusions>
        </dependency>
        <dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
        <!-- DB 相关 end -->

		<!-- api组件 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-adapter-bsp-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-module-pam-api</artifactId>
		</dependency>
		<!-- api组件 end -->

		<!-- 工具类相关 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-common</artifactId>
		</dependency>
		<dependency>
		  	<groupId>org.bouncycastle</groupId>
 			<artifactId>bcprov-jdk15on</artifactId>
		  	<version>1.69</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-starter-oss</artifactId>
		</dependency>
		<!-- api组件 begin -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<groupId>org.junit.platform</groupId>
			<artifactId>junit-platform-launcher</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.vintage</groupId>
			<artifactId>junit-vintage-engine</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
		</dependency>
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-starter-pdf</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-expression</artifactId>
			<scope>provided</scope> <!-- 设置为 provided，只有工具类需要使用到 -->
		</dependency>
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-starter-job</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-starter-pdf</artifactId>
		</dependency>

		<!-- 生成条形码相关依赖 -->
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>3.4.1</version>
		</dependency>
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
			<version>3.4.1</version>
		</dependency>
	</dependencies>
</project>
