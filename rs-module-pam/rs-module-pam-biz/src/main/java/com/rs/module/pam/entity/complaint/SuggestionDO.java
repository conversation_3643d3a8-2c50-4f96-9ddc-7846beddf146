package com.rs.module.pam.entity.complaint;

import lombok.*;
import java.util.*;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;

/**
 * 监所事务管理-投诉建议 DO
 *
 * <AUTHOR>
 */
@TableName("pam_complaint_suggestion")
@KeySequence("pam_complaint_suggestion_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_complaint_suggestion")
public class SuggestionDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 监管人员姓名
     */
    private String jgryxm;
    /**
     * 监室ID
     */
    private String roomId;
    /**
     * 监室名称
     */
    private String roomName;
    /**
     * 投诉类型
     */
    private String complaintType;
    /**
     * 投诉内容
     */
    private String complaintContent;
    /**
     * 处理状态
     */
    private String handleStatus;
    /**
     * 处理人身份证号
     */
    private String handleUserSfzh;
    /**
     * 处理人姓名
     */
    private String handleUserName;
    /**
     * 处理反馈
     */
    private String handleFeedback;

    @TableField(exist = false)
    private String xb;

    @TableField(exist = false)
    private Integer age;

}
