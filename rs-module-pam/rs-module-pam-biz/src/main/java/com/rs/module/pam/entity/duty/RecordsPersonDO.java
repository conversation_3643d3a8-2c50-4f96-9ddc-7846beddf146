package com.rs.module.pam.entity.duty;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 监所事务管理-监室值班人员 DO
 *
 * <AUTHOR>
 */
@TableName("pam_duty_records_person")
@KeySequence("pam_duty_records_person_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_duty_records_person")
public class RecordsPersonDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 值班ID
     */
    private String dutyId;
    /**
     * 值班记录ID
     */
    private String recordsId;
    /**
     * 监管人员编号
     */
    private String jgrybm;
    /**
     * 值班类型
     */
    private String dutyType;

}
