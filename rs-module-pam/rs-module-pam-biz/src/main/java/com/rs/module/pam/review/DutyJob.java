package com.rs.module.pam.review;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.entity.pm.PrisonRoomWarderDO;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.base.service.pm.PrisonRoomWarderService;
import com.rs.module.pam.controller.admin.duty.vo.*;
import com.rs.module.pam.entity.duty.RoomAutoConfigDO;
import com.rs.module.pam.entity.duty.ShiftDO;
import com.rs.module.pam.service.duty.DutyService;
import com.rs.module.pam.service.duty.RecordsService;
import com.rs.module.pam.service.duty.RoomAutoConfigService;
import com.rs.module.pam.service.duty.ShiftService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 监室排班任务
 *
 */
@Component
@Slf4j
public class DutyJob {

    @Resource
    private DutyService dutyService;
    @Resource
    private RecordsService recordsService;
    @Resource
    private ShiftService shiftService;
    @Resource
    private RoomAutoConfigService roomAutoConfigService;
    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;
    @Resource
    private PrisonRoomWarderService roomWarderService;

    /**
     * 自动排班任务
     */
    @XxlJob("dutyJob")
    @Transactional
    public void dutyJob() {
        List<RoomAutoConfigDO> configDOList = roomAutoConfigService.list(new LambdaQueryWrapper<RoomAutoConfigDO>()
                .eq(RoomAutoConfigDO::getIsEnabled, 1));
        for (RoomAutoConfigDO roomAutoConfigDO : configDOList) {
            String orgCode = roomAutoConfigDO.getOrgCode();
            String roomId = roomAutoConfigDO.getRoomId();
            Date now = new Date();
            DateTime startDate = DateUtil.beginOfWeek(now);
            DateTime endDate = DateUtil.endOfWeek(now);
            DutyVO dutyVO = new DutyVO();
            XxlJobHelper.log(orgCode + "-" + roomId);
            try{
                List<DutyVO> dutyList = dutyService.autoShift(orgCode, roomId, startDate, endDate);
                if (CollectionUtil.isNotEmpty(dutyList)) {
                    dutyVO = dutyList.get(0);
                }
            } catch (Exception e) {
                XxlJobHelper.log("生成排班数据失败：" + e.getMessage());
                continue;
            }

            List<DutyListRespVO> dutyList = dutyVO.getDutyList();
            List<DutyPrisonerVO> personerList = dutyList.stream()
                    .flatMap(a -> a.getDutyInfo()
                            .stream()
                            .flatMap(b -> b.getPrisonerList().stream()))
                    .collect(Collectors.toList());

            if (personerList.size() > 0) {
                DutySaveVO dutySaveVO = new DutySaveVO();
                dutySaveVO.setOrgCode(orgCode);
                dutySaveVO.setRoomId(roomId);
                dutySaveVO.setDutyList(dutyList);
                try {
                    dutyService.create(dutySaveVO, true);
                } catch (Exception e) {
                    XxlJobHelper.log(orgCode + "-" + roomId + "排班添加失败：" + e.getMessage());
                }
                XxlJobHelper.log(orgCode + "-" + roomId + "排班生成成功！");
            }
        }
    }

    /**
     * 值班人数检测
     */
    @XxlJob("dutyNumDetectionJob")
    @Transactional
    public void dutyNumDetectionJob() {
        List<AreaPrisonRoomDO> roomList = areaPrisonRoomService.list();
        for (AreaPrisonRoomDO prisonRoomDO : roomList) {
            List<PrisonRoomWarderDO> warderList = roomWarderService.getByRoomId(prisonRoomDO.getOrgCode(), prisonRoomDO.getRoomCode(), null);
            if (CollectionUtil.isEmpty(warderList)) {
                continue;
            }
            List<ShiftDO> shiftList = shiftService.getEffectiveShift(prisonRoomDO.getOrgCode(), prisonRoomDO.getRoomCode());
            if (CollectionUtil.isEmpty(shiftList)) {
                continue;
            }
            DateTime tomorrow = DateUtil.beginOfDay(DateUtil.tomorrow());
            List<RecordsVO> recordsVOS = recordsService.selectList(prisonRoomDO.getOrgCode(), prisonRoomDO.getRoomCode(), tomorrow);
            Map<String, RecordsVO> map = recordsVOS.stream()
                    .collect(Collectors.toMap(
                            RecordsVO::getShiftId,
                            obj -> obj,
                            (existing, replacement) -> replacement // 保留最后一个对象
                    ));

            for (ShiftDO shiftDO : shiftList) {
                RecordsVO recordsVO = map.get(shiftDO.getId());
                if (ObjectUtil.isEmpty(recordsVO) || (shiftDO.getId().equals(recordsVO.getShiftId()) && recordsVO.getPersonList().size() < 2)) {
                    String tomorrowCh = DateUtil.format(tomorrow, "yyyy年MM月dd日");
                    String title = String.format("【监室值班安排提醒】：%s监室%s的监室值班安排人员不足，请进行手动排班。", prisonRoomDO.getRoomName(), tomorrowCh);
                    String content = String.format("【监室值班安排提醒】：%s监室%s的监室值班安排人员不足，请进行手动排班。", prisonRoomDO.getRoomName(), tomorrowCh);
                    String url = "/#/discipline/prisonRoomDuty";
                    List<ReceiveUser> receiveUserList = warderList.stream()
                            .flatMap(warder -> Stream.of(new ReceiveUser(warder.getPoliceSfzh(), warder.getOrgCode())))
                            .collect(Collectors.toList());
                    SendMessageUtil.sendAlertMsg(title, content, url, "acp", "admin", "系统管理员", "", "",
                            null, null, "pc", null, receiveUserList, "acp");
                    break;
                }
            }
        }
    }

}
