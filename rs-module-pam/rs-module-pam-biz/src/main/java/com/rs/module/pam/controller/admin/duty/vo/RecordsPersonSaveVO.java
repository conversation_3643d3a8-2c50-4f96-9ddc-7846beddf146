package com.rs.module.pam.controller.admin.duty.vo;

import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-值班人员新增/修改 VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RecordsPersonSaveVO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 监管人员编码
     */
    private String jgrybm;
    /**
     * 值班类型
     */
    private String dutyType;

    public RecordsPersonSaveVO(String jgrybm, String dutyType) {
        this.dutyType = dutyType;
        this.jgrybm = jgrybm;
    }

    public RecordsPersonSaveVO() {
    }
}
