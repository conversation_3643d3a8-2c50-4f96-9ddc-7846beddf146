package com.rs.module.pam.service.screen;

import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.controller.admin.screen.vo.JsswItemRespVO;
import com.rs.module.pam.entity.screen.CommonMeetingDO;
import com.rs.module.pam.entity.screen.FocusOnPersonnelDO;
import com.rs.module.pam.entity.screen.WgdjDO;

import java.util.List;

public interface GjLargeScreenService {

    // 获取羁押动态数据
    JSONObject getJydtJyrs(String code, String codeType);

    // 获取 风险人员分布数据
    JSONObject getFxryfb(String code, String codeType);

    // 获取 监室调整信息
    JSONObject getJydtJstz(String code, String codeType);

    int getWcdtTx(String code, String codeType);

    int getWcdtTj(String code, String codeType);

    int getWcdtLshj(String code, String codeType);

    int getWcdtJshj(String code, String codeType);

    int getWcdtSglshj(String code, String codeType);

    int getWcdtCsjy(String code, String codeType);

    PageResult<FocusOnPersonnelDO> getZdgzryDdgy(int pageNo, int pageSize, String code, String codeType);

    PageResult<FocusOnPersonnelDO> getZdgzryJjsy(int pageNo, int pageSize, String code, String codeType);

    PageResult<FocusOnPersonnelDO> getZdgzryLsgd(int pageNo, int pageSize, String code, String codeType);

    PageResult<FocusOnPersonnelDO> getZdgzryZdry(int pageNo, int pageSize, String code, String codeType);

    PageResult<FocusOnPersonnelDO> getZdgzryWjry(int pageNo, int pageSize, String code, String codeType);

    PageResult<FocusOnPersonnelDO> getZdgzryJrdq(int pageNo, int pageSize, String code, String codeType);

    PageResult<FocusOnPersonnelDO> getZdgzryZbh(int pageNo, int pageSize, String code, String codeType);

    PageResult<FocusOnPersonnelDO> getZdgzryFxry(int pageNo, int pageSize, String code, String codeType);

    PageResult<FocusOnPersonnelDO> getZdgzryAllPage(int pageNo, int pageSize, String code, String codeType, List<String> methodNames);

    JSONObject getTopInfo(String orgCode);

    PageResult<CommonMeetingDO> getTodayMeetingPage(int pageNo, int pageSize, String orgCode);

    List<JSONObject> getJsswItemCount(String code, String codeType);

    List<JsswItemRespVO> getJsswItemList(String code, String codeType, String busType);

    // 管教大屏获取外出动态所有数据
    JSONObject getWcdtAll(String code, String codeType, List<String> methodNames);

    JSONObject getZdgzryAllCount(String code, String codeType, List<String> methodNames);

    PageResult<WgdjDO> getWggjPage(int pageNo, int pageSize, String timeRange, String handleStatus, String code, String codeType);

    JSONObject getWggjAllCount(String timeRange, String code, String codeType);

    void test();
}
