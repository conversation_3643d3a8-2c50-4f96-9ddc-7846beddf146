package com.rs.module.pam.service.cook;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.pam.controller.admin.cook.vo.SpecialApplyDoctorApproveReqVO;
import com.rs.module.pam.controller.admin.cook.vo.SpecialApplyLeaderApproveReqVO;
import com.rs.module.pam.controller.admin.cook.vo.SpecialApplySaveReqVO;
import com.rs.module.pam.controller.app.vo.AppSpecialApplySaveSowReqVO;
import com.rs.module.pam.entity.cook.SpecialApplyDO;

import java.util.List;
import java.util.Map;

/**
 * 监所事务管理-特殊餐申请 Service 接口
 *
 * <AUTHOR>
 */
public interface SpecialApplyService extends IBaseService<SpecialApplyDO> {

    /**
     * 创建监所事务管理-特殊餐申请
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSpecialApply(SpecialApplySaveReqVO createReqVO);

    /**
     * 创建监所事务管理-特殊餐申请
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void createSpecialApplyBatch(List<SpecialApplySaveReqVO> createReqVO);

    /**
     * 更新监所事务管理-特殊餐申请
     *
     * @param updateReqVO 更新信息
     */
    void updateSpecialApply(SpecialApplySaveReqVO updateReqVO);

    /**
     * 验证是否已经存在申请
     * @param jgrybm
     */
    void validateExistingApplication(String jgrybm);
    /**
     * 删除监所事务管理-特殊餐申请
     *
     * @param id 编号
     */
    void deleteSpecialApply(String id);

    /**
     * 获得监所事务管理-特殊餐申请
     *
     * @param id 编号
     * @return 监所事务管理-特殊餐申请
     */
    SpecialApplyDO getSpecialApply(String id);

    /**
     * @param id
     * @param regStatus
     * @param actInstId
     * @return
     */
    Boolean updateProcessStatus(String id, String regStatus, String actInstId);

    /**
     * 医生审批
     *
     * @param approveReqVO
     */
    void doctorApprove(SpecialApplyDoctorApproveReqVO approveReqVO);

    /**
     * 领导审批
     *
     * @param approveReqVO
     */
    void leaderApprove(SpecialApplyLeaderApproveReqVO approveReqVO);

    /**
     * 根据监管人员编码查询特殊申请人员信息
     *
     * @param jgrybm
     */
    PrisonerVwRespVO getSpecialApplyPrisonerByJgrybm(String jgrybm);

    /**
     * 内屏被监管人员申请特殊餐
     */
    String applySpecialMeal(AppSpecialApplySaveSowReqVO saveReqVO);

    /**
     * 管教民警确认申请
     *
     * @param id      编号
     * @param success 状态
     */
    void confirmSpecialApply(String id, String success);

    /**
     * 获取配餐类型
     * @param jgrybms
     * @param mealPeriods
     * @return
     */
    List<Map<String, Object>> getMealType(List<String> jgrybms, String mealPeriods);
}
