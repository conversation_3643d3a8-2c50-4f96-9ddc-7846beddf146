package com.rs.module.pam.service.duty.day;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.duty.day.vo.DayDutyShiftSaveReqVO;
import com.rs.module.pam.controller.admin.duty.day.vo.DayShiftRespVO;
import com.rs.module.pam.controller.admin.duty.vo.ShiftRespVO;
import com.rs.module.pam.entity.duty.day.DayDutyShiftDO;

import java.util.List;

/**
 * 监所事务管理-值日班次 Service 接口
 *
 * <AUTHOR>
 */
public interface DayDutyShiftService extends IBaseService<DayDutyShiftDO>{

    /**
     * 监室值日-班次管理-保存编辑
     *
     * @param reqVO
     */
    void shiftManageSave(DayDutyShiftSaveReqVO reqVO);

    /**
     * 获取监室的值班班次
     * @param orgCode
     * @param roomId
     * @return
     */
    List<DayShiftRespVO> getShift(String orgCode, String roomId);

    /**
     * 监室值日班次数据初始化
     * @param orgCode
     * @param roomId
     * @return
     */
    List<DayDutyShiftDO> createDefaultShift(String orgCode, String roomId);

}
