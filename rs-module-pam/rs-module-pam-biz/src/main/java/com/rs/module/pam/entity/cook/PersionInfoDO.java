package com.rs.module.pam.entity.cook;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 监所事务管理-伙房人员信息 DO
 *
 * <AUTHOR>
 */
@TableName("pam_cook_persion_info")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_cook_persion_info")
public class PersionInfoDO extends BaseDO {
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 身份证号
     */
    private String sfz;
    /**
     * 联系电话
     */
    private String contactPhone;
    /**
     * 背景审查时间
     */
    private Date backgroundCheckTime;
    /**
     * 背景审查情况
     */
    private String backgroundCheckResult;
    /**
     * 取得健康证明日期
     */
    private Date healthCertDate;
    /**
     * 健康证明材料（可存储图片路径或其他说明）
     */
    private String healthCertMaterial;
    /**
     * 食品安全知识和技能培训时间
     */
    private Date foodSafetyTrainTime;
    /**
     * 食品安全知识和技能培训情况
     */
    private String foodSafetyTrainResult;
    /**
     * 登记时间
     */
    private Date operTime;

}
