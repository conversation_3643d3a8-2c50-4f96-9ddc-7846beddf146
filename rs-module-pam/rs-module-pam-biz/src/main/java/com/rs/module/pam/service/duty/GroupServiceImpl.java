package com.rs.module.pam.service.duty;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.gosun.zhjg.common.util.PinyinUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.pam.controller.admin.duty.vo.GroupSaveReqVO;
import com.rs.module.pam.controller.admin.duty.vo.GroupVO;
import com.rs.module.pam.dao.duty.DutyConfigDao;
import com.rs.module.pam.dao.duty.GroupDao;
import com.rs.module.pam.dao.duty.GroupPersonDao;
import com.rs.module.pam.entity.duty.DutyConfigDO;
import com.rs.module.pam.entity.duty.GroupDO;
import com.rs.module.pam.entity.duty.GroupPersonDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 监所事务管理-值班组 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GroupServiceImpl extends BaseServiceImpl<GroupDao, GroupDO> implements GroupService {

    @Resource
    private GroupDao groupDao;
    @Resource
    private GroupPersonDao groupPersonDao;
    @Resource
    private DutyConfigDao dutyConfigDao;
    @Resource
    private PrisonerService prisonerService;

    @Override
    public String createGroup(GroupSaveReqVO createReqVO) {
        DutyConfigDO configDO = dutyConfigDao.getByOrgCode(createReqVO.getOrgCode());
        List<String> jgrybmList = createReqVO.getJgrybmList();
        if (jgrybmList.size() > configDO.getGroupPersonNum()) {
            throw new RuntimeException("编组人员数量需要大于1且小于" + configDO.getGroupPersonNum());
        }
        // 查询出人员姓名
        List<PrisonerInDO> prinsonerInList = prisonerService.getPrisonerInOneList(jgrybmList);
        Map<String, String> prisonerNameMap = prinsonerInList.stream()
                .collect(Collectors.toMap(PrisonerInDO::getJgrybm, PrisonerInDO::getXm));
        String groupName = "";

        for (String jgrybm : jgrybmList) {
            // 校验人员编号是否存在
            String prisonName = prisonerNameMap.get(jgrybm);
            if (StringUtils.isBlank(prisonName)) {
                throw new RuntimeException(String.format("人员：%s不存在", jgrybm));
            }
            // 校验人员是否已经编组
            Integer count = groupPersonDao.selectCount(new LambdaQueryWrapper<GroupPersonDO>()
                    .eq(GroupPersonDO::getOrgCode, createReqVO.getOrgCode())
                    .eq(GroupPersonDO::getRoomId, createReqVO.getRoomId())
                    .eq(GroupPersonDO::getJgrybm, jgrybm));
            if (count > 0) {
                throw new RuntimeException(String.format("人员%s已编组", prisonerNameMap.get(jgrybm)));
            }
            // 获取人员姓名首字母，用于组成值班组名称
            groupName += PinyinUtils.getFirstSpell(String.valueOf(PinyinUtils.getFirstSpell(prisonName).charAt(0)).toUpperCase());
        }
        // 生成组序号
        Integer groupNo = groupDao.getRoomDutyGroupNo(createReqVO.getOrgCode(), createReqVO.getRoomId());
        if (groupNo == null) {
            groupNo = 1;
        } else {
            groupNo++;
        }
        // 生成组名
        if (StringUtils.isBlank(createReqVO.getGroupName())) {
            groupName = groupName + "值班组";
            // 检测是否组名重复。重复加1
            List<GroupDO> groupList = groupDao.getByGroupName(createReqVO.getOrgCode(), createReqVO.getRoomId(), groupName);
            if (!groupList.isEmpty()) {
                final String gName = groupName;
                List<String> groupNameList = groupList.stream().map(GroupDO::getGroupName).collect(Collectors.toList());
                OptionalLong optional = groupNameList.stream().map(m -> m.replaceFirst(gName, ""))
                        .filter(NumberUtils::isNumber).mapToLong(Long::valueOf).max();
                if (optional.isPresent()) {
                    groupName += optional.getAsLong() + 1;
                } else {
                    groupName += 2;
                }
            }
            createReqVO.setGroupName(groupName);
        }
        GroupDO groupDO = new GroupDO();
        groupDO.setOrgCode(createReqVO.getOrgCode());
        groupDO.setRoomId(createReqVO.getRoomId());
        groupDO.setGroupName(createReqVO.getGroupName());
        groupDO.setGroupNo(groupNo);
        groupDao.insert(groupDO);

        // 插入子表
        List<GroupPersonDO> groupPersonList = new ArrayList<>();
        for (String jgrybm : jgrybmList) {
            GroupPersonDO groupPersonDO = new GroupPersonDO();
            groupPersonDO.setGroupId(groupDO.getId());
            groupPersonDO.setRoomId(createReqVO.getRoomId());
            groupPersonDO.setJgrybm(jgrybm);
            groupPersonList.add(groupPersonDO);
        }
        groupPersonDao.insertBatch(groupPersonList);

        return groupDO.getId();
    }

    @Override
    public void deleteGroup(String id) {
        // 删除
        groupDao.deleteById(id);
        // 删除子表
        deleteGroupPerson(id);
    }

    public void deleteGroupPerson(String groupId) {
        groupPersonDao.delete(new LambdaQueryWrapper<GroupPersonDO>()
                .eq(GroupPersonDO::getGroupId, groupId));
    }

    @Override
    public List<GroupVO> selectList(String orgCode, String roomId) {
        List<GroupDO> groupDOList = groupDao.selectList(orgCode, roomId);
        return dataFilling(groupDOList);
    }

    @Override
    public List<GroupVO> selectList(String orgCode, String roomId, List<String> ids) {
        List<GroupDO> groupDOList = groupDao.selectList(orgCode, roomId, ids);
        return dataFilling(groupDOList);
    }

    private List<GroupVO> dataFilling(List<GroupDO> groupDOList) {
        List<GroupVO> list = BeanUtils.toBean(groupDOList, GroupVO.class);
        for (GroupVO groupVO : list) {
            List<GroupPersonDO> personList = groupPersonDao.getByGroupId(groupVO.getId());
            groupVO.setJgrybmList(personList.stream().map(person -> person.getJgrybm()).collect(Collectors.toList()));
        }
        return list;
    }

}
