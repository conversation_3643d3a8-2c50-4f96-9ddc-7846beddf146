package com.rs.module.pam.controller.admin.bed;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.pam.controller.admin.bed.vo.*;
import com.rs.module.pam.entity.bed.ConfigDO;
import com.rs.module.pam.entity.bed.PrisonerChangeDO;
import com.rs.module.pam.service.bed.ConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "床位管理-监室床位配置")
@RestController
@RequestMapping("/pam/bed/config")
@Validated
public class ConfigController {

    @Resource
    private ConfigService configService;
    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;

    @PostMapping("/create")
    @ApiOperation(value = "创建监室床位配置")
    public CommonResult<ConfigRespVO> createConfig(@Valid @RequestBody ConfigSaveReqVO createReqVO) {
        return success(configService.createConfig(createReqVO));
    }

    @PostMapping("/updateConfig")
    @ApiOperation(value = "更新监室床位自动排班配置")
    public CommonResult<Boolean> updateConfig(@Valid @RequestBody ConfigUpdateReqVO updateReqVO) {
        configService.update(new LambdaUpdateWrapper<ConfigDO>()
                .eq(ConfigDO::getOrgCode, updateReqVO.getOrgCode())
                .eq(ConfigDO::getRoomId, updateReqVO.getRoomId())
                .set(ConfigDO::getBedAutoConfig, updateReqVO.getBedAutoConfig()));
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除监室床位配置", hidden = true)
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteConfig(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           configService.deleteConfig(id);
        }
        return success(true);
    }

    @GetMapping("/getByRoomId")
    @ApiOperation(value = "获得监室床位配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号"),
            @ApiImplicitParam(name = "roomId", value = "监室编号")
    })
    @BusTrace(busType = BusTypeEnum.YEWU_CWGL, condition = "false", content = "{\"监室编号\":\"{{#roomId}}\"}")
    public CommonResult<ConfigRespVO> getByRoomId(@RequestParam("orgCode") String orgCode,
                                                  @RequestParam("roomId") String roomId) {
        return success(configService.getByRoomId(orgCode, roomId));
    }


    @GetMapping("/getPrisoner")
    @ApiOperation(value = "获得监室人员信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号"),
            @ApiImplicitParam(name = "roomId", value = "监室编号")
    })
    @BusTrace(busType = BusTypeEnum.YEWU_CWGL, condition = "false", content = "{\"监室编号\":\"{{#roomId}}\"}")
    public CommonResult<JSONObject> getPrisoner(@RequestParam("orgCode") String orgCode,
                                                @RequestParam("roomId") String roomId) {
        JSONObject prisoner = configService.getPrisoner(orgCode, roomId);
        return success(prisoner);
    }

    @PostMapping("/getChangePage")
    @ApiOperation(value = "获得人员床位调整记录")
    @BusTrace(busType = BusTypeEnum.YEWU_CWGL, condition = "false", content = "{\"监室编号\":\"{{#pageReqVO.roomId}}\"," +
            "\"添加时间\":\"{{#pageReqVO.addTime}}\"}")
    public CommonResult<List<ConfigRespVO>> getChangePage(@Valid @RequestBody PrisonerChangePageReqVO pageReqVO) {
        PageResult<PrisonerChangeDO> changePage = configService.getChangePage(pageReqVO);
        List<PrisonerChangeDO> changeList = changePage.getList();

        List<ConfigRespVO> list = new ArrayList<>();
        for(PrisonerChangeDO changeDO: changeList) {
            ConfigRespVO configRespVO = new ConfigRespVO();
            LayoutConfigVO changeLayoutConfig = JSONObject.parseObject(changeDO.getLayoutConfig(), LayoutConfigVO.class);
            configRespVO.setLayoutId(changeLayoutConfig.getId());
            configRespVO.setLayoutUrl(changeLayoutConfig.getLayoutUrl());
            configRespVO.setChangeUserName(changeDO.getAddUserName());
            configRespVO.setChangeDate(changeDO.getAddTime());
            // 关联床位信息与人员信息
            List<BedAreaConfigVO> bedAreaConfigList = changeLayoutConfig.getBedAreaConfigs();
            List<PrisonerBedDetailsVO> prisonerBedDetailsList = JSONObject.parseArray(changeDO.getPrisonerBedNo(), PrisonerBedDetailsVO.class);
            for (BedAreaConfigVO bedAreaConfig: bedAreaConfigList) {
                for (PrisonerBedDetailsVO bedDetails: bedAreaConfig.getBedList()) {
                    for (PrisonerBedDetailsVO prisonerBedDetails: prisonerBedDetailsList) {
                        if (bedDetails.getCwh().equals(prisonerBedDetails.getCwh())) {
                            bedDetails.setJgrybm(prisonerBedDetails.getJgrybm());
                            bedDetails.setJgryxm(prisonerBedDetails.getJgryxm());
                            bedDetails.setFrontPhoto(prisonerBedDetails.getFrontPhoto());
                            bedDetails.setEnterDay(prisonerBedDetails.getEnterDay());
                            bedDetails.setIsSick(prisonerBedDetails.getIsSick());
                            bedDetails.setRiskLevel(prisonerBedDetails.getRiskLevel());
                        }
                    }
                }
            }
            configRespVO.setPrisonerNum(prisonerBedDetailsList.size());
            configRespVO.setLayoutConfigs(bedAreaConfigList);
            // 未安排床位的人员信息
            List<PrisonerBedDetailsVO> notPlan = prisonerBedDetailsList.stream()
                    .filter(bedDetail -> StringUtil.isNullBlank(bedDetail.getCwh())).collect(Collectors.toList());
            configRespVO.setNotPlan(notPlan);
            list.add(configRespVO);
        }
        return success(list);
    }

    @PostMapping("/bedChange")
    @ApiOperation(value = "人员床位调整")
    @BusTrace(busType = BusTypeEnum.YEWU_CWGL, condition = "false", content = "{\"监室编号\":\"{{#saveReqVO.roomId}}\"," +
            "\"添加时间\":\"{{#pageReqVO.addTime}}\"}")
    public CommonResult bedChange(@Valid @RequestBody ChangeSaveRespVO saveReqVO) {
        configService.roomBedChange(saveReqVO);
        return success();
    }

    @RequestMapping(value = "/autoBedByConfig", method = RequestMethod.GET)
    @ApiOperation(value = "自动安排床位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号"),
            @ApiImplicitParam(name = "roomId", value = "监室编号")
    })
    public CommonResult<ConfigRespVO> autoBedByConfig(@RequestParam("orgCode") String orgCode,
                                        @RequestParam("roomId") String roomId) {
        ConfigRespVO changeRespVO = configService.autoBedByConfig(orgCode, roomId);
        return success(changeRespVO);
    }

    @RequestMapping(value = "/initialize", method = RequestMethod.GET)
    @ApiOperation(value = "床位初始化")
    public CommonResult<?> initialize() {
        List<AreaPrisonRoomDO> roomList = areaPrisonRoomService.list();
        List<ConfigDO> configList = configService.list();
        List<String> idList = configList.stream().map(config -> config.getRoomId()).collect(Collectors.toList());
        List<String> result = new ArrayList<>();
        for (AreaPrisonRoomDO prisonRoomDO : roomList) {
            if (idList.contains(prisonRoomDO.getId())) {
                continue;
            }
            try {
                configService.createDefaultConfig(prisonRoomDO.getOrgCode(), prisonRoomDO.getId());
                result.add(String.format("机构：%s，监室：%s床位初始化成功", prisonRoomDO.getOrgCode(), prisonRoomDO.getId()));
            } catch (Exception e) {
                result.add(e.getMessage());
            }
        }
        return success(result);
    }
}
