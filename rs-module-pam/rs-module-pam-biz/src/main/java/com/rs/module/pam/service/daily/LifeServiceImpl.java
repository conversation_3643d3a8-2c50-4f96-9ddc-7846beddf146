package com.rs.module.pam.service.daily;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.gosun.zhjg.common.util.StringUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.pam.controller.admin.daily.vo.*;
import com.rs.module.pam.dao.daily.LifeDao;
import com.rs.module.pam.dao.daily.LifeEventDao;
import com.rs.module.pam.dao.daily.LifeRoomDao;
import com.rs.module.pam.entity.daily.LifeDO;
import com.rs.module.pam.entity.daily.LifeEventDO;
import com.rs.module.pam.entity.daily.LifeHolidaysDO;
import com.rs.module.pam.entity.daily.LifeRoomDO;
import com.rs.module.pam.enums.DailyLifeCycleEnum;
import com.rs.module.pam.enums.DailyLifeStatusEnum;
import com.rs.module.pam.service.daily.bo.LifeEventBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 监所事务管理-一日生活制度 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class LifeServiceImpl extends BaseServiceImpl<LifeDao, LifeDO> implements LifeService {

    @Resource
    private LifeDao lifeDao;

    @Resource
    private LifeEventDao lifeEventDao;

    @Resource
    private LifeRoomDao lifeRoomDao;

    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;

    @Resource
    private LifeHolidaysService lifeHolidaysService;

    private static final String msgUrl = "/#/discipline/oneDaylifeSystem/oneDay?id=";

    private static final String defKey = "yirishenghuozhiduliuchengshenpi";

    private static SimpleDateFormat sdfYmdhms = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    //private static SimpleDateFormat sdfYmd = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createLife(LifeSaveReqVO createReqVO) {
        checkEventTime(createReqVO.getLifeEvents());
        // 是否依据roomId以管理的制度做其他处理， 暂定不处理
        List<LifeRoomDO> lifeRooms = createReqVO.getLifeRooms();
        // 插入
        LifeDO life = BeanUtils.toBean(createReqVO, LifeDO.class);
        // 自定义时间校验
        checkdefinedTime(life);

        life.setStatus(DailyLifeStatusEnum.DSH.getCode());
        lifeDao.insert(life);

        // 插入子表
        createLifeEventList(life.getId(), createReqVO.getLifeEvents());
        createLifeRoomList(life.getId(), lifeRooms);

        //启动流程审批
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", life.getId());
        JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode() + "-" + defKey, life.getId(),
                "生活制度流程审批", msgUrl + life.getId(), variables);
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            life.setActInstId(bpmTrail.getString("actInstId"));
            life.setTaskId(bpmTrail.getString("taskId"));
            lifeDao.updateById(life);
        } else {
            throw new ServerException("流程启动失败");
        }
        return life.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLife(LifeSaveReqVO updateReqVO) {
        // 校验存在
        validateLifeExists(updateReqVO.getId());
        checkEventTime(updateReqVO.getLifeEvents());
        // 更新
        LifeDO updateObj = BeanUtils.toBean(updateReqVO, LifeDO.class);
        lifeDao.updateById(updateObj);

        // 更新子表
        updateLifeEventList(updateReqVO.getId(), updateReqVO.getLifeEvents());
        updateLifeRoomList(updateReqVO.getId(), updateReqVO.getLifeRooms());
    }

    private void checkEventTime(List<LifeEventDO> lifeEvents) {
        if (CollectionUtil.isNotEmpty(lifeEvents)) {
            List<LifeEventBO> list = new ArrayList<>();
            for (LifeEventDO lifeEvent : lifeEvents) {
                LifeEventBO lifeEventBO = new LifeEventBO();
                String startTime = lifeEvent.getStartTime();
                try {
                    LocalTime.parse(startTime);
                } catch (Exception e) {
                    throw new RuntimeException("开始时间（" + startTime + "）格式不正确");
                }
                String[] s = startTime.split(":");
                lifeEventBO.setH1(Integer.parseInt(s[0]));
                lifeEventBO.setM1(Integer.parseInt(s[1]));
                lifeEventBO.setT1(lifeEventBO.getH1() * 100 + lifeEventBO.getM1());
                String endTime = lifeEvent.getEndTime();
                try {
                    LocalTime.parse(endTime);
                } catch (Exception e) {
                    throw new RuntimeException("结束时间（" + endTime + "）格式不正确");
                }
                String[] e = endTime.split(":");
                String dw;
                if (Objects.nonNull(lifeEvent.getEndTimeSpan()) && lifeEvent.getEndTimeSpan().intValue() == 1) {
                    dw = "次日";
                    lifeEventBO.setH2(Integer.parseInt(e[0]) + 24);
                } else {
                    dw = "当日";
                    lifeEventBO.setH2(Integer.parseInt(e[0]));
                }
                lifeEventBO.setM2(Integer.parseInt(e[1]));
                lifeEventBO.setT2(lifeEventBO.getH2() * 100 + lifeEventBO.getM2());
                if (lifeEventBO.getT1() >= lifeEventBO.getT2()) {
                    throw new RuntimeException("开始时间（" + startTime + "）不能大于结束时间（" + endTime + "）");
                }
                lifeEventBO.setSjd(startTime + "~" + dw + endTime);
                list.add(lifeEventBO);
            }
            //升序
            list.sort(Comparator.comparing(LifeEventBO::getT2));

            for (int i = 1; i < list.size(); i++) {
                LifeEventBO before = list.get(i - 1);
                LifeEventBO now = list.get(i);
                if (before.getT2() > now.getT1()) {
                    throw new RuntimeException("事务选项时间段有交叉：时间段1：" + before.getSjd() + "，时间段2：" + now.getSjd());
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLife(String id) {
        // 校验存在
        validateLifeExists(id);
        // 删除
        lifeDao.deleteById(id);

        // 删除子表
        deleteLifeEventByDailyLifeId(id);
        deleteLifeRoomByDailyLifeId(id);
    }

    private void validateLifeExists(String id) {
        if (lifeDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-一日生活制度数据不存在");
        }
    }

    private void validateLifeEventExists(String id) {
        if (lifeEventDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-一日生活制度事务项数据不存在");
        }
    }

    @Override
    public LifeDO getLife(String id) {
        return lifeDao.selectById(id);
    }

    @Override
    public PageResult<LifeDO> getLifePage(LifePageReqVO pageReqVO) {
        return lifeDao.selectPage(pageReqVO);
    }

    @Override
    public List<LifeDO> getLifeList(LifeListReqVO listReqVO) {
        return lifeDao.selectList(listReqVO);
    }


    // ==================== 子表（监所事务管理-一日生活制度关联事务） ====================

    @Override
    public List<LifeEventDO> getLifeEventListByDailyLifeId(String dailyLifeId) {
        return lifeEventDao.selectListByDailyLifeId(dailyLifeId);
    }

    private void createLifeEventList(String dailyLifeId, List<LifeEventDO> list) {
        list.forEach(o -> o.setDailyLifeId(dailyLifeId));
        list.forEach(o -> lifeEventDao.insert(o));
    }

    private void updateLifeEventList(String dailyLifeId, List<LifeEventDO> list) {
        deleteLifeEventByDailyLifeId(dailyLifeId);
        list.forEach(o -> o.setId(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createLifeEventList(dailyLifeId, list);
    }

    private void deleteLifeEventByDailyLifeId(String dailyLifeId) {
        lifeEventDao.deleteByDailyLifeId(dailyLifeId);
    }

    // ==================== 子表（监所事务管理-一日生活制度关联监室） ====================

    @Override
    public List<LifeRoomDO> getLifeRoomListByDailyLifeId(String dailyLifeId) {
        List<LifeRoomDO> lifeRoomDOS = lifeRoomDao.selectListByDailyLifeId(dailyLifeId);
        List<String> roomIds = lifeRoomDOS.stream().map(LifeRoomDO::getRoomId).collect(Collectors.toList());
        LambdaQueryWrapper<AreaPrisonRoomDO> roomQueryWrapper = Wrappers.lambdaQuery();
        roomQueryWrapper.select(AreaPrisonRoomDO::getId, AreaPrisonRoomDO::getRoomName).in(AreaPrisonRoomDO::getId, roomIds);
        List<AreaPrisonRoomDO> list = areaPrisonRoomService.list(roomQueryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            Map<String, String> roomMap = list.stream().collect(Collectors.toMap(AreaPrisonRoomDO::getId, AreaPrisonRoomDO::getRoomName, (k1, k2) -> k2));
            lifeRoomDOS.forEach(a -> a.setRoomName(roomMap.get(a.getRoomId())));
        }
        return lifeRoomDOS;
    }

    @Override
    public void updateLifeEvent(LifeEventSaveReqVO updateReqVO) {
        // 校验存在
        validateLifeEventExists(updateReqVO.getId());
        // 更新
        LifeEventDO updateObj = BeanUtils.toBean(updateReqVO, LifeEventDO.class);
        lifeEventDao.updateById(updateObj);
    }

    @Override
    public void approvalUpdateLife(ApprovalLifeSaveReqVO updateReqVO) {
        // 校验存在
        LifeDO lifeDO = lifeDao.selectById(updateReqVO.getId());
        if (lifeDO == null) {
            throw new ServerException("监所事务管理-一日生活制度数据不存在");
        }
        // 更新
        lifeDO.setStatus(updateReqVO.getStatus());
        lifeDO.setApprovalComments(updateReqVO.getApprovalComments());
        BspApproceStatusEnum bspApproceStatusEnum;
        if (StringUtils.equals(DailyLifeStatusEnum.SHTG.getCode(), updateReqVO.getStatus())) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
            lifeDO.setApprovalResult("" + BspApproceStatusEnum.PASSED_END.getCode());
        } else if (StringUtils.equals(DailyLifeStatusEnum.SHBTG.getCode(), updateReqVO.getStatus())) {
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
            lifeDO.setApprovalResult("" + BspApproceStatusEnum.NOT_PASSED_END.getCode());
        } else {
            throw new ServerException("非法审批状态：" + updateReqVO.getStatus());
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(lifeDO.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }

        lifeDO.setApproverXm(sessionUser.getName());
        lifeDO.setApproverSfzh(sessionUser.getIdCard());
        lifeDO.setApproverTime(new Date());
        if (StringUtils.isNotEmpty(updateReqVO.getApprovalAutograph())) {
            lifeDO.setApprovalAutograph(updateReqVO.getApprovalAutograph());
            lifeDO.setApprovalAutographTime(new Date());
        }
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", lifeDO.getId());
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ||
                BspApproceStatusEnum.NOT_PASSED_END.getCode() == bspApproceStatusEnum.getCode();
        JSONObject result = BspApprovalUtil.approvalProcess(HttpUtils.getAppCode() + "-" + defKey, lifeDO.getActInstId(), lifeDO.getTaskId(), lifeDO.getId(),
                bspApproceStatusEnum, updateReqVO.getApprovalComments(), null, null, terminateTask,
                variables, nowApproveUser, "acp");
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            lifeDO.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程启动失败");
        }
        lifeDao.updateById(lifeDO);
    }

    @Override
    public void updateOneLife(LifeOneSaveReqVO updateReqVO) {
        // 校验存在
        validateLifeExists(updateReqVO.getId());
        // 更新
        LifeDO updateObj = BeanUtils.toBean(updateReqVO, LifeDO.class);
        lifeDao.updateById(updateObj);
    }

    @Override
    public List<LifeEventDO> getLifeEvent(String roomId) {
        //获取 能匹配到今天的 且是最近审批通过的制度事务。
        //按审批时间降序  获取list
        List<LifeDO> lifeDOS = lifeDao.selectLifeListByRoomId(roomId);
        for (LifeDO lifeDO : lifeDOS) {
            // 1、判断当天是否在本周期内，在则返回，不在则判断下一个
            if (DailyLifeCycleEnum.MT.getCode().equals(lifeDO.getCycleSetting())) {
                log.info("========每天 lifeName:{}, roomId:{}", lifeDO.getName(), roomId);
                return lifeEventDao.selectListByDailyLifeId(lifeDO.getId());
            } else if (DailyLifeCycleEnum.ZDY.getCode().equals(lifeDO.getCycleSetting())) {
                // 自定义逻辑  区分自定义时间 和 自定义星期
                if (StringUtils.isEmpty(lifeDO.getCycleConfig())) {
                    // 自定义时间
                    String customizeTime = lifeDO.getCustomizeTime();
                    try {
                        String[] split = customizeTime.split("~");
                        long startTime = sdfYmdhms.parse(split[0]).getTime();
                        long nowTime = new Date().getTime();
                        long endTime = sdfYmdhms.parse(split[1]).getTime();
                        if (nowTime >= startTime && endTime >= nowTime) {
                            log.info("=========自定义---时间区间 lifeName:{}, roomId:{}", lifeDO.getName(), roomId);
                            return lifeEventDao.selectListByDailyLifeId(lifeDO.getId());
                        }
                    } catch (Exception e) {
                        log.error("=====getLifeEvent zdy error:", e);
                    }

                } else {
                    // 自定义星期
                    Calendar instance = Calendar.getInstance();
                    int week = instance.get(Calendar.DAY_OF_WEEK);
                    if (lifeDO.getCycleConfig().contains(String.valueOf(week))) {
                        log.info("=========自定义---星期 lifeName:{}, roomId:{}", lifeDO.getName(), roomId);
                        return lifeEventDao.selectListByDailyLifeId(lifeDO.getId());
                    }
                }
            } else {
                // 节假日逻辑
                //sdfYmd.parse(sdfYmdhms.format(new Date()))
                LocalDate localDate = LocalDate.now(); // 获取当前日期 yyyy-MM-dd
                Date nowDate = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant()); // 转换为Date类型
                List<LifeHolidaysDO> list = lifeHolidaysService.list(Wrappers.lambdaQuery(LifeHolidaysDO.class)
                        .eq(LifeHolidaysDO::getOrgCode, lifeDO.getOrgCode()).eq(LifeHolidaysDO::getHolidaysDate,
                                nowDate));
                if (CollectionUtil.isNotEmpty(list)) {
                    log.info("==========节假日 lifeName:{}, roomId:{}", lifeDO.getName(), roomId);
                    return lifeEventDao.selectListByDailyLifeId(lifeDO.getId());
                }
            }
        }
        return null;
    }

    private void createLifeRoomList(String dailyLifeId, List<LifeRoomDO> list) {
        list.forEach(o -> o.setDailyLifeId(dailyLifeId));
        list.forEach(o -> lifeRoomDao.insert(o));
    }

    private void updateLifeRoomList(String dailyLifeId, List<LifeRoomDO> list) {
        deleteLifeRoomByDailyLifeId(dailyLifeId);
        list.forEach(o -> o.setId(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createLifeRoomList(dailyLifeId, list);
    }

    private void deleteLifeRoomByDailyLifeId(String dailyLifeId) {
        lifeRoomDao.deleteByDailyLifeId(dailyLifeId);
    }

    // 自定义时间校验
    private void checkdefinedTime(LifeDO life) {
        if (DailyLifeCycleEnum.ZDY.getCode().equals(life.getCycleSetting())) {
            // 自定义逻辑  区分自定义时间 和 自定义星期
            if (StringUtils.isEmpty(life.getCycleConfig())) {
                // 自定义时间
                String customizeTime = life.getCustomizeTime();
                try {
                    String[] split = customizeTime.split("~");
                    sdfYmdhms.parse(split[0]);
                    sdfYmdhms.parse(split[1]);
                } catch (Exception e) {
                    throw new ServerException("自定义时间格式不正确");
                }
            } else {
                List<String> cnWeek = new ArrayList<>();
                if (StringUtils.isNotEmpty(life.getCycleConfig())) {
                    String[] weekArray = life.getCycleConfig().split(",");
                    for (String s : weekArray) {
                        if (Calendar.MONDAY == Integer.valueOf(s)) {
                            cnWeek.add("星期一");
                        } else if (Calendar.TUESDAY == Integer.valueOf(s)) {
                            cnWeek.add("星期二");
                        } else if (Calendar.WEDNESDAY == Integer.valueOf(s)) {
                            cnWeek.add("星期三");
                        } else if (Calendar.THURSDAY == Integer.valueOf(s)) {
                            cnWeek.add("星期四");
                        } else if (Calendar.FRIDAY == Integer.valueOf(s)) {
                            cnWeek.add("星期五");
                        } else if (Calendar.SATURDAY == Integer.valueOf(s)) {
                            cnWeek.add("星期六");
                        } else if (Calendar.SUNDAY == Integer.valueOf(s)) {
                            cnWeek.add("星期日");
                        } else {
                            throw new ServerException("非法星期");
                        }
                    }
                }
                life.setCustomizeTime(CollectionUtil.isEmpty(cnWeek) ? "" : CollectionUtil.join(cnWeek, "、"));
            }
        }
    }

}
