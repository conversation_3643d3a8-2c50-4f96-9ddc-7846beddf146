package com.rs.module.pam.service.bed;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.dto.OrgRespDTO;
import com.rs.enums.PrisonTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.dao.pm.AreaPrisonRoomDao;
import com.rs.module.base.dao.pm.PrisonerInDao;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.enums.BjgrylxEnum;
import com.rs.module.pam.controller.admin.bed.vo.*;
import com.rs.module.pam.dao.bed.ConfigDao;
import com.rs.module.pam.dao.bed.LayoutDao;
import com.rs.module.pam.dao.bed.PrisonerChangeDao;
import com.rs.module.pam.entity.bed.ConfigDO;
import com.rs.module.pam.entity.bed.LayoutDO;
import com.rs.module.pam.entity.bed.PrisonerChangeDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 监所事务管理-监室床位配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConfigServiceImpl extends BaseServiceImpl<ConfigDao, ConfigDO> implements ConfigService {

    @Resource
    private BspApi bspApi;
    @Resource
    private ConfigDao configDao;
    @Resource
    private LayoutDao layoutDao;
    @Resource
    private PrisonerChangeDao prisonerChangeDao;
    @Resource
    private PrisonerInDao prisonerInDao;
    @Resource
    private AreaPrisonRoomDao areaPrisonRoomDao;
    @Resource
    private PrisonerChangeService prisonerChangeService;

    @Override
    @Transactional
    public ConfigRespVO createConfig(ConfigSaveReqVO createReqVO) {
        LayoutDO layoutDO = layoutDao.selectById(createReqVO.getLayoutId());
        if ("02".equals(layoutDO.getLayoutType()) && layoutDO.getLayoutRow() < createReqVO.getLayoutRow()) {
            throw new ServerException("行数最大不能超过" + layoutDO.getLayoutRow());
        }
        if ("02".equals(layoutDO.getLayoutType()) && layoutDO.getLayoutColumn() < createReqVO.getLayoutColumn()) {
            throw new ServerException("列数最大不能超过" + layoutDO.getLayoutColumn());
        }
        // 插入
        ConfigDO config = BeanUtils.toBean(createReqVO, ConfigDO.class);
        config.setLayoutType(layoutDO.getLayoutType());
        config.setLayoutName(layoutDO.getLayoutName());

        String layoutConfig = layoutDO.getLayoutConfig();
        // 自定义床位动态生成床位号
        if ("02".equals(layoutDO.getLayoutType())) {
            List<JSONObject> configJson = JSONObject.parseArray(layoutConfig, JSONObject.class);
            List<BedAreaConfigVO> bedAreaConfigVOS = new ArrayList<>();
            List<PrisonerBedDetailsVO> bedList = new ArrayList<>();
            Integer layoutRow = createReqVO.getLayoutRow();
            Integer layoutColumn = createReqVO.getLayoutColumn();
            int cwh = 1;
            for (int i = 0; i < layoutRow; i++) {
                for (int j = 0; j < layoutColumn; j++) {
                    PrisonerBedDetailsVO bedDetailsVO = new PrisonerBedDetailsVO();
                    bedDetailsVO.setId(StringUtil.getGuid32());
                    bedDetailsVO.setCwh(cwh+"");
                    bedDetailsVO.setIsEntity(true);
                    bedDetailsVO.setRow(i);
                    bedDetailsVO.setColumn(j);
                    bedList.add(bedDetailsVO);
                    cwh++;
                }
            }
            BedAreaConfigVO bedAreaConfigVO = new BedAreaConfigVO();
            bedAreaConfigVO.setLayoutRow(layoutRow);
            bedAreaConfigVO.setLayoutColumn(layoutColumn);
            bedAreaConfigVO.setX1(configJson.get(0).getString("x1"));
            bedAreaConfigVO.setY1(configJson.get(0).getString("y1"));
            bedAreaConfigVO.setBedList(bedList);
            bedAreaConfigVOS.add(bedAreaConfigVO);
            layoutConfig = JSONObject.toJSONString(bedAreaConfigVOS);
        }
        config.setLayoutConfig(layoutConfig);
        AreaPrisonRoomDO prisonRoomDO = areaPrisonRoomDao.selectByRoomCode(createReqVO.getRoomId());
        config.setRoomName(prisonRoomDO.getRoomName());
        if (ObjectUtil.isEmpty(createReqVO.getId())) {
            config.setBedAutoConfig("01");
            configDao.insert(config);
        } else {
            // 校验存在
            validateConfigExists(createReqVO.getId());
            configDao.updateById(config);
        }

        ConfigRespVO configRespVO = BeanUtils.toBean(config, ConfigRespVO.class);
        configRespVO.setLayoutUrl(layoutDO.getLayoutImageUrl());
        // 关联床位信息与人员信息
        List<BedAreaConfigVO> bedAreaConfigVOS = JSONObject.parseArray(config.getLayoutConfig(), BedAreaConfigVO.class);
        List<PrisonerInDO> prisonerList = prisonerInDao.getByJsh(createReqVO.getOrgCode(), createReqVO.getRoomId());
        bedAreaConfigVOS = assembleBedPrisoner(bedAreaConfigVOS, prisonerList);
        resetCwh(prisonerList);

        configRespVO.setPrisonerNum(prisonerList.size());
        configRespVO.setLayoutConfigs(JSONObject.parseArray(JSONObject.toJSONString(bedAreaConfigVOS), BedAreaConfigVO.class));
        // 返回
        return configRespVO;
    }

    @Override
    public ConfigDO createDefaultConfig(String orgCode, String roomId) {
        List<LayoutDO> layoutDOS = layoutDao.selectList(new LambdaQueryWrapper<LayoutDO>()
                .eq(LayoutDO::getOrgCode, orgCode)
                .eq(LayoutDO::getLayoutType, "02"));
        if (CollectionUtil.isEmpty(layoutDOS)) {
            throw new ServerException(orgCode + ":没有默认床位信息，请联系运维人员");
        }
        AreaPrisonRoomDO prisonRoomDO = areaPrisonRoomDao.selectByRoomCode(orgCode, roomId);
        if (ObjectUtil.isEmpty(prisonRoomDO)) {
            throw new ServerException(roomId + ":监室数据不存在");
        }
        if (CollectionUtil.isNotEmpty(layoutDOS)) {
            LayoutDO layoutDO = layoutDOS.get(0);
            ConfigDO configDO = new ConfigDO();
            configDO.setOrgCode(orgCode);
            configDO.setRoomId(roomId);
            configDO.setRoomName(prisonRoomDO.getRoomName());
            configDO.setLayoutId(layoutDO.getId());
            configDO.setLayoutName(layoutDO.getLayoutName());
            configDO.setLayoutType(layoutDO.getLayoutType());
            configDO.setLayoutRow(layoutDO.getLayoutRow());
            configDO.setLayoutColumn(layoutDO.getLayoutColumn());
            List<BedAreaConfigVO> bedAreaConfigVOS = JSONObject.parseArray(layoutDO.getLayoutConfig(), BedAreaConfigVO.class);
            for (BedAreaConfigVO bedAreaConfigVO : bedAreaConfigVOS) {
                for (PrisonerBedDetailsVO bedDetailsVO : bedAreaConfigVO.getBedList()) {
                    bedDetailsVO.setId(StringUtil.getGuid32());
                }
            }
            configDO.setLayoutConfig(JSONObject.toJSONString(bedAreaConfigVOS));
            configDO.setLayoutType(layoutDO.getLayoutType());
            configDO.setBedAutoConfig("01");
            configDao.insert(configDO);
            return configDO;
        }
        return null;
    }

    @Override
    public void deleteConfig(String id) {
        // 校验存在
        validateConfigExists(id);
        // 删除
        configDao.deleteById(id);
    }

    @Override
    public void deleteConfig(String orgCode, String roomId) {
        configDao.delete(new LambdaQueryWrapper<ConfigDO>()
                .eq(ConfigDO::getOrgCode, orgCode)
                .eq(ConfigDO::getRoomId, roomId));
    }

    private void validateConfigExists(String id) {
        if (configDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-监室床位配置数据不存在");
        }
    }

    @Override
    public ConfigRespVO getByRoomId(String orgCode, String roomId) {
        ConfigDO config = getConfigByRoomId(orgCode, roomId);
        if (ObjectUtil.isEmpty(config)) {
            throw new ServerException("监室床位配置数据不存在");
        }
        ConfigRespVO configRespVO = BeanUtils.toBean(config, ConfigRespVO.class);
        // 获取床位布局信息
        LayoutDO layoutDO = layoutDao.selectById(config.getLayoutId());
        configRespVO.setLayoutType(layoutDO.getLayoutType());
        configRespVO.setLayoutUrl(layoutDO.getLayoutImageUrl());
        configRespVO.setLayoutWidth(layoutDO.getWidth());
        configRespVO.setLayoutHeight(layoutDO.getHeight());
        // 关联床位信息与人员信息
        List<PrisonerInDO> prisonerList = prisonerInDao.getByJsh(orgCode, roomId);
        List<BedAreaConfigVO> bedAreaConfigVOS = JSONObject.parseArray(config.getLayoutConfig(), BedAreaConfigVO.class);
        bedAreaConfigVOS = assembleBedPrisoner(bedAreaConfigVOS, prisonerList);

        configRespVO.setPrisonerNum(prisonerList.size());
        configRespVO.setLayoutConfigs(JSONObject.parseArray(JSONObject.toJSONString(bedAreaConfigVOS), BedAreaConfigVO.class));
        // 获取调整记录
        List<PrisonerChangeDO> list = prisonerChangeService.list(new LambdaQueryWrapper<PrisonerChangeDO>()
                .eq(PrisonerChangeDO::getOrgCode, orgCode)
                .eq(PrisonerChangeDO::getRoomId, roomId)
                .orderByDesc(PrisonerChangeDO::getAddTime));
        if (CollectionUtil.isNotEmpty(list)) {
            PrisonerChangeDO changeDO = list.get(0);
            configRespVO.setChangeDate(changeDO.getAddTime());
            configRespVO.setChangeUserName(changeDO.getAddUserName());
        }
        return configRespVO;
    }

    private ConfigDO getConfigByRoomId(String orgCode, String roomId) {
        ConfigDO config = configDao.getByRoomId(orgCode, roomId);
        List<PrisonerInDO> prisonerList = prisonerInDao.getByJsh(orgCode, roomId);
        if (ObjectUtil.isEmpty(config)) {
            try {
                config = createDefaultConfig(orgCode, roomId);
                resetCwh(prisonerList);
            } catch (Exception e) {
                System.out.println("已存在默认床位信息，无需创建");
                return configDao.getByRoomId(orgCode, roomId);
            }
        }
        return config;
    }

    private void resetCwh(List<PrisonerInDO> prisonerList) {
        logger.info("监室布局发生变化，重置监管人员床位号");
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        for (PrisonerInDO prisoner : prisonerList) {
            prisoner.setCwh("");
            if (PrisonTypeEnum.KSS.getValue().equals(sessionUser.getOrgType()) && BjgrylxEnum.ZYRY.getCode().equals(prisoner.getBjgrylx()))
                prisonerInDao.updateKssCwh(prisoner.getOrgCode(), prisoner.getJsh(), prisoner.getJgrybm(), "");
            if (PrisonTypeEnum.JLS.getValue().equals(sessionUser.getOrgType()) && BjgrylxEnum.ZJRY.getCode().equals(prisoner.getBjgrylx()))
                prisonerInDao.updateJlsCwh(prisoner.getOrgCode(), prisoner.getJsh(), prisoner.getJgrybm(), "");
            if (PrisonTypeEnum.JDS.getValue().equals(sessionUser.getOrgType()) && BjgrylxEnum.JDRY.getCode().equals(prisoner.getBjgrylx()))
                prisonerInDao.updateJdsCwh(prisoner.getOrgCode(), prisoner.getJsh(), prisoner.getJgrybm(), "");
        }
    }

    /**
     * 关联床位信息与人员信息
     * @param bedAreaConfigVOS
     * @param prisonerList
     */
    private List<BedAreaConfigVO> assembleBedPrisoner(List<BedAreaConfigVO> bedAreaConfigVOS, List<PrisonerInDO> prisonerList) {
        for (BedAreaConfigVO bedAreaConfigVO : bedAreaConfigVOS) {
            long count = bedAreaConfigVO.getBedList().stream().filter(bed -> bed.getIsEntity()).count();
            bedAreaConfigVO.setEntityBedCount(Long.valueOf(count).intValue());

            List<PrisonerBedDetailsVO> bedList = bedAreaConfigVO.getBedList();
            bedList.sort((o1, o2) -> {
                // 提取字符串中的数字部分
                int num1 = extractNumber(o1.getCwh());
                int num2 = extractNumber(o2.getCwh());

                // 优先比较数字部分
                int numCompare = Integer.compare(num1, num2);
                if (numCompare != 0) return numCompare;

                // 数字相同则按原始字符串比较
                return o1.getCwh().compareTo(o2.getCwh());
            });

            for (PrisonerBedDetailsVO bed : bedList) {
                for (PrisonerInDO prisonerInDO : prisonerList) {
                    if (bed.getCwh().equals(prisonerInDO.getCwh())) {
                        bed.setZjhm(prisonerInDO.getZjhm());
                        bed.setJgrybm(prisonerInDO.getJgrybm());
                        bed.setJgryxm(prisonerInDO.getXm());
                        bed.setFrontPhoto(prisonerInDO.getFrontPhoto());
                        // 计算入仓时间
                        Date addTime = ObjectUtil.isEmpty(prisonerInDO.getRssj()) ? new Date() : prisonerInDO.getRssj();
                        bed.setEnterDay(Math.toIntExact(DateUtil.betweenDay(addTime, new Date(), true)));
                        // 是否为重病号
                        bed.setIsSick(prisonerInDao.isSickByJgrybm(prisonerInDO.getJgrybm()));
                        // 风险等级
                        bed.setRiskLevel(prisonerInDO.getFxdj());
                    }
                }
            }
            bedAreaConfigVO.setBedList(bedList);
        }
        return bedAreaConfigVOS;
    }

    private static int extractNumber(String str) {
        // 移除所有非数字字符
        String numbers = str.replaceAll("\\D+", "");
        // 空字符串返回0防止报错
        return numbers.isEmpty() ? 0 : Integer.parseInt(numbers);
    }

    @Override
    public JSONObject getPrisoner(String orgCode, String roomId) {
        List<PrisonerInDO> prisonerList = prisonerInDao.getByJsh(orgCode, roomId);
        JSONObject data = new JSONObject();
        if (CollectionUtil.isEmpty(prisonerList)) {
            data.put("plan", new ArrayList<>());
            data.put("notPlan", new ArrayList<>());
            data.put("count", 0);
            return data;
        }

        ConfigDO configDO = getConfigByRoomId(orgCode, roomId);
        List<BedAreaConfigVO> bedAreaConfigVOS = JSONObject.parseArray(configDO.getLayoutConfig(), BedAreaConfigVO.class);
        List<String> cwhList = bedAreaConfigVOS.stream()
                .flatMap(a -> a.getBedList().stream())
                .map(PrisonerBedDetailsVO::getCwh)
                .collect(Collectors.toList());

        List<PrisonerBedDetailsVO> list = BeanUtils.toBean(prisonerList, PrisonerBedDetailsVO.class);
        list.forEach(prisoner -> prisoner.setJgryxm(prisoner.getXm()));
        List<PrisonerBedDetailsVO> plan = list.stream().filter(
                prisoner -> ObjectUtil.isNotEmpty(prisoner.getCwh()) && cwhList.contains(prisoner.getCwh())).collect(Collectors.toList());
        List<PrisonerBedDetailsVO> notPlan = list.stream().filter(
                prisoner -> ObjectUtil.isEmpty(prisoner.getCwh()) || !cwhList.contains(prisoner.getCwh())).collect(Collectors.toList());

        for  (PrisonerBedDetailsVO prisoner : notPlan) {
            prisoner.setIsSick(prisonerInDao.isSickByJgrybm(prisoner.getJgrybm()));
            prisoner.setRiskLevel(prisoner.getFxdj());
        }

        data.put("plan", plan);
        data.put("notPlan", notPlan);
        data.put("count", list.size());
        return data;
    }

    @Override
    public PageResult<PrisonerChangeDO> getChangePage(PrisonerChangePageReqVO pageReqVO) {
        return prisonerChangeDao.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void roomBedChange(ChangeSaveRespVO saveReqVO) {
        OrgRespDTO org = bspApi.getOrgByCode(saveReqVO.getOrgCode());
        ConfigDO configDO = getConfigByRoomId(saveReqVO.getOrgCode(), saveReqVO.getRoomId());
        LayoutDO layoutDO = layoutDao.selectById(configDO.getLayoutId());
        List<BedAreaConfigVO> bedAreaConfigs = saveReqVO.getLayoutConfigs();
        List<BedAreaConfigVO> changeLayoutConfigs = new ArrayList<>();
        List<PrisonerInDO> prisonerInList = prisonerInDao.getByJsh(saveReqVO.getOrgCode(), saveReqVO.getRoomId());

        // 将提交的床位信息与数据库中的床位信息进行对比并组装
        for (BedAreaConfigVO bedAreaConfigVO : bedAreaConfigs) {
            BedAreaConfigVO layoutConfig = new BedAreaConfigVO();
            layoutConfig.setId(bedAreaConfigVO.getId());
            layoutConfig.setX1(bedAreaConfigVO.getX1());
            layoutConfig.setY1(bedAreaConfigVO.getY1());
//            layoutConfig.setLayoutRow(bedAreaConfigVO.getLayoutRow());
//            layoutConfig.setLayoutColumn(bedAreaConfigVO.getLayoutColumn());
            layoutConfig.setBedType(bedAreaConfigVO.getBedType());
            layoutConfig.setIsAllowedAdd(bedAreaConfigVO.getIsAllowedAdd());
            layoutConfig.setAddLocation(bedAreaConfigVO.getAddLocation());
            List<PrisonerBedDetailsVO> bedDetailList = new ArrayList<>();

            Map<String, PrisonerBedDetailsVO> layoutMap = bedAreaConfigVO.getBedList().stream()
                    .collect(Collectors.toMap(
                            PrisonerBedDetailsVO::getCwh,
                            obj -> obj,
                            (existing, replacement) -> replacement
                    ));

            // 记录所有床位信息
            for (String cwh : layoutMap.keySet()) {
                PrisonerBedDetailsVO bedDetail = new PrisonerBedDetailsVO();
                PrisonerBedDetailsVO newBedDetail = layoutMap.get(cwh);
                bedDetail.setId(bedAreaConfigVO.getId());
                bedDetail.setCwh(cwh);
                bedDetail.setRow(newBedDetail.getRow());
                bedDetail.setColumn(newBedDetail.getColumn());
                bedDetail.setIsEntity(newBedDetail.getIsEntity());
                bedDetail.setEnterDay(newBedDetail.getEnterDay());
                bedDetail.setIsSick(newBedDetail.getIsSick());
                bedDetail.setRiskLevel(newBedDetail.getRiskLevel());
                bedDetailList.add(bedDetail);
            }

            layoutConfig.setBedList(bedDetailList);
            layoutConfig.setEntityBedCount(Long.valueOf(bedDetailList.stream().filter(bed -> bed.getIsEntity()).count()).intValue());
            changeLayoutConfigs.add(layoutConfig);
        }

        // 修改各所人员床位号
        List<PrisonerBedDetailsVO> AllBedList = bedAreaConfigs.stream()
                .flatMap(obj1 -> obj1.getBedList().stream())
                .collect(Collectors.toList());
        List<JSONObject> prisonerChwUpdate = new ArrayList<>();
        for (PrisonerInDO prisonerInDO : prisonerInList) {
            JSONObject update = new JSONObject();
            update.put("jgrybm", prisonerInDO.getJgrybm());
            update.put("jgryxm", prisonerInDO.getXm());
            update.put("frontPhoto", prisonerInDO.getFrontPhoto());
            String updateCwh = "";

            for (PrisonerBedDetailsVO bedDetailsVO : AllBedList) {
                if (prisonerInDO.getJgrybm().equals(bedDetailsVO.getJgrybm())) {
                    updateCwh = bedDetailsVO.getCwh();
                    break;
                }
            }
            if (PrisonTypeEnum.KSS.getValue().equals(org.getOrgType()) && BjgrylxEnum.ZYRY.getCode().equals(prisonerInDO.getBjgrylx()))
                prisonerInDao.updateKssCwh(saveReqVO.getOrgCode(), saveReqVO.getRoomId(), prisonerInDO.getJgrybm(), updateCwh);
            if (PrisonTypeEnum.JLS.getValue().equals(org.getOrgType()) && BjgrylxEnum.ZJRY.getCode().equals(prisonerInDO.getBjgrylx()))
                prisonerInDao.updateJlsCwh(saveReqVO.getOrgCode(), saveReqVO.getRoomId(), prisonerInDO.getJgrybm(), updateCwh);
            if (PrisonTypeEnum.JDS.getValue().equals(org.getOrgType()) && BjgrylxEnum.JDRY.getCode().equals(prisonerInDO.getBjgrylx()))
                prisonerInDao.updateJdsCwh(saveReqVO.getOrgCode(), saveReqVO.getRoomId(), prisonerInDO.getJgrybm(), updateCwh);
            update.put("cwh", updateCwh);
            prisonerChwUpdate.add(update);
        }

        // 修改监室床位配置，会新增临时床位
        configDO.setLayoutConfig(JSONObject.toJSONString(changeLayoutConfigs));
        configDao.updateById(configDO);
        // 新增床位调整记录
        PrisonerChangeDO changeDO = new PrisonerChangeDO();
        LayoutConfigVO layoutConfigVO = new LayoutConfigVO();
        layoutConfigVO.setId(layoutDO.getId());
        layoutConfigVO.setLayoutUrl(layoutDO.getLayoutImageUrl());
        layoutConfigVO.setBedAreaConfigs(changeLayoutConfigs);
        changeDO.setLayoutConfig(JSONObject.toJSONString(layoutConfigVO));
        changeDO.setPrisonerBedNo(JSONObject.toJSONString(prisonerChwUpdate));
        changeDO.setOrgCode(saveReqVO.getOrgCode());
        changeDO.setRoomId(saveReqVO.getRoomId());
        prisonerChangeDao.insert(changeDO);
    }

    @Override
    public ConfigRespVO autoBedByConfig(String orgCode, String roomId) {
        ConfigDO configDO = getConfigByRoomId(orgCode, roomId);
        LayoutDO layoutDO = layoutDao.selectById(configDO.getLayoutId());
//        List<PrisonerInDO> prisonerInList = prisonerInDao.getNotPlanCwh(orgCode, roomId);
        List<PrisonerInDO> prisonerInList = prisonerInDao.getByJsh(orgCode, roomId);
        List<PrisonerInDO> notPlanList = prisonerInList.stream().filter(prisoner ->
                StringUtil.isNullBlank(prisoner.getCwh())).collect(Collectors.toList());
        List<String> planCwh = prisonerInList.stream()
                .filter(prisoner -> StringUtil.isNotEmpty(prisoner.getCwh()))
                .map(prisoner -> prisoner.getCwh()).collect(Collectors.toList());
        List<BedAreaConfigVO> bedAreaConfigs = JSONObject.parseArray(configDO.getLayoutConfig(), BedAreaConfigVO.class);
        assembleBedPrisoner(bedAreaConfigs, prisonerInList);

        switch (configDO.getBedAutoConfig()){
            case "01":
                // 随机排序
                Collections.shuffle(notPlanList);
                for (BedAreaConfigVO bedAreaConfig : bedAreaConfigs) {
                    for (PrisonerBedDetailsVO bedDetail : bedAreaConfig.getBedList()) {
                        if (planCwh.contains(bedDetail.getCwh())) {
                            continue;
                        }
                        for (PrisonerInDO prisonerInDO : notPlanList) {
                            bedDetail.setZjhm(prisonerInDO.getZjhm());
                            bedDetail.setJgrybm(prisonerInDO.getJgrybm());
                            bedDetail.setJgryxm(prisonerInDO.getXm());
                            bedDetail.setFrontPhoto(prisonerInDO.getFrontPhoto());
                            // 计算入仓时间
                            Date addTime = ObjectUtil.isEmpty(prisonerInDO.getRssj()) ? new Date() : prisonerInDO.getRssj();
                            bedDetail.setEnterDay(Math.toIntExact(DateUtil.betweenDay(addTime, new Date(), true)));
                            // 是否为重病号
                            bedDetail.setIsSick(prisonerInDao.isSickByJgrybm(prisonerInDO.getJgrybm()));
                            // 风险等级
                            bedDetail.setRiskLevel(prisonerInDO.getFxdj());
                            break;
                        }
                        if (notPlanList.size() > 0) {
                            notPlanList.remove(0);
                        }
                    }
                }
        }

        List<PrisonerBedDetailsVO> prisonerBedList = new ArrayList<>();
        for  (PrisonerInDO prisoner : notPlanList) {
            PrisonerBedDetailsVO prisonerBedDetailsVO = new PrisonerBedDetailsVO();
            prisonerBedDetailsVO.setZjhm(prisoner.getZjhm());
            prisonerBedDetailsVO.setJgrybm(prisoner.getJgrybm());
            prisonerBedDetailsVO.setJgryxm(prisoner.getXm());
            prisonerBedDetailsVO.setFrontPhoto(prisoner.getFrontPhoto());
            // 计算入仓时间
            Date addTime = ObjectUtil.isEmpty(prisoner.getRssj()) ? new Date() : prisoner.getRssj();
            prisonerBedDetailsVO.setEnterDay(Math.toIntExact(DateUtil.betweenDay(addTime, new Date(), true)));
            // 是否为重病号
            prisonerBedDetailsVO.setIsSick(prisonerInDao.isSickByJgrybm(prisoner.getJgrybm()));
            // 风险等级
            prisonerBedDetailsVO.setRiskLevel(prisoner.getFxdj());
            prisonerBedList.add(prisonerBedDetailsVO);
        }
        ConfigRespVO configRespVO = BeanUtils.toBean(configDO, ConfigRespVO.class);// 获取床位布局信息
        configRespVO.setLayoutUrl(layoutDO.getLayoutImageUrl());
        configRespVO.setLayoutConfigs(bedAreaConfigs);
        configRespVO.setNotPlan(prisonerBedList);
        return configRespVO;
    }


}
