package com.rs.module.pam.service.screen;

import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.entity.OpsDicCode;
import com.rs.framework.mybatis.util.DicUtil;
import com.rs.module.pam.dao.screen.AqdtScreenDao;
import com.rs.module.pam.dao.screen.JsgkScreenDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class JsgkScreenServiceImpl implements JsgkScreenService {

    @Resource
    private JsgkScreenDao jsgkScreenDao;

    @Resource
    private AqdtScreenDao aqdtScreenDao;


    @Override
    public List<JSONObject> jlzb(String code) {
        List<OpsDicCode> dicAsc = DicUtil.getDicAsc("ZD_POST", "bsp");
        List<JSONObject> postList = jsgkScreenDao.getPostByOrgCode(code);
        Map<String, Integer> collect = postList.stream().collect(Collectors.toMap(a -> a.getString("post"), b -> b.getIntValue("total")));
        List<JSONObject> result = new ArrayList<>();
        for (OpsDicCode opsDicCode : dicAsc) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code", opsDicCode.getCode());
            jsonObject.put("name", opsDicCode.getName());
            jsonObject.put("total", collect.getOrDefault(opsDicCode.getCode(), 0));
            result.add(jsonObject);
        }
        if (collect.containsKey("99")) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code", "99");
            jsonObject.put("name", "其他");
            jsonObject.put("total", collect.getOrDefault("99", 0));
            result.add(jsonObject);
        }
        return result;
    }

    @Override
    public JSONObject sqdt(String code, String type) {
        JSONObject sqdt = checkJson(jsgkScreenDao.sqdt(code, type));
        int total = sqdt.getIntValue("total");
        int noHandleStatus = sqdt.getIntValue("no_handle_status");
        int hasHandleStatus = total - noHandleStatus;
        sqdt.put("has_handle_percent", total == 0 ? "0.00" : String.format("%.2f", hasHandleStatus * 100f / total));
        return sqdt;
    }

    @Override
    public JSONObject topInfo(String code) {
        JSONObject jsonObject = aqdtScreenDao.jyCount(code);
        int jyCount = jsonObject.getIntValue("jyzs");
        int foreingCount = jsonObject.getIntValue("foreing_count");
        JSONObject orgInfo = checkJson(jsgkScreenDao.getPmOrgInfo(code));
        int mjzs = orgInfo.getIntValue("mjzs");
        JSONObject result = new JSONObject();
        result.put("jyCount", jyCount);
        result.put("foreingCount", foreingCount);
        result.put("jyb", mjzs == 0 ? "0.00%" : String.format("%.2f", jyCount * 100f / mjzs));
        return result;
    }

    @Override
    public JSONObject cnwptj(String code) {
        return jsgkScreenDao.cnwptj(code);
    }

    @Override
    public JSONObject zdztfb(String code, String type) {
        return jsgkScreenDao.zdztfb(code, type);
    }


    private JSONObject checkJson(JSONObject json) {
        if (Objects.isNull(json)) {
            json = new JSONObject();
        }
        return json;
    }
}
