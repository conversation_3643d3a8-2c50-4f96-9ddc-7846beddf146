package com.rs.module.pam.controller.admin.duty.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-监室值班列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DutyPrisonerVO extends BaseVO implements TransPojo {

    private String id;

    @ApiModelProperty(value = "监管人员编号")
    private String jgrybm;

    @ApiModelProperty(value = "是否为值班组")
    private Boolean isGroup;

    @ApiModelProperty(value = "值班组id")
    private String groupId;

    @ApiModelProperty(value = "监管人员名称")
    private String name;

    @ApiModelProperty(value = "监管人员正面照")
    private String frontPhoto;

    @ApiModelProperty(value = "本周排班次数")
    private Integer weekDutyCount;

    @ApiModelProperty(value = "是否重病号")
    private Boolean isSick;

    @ApiModelProperty("风险等级")
    @Trans(type = TransType.DICTIONARY,  key = "ZD_JGRY_FXDJ")
    private String riskLevel;

    @ApiModelProperty("签到状态字典：ZD_ZBGL_QDZT）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_ZBGL_QDZT")
    private String signStatus;

    @ApiModelProperty("签到时间")
    private Date signDate;

    @JsonIgnore
    @ApiModelProperty("入所时间")
    private Date rssj;

    @JsonIgnore
    @ApiModelProperty("进入当前监室的时间")
    private Date entryRoomTime;

    @JsonIgnore
    @ApiModelProperty("人员状态")
    private String ryzt;

    @JsonIgnore
    @ApiModelProperty("监室号")
    private String jsh;

    @ApiModelProperty(hidden = true)
    private List<DutyPrisonerVO> prisonerList;

    @ApiModelProperty("入所天数")
    private Integer entryDays;

    @ApiModelProperty("值班类型(字典：ZD_ZBGL_ZBLX)")
    private String dutyType;


    /**
     * 入所时间-多少天
     */
    public Long getDays() {
        if (rssj == null) {
            return 0L;
        }
        // 获取两个日期之间的天数差异
        Long daysDiff = (System.currentTimeMillis() - rssj.getTime()) / (24 * 60 * 60 * 1000);
        return daysDiff;
    }


}
