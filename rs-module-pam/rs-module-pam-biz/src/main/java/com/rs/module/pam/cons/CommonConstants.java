package com.rs.module.pam.cons;

public class CommonConstants {

	/** 字典-子系统，值为@Value */
	public static final String DIC_SUB_SYSTEM = "ZD_RS_SUB_SYSTEM";

	/** 字典-监室信息发布状态-启用，值为@Value */
	public static final String INFORMATION_PUBLIC_STATUS_ENABLE = "1";

	/** 字典-监室信息发布状态-停用，值为@Value */
	public static final String INFORMATION_PUBLIC_STATUS_DISABLE = "0";



	/** 字典-重点关注人员审批状态-待审批，值为@Value */
	public static final String PRISONER_ARCHIVE_STATUS_WAIT = "1";

	/** 字典-重点关注人员审批状态-不同意，值为@Value */
	public static final String PRISONER_ARCHIVE_STATUS_NOT_PASSED = "2";

	/** 字典-重点关注人员审批状态-关注中，值为@Value */
	public static final String PRISONER_ARCHIVE_STATUS_PASSED = "3";

	/** 字典-重点关注人员审批状态-已结束，值为@Value */
	public static final String PRISONER_ARCHIVE_STATUS_END = "4";

	/** 字典-重点关注人员审批状态-逾期未审批，值为@Value */
	public static final String PRISONER_ARCHIVE_STATUS_OVERDUE = "5";

	/**
	 * 个人最大借阅数
	 */
	public static final String ZDJY_LIMIT = "ZDJYS_LIMIT";
	/**
	 * 个人最大借阅时长（天）
	 */
	public static final String ZDJY_DURATION = "ZDJY_DURATION";




	/** 字典-自动排班规则状态-启用，值为@Value */
	public static final Short AUTO_CONFIG_STATUS_ENABLE = (short)1;
	/** 字典-自动排班规则状态-停用，值为@Value */
	public static final Short AUTO_CONFIG_STATUS_DISABLE = (short)0;


	/** 字典-自动排班规则-01，值为@Value */
	public static final String AUTO_CONFIG_01 = "01";
	/** 字典-自动排班规则-01，值为@Value */
	public static final String AUTO_CONFIG_02 = "02";


	/** 字典-值班类型-站岗，值为@Value */
	public static final String DUTY_TYPE_STAND = "01";
	/** 字典-值班类型-坐岗，值为@Value */
	public static final String DUTY_TYPE_SIT = "02";

	/**
	 * 已上架
	 */
	public static final String ENABLED = "1";
	/**
	 * 已下架
	 */
	public static final String DISABLED = "0";

    /**
     * 管教大屏查询类型 监室：01
     */
	public static final String GJ_LARGE_SCREEN_ROOM = "01";
    /**
     * 管教大屏查询类型 监区：02
     */
	public static final String GJ_LARGE_SCREEN_AREA = "02";
    /**
     * 管教大屏查询类型 监所：03
     */
	public static final String GJ_LARGE_SCREEN_ORG = "03";

	/**
	 * 心理测评计划推送
	 */
	public static final String PSY_EXECTOR_JOBHANDLER = "execPsyPlan";


}

