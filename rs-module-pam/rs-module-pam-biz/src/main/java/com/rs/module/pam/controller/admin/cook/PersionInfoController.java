package com.rs.module.pam.controller.admin.cook;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.cook.vo.PersionInfoRespVO;
import com.rs.module.pam.controller.admin.cook.vo.PersionInfoSaveReqVO;
import com.rs.module.pam.entity.cook.PersionInfoDO;
import com.rs.module.pam.service.cook.PersionInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监室配餐-伙房人员信息")
@RestController
@RequestMapping("/pam/cook/persionInfo")
@Validated
public class PersionInfoController {

    @Resource
    private PersionInfoService persionInfoService;

    @PostMapping("/create")
    @ApiOperation(value = "创建-伙房人员信息")
    public CommonResult<String> createPersionInfo(@Valid @RequestBody PersionInfoSaveReqVO createReqVO) {
        return success(persionInfoService.createPersionInfo(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新-伙房人员信息")
    public CommonResult<Boolean> updatePersionInfo(@Valid @RequestBody PersionInfoSaveReqVO updateReqVO) {
        persionInfoService.updatePersionInfo(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除-伙房人员信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deletePersionInfo(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            persionInfoService.deletePersionInfo(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得-伙房人员信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<PersionInfoRespVO> getPersionInfo(@RequestParam("id") String id) {
        PersionInfoDO persionInfo = persionInfoService.getPersionInfo(id);
        return success(BeanUtils.toBean(persionInfo, PersionInfoRespVO.class));
    }

}
