package com.rs.module.pam.service.duty;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.duty.vo.RecordsVO;
import com.rs.module.pam.entity.duty.RecordsDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 监所事务管理-监室值班记录 Service 接口
 *
 * <AUTHOR>
 */
public interface RecordsService extends IBaseService<RecordsDO>{

    /**
     *
     * @param recordsSaveVO
     * @return
     */
    boolean save(RecordsVO recordsSaveVO);

    /**
     *
     * @param recordsSaveVO
     * @return
     */
    boolean updateById(RecordsVO recordsSaveVO);

    /**
     * 删除监室指定日期的排班
     * @param orgCode
     * @param roomId
     * @param dutyDate
     * @return
     */
    boolean remove(String orgCode, String roomId, Date dutyDate);

    /**
     * 删除监室指定日期的排班
     * @param ids
     * @return
     */
    boolean deleteByIds(List<String> ids);

    /**
     * 获取指定日期的值班记录列表
     * @param orgCode
     * @param roomId
     * @param dutyDate
     * @return
     */
    List<RecordsVO> selectList(String orgCode, String roomId, Date dutyDate);

    /**
     * 获取指定日期范围的值班记录列表
     * @param orgCode
     * @param roomId
     * @param startDate
     * @param endDate
     * @return
     */
    List<RecordsVO> selectList(String orgCode, String roomId, Date startDate, Date endDate);

    /**
     * 获取指定监室当前时间的排班情况
     * @param orgCode
     * @param roomId
     * @param shiftId
     * @return
     */
    RecordsVO getNowRecords(@Param("orgCode") String orgCode, @Param("roomId") String roomId,
                            @Param("shiftId") String shiftId);


}
