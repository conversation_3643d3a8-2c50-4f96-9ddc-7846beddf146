package com.rs.module.pam.service.duty;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.module.pam.dao.duty.RecordsPersonDao;
import com.rs.module.pam.entity.duty.RecordsPersonDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 监所事务管理-监室值班人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RecordsPersonServiceImpl extends BaseServiceImpl<RecordsPersonDao, RecordsPersonDO> implements RecordsPersonService {

    @Resource
    private RecordsPersonDao recordsPersonDao;



}
