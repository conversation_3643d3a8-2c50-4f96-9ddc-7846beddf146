package com.rs.module.pam.entity.represent;

import com.baomidou.mybatisplus.annotation.*;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 监所事务管理-监室点名 DO
 *
 * <AUTHOR>
 */
@TableName("pam_represent")
@KeySequence("pam_represent_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pam_represent")
public class RepresentDO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 监室name
     */
    private String roomName;
    /**
     * 监室总关押数
     */
    private Integer allInNum;
    /**
     * 监室在押数
     */
    private Integer inNum;
    /**
     * 监室外出数
     */
    private Integer outNum;
    /**
     * 点名开始时间
     */
    private Date startTime;
    /**
     * 点名结束时间
     */
    private Date endTime;
    /**
     * 点名状态（1：进行中，2：已结束，3：异常待处置）
     */
    private String presentStatus;
    /**
     * 已点人数
     */
    private Integer presentNum;
    /**
     * 异常人数
     */
    private Integer errorNum;
    /**
     * 异常处置结果
     */
    private String errorHandleResult;
    /**
     * 发起人
     */
    @TableField(exist = false)
    private String operatePeopleId;
    /**
     * 发起人
     */
    private String operatePeopleSfzh;
    /**
     * 发起人名字
     */
    private String operatePeopleName;
    /**
     * 发起时间
     */
    private Date operateTime;
    /**
     * 监所id
     */
    @TableField(exist = false)
    private String prisonId;
    /**
     * 监区id
     */
    private String areaId;
    /**
     * 监区name
     */
    private String areaName;
    /**
     * 点名批号
     */
    private String presentNo;
    /**
     * 点名类型 1-自动点名 2-临时点名
     */
    private Integer presentType;
    /**
     * 有效期
     */
    private Integer expiryDate;
    /**
     * 1-视频点名
     */
    private Integer isVideo;
    /**
     * 点名测温正常人数
     */
    private Integer temperatureNum;
    /**
     * 点名测温异常人数
     */
    private Integer temperatureErrNum;
    /**
     * 监室报备人数
     */
    private Integer reportNum;
    /**
     * 发起方 1-实战平台 2-仓外屏
     */
    private Integer initSource;

}
