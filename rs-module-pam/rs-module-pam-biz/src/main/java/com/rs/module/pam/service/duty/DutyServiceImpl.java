package com.rs.module.pam.service.duty;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.common.exception.BaseException;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.feign.SocketPushFeign;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.dto.OrgRespDTO;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.date.DateUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenDO;
import com.rs.module.acp.service.pm.BaseDeviceInscreenService;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.pm.PrisonerInDO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.pam.cons.CommonConstants;
import com.rs.module.pam.controller.admin.duty.vo.*;
import com.rs.module.pam.controller.app.vo.AppDutyRespVO;
import com.rs.module.pam.controller.app.vo.AppDutyShiftRespVO;
import com.rs.module.pam.controller.app.vo.AppDutySigninReqVO;
import com.rs.module.pam.dao.duty.*;
import com.rs.module.pam.entity.duty.*;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 监所事务管理-监室值班 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Log4j2
public class DutyServiceImpl extends BaseServiceImpl<DutyDao, DutyDO> implements DutyService {

    @Resource
    private DutyDao dutyDao;
    @Resource
    private DutyConfigDao dutyConfigDao;
    @Resource
    private RecordsService recordsService;
    @Resource
    private RecordsSigninService recordsSigninService;
    @Resource
    private ShiftDao shiftDao;
    @Resource
    private GroupService groupService;
    @Resource
    private PrisonerService prisonerService;
    @Resource
    private BaseDeviceInscreenService inscreenService;
    @Resource
    private SocketPushFeign socketPushFeign;
    @Resource
    private RoomAutoConfigDao roomAutoConfigDao;
    @Resource
    private DutyPrisonerDao dutyPrisonerDao;
    @Resource
    private BspApi bspApi;

    @Override
    @Transactional
    public void create(DutySaveVO createReqVO, Boolean isAuto) {
        dataCheck(createReqVO.getOrgCode(), createReqVO.getDutyList());
        OrgRespDTO org = bspApi.getOrgByCode(createReqVO.getOrgCode());
        // 插入
        String idCard = "";
        String name = "";
        if(!isAuto) {
            SessionUser sessionUser = SessionUserUtil.getSessionUser();
            idCard = sessionUser.getIdCard();
            name = sessionUser.getName();
        }
        String orgCode = createReqVO.getOrgCode();
        String roomId = createReqVO.getRoomId();
        Date now = new Date();
        List<DutyListRespVO> dutyList = createReqVO.getDutyList();
        List<DutyInfoRespVO> allDutyInfoList = dutyList.stream().filter(f -> f.getDutyInfo() != null)
                .map(DutyListRespVO::getDutyInfo)
                .flatMap(List::stream).collect(Collectors.toList());
        // 获取班次信息
        List<String> shiftIdList = allDutyInfoList.stream().map(DutyInfoRespVO::getShiftId).distinct().collect(Collectors.toList());
        List<ShiftDO> shiftList = shiftDao.selectList(orgCode, roomId, shiftIdList);
        Map<String, ShiftDO> shiftMap = shiftList.stream()
                .collect(Collectors.toMap(
                        ShiftDO::getId,
                        obj -> obj,
                        (existing, replacement) -> replacement
                ));

        // 获取值班组信息
        List<String> groupIdList = allDutyInfoList.stream().filter(dutyInfo ->
                Boolean.TRUE.equals(dutyInfo.getIsGroup())).map(DutyInfoRespVO::getGroupId)
                .distinct().collect(Collectors.toList());
        Map<String, GroupVO> groupMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(groupIdList)) {
            List<GroupVO> groupList = groupService.selectList(orgCode, roomId, groupIdList);
            groupMap = groupList.stream()
                    .collect(Collectors.toMap(
                            GroupVO::getId,
                            obj -> obj,
                            (existing, replacement) -> replacement
                    ));
        }

        // 校验值班组与班次是否存在。未来的（未开始的）班次，如果是小组，则小组必须存在。填补出currentShift参数。
        for (DutyListRespVO duty : dutyList) {
            Date dutyDate = duty.getDutyDate();
            for (DutyInfoRespVO dutyInfo : duty.getDutyInfo()) {
                final String groupId = dutyInfo.getId();
                ShiftDO shiftDO = shiftMap.get(dutyInfo.getShiftId());
                if (ObjectUtil.isEmpty(shiftDO)) {
                    throw new RuntimeException(String.format("班次[%s]不存在", dutyInfo.getShiftId()));
                }
                // 校验班次有效时间，避免胡乱指定班次
                if (shiftDO.getEffectiveStartDate().compareTo(dutyDate) <= 0
                        && (shiftDO.getEffectiveEndDate() == null || shiftDO.getEffectiveEndDate().compareTo(dutyDate) > 0)) {
                } else {
                    throw new RuntimeException(String.format("无效班次[%s]", shiftDO.getShiftName()));
                }
                // 班次开始时间
                Date shiftStartTime = parseDateAndTime(dutyDate, shiftDO.getStartTime());
                // 班次是否还未开始
                boolean shiftFuture = this.checkIsFuture(dutyDate, shiftStartTime, now);
                dutyInfo.setShiftType(shiftFuture ? "2" : "0");
                if (StringUtils.isBlank(groupId)) {
                    continue;
                }
                // 校验未开始的班次，小组是否存在
                if (shiftFuture && ObjectUtil.isEmpty(groupMap.get(groupId))) {
                    String groupName = StringUtils.isNotBlank(dutyInfo.getGroupName()) ? dutyInfo.getGroupName() : groupId;
                    throw new RuntimeException(String.format("值班小组[%s]不存在", groupName));
                }
            }
        }

        // 校验人员是否在监室
        List<PrisonerInDO> prisonerInList = prisonerService.getPrisonerInList(orgCode, roomId);
        List<String> prisonerJgrybmList = prisonerInList.stream().map(PrisonerInDO::getJgrybm).collect(Collectors.toList());
        Map<String, String> prisonerDutyTypeMap = new HashMap<>();
        for (DutyInfoRespVO dutyInfo : allDutyInfoList) {
            if ("0".equals(dutyInfo.getShiftType()) || "1".equals(dutyInfo.getShiftType())) {
                // 已经过的班次不校验人员是否还存在
                continue;
            }
            List<String> jgrybmList = dutyInfo.getPrisonerList().stream().map(DutyPrisonerVO::getJgrybm).collect(Collectors.toList());
                prisonerDutyTypeMap.putAll(dutyInfo.getPrisonerList().stream()
                    .collect(Collectors.toMap(
                            DutyPrisonerVO::getJgrybm,
                            obj -> Optional.ofNullable(obj.getDutyType())
                                    .filter(s -> !s.isEmpty())
                                    .orElse("01"),
                            (oldVal, newVal) -> newVal // 重复key处理
                    )));
            boolean isGroup = false;
            final String groupId = dutyInfo.getGroupId();
            if (StringUtils.isNotBlank(groupId)) {
                isGroup = true;
                // 小组。目的校验小组的中的人是否还在监室。如果不再测移除小组
                GroupVO group = groupMap.get(groupId);
                if (ObjectUtil.isNotEmpty(group)) {
                    jgrybmList = group.getJgrybmList();
                } else {
                    // 已过的班次，可能会存在当前小组不存在的情况
                }
            }
            for (String jgrybm : jgrybmList) {
                if (StringUtils.isNotBlank(jgrybm) && !prisonerJgrybmList.contains(jgrybm)) {
                    if (isGroup) {
                        // 小组中存在某成员，已经离开该监室。移除小组
                        new Thread(() -> {
                            // 异步使事务失效
                            groupService.removeById(groupId);
                        }).start();
                    }
                    PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm(jgrybm);
                    if (ObjectUtil.isNotEmpty(prisoner)) {
                        throw new RuntimeException(String.format("人员[%s](%s)不存在", prisoner.getXm(), prisoner.getJgrybm()));
                    } else {
                        throw new RuntimeException(String.format("人员[%s](%s)已离开该监室", prisoner.getXm(), prisoner.getJgrybm()));
                    }
                }
            }
        }


        for (DutyListRespVO dutyDto : dutyList) {
            List<DutyInfoRespVO> dutyInfoList = Optional.ofNullable(dutyDto.getDutyInfo()).orElse(Collections.EMPTY_LIST);
            // 排班日期
            Date dutyDate = dutyDto.getDutyDate();
            if (dutyInfoList.isEmpty()) {
                dutyDao.delete(new LambdaQueryWrapper<DutyDO>()
                        .eq(DutyDO::getOrgCode, orgCode)
                        .eq(DutyDO::getRoomId, roomId)
                        .eq(DutyDO::getDutyDate, dutyDate)
                );
                recordsService.remove(orgCode, roomId, dutyDate);
                continue;
            }
            boolean flag = false;
            DutyDO entity = dutyDao.selectOne(new LambdaQueryWrapper<DutyDO>()
                    .eq(DutyDO::getOrgCode, orgCode)
                    .eq(DutyDO::getRoomId, roomId)
                    .eq(DutyDO::getDutyDate, dutyDate)
                    .select(DutyDO::getId)
            );
            if (entity == null) {
                flag = true;
                entity = new DutyDO();
                entity.setId(StringUtil.getGuid32());
            }
            entity.setRoomId(roomId);
            entity.setDutyDate(dutyDate);
            entity.setAssignerUserSfzh(idCard);
            entity.setAssignerUserName(name);
            entity.setAssignedTime(now);
            // 该监室当前的排班情况
            List<RecordsDO> dbRecordsList = recordsService.list(new LambdaQueryWrapper<RecordsDO>()
                    .eq(RecordsDO::getOrgCode, orgCode)
                    .eq(RecordsDO::getRoomId, roomId)
                    .eq(RecordsDO::getDutyDate, dutyDate)
                    .select(RecordsDO::getId, RecordsDO::getShiftId)
            );
            List<String> recordsIds = new ArrayList<>();
            for (DutyInfoRespVO dutyInfoDto : dutyInfoList) {
                RecordsVO recordsDO = new RecordsVO();
                if (isAuto) {
                    recordsDO.setAddUser("");
                    recordsDO.setAddUserName("");
                }
                recordsDO.setOrgCode(org.getId());
                recordsDO.setOrgName(org.getName());
                recordsDO.setRoomId(roomId);
                recordsDO.setDutyId(entity.getId());
                recordsDO.setDutyDate(dutyDate);
                recordsDO.setShiftId(dutyInfoDto.getShiftId());
                // 填充班次信息
                ShiftDO shiftDO = shiftMap.get(dutyInfoDto.getShiftId());
                if (ObjectUtil.isNotEmpty(shiftDO)) {
                    recordsDO.setShiftName(shiftDO.getShiftName());
                    recordsDO.setShiftStartTime(parseDateAndTime(dutyDate, shiftDO.getStartTime()));
                    recordsDO.setShiftEndTime(parseDateAndTime(dutyDate, shiftDO.getEndTime()));
                }
                // 填充值班人员信息
                recordsDO.setGroupId("");
                recordsDO.setGroupName("");
                List<DutyPrisonerVO> prisonerList = Optional.ofNullable(dutyInfoDto.getPrisonerList()).orElse(Collections.EMPTY_LIST);
                if (prisonerList != null && prisonerList.size() > 0) {
                    List<String> jgrybmList = prisonerList.stream().map(prisoner -> prisoner.getJgrybm()).collect(Collectors.toList());
                    recordsDO.setPersonList(AssembleData(jgrybmList, prisonerDutyTypeMap));
                }
                // 填充值班组信息
                if (StringUtils.isNotBlank(dutyInfoDto.getGroupId())) {
                    recordsDO.setGroupId(dutyInfoDto.getGroupId());
                    recordsDO.setGroupName(dutyInfoDto.getGroupName());
                    GroupVO group = groupMap.get(dutyInfoDto.getGroupId());
                    if (ObjectUtil.isNotEmpty(group)) {
                        recordsDO.setGroupName(group.getGroupName());
                        recordsDO.setPersonList(AssembleData(group.getJgrybmList(), prisonerDutyTypeMap));
                    }
                }
                if (StringUtils.isBlank(recordsDO.getGroupId()) && CollectionUtil.isEmpty(recordsDO.getPersonList())) {
                    // 该值班无组，无人员
                    continue;
                }
                recordsDO.setAssignerUserSfzh(idCard);
                recordsDO.setAssignerUserName(name);
                recordsDO.setAssignedTime(now);
                Optional<RecordsDO> opt = dbRecordsList.stream().filter(f -> f.getShiftId() != null &&
                        f.getShiftId().equals(dutyInfoDto.getShiftId())).findFirst();
                if (opt.isPresent()) {
                    recordsDO.setId(opt.get().getId());
                    recordsService.updateById(recordsDO);
                } else {
                    recordsDO.setId(StringUtil.getGuid32());
                    recordsService.save(recordsDO);
                    // 同步添加签到数据
                    RecordsSigninDO signinDO = new RecordsSigninDO();
                    if (isAuto) {
                        signinDO.setAddUser("");
                        signinDO.setAddUserName("");
                    }
                    signinDO.setOrgCode(org.getId());
                    signinDO.setOrgName(org.getName() == null ? "" : org.getName());
                    signinDO.setRoomId(roomId);
                    signinDO.setDutyDate(dutyDate);
                    signinDO.setRecordsId(recordsDO.getId());
                    signinDO.setSigninStatus("0");
                    for (RecordsPersonSaveVO person : recordsDO.getPersonList()) {
                        signinDO.setId(null);
                        signinDO.setJgrybm(person.getJgrybm());
                        recordsSigninService.save(signinDO);
                    }
                }
                recordsIds.add(recordsDO.getId());
            }
            if (recordsIds.isEmpty()) {
                // 如果某天没有一个班次，则吧改天的排班从 pam_duty 表也删除
                dutyDao.delete(new LambdaQueryWrapper<DutyDO>()
                        .eq(DutyDO::getRoomId, roomId)
                        .eq(DutyDO::getDutyDate, dutyDate)
                );
            } else {
                if (isAuto) {
                    entity.setAddUser("");
                    entity.setAddUserName("");
                }
                entity.setCityCode(org.getCityId());
                entity.setCityName(org.getCityName());
                entity.setRegCode(org.getRegionId());
                entity.setRegName(org.getRegionName());
                entity.setOrgCode(org.getId());
                entity.setOrgName(org.getName() == null ? "" : org.getName());
                if (flag) {
                    dutyDao.insert(entity);
                } else {
                    dutyDao.updateById(entity);
                }
            }
            // 删除旧的和没有设置值班人员的值班数据
            List<String> deleteIds = dbRecordsList.stream().filter(f ->
                    !recordsIds.contains(f.getId())).map(RecordsDO::getId).collect(Collectors.toList());
            if (!deleteIds.isEmpty()) {
                recordsService.deleteByIds(deleteIds);
                // 删除签到数据
                List<RecordsDO> deleteRecords = dbRecordsList.stream().filter(f ->
                        !recordsIds.contains(f.getId())).collect(Collectors.toList());
                for (RecordsDO recordsDO : deleteRecords) {
                    recordsSigninService.deleteRecordsSignin(recordsDO.getOrgCode(), recordsDO.getRoomId(),
                            recordsDO.getId(), recordsDO.getDutyDate());
                }
            }
        }
        try {
            // 通知内屏刷新提醒列表
            dutyChangeNotifyCnp(orgCode);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    private RoomAutoConfigDO createDefaultAutoConfig(String orgCode) {
        OrgRespDTO org = bspApi.getOrgByCode(orgCode);
        RoomAutoConfigDO config = new RoomAutoConfigDO();
        config.setCityCode(org.getCityId());
        config.setCityName(org.getCityName());
        config.setRegCode(org.getRegionId());
        config.setRegName(org.getRegionName());
        config.setOrgCode(orgCode);
        config.setOrgName(org.getName());
        config.setIsEnabled(CommonConstants.AUTO_CONFIG_STATUS_ENABLE);
        config.setSchedulingRule(CommonConstants.AUTO_CONFIG_01+","+CommonConstants.AUTO_CONFIG_02);
        roomAutoConfigDao.insert(config);
        return config;
    }

    private void dataCheck(String orgCode, List<DutyListRespVO> dutyList) {
        DutyConfigDO configDO = dutyConfigDao.getByOrgCode(orgCode);
        for (DutyListRespVO dutyListRespVO : dutyList) {
            for (DutyInfoRespVO dutyInfoRespVO : dutyListRespVO.getDutyInfo()) {
                if (dutyInfoRespVO.getPrisonerList().size() > configDO.getDutyPersonNum())
                    throw new RuntimeException("值班人数不能超过" + configDO.getDutyPersonNum());
            }
        }
    }

    private List<RecordsPersonSaveVO> AssembleData(List<String> jgrybmList, Map<String, String> prisonerDutyTypeMap) {
        List<RecordsPersonSaveVO> list = new ArrayList<>();
        for (String jgrybm : jgrybmList) {
            list.add(new RecordsPersonSaveVO(jgrybm, prisonerDutyTypeMap.get(jgrybm)));
        }
        return list;
    }

    /**
     * 将时间天，加上时间点 的字符串。拼接成时间返回
     *
     * @param date
     * @param timeString
     * @return
     */
    private Date parseDateAndTime(Date date, String timeString) {
        if (StringUtils.isBlank(timeString)) {
            return null;
        }
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalTime localTime = LocalTime.parse(timeString);
        return Date.from(LocalDateTime.of(localDate, localTime).atZone(ZoneId.systemDefault()).toInstant());
    }

    private void dutyChangeNotifyCnp(String orgCode) {
        // 查询该监所下所有仓内屏序列号
        List<BaseDeviceInscreenDO> inscreenList = inscreenService.getByOrgCode(orgCode);
        if (inscreenList.isEmpty()) {
            return;
        }

        List<String> numberList = inscreenList.stream().map(inscreen -> inscreen.getSerialNumber()).distinct().collect(Collectors.toList());
        PushMessageForm form = new PushMessageForm();
        form.setSerialNumbers(numberList);
        form.setAction(SocketActionConstants.web_prisonRoomDutyChange);
        form.setTarget(SocketActionConstants.PushMessageTargetEnum.web.name());
        form.setTerminal(SocketActionConstants.PushMessageTerminalEnum.CNP.name());
        new Thread(() -> {
            socketPushFeign.pushMessageToSerialNumber(form);
        }).start();
    }

    /**
     * 判断班次是否是未来班次
     *
     * @param dutyDate         班次天
     * @param shiftStartTime   班次开始时间
     * @param now              当前时间
     * @return
     */
    private boolean checkIsFuture(Date dutyDate, Date shiftStartTime, Date now) {
        Date currentDate = new Date(now.getYear(), now.getMonth(), now.getDate());
        boolean future = dutyDate.compareTo(currentDate) >= 0 && shiftStartTime.compareTo(now) >= 0;
        return future;
    }


    @Override
    public void delete(String id) {
        // 校验存在
        validateExists(id);
        // 删除
        dutyDao.deleteById(id);
    }

    private void validateExists(String id) {
        if (dutyDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-监室值班数据不存在");
        }
    }

    @Override
    public DutyDO get(String id) {
        return dutyDao.selectById(id);
    }

    @Override
    public List<DutyVO> emptyDuty(String orgCode, String roomId, Date startDate, Date endDate) {
        Date now = new Date();
        // 获取值班记录
        List<RecordsVO> recordList = recordsService.selectList(orgCode, roomId, startDate, endDate);
        // 获取值班组信息
        List<String> recordsGroupIdList = recordList.stream().map(record -> record.getGroupId())
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<GroupVO> recordsGroupList = new ArrayList<>();
        Map<String, GroupVO> recordsGroupMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(recordsGroupIdList)) {
            recordsGroupList = groupService.selectList(orgCode, roomId, recordsGroupIdList);
            recordsGroupMap = recordsGroupList.stream()
                    .collect(Collectors.toMap(
                            GroupVO::getId,
                            obj -> obj,
                            (existing, replacement) -> replacement));
        }
        // 获取排班中的人员信息
        List<String> jgrybmList = recordList.stream()
                .flatMap(record -> record.getPersonList().stream())
                .map(person -> person.getJgrybm())
                .collect(Collectors.toList());
        jgrybmList.addAll(recordsGroupList.stream().flatMap(obj -> obj.getJgrybmList().stream()).collect(Collectors.toList()));
        jgrybmList = jgrybmList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 查询排班信息中的在所人员信息
        List<DutyPrisonerVO> prisonerInList = new ArrayList<>();
        if  (CollectionUtil.isNotEmpty(jgrybmList)) {
            prisonerInList = dutyPrisonerDao.getPrisonerInByJgrybms(jgrybmList);
        }
        List<String> prisonerInIdList = prisonerInList.stream().map(prisoner -> prisoner.getJgrybm()).collect(Collectors.toList());
        Map<String, DutyPrisonerVO> prisonerInMap = prisonerInList.stream()
                .collect(Collectors.toMap(
                        DutyPrisonerVO::getJgrybm,
                        obj -> obj,
                        (existing, replacement) -> replacement));

        // 获取班次信息，并以生效日期与失效日期分组，判断班次是否变更过
        List<ShiftDO> shiftList = shiftDao.selectShiftByEffectiveDate(orgCode, roomId, startDate, endDate);
        if (CollectionUtil.isEmpty(shiftList)) {
            shiftList = shiftDao.createDefaultShift(orgCode, roomId);
        }
        // 根据生效日期对班次进行分组
        Map<Date, List<ShiftDO>> grouped = shiftList.stream()
                .collect(Collectors.groupingBy(
                        ShiftDO::getEffectiveStartDate,
                        TreeMap::new,
                        Collectors.toList()
                ));
        // 根据序号对班次进行排序
        List<List<ShiftDO>> collect = grouped.values().stream()
                .map(group -> group.stream()
                        .sorted(Comparator.comparing(ShiftDO::getSort))
                        .collect(Collectors.toList()))
                .collect(Collectors.toList());

        List<DutyVO> result = new ArrayList<>();
        for(List<ShiftDO> shiftRespVOS : collect) {
            DutyVO dutyVO = new DutyVO();
            dutyVO.setRoomId(roomId);
            if (CollectionUtil.isEmpty(shiftRespVOS)) {
                continue;
            }
            // 返回有效时间段内所有的天。需要注意不能返回查询时间外的天
            Date dutyDate = shiftRespVOS.get(0).getEffectiveStartDate();
            if (startDate.compareTo(shiftRespVOS.get(0).getEffectiveStartDate()) > 0) {
                dutyDate = startDate;
            }
            Date dutyDateEnd = endDate;
            if (shiftRespVOS.get(0).getEffectiveEndDate() != null && endDate.compareTo(shiftRespVOS.get(0).getEffectiveEndDate()) >= 0) {
                dutyDateEnd = shiftRespVOS.get(0).getEffectiveEndDate();
                //  表的effective_end_date 不包含该天，需要减1
                dutyDateEnd = new Date(dutyDateEnd.getYear(), dutyDateEnd.getMonth(), dutyDateEnd.getDate() - 1);
            }
            // 遍历每天
            List<DutyListRespVO> list = new ArrayList<>();
            while (dutyDate.compareTo(dutyDateEnd) <= 0) {
                final Date finalDutyDate = dutyDate;
                DutyListRespVO dutyListRespVO = new DutyListRespVO();
                dutyListRespVO.setDutyDate(dutyDate);
                List<RecordsVO> recordsInfoList = recordList.stream().filter(f -> f.getDutyDate() != null &&
                        finalDutyDate.compareTo(f.getDutyDate()) == 0).collect(Collectors.toList());

                List<RecordsVO> completionRecordsList = new ArrayList<>();
                // 判断值班信息中的班次是否存在
                for (ShiftDO shift : shiftRespVOS) {
                    Optional<RecordsVO> opt = recordsInfoList.stream().filter(f ->
                            f.getShiftId() != null && f.getShiftId().equals(shift.getId())).findFirst();
                    RecordsVO recordsDO;
                    if (opt.isPresent()) {
                        recordsDO = BeanUtils.toBean(opt.get(), RecordsVO.class);
                        completionRecordsList.add(recordsDO);
                    } else {
                        recordsDO = new RecordsVO();
                        recordsDO.setShiftId(shift.getId());
                        completionRecordsList.add(recordsDO);
                    }
                    recordsDO.setShiftStartTime(parseDateAndTime(dutyDate, shift.getStartTime()));
                    recordsDO.setShiftEndTime(parseDateAndTime(dutyDate, shift.getEndTime()));
                }

                List<DutyInfoRespVO> dutyInfoRespVOList = new ArrayList<>();
                for (RecordsVO records : completionRecordsList) {
                    // 判断是否为未来班次，未开始的班次
                    boolean future = checkIsFuture(now, records.getShiftStartTime(), now);
                    DutyInfoRespVO dutyInfoRespVO = new DutyInfoRespVO();
                    dutyInfoRespVO.setShiftId(records.getShiftId());
                    // 判断当前时间是值班前、值班中还是值班后
                    dutyInfoRespVO.setShiftType("0");
                    dutyInfoRespVO.setIsGroup(false);
                    if (records.getShiftStartTime() != null && records.getShiftEndTime() != null) {
                        if (records.getShiftStartTime().compareTo(now) > 0) {
                            dutyInfoRespVO.setShiftType("2");
                        } else if ((records.getShiftEndTime().compareTo(now) > 0)) {
                            dutyInfoRespVO.setShiftType("1");
                        }
                    }
                    if (future) {
                        dutyInfoRespVO.setPrisonerList(new ArrayList<>());
                        dutyInfoRespVOList.add(dutyInfoRespVO);
                        continue;
                    }

                    List<String> dutyJgrybmList = new ArrayList<>();
                    List<DutyPrisonerVO> prisonerListRes = new ArrayList<>();
                    // 带出人员列表信息。优先从分组中拿，拿不到再从值班记录中拿
                    GroupVO groupDO = recordsGroupMap.get(records.getGroupId());
//                    if (ObjectUtil.isNotEmpty(groupDO)) {
//                        dutyJgrybmList = groupDO.getJgrybmList();
//                    } else {
                        dutyJgrybmList = records.getPersonList().stream().map(person -> person.getJgrybm()).collect(Collectors.toList());
//                    }

                    for (String jgrybm : dutyJgrybmList) {
                        if (StringUtils.isNotBlank(jgrybm) && prisonerInMap.containsKey(jgrybm)) {
                            DutyPrisonerVO dutyPrisonerVO = prisonerInMap.get(jgrybm);
                            prisonerListRes.add(dutyPrisonerVO);
                        }
                    }
                    dutyJgrybmList = prisonerListRes.stream().map(DutyPrisonerVO::getJgrybm).collect(Collectors.toList());
                    dutyInfoRespVO.setPrisonerList(BeanUtils.toBean(prisonerListRes, DutyPrisonerVO.class));
                    // 判断是否为值班组，并填充值班组信息
                    if (StringUtils.isNotBlank(records.getGroupId())) {
                        long inRoomCount = dutyJgrybmList.size();
                        if (inRoomCount < 2 || groupDO == null) {
                            // 如果值班时间还未到，但是组没有了。这种情况流程上不存在。因为删除组会级联删除未来的排班
                            dutyInfoRespVO.setIsGroup(false);
                        } else {
                            dutyInfoRespVO.setIsGroup(true);
                            dutyInfoRespVO.setGroupId(records.getGroupId());
                            dutyInfoRespVO.setGroupName(groupDO.getGroupName());
                            dutyInfoRespVO.setName(groupDO.getGroupName());
                            dutyInfoRespVO.getPrisonerList().forEach(p -> p.setIsGroup(true));
                        }
                    }
                    dutyInfoRespVOList.add(dutyInfoRespVO);
                }
                dutyListRespVO.setDutyInfo(dutyInfoRespVOList);
                list.add(dutyListRespVO);
                // 加一天
                dutyDate = new Date(dutyDate.getYear(), dutyDate.getMonth(), dutyDate.getDate() + 1);
            }
            dutyVO.setDutyList(list);
            dutyVO.setShiftList(BeanUtils.toBean(shiftRespVOS, ShiftRespVO.class));
            result.add(dutyVO);
        }

        return result;
    }

    @Override
    public List<DutyVO> dutyRecords(String orgCode, String roomId, Date startDate, Date endDate) {
        Date now = new Date();
        // 获取值班记录
        List<RecordsVO> recordList = recordsService.selectList(orgCode, roomId, startDate, endDate);
        // 获取值班组信息
        List<String> recordsGroupIdList = recordList.stream().map(record -> record.getGroupId())
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<GroupVO> recordsGroupList = new ArrayList<>();
        Map<String, GroupVO> recordsGroupMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(recordsGroupIdList)) {
            recordsGroupList = groupService.selectList(orgCode, roomId, recordsGroupIdList);
            recordsGroupMap = recordsGroupList.stream()
                    .collect(Collectors.toMap(
                            GroupVO::getId,
                            obj -> obj,
                            (existing, replacement) -> replacement));
        }
        // 获取排班中的人员信息
        List<String> jgrybmList = recordList.stream().flatMap(obj -> obj.getPersonList().stream())
                .map(person -> person.getJgrybm())
                .collect(Collectors.toList());

        Map<String, String> prisonerDutyTypeMap = recordList.stream().flatMap(obj -> obj.getPersonList().stream()).collect(Collectors.toMap(
                RecordsPersonSaveVO::getJgrybm,
                obj -> Optional.ofNullable(obj.getDutyType())
                        .filter(s -> !s.isEmpty())
                        .orElse("01"),
                (oldVal, newVal) -> newVal // 重复key处理
        ));
        jgrybmList.addAll(recordsGroupList.stream().flatMap(obj -> obj.getJgrybmList().stream()).collect(Collectors.toList()));
        jgrybmList = jgrybmList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 查询排班信息中的在所人员信息
        List<DutyPrisonerVO> prisonerInList = new ArrayList<>();
        if  (CollectionUtil.isNotEmpty(jgrybmList)) {
            prisonerInList = dutyPrisonerDao.getPrisonerInByJgrybms(jgrybmList);
        }
        List<String> prisonerInIdList = prisonerInList.stream().map(prisoner -> prisoner.getJgrybm()).collect(Collectors.toList());
        Map<String, DutyPrisonerVO> prisonerInMap = prisonerInList.stream()
                .collect(Collectors.toMap(
                        DutyPrisonerVO::getJgrybm,
                        obj -> obj,
                        (existing, replacement) -> replacement));

        // 获取班次信息，并以生效日期与失效日期分组，判断班次是否变更过
        List<ShiftDO> shiftList = shiftDao.selectShiftByEffectiveDate(orgCode, roomId, startDate, endDate);
        if (CollectionUtil.isEmpty(shiftList)) {
            shiftList = shiftDao.createDefaultShift(orgCode, roomId);
        }
        // 根据生效日期对班次进行分组
        Map<Date, List<ShiftDO>> grouped = shiftList.stream()
                .collect(Collectors.groupingBy(
                        ShiftDO::getEffectiveStartDate,
                        TreeMap::new,
                        Collectors.toList()
                ));
        // 根据序号对班次进行排序
        List<List<ShiftDO>> collect = grouped.values().stream()
                .map(group -> group.stream()
                        .sorted(Comparator.comparing(ShiftDO::getSort))
                        .collect(Collectors.toList()))
                .collect(Collectors.toList());

        List<DutyVO> result = new ArrayList<>();
        for(List<ShiftDO> shiftRespVOS : collect) {
            DutyVO dutyVO = new DutyVO();
            dutyVO.setRoomId(roomId);
            if (CollectionUtil.isEmpty(shiftRespVOS)) {
                continue;
            }
            // 返回有效时间段内所有的天。需要注意不能返回查询时间外的天
            Date dutyDate = shiftRespVOS.get(0).getEffectiveStartDate();
            if (startDate.compareTo(shiftRespVOS.get(0).getEffectiveStartDate()) > 0) {
                dutyDate = startDate;
            }
            Date dutyDateEnd = endDate;
            if (shiftRespVOS.get(0).getEffectiveEndDate() != null && endDate.compareTo(shiftRespVOS.get(0).getEffectiveEndDate()) >= 0) {
                dutyDateEnd = shiftRespVOS.get(0).getEffectiveEndDate();
                //  表的effective_end_date 不包含该天，需要减1
                dutyDateEnd = new Date(dutyDateEnd.getYear(), dutyDateEnd.getMonth(), dutyDateEnd.getDate() - 1);
            }
            // 记录离开监室的人员。后续删除
            List<String> roomOutJgrybmList = new ArrayList<>();
            // 遍历每天
            List<DutyListRespVO> list = new ArrayList<>();
            while (dutyDate.compareTo(dutyDateEnd) <= 0) {
                final Date finalDutyDate = dutyDate;
                DutyListRespVO dutyListRespVO = new DutyListRespVO();
                dutyListRespVO.setDutyDate(dutyDate);
                List<RecordsVO> recordsInfoList = recordList.stream().filter(f -> f.getDutyDate() != null &&
                        finalDutyDate.compareTo(f.getDutyDate()) == 0).collect(Collectors.toList());

                List<RecordsVO> completionRecordsList = new ArrayList<>();
                // 判断值班信息中的班次是否存在
                for (ShiftDO shift : shiftRespVOS) {
                    Optional<RecordsVO> opt = recordsInfoList.stream().filter(f ->
                            f.getShiftId() != null && f.getShiftId().equals(shift.getId())).findFirst();
                    RecordsVO recordsDO;
                    if (opt.isPresent()) {
                        completionRecordsList.add(recordsDO = opt.get());
                    } else {
                        recordsDO = new RecordsVO();
                        recordsDO.setShiftId(shift.getId());
                        completionRecordsList.add(recordsDO);
                    }
                    recordsDO.setShiftStartTime(parseDateAndTime(dutyDate, shift.getStartTime()));
                    recordsDO.setShiftEndTime(parseDateAndTime(dutyDate, shift.getEndTime()));
                }

                List<DutyInfoRespVO> dutyInfoRespVOList = new ArrayList<>();
                for (RecordsVO records : completionRecordsList) {
                    // 判断是否为未来班次，未开始的班次
                    boolean future = checkIsFuture(now, records.getShiftStartTime(), now);
                    DutyInfoRespVO dutyInfoRespVO = new DutyInfoRespVO();

                    List<String> dutyJgrybmList = new ArrayList<>();
                    List<DutyPrisonerVO> prisonerListRes = new ArrayList<>();
                    // 带出人员列表信息。优先从分组中拿，拿不到再从值班记录中拿
                    GroupVO groupDO = recordsGroupMap.get(records.getGroupId());
//                    if (ObjectUtil.isNotEmpty(groupDO)) {
//                        dutyJgrybmList = groupDO.getJgrybmList();
//                        records.setJgrybmList(groupDO.getJgrybmList());
//                    } else {
                        dutyJgrybmList = records.getPersonList().stream().map(person -> person.getJgrybm()).collect(Collectors.toList());
//                    }

                    for (String jgrybm : dutyJgrybmList) {
                        if (StringUtils.isNotBlank(jgrybm) && prisonerInMap.containsKey(jgrybm)) {
                            DutyPrisonerVO dutyPrisonerVO = prisonerInMap.get(jgrybm);
                            dutyPrisonerVO.setDutyType(prisonerDutyTypeMap.get(jgrybm));
                            prisonerListRes.add(dutyPrisonerVO);
                        }
                    }

                    if (future) {
                        // 未来的值班记录中 需要排除不在该监室的人员
                        Iterator<DutyPrisonerVO> iterator = prisonerListRes.iterator();
                        while (iterator.hasNext()) {
                            DutyPrisonerVO p = iterator.next();
                            if (!prisonerInIdList.contains(p.getJgrybm())) {
                                if (!roomOutJgrybmList.contains(p.getJgrybm())) {
                                    roomOutJgrybmList.add(p.getJgrybm());
                                }
                                iterator.remove();
                            }
                        }
                    }
                    dutyJgrybmList = prisonerListRes.stream().map(DutyPrisonerVO::getJgrybm).collect(Collectors.toList());

                    dutyInfoRespVO.setShiftId(records.getShiftId());
                    dutyInfoRespVO.setPrisonerList(BeanUtils.toBean(prisonerListRes, DutyPrisonerVO.class));
                    // 判断当前时间是值班前、值班中还是值班后
                    dutyInfoRespVO.setShiftType("0");
                    if (records.getShiftStartTime() != null && records.getShiftEndTime() != null) {
                        if (records.getShiftStartTime().compareTo(now) > 0) {
                            dutyInfoRespVO.setShiftType("2");
                        } else if ((records.getShiftEndTime().compareTo(now) > 0)) {
                            dutyInfoRespVO.setShiftType("1");
                        }
                    }
                    // 判断是否为值班组，并填充值班组信息
                    if (StringUtils.isNotBlank(records.getGroupId())) {
                        long inRoomCount = dutyJgrybmList.size();
                        if (inRoomCount < 2 || groupDO == null) {
                            // 如果值班时间还未到，但是组没有了。这种情况流程上不存在。因为删除组会级联删除未来的排班
                            dutyInfoRespVO.setIsGroup(false);
                        } else {
                            dutyInfoRespVO.setIsGroup(true);
                            dutyInfoRespVO.setGroupId(records.getGroupId());
                            dutyInfoRespVO.setGroupName(groupDO.getGroupName());
                            dutyInfoRespVO.setName(groupDO.getGroupName());
                            dutyInfoRespVO.getPrisonerList().forEach(p -> {
                                p.setIsGroup(true);
                                p.setDutyType(prisonerDutyTypeMap.get(p.getJgrybm()));
                            });
                        }
                    } else {
                        dutyInfoRespVO.setIsGroup(false);
                    }
                    dutyInfoRespVOList.add(dutyInfoRespVO);
                }
                dutyListRespVO.setDutyInfo(dutyInfoRespVOList);
                list.add(dutyListRespVO);
                // 加一天
                dutyDate = new Date(dutyDate.getYear(), dutyDate.getMonth(), dutyDate.getDate() + 1);
            }
            dutyVO.setDutyList(list);
            dutyVO.setShiftList(BeanUtils.toBean(shiftRespVOS, ShiftRespVO.class));
            result.add(dutyVO);
        }
        return result;
    }

//    public void roomOut(JSONObject fromArray) {
//        // 冗余校验，人员是否在所
//        List<String> rybhList = Arrays.asList(fromArray).stream().map(PrisonRoomDutyV2RoomOutDto::getRybh).distinct().collect(Collectors.toList());
//        List<PrisonRoomDutyV2PrisonerListVO> prisonerList = prisonRoomDutyV2Dao.selectPrisonerByIds(rybhList);
//        List<PrisonRoomDutyV2RoomOutDto> fromList = new ArrayList<>();
//        for (PrisonRoomDutyV2RoomOutDto form : fromArray) {
//            final String rybh = form.getRybh();
//            if (StringUtils.isBlank(rybh)) {
//                continue;
//            }
//            Optional<PrisonRoomDutyV2PrisonerListVO> optional = prisonerList.stream().filter(f -> rybh.equals(f.getId())).findFirst();
//            if (!optional.isPresent()) {
//                // 人员不存在
//                fromList.add(form);
//                continue;
//            }
//            PrisonRoomDutyV2PrisonerListVO prisonerInfo = optional.get();
//            boolean prisonerIn = "10".equals(prisonerInfo.getRyzt());
//            form.setCurrentRoomId(prisonerInfo.getJsh());
//            form.setPrisonerIn(prisonerIn);
//            if (!prisonerIn || ObjectUtils.notEqual(form.getRoomId(), prisonerInfo.getJsh())) {
//                // 人员不再所 或者监室不一致
//                fromList.add(form);
//                continue;
//            }
//        }
//        // 清理值班记录
//        for (PrisonRoomDutyV2RoomOutDto form : fromList) {
//            deletePrisonerRecordAndGroup(form);
//        }
//    }

    /**
     * 监室人员列表，分组放在人员前面
     *
     * @param roomId
     * @return
     */
    @Override
    public List<DutyPrisonerVO> prisonerList(String orgCode, String roomId) {
        // 查询出监室所有人员
        List<DutyPrisonerVO> prisonerListVOS = dutyPrisonerDao.getPrisonerInByRoomId(orgCode, roomId);
        // 查询本周人员值班数量
        List<Map<String, Object>> prisonerWeekDutyCountList = Collections.EMPTY_LIST;
        if (!prisonerListVOS.isEmpty()) {
            Date now = new Date();
            Date weekMonday = DateUtil.beginOfWeek(DateUtil.offsetDay(now, -1)).toJdkDate();
            Date weekSunday = DateUtil.endOfWeek(DateUtil.offsetDay(now, -1)).toJdkDate();
            prisonerWeekDutyCountList = dutyDao.selectPrisonerWeekDutyCount(orgCode, roomId, weekMonday, new Date(weekSunday.getTime() + 86400000 - 1));
        }
        // 填充人员本周值班次数与入所天数
        for (DutyPrisonerVO vo : prisonerListVOS) {
            vo.setIsGroup(false);
            vo.setWeekDutyCount(0);
            vo.setDutyType(CommonConstants.DUTY_TYPE_STAND);
            vo.setEntryDays(vo.getDays().intValue());
            for (Map<String, Object> countVO : prisonerWeekDutyCountList) {
                if (countVO.get("value") != null && countVO.get("value").equals(vo.getId())) {
                    vo.setWeekDutyCount(Integer.valueOf(String.valueOf(countVO.get("count"))));
                    break;
                }
            }
        }
        List<GroupVO> dutyGroupList = groupService.selectList(orgCode, roomId);

        // 这里面的人员是已经分组的，后需要从 prisonerListVOS 中剔除掉
        final Set<String> jgrybmSet = new HashSet<>();
        List<DutyPrisonerVO> groupRecords = new ArrayList<>();
        for (GroupVO groupDO : dutyGroupList) {
            List<DutyPrisonerVO> groupPrisonerList = new ArrayList<>();

            for (String jgrybm : groupDO.getJgrybmList()) {
                prisonerListVOS.stream().filter(f -> jgrybm != null && jgrybm.equals(f.getJgrybm())).findFirst().ifPresent(f -> {
                    groupPrisonerList.add(f);
                });
            }
            if (groupPrisonerList.size() < 2) {
                // 小组人员不足2人。将小组移除
                log.info("小组人员不足2人。移除小组：{}[{}]", groupDO.getGroupName(), groupDO.getId());
                groupService.removeById(groupDO.getId());
                continue;
            }
            prisonerListVOS.removeAll(groupPrisonerList);
            groupPrisonerList.forEach(p -> {
                p.setIsGroup(true);
            });
            DutyPrisonerVO pvo = new DutyPrisonerVO();
            pvo.setId(groupDO.getId());
            pvo.setGroupId(groupDO.getId());
            pvo.setName(groupDO.getGroupName());
            pvo.setIsGroup(true);
            jgrybmSet.addAll(groupDO.getJgrybmList());
            pvo.setPrisonerList(groupPrisonerList);
            groupRecords.add(pvo);
        }

        List<DutyPrisonerVO> records = new ArrayList<>();
        Iterator<DutyPrisonerVO> iterator = prisonerListVOS.iterator();
        while (iterator.hasNext()) {
            DutyPrisonerVO vo = iterator.next();
            if (jgrybmSet.contains(vo.getId())) {
                continue;
            }
            records.add(vo);
        }

        return prisonerSort(groupRecords, records);
    }

    private List<DutyPrisonerVO> prisonerSort(List<DutyPrisonerVO> groupRecords, List<DutyPrisonerVO> records) {
        Collections.sort(groupRecords, new Comparator<DutyPrisonerVO>() {
            @Override
            public int compare(DutyPrisonerVO o1, DutyPrisonerVO o2) {
                return Integer.compare(
                        o1.getPrisonerList().size(),
                        o2.getPrisonerList().size()
                );
            }
        });
        // 重组数据
        List<DutyPrisonerVO> result = new ArrayList<>();
        List<DutyPrisonerVO> tempList = new ArrayList<>();
        int currentCount = 0;

        // 处理值班组数据
        for (DutyPrisonerVO group : groupRecords) {
            if (currentCount + group.getPrisonerList().size() <= 4) {
                tempList.add(group);
                currentCount += group.getPrisonerList().size();
            } else {
                if (currentCount < 4) {
                    int index = 4 - currentCount;
                    if (index > records.size()) {
                        index = records.size() - 1;
                    }
                    List<DutyPrisonerVO> prisonerVOList = records.subList(0, index);
                    tempList.addAll(prisonerVOList);
                    records.removeAll(prisonerVOList);
                }
                result.addAll(tempList);
                tempList.clear();
                tempList.add(group);
                currentCount = group.getPrisonerList().size();
            }
        }

        if (CollectionUtil.isNotEmpty(tempList)) {
            result.addAll(tempList);
        }

        if (records.size() > 0) {
            result.addAll(records);
        }
        return result;
    }


    @Override
    public List<DutyVO> autoShift(String orgCode, String roomId, Date startDate, Date endDate) {
        DutyConfigDO configDO = dutyConfigDao.getByOrgCode(orgCode);
        Date now = new Date();
        // 当天开始时间
        Date today = new Date(now.getYear(), now.getMonth(), now.getDate());
        // 3天前
        Date daysAgo = new Date(today.getYear(), today.getMonth(), today.getDate() - 3);
        // 查询出监室所有人员
        List<DutyPrisonerVO> prisonerList = dutyPrisonerDao.getPrisonerInByRoomId(orgCode, roomId);
        // 不在监室中的人员
        List<DutyPrisonerVO> prisonerListNotRoom = new ArrayList<>();
        List<String> roomPrisonerList = new ArrayList<>();
        prisonerList.forEach(info -> roomPrisonerList.add(info.getId()));
        // 该监室人员近3天入监室时间
        List<DutyPrisonerVO> entryRoomRecords = dutyPrisonerDao.selectPrisonerEntryRoomTime(orgCode, roomId, daysAgo);
        // 查询值班小组
        List<GroupVO> dutyGroupList = groupService.selectList(orgCode, roomId);
        int dutyGroupListSize = dutyGroupList.size();
        // 根据配置的自动排班规则筛选人员集合、已分组
        RoomAutoConfigDO autoConfigDO = roomAutoConfigDao.selectOne(new LambdaQueryWrapper<RoomAutoConfigDO>()
                .eq(RoomAutoConfigDO::getOrgCode, orgCode)
                .eq(RoomAutoConfigDO::getRoomId, roomId));
        if (ObjectUtil.isEmpty(autoConfigDO)) {
            autoConfigDO = createDefaultAutoConfig(orgCode);
        }
        String schedulingRule = autoConfigDO.getSchedulingRule();
        List<DutyPrisonerVO> prisonerListBak = prisonerList.stream()
                .filter(f -> f != null
                        // 排除重病号人员
                        && (f.getIsSick() == null || Boolean.FALSE.equals(f.getIsSick()))
                        // 排除新入监所人员(3天)
                        && (schedulingRule.contains("01") && f.getDays() != null && f.getDays() > 3)
                        // 排出新入监室人员(3天)
                        && (schedulingRule.contains("01") && !entryRoomRecords.stream().anyMatch(
                                a -> a != null &&
                                a.getJgrybm() != null &&
                                a.getJgrybm().equals(f.getJgrybm())))
                        // 值班组人员组合排班，排除值班组人员
//                        && dutyGroupList != null
                        && dutyGroupList.stream().noneMatch(a -> a != null && a.getJgrybmList().contains(f.getJgrybm()))
                ).collect(Collectors.toList());
        System.out.println(String.format("人员一共%s人，条件排除后剩余%s人", prisonerList.size(), prisonerListBak.size()));
        System.out.println(String.format("查询到%s个组数据", dutyGroupListSize));
        if ((dutyGroupListSize * 2 + prisonerListBak.size()) < 3) {
            // 假如一天3个班次，只有2个组。剩下2个空位 需要找2个人补充上去，
            // 如果监室人员（排除新入所、重病号）不足2人。则提示“监室人员不足、无法自动排班”。如果够2人，执行【对人进行自动排班】
            throw new RuntimeException("监室人员不足，无法自动排班");
        }
        if (prisonerListBak.size() < configDO.getDutyPersonNum()) {
            configDO.setDutyPersonNum(prisonerListBak.size());
        }


        // 获取班次信息，并以生效日期与失效日期分组，判断班次是否变更过
        List<ShiftDO> shiftList = shiftDao.selectShiftByEffectiveDate(orgCode, roomId, startDate, endDate);
        if (CollectionUtil.isEmpty(shiftList)) {
            shiftList = shiftDao.createDefaultShift(orgCode, roomId);
        }
        // 根据生效日期对班次进行分组
        Map<Date, List<ShiftDO>> shiftGroupMap = shiftList.stream()
                .collect(Collectors.groupingBy(
                        ShiftDO::getEffectiveStartDate,
                        TreeMap::new,
                        Collectors.toList()
                ));
        // 根据序号对班次进行排序
        List<List<ShiftDO>> shiftGroupList = shiftGroupMap.values().stream()
                .map(group -> group.stream()
                        .sorted(Comparator.comparing(ShiftDO::getSort))
                        .collect(Collectors.toList()))
                .collect(Collectors.toList());

        List<DutyVO> result = new ArrayList<>();
        for(List<ShiftDO> shiftRespVOS : shiftGroupList) {
            DutyVO dutyVO = new DutyVO();
            dutyVO.setRoomId(roomId);
            if (CollectionUtil.isEmpty(shiftRespVOS)) {
                continue;
            }
            // 返回有效时间段内所有的天。需要注意不能返回查询时间外的天
            Date dutyDateStart = shiftRespVOS.get(0).getEffectiveStartDate();
            if (startDate.compareTo(shiftRespVOS.get(0).getEffectiveStartDate()) > 0) {
                dutyDateStart = startDate;
            }
            Date dutyDateEnd = endDate;
            if (shiftRespVOS.get(0).getEffectiveEndDate() != null && endDate.compareTo(shiftRespVOS.get(0).getEffectiveEndDate()) >= 0) {
                dutyDateEnd = shiftRespVOS.get(0).getEffectiveEndDate();
                //  表的effective_end_date 不包含该天，需要减1
                dutyDateEnd = new Date(dutyDateEnd.getYear(), dutyDateEnd.getMonth(), dutyDateEnd.getDate() - 1);
            }

            // 查询时间段内的排班情况
            List<RecordsVO> dbDutyRecords = recordsService.selectList(orgCode, roomId, dutyDateStart, dutyDateEnd);
            // 查询值班记录，可能会包含今日已过的班次。这些人不一定当前还在监室。最好筛选出来根据编号再次查询下。避免根据监室号查询不出来这些人
            List<String> dbRecordjgrybmList = dbDutyRecords.stream()
                    .flatMap(records -> records.getPersonList().stream())
                    .map(person -> person.getJgrybm())
                    .distinct()
                    .filter(f2 -> !roomPrisonerList.contains(f2))
                    .collect(Collectors.toList());
            if (!dbRecordjgrybmList.isEmpty()) {
                prisonerListNotRoom = dutyPrisonerDao.getPrisonerByJgrybms(dbRecordjgrybmList);
            }

            // 填充排班班次信息
            dutyVO.setShiftList(BeanUtils.toBean(shiftRespVOS, ShiftRespVO.class));
            List<DutyListRespVO> dutyList = new ArrayList<>();
            // 遍历每一天
            long days = DateUtil.between(dutyDateStart, dutyDateEnd, DateUnit.DAY);
            int groupPointer = 0;
            boolean useGroupsPhase = true;
            // 临时人员list，用于安排人员值班，安排之后删除相应人员，确保人员值班不重复
            List<DutyPrisonerVO> tmpPrisonerList = new ArrayList<>(prisonerListBak);
            for (int i = 0; i <= days; i++) {
                DateTime dutyDate = DateUtil.offsetDay(dutyDateStart, i);
                final Date finalDutyDate = dutyDate;
                DutyListRespVO dutyListRespVO = new DutyListRespVO();
                List<DutyInfoRespVO> dutyInfoRecords = new ArrayList<>();
                dutyListRespVO.setDutyDate(dutyDate);
                dutyListRespVO.setDutyInfo(dutyInfoRecords);
                dutyList.add(dutyListRespVO);
                for (ShiftRespVO shift : dutyVO.getShiftList()) {
                    DutyInfoRespVO obj = new DutyInfoRespVO();
                    dutyInfoRecords.add(obj);
                    obj.setShiftId(shift.getId());
                    obj.setShiftType("2");
                    obj.setPrisonerList(Collections.EMPTY_LIST);
                    boolean historical = !this.checkIsFuture(dutyDate, this.parseDateAndTime(dutyDate, shift.getStartTime()), now);
                    // 如果班次时间已经过。不对其进行排班
                    if (historical) {
                        obj.setShiftType("0");
                        // 如果班次开始时间已到，但是未结束。标记进心中状态
                        if (this.parseDateAndTime(dutyDate, shift.getEndTime()).compareTo(now) > 0) {
                            obj.setShiftType("1");
                        }
                        // 如果班次时间已经过。不对其进行排班。但是需要补充出之前已经有的排班
                        Optional<RecordsVO> dbDutyRecords_t = dbDutyRecords.stream()
                                .filter(f -> f.getDutyDate() != null
                                        && finalDutyDate.compareTo(f.getDutyDate()) == 0
                                        && shift.getId().equals(f.getShiftId()))
                                .findFirst();
                        if (dbDutyRecords_t.isPresent()) {
                            RecordsVO recordsVO = dbDutyRecords_t.get();
                            List<DutyPrisonerVO> combined = Stream.concat(prisonerList.stream(),
                                    prisonerListNotRoom.stream()).collect(Collectors.toList());
                            obj.setShiftId(recordsVO.getShiftId());
                            List<DutyPrisonerVO> collect = new ArrayList<>();
                            for (RecordsPersonSaveVO person : recordsVO.getPersonList()) {
                                combined.stream().filter(f -> f.getJgrybm() != null && f.getJgrybm().equals(person.getJgrybm()))
                                        .findFirst().ifPresent(f -> { collect.add(f); });
                            }
                            obj.setIsGroup(false);
                            if (!StringUtil.isNullBlank(recordsVO.getGroupId())) {
                                obj.setIsGroup(true);
                                obj.setGroupId(recordsVO.getGroupId());
                                obj.setGroupName(recordsVO.getGroupName());
                                obj.setName(recordsVO.getGroupName());
                                collect.forEach(p -> {
                                    p.setIsGroup(true);
                                    p.setDutyType(CommonConstants.DUTY_TYPE_STAND);
                                });
                            }
                            obj.setPrisonerList(collect);
                        }
                        continue;
                    }

                    // 班次时间未到，可以安排值班人员
                    if (useGroupsPhase) {
                        // 优先安排值班组
                        if (groupPointer < dutyGroupListSize) {
                            GroupVO groupDO = dutyGroupList.get(groupPointer);
                            obj.setIsGroup(true);
                            obj.setGroupId(groupDO.getId());
                            obj.setGroupName(groupDO.getGroupName());
                            obj.setName(groupDO.getGroupName());
                            List<String> jgrybmList = groupDO.getJgrybmList();
                            // 填充分组下人员
                            List<DutyPrisonerVO> collect = prisonerList.stream().filter(p ->
                                    jgrybmList.contains(p.getJgrybm())).collect(Collectors.toList());
                            collect.forEach(p -> {
                                p.setIsGroup(true);
                                p.setDutyType(CommonConstants.DUTY_TYPE_STAND);
                            });
                            obj.setPrisonerList(collect);
                            groupPointer++;
                        } else {
                            // 人员安排
                            groupPointer = 0;
                            assignPrisoner(obj, tmpPrisonerList, configDO.getDutyPersonNum());
                            useGroupsPhase = false;
                        }
                    } else {
                        // 人员安排
                        assignPrisoner(obj, tmpPrisonerList, configDO.getDutyPersonNum());
                        // 人员安排完毕或者人员不足，重新启动值班组安排，并补充临时人员list
                        if (tmpPrisonerList.size() <= 0) {
                            tmpPrisonerList = new ArrayList<>(prisonerListBak);
                            useGroupsPhase = true;
                        }
                    }
                }
            }
            dutyVO.setDutyList(BeanUtils.toBean(dutyList, DutyListRespVO.class));
            result.add(dutyVO);
        }
        return result;
    }

    /**
     * 安排值班人员
     * @param dutyInfo
     * @param prisonerList
     * @param personNum 值班人数
     */
    private void assignPrisoner(DutyInfoRespVO dutyInfo, List<DutyPrisonerVO> prisonerList, Integer personNum) {
        Collections.shuffle(prisonerList);
        int count = Math.min(personNum, prisonerList.size());
        List<DutyPrisonerVO> selected = new ArrayList<>(prisonerList.subList(0, count));
        dutyInfo.setIsGroup(false);
        dutyInfo.setPrisonerList(selected);
        prisonerList.removeAll(selected);
    }

    @Override
    public DutyVO getSignRecord(String orgCode, String roomId, Date dutyDate) {
        DutyVO dutyVO = new DutyVO();
        dutyVO.setOrgCode(orgCode);
        dutyVO.setRoomId(roomId);

        // 获取值班信息
        List<RecordsVO> recordsList = recordsService.selectList(orgCode, roomId, dutyDate);

        // 获取签到信息
        List<String> recordsIds = recordsList.stream().map(records -> records.getId()).collect(Collectors.toList());
        List<RecordsSigninDO> signList = new ArrayList<>();
        if (recordsIds.size() > 0) {
            signList = recordsSigninService.list(new LambdaQueryWrapper<RecordsSigninDO>()
                    .in(RecordsSigninDO::getRecordsId, recordsIds));
        }

        // 获取班次信息
        List<ShiftDO> shiftDOS = shiftDao.getShift(orgCode, roomId, dutyDate);
        if (CollectionUtil.isEmpty(shiftDOS)) {
            shiftDOS = shiftDao.getEffectiveShift(orgCode, roomId);
            if (CollectionUtil.isEmpty(shiftDOS)) {
                shiftDOS = shiftDao.createDefaultShift(orgCode, roomId);
            }
        }
        dutyVO.setShiftList(BeanUtils.toBean(shiftDOS, ShiftRespVO.class));

        // 获取排班中的人员信息
        List<String> jgrybmList = recordsList.stream().flatMap(obj -> obj.getPersonList().stream())
                .map(person -> person.getJgrybm())
                .collect(Collectors.toList());
        List<DutyPrisonerVO> prisonerList = new ArrayList<>();
        if (jgrybmList.size() > 0) {
            prisonerList = dutyPrisonerDao.getPrisonerByJgrybms(jgrybmList);
        }
        Map<String, DutyPrisonerVO> prisonerMap = prisonerList.stream()
                .collect(Collectors.toMap(
                        DutyPrisonerVO::getJgrybm,
                        obj -> obj,
                        (existing, replacement) -> replacement));

        DutyListRespVO dutyListRespVO = new DutyListRespVO();
        dutyListRespVO.setDutyDate(dutyDate);
        List<DutyInfoRespVO> dutyInfoRespVOList = new ArrayList<>();
        for (ShiftDO shift : shiftDOS) {
            DutyInfoRespVO dutyInfoRespVO = new DutyInfoRespVO();
            dutyInfoRespVO.setDutyDate(dutyDate);
            dutyInfoRespVO.setShiftId(shift.getId());
            List<DutyPrisonerVO> recordsPrisonerList = new ArrayList<>();
            for (RecordsVO records : recordsList) {
                if (shift.getId().equals(records.getShiftId())) {
                    dutyInfoRespVO.setIsGroup(!StringUtil.isNullBlank(records.getGroupId()));
                    dutyInfoRespVO.setGroupId(records.getGroupId());
                    dutyInfoRespVO.setGroupName(records.getGroupName());

                    for (RecordsPersonSaveVO person : records.getPersonList()) {
                        DutyPrisonerVO copy = new DutyPrisonerVO();
                        BeanUtil.copyProperties(prisonerMap.get(person.getJgrybm()), copy);
                        recordsPrisonerList.add(copy);
                    }

                    // 获取签到信息
                    for (DutyPrisonerVO prisoner : recordsPrisonerList) {
                        for (RecordsSigninDO sign : signList) {
                            if (records.getId().equals(sign.getRecordsId()) && prisoner.getJgrybm().equals(sign.getJgrybm())) {
                                prisoner.setSignDate(sign.getSigninTime());
                                prisoner.setSignStatus(sign.getSigninStatus());
                                break;
                            }
                        }
                        if (StringUtil.isNullBlank(prisoner.getSignStatus()))
                            prisoner.setSignStatus("0");
                        prisoner.setIsGroup(dutyInfoRespVO.getIsGroup());
                    }
                }
            }
            dutyInfoRespVO.setPrisonerList(recordsPrisonerList);
            dutyInfoRespVOList.add(dutyInfoRespVO);
        }
        dutyListRespVO.setDutyInfo(dutyInfoRespVOList);
        dutyVO.setDutyList(Arrays.asList(dutyListRespVO));

        return dutyVO;
    }

    @Override
    public List<AppDutyRespVO> appDutyRecords(String orgCode, String roomId, Date startDate, Date endDate) {
        List<AppDutyRespVO> returnList = new ArrayList<>();

        List<DutyVO> records = this.dutyRecords(orgCode, roomId, startDate, endDate);
        // 所有的班次
        List<ShiftRespVO> shiftList = records.stream().flatMap(obj -> Stream.of(obj.getShiftList()))
                .flatMap(List::stream)
                .collect(Collectors.toList());
        // 所有的值班信息
        List<DutyListRespVO> dutyList = records.stream().flatMap(obj -> Stream.of(obj.getDutyList()))
                .flatMap(List::stream)
                .collect(Collectors.toList());
        dutyList = dutyList.stream().sorted(Comparator.comparing(DutyListRespVO::getDutyDate)).collect(Collectors.toList());

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        for (DutyListRespVO dutyDto : dutyList) {
            AppDutyRespVO vo = new AppDutyRespVO();
            returnList.add(vo);

            vo.setDutyDate(dutyDto.getDutyDate());
            vo.setDutyName(sdf.format(dutyDto.getDutyDate()));
            vo.setRoomId(roomId);
            List<AppDutyShiftRespVO> dutyShiftList = new ArrayList<>();
            vo.setDutyShiftList(dutyShiftList);
            vo.setWeekday(DateUtils.getWeekOfDay(dutyDto.getDutyDate()));
            // 每个班次信息
            for (DutyInfoRespVO dtoDutyInfo : dutyDto.getDutyInfo()) {
                Optional<ShiftRespVO> optional = shiftList.stream().filter(ff -> ObjectUtils.equals(dtoDutyInfo.getShiftId(), ff.getId())).findFirst();
                if (!optional.isPresent()) {
                    // 一定不会出现
                    continue;
                }
                ShiftRespVO shiftManageDto = optional.get();
                AppDutyShiftRespVO shiftVO = new AppDutyShiftRespVO();
                dutyShiftList.add(shiftVO);
                shiftVO.setId(dtoDutyInfo.getShiftId());
                shiftVO.setShiftName(shiftManageDto.getShiftName());
                shiftVO.setStartTime(shiftManageDto.getStartTime());
                shiftVO.setEndTime(shiftManageDto.getEndTime());
                List<DutyPrisonerVO> personList = new ArrayList<>();
                shiftVO.setPrisonerList(personList);
                // 每个班次的值班人员
                for (DutyPrisonerVO dutyPrisonerInfo : dtoDutyInfo.getPrisonerList()) {
                    DutyPrisonerVO prisonerVO = BeanUtils.toBean(dutyPrisonerInfo, DutyPrisonerVO.class);
                    personList.add(prisonerVO);
                }
            }
        }
        // 填充数据，可能会出现 查询时间内没有班次时返回的数据会缺少一些天，将缺少天的数据补充
        if (endDate != null && startDate != null && endDate.compareTo(startDate) >= 0) {
            Date startTime = startDate;
            while (startTime.compareTo(endDate) <= 0) {
                final String dutyName = sdf.format(startTime);
                boolean match = returnList.stream().anyMatch(a -> dutyName.equals(a.getDutyName()));
                if (match) {
                    startTime = new Date(startTime.getTime() + 86400000);
                    continue;
                }
                AppDutyRespVO vo = new AppDutyRespVO();
                returnList.add(vo);
                vo.setDutyDate(startTime);
                vo.setDutyName(dutyName);
                vo.setRoomId(roomId);
                List<AppDutyShiftRespVO> dutyShiftList = new ArrayList<>();
                vo.setDutyShiftList(dutyShiftList);
                vo.setWeekday(DateUtils.getWeekOfDay(startTime));
                startTime = new Date(startTime.getTime() + 86400000);
            }
            returnList.sort(Comparator.comparing(AppDutyRespVO::getDutyDate));
        }
        return returnList;
    }

    @Override
    public List<AppDutyShiftRespVO> dutyRecordsByDate(String orgCode, String roomId, Date date) {
        List<AppDutyShiftRespVO> returnList = new ArrayList<>();

        List<ShiftDO> shiftList = shiftDao.selectShiftByEffectiveDate(orgCode, roomId, date, date);
        List<RecordsVO> recordList = recordsService.selectList(orgCode, roomId, date, date);

        List<String> jgrybmList = recordList.stream().flatMap(obj -> obj.getPersonList().stream())
                .map(person -> person.getJgrybm())
                .collect(Collectors.toList());
        jgrybmList = jgrybmList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<DutyPrisonerVO> personerList = new ArrayList<>();
        if  (CollectionUtil.isNotEmpty(jgrybmList)) {
            personerList = dutyPrisonerDao.getPrisonerInByJgrybms(jgrybmList);
        }
        Map<String, DutyPrisonerVO> prisonerInMap = personerList.stream()
                .collect(Collectors.toMap(
                        DutyPrisonerVO::getJgrybm,
                        obj -> obj,
                        (existing, replacement) -> replacement));
        for (ShiftDO shiftDO : shiftList) {
            AppDutyShiftRespVO appDutyShiftRespVO = new AppDutyShiftRespVO();
            appDutyShiftRespVO.setId(shiftDO.getId());
            appDutyShiftRespVO.setShiftName(shiftDO.getShiftName());
            appDutyShiftRespVO.setStartTime(shiftDO.getStartTime());
            appDutyShiftRespVO.setEndTime(shiftDO.getEndTime());
            appDutyShiftRespVO.setSort(shiftDO.getSort());
            List<DutyPrisonerVO> prisonerList = new ArrayList<>();
            for (RecordsVO recordsDO : recordList) {
                if (ObjectUtils.equals(shiftDO.getId(), recordsDO.getShiftId())) {
                    for (RecordsPersonSaveVO person : recordsDO.getPersonList()) {
                        prisonerList.add(prisonerInMap.get(person.getJgrybm()));
                    }
                    break;
                }
            }
            appDutyShiftRespVO.setPrisonerList(prisonerList);
            returnList.add(appDutyShiftRespVO);
        }
        returnList.sort(Comparator.comparing(AppDutyShiftRespVO::getSort));
        return returnList;
    }

    @Override
    public AppDutyShiftRespVO getNowShiftDetail(String orgCode, String roomId) {
        AppDutyShiftRespVO appDutyShiftRespVO = new AppDutyShiftRespVO();
        // 查询班次信息
        ShiftDO shiftDO = shiftDao.getNowShift(orgCode, roomId);
        if (shiftDO == null) {
            throw new ServerException(200, "当前时间无值班安排");
        }
        appDutyShiftRespVO.setId(shiftDO.getId());
        appDutyShiftRespVO.setShiftName(shiftDO.getShiftName());
        appDutyShiftRespVO.setStartTime(shiftDO.getStartTime());
        appDutyShiftRespVO.setEndTime(shiftDO.getEndTime());
        appDutyShiftRespVO.setSort(shiftDO.getSort());

        // 获取值班配置
        DutyConfigDO dutyConfigDO = dutyConfigDao.getByOrgCode(orgCode);
        Short signinValidityPeriod = ObjectUtil.isEmpty(dutyConfigDO) || ObjectUtil.isEmpty(dutyConfigDO.getSigninValidityPeriod()) ?
                (short)15 : dutyConfigDO.getSigninValidityPeriod();
        appDutyShiftRespVO.setSigninValidityPeriod(signinValidityPeriod);
        // 查询值班人员信息
        RecordsVO recordsDO = recordsService.getNowRecords(orgCode, roomId, shiftDO.getId());
        if (recordsDO == null) {
            throw new ServerException(200, "当前时间无值班安排");
        }
        List<String> jgrybmList = recordsDO.getPersonList().stream().map(person -> person.getJgrybm()).collect(Collectors.toList());
        List<DutyPrisonerVO> prisonerList = dutyPrisonerDao.getPrisonerInByJgrybms(jgrybmList);
        appDutyShiftRespVO.setPrisonerList(prisonerList);
        // 查询签到信息
        List<RecordsSigninDO> signinList = recordsSigninService.getNowSigninByRecordsId(orgCode, roomId, recordsDO.getId());
        appDutyShiftRespVO.setSinginList(BeanUtils.toBean(signinList, RecordsSigninRespVO.class));
        return appDutyShiftRespVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<RecordsSigninRespVO> signinSave(AppDutySigninReqVO reqVO) {
        Date now = new Date();
        String orgCode = reqVO.getOrgCode();
        String roomId = reqVO.getRoomId();
        String jgrybm = reqVO.getJgrybm();

        List<CurrentDutyRecordsInfoVO> dutyList = dutyDao.getPrisonerCurrentDuty(orgCode, roomId, jgrybm, now);
        if (dutyList.isEmpty()) {
            throw new BaseException("当前没有值班情况");
        }
        CurrentDutyRecordsInfoVO prisonerCurrentDuty = dutyList.get(0);

        List<RecordsSigninDO> shiftSignin = recordsSigninService.getNowRecordsSignin(orgCode, roomId);
        List<String> collect = shiftSignin.stream().map(signin -> signin.getJgrybm()).collect(Collectors.toList());
        if (collect.contains(jgrybm)) {
            throw new BaseException("请勿重复签到");
        }

        // 获取签到有效期
        DutyConfigDO dutyConfigDO = dutyConfigDao.getByOrgCode(orgCode);
        if (ObjectUtil.isEmpty(dutyConfigDO)) {
            dutyConfigDO = dutyConfigDao.createDefaultConfig(orgCode);
        }
        Short signinValidityPeriod = ObjectUtil.isEmpty(dutyConfigDO.getSigninValidityPeriod()) ?
                (short)30 : dutyConfigDO.getSigninValidityPeriod();

        // 验证本次签到是否已过有效期
        if (prisonerCurrentDuty.getShiftStartTime().getTime() + signinValidityPeriod * 60 * 1000 < now.getTime()) {
            throw new BaseException("已超过签到有效期，无法签到");
        }

        RecordsSigninDO signinDO = new RecordsSigninDO();
        signinDO.setOrgCode(orgCode);
        signinDO.setOrgName(ObjectUtil.isEmpty(dutyConfigDO.getOrgName()) ? "" : dutyConfigDO.getOrgName());
        signinDO.setRoomId(roomId);
        signinDO.setJgrybm(jgrybm);
        signinDO.setRecordsId(prisonerCurrentDuty.getId());
        signinDO.setDutyDate(prisonerCurrentDuty.getDutyDate());
        signinDO.setIsSigninValid(0);
        signinDO.setSigninStatus("1");
        signinDO.setSigninTime(now);
        recordsSigninService.save(signinDO);

//        List<RecordsSigninDO> signinList = recordsSigninService.getNowRecordsSignin(orgCode, roomId);
        shiftSignin.add(signinDO);
        return BeanUtils.toBean(shiftSignin, RecordsSigninRespVO.class);
    }


}
