package com.rs.module.pam.controller.admin.duty.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监室值班班次新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DutyShiftSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户身份证号")
    private String userIdCard;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("机构编号")
    private String orgCode;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("监室id")
    private String roomId;

    @ApiModelProperty("是否全局(0:否,1:是)")
    private String isGlobal;

    private List<ShiftSaveReqVO> shiftList;

}
