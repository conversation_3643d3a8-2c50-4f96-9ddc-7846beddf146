package com.rs.module.pam.service.represent;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.gosun.zhjg.device.server.modules.inscreen.vo.BaseDeviceInscreenVO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.controller.admin.represent.vo.*;
import com.rs.module.pam.controller.admin.represent.vo.extra.*;
import com.rs.module.pam.entity.represent.RepresentDO;

import javax.validation.Valid;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 监所事务管理-监室点名 Service 接口
 *
 * <AUTHOR>
 */
public interface RepresentService extends IBaseService<RepresentDO>{

    /**
     * 创建监所事务管理-监室点名
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String create(@Valid RepresentSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-监室点名
     *
     * @param updateReqVO 更新信息
     */
    void update(@Valid RepresentSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-监室点名
     *
     * @param id 编号
     */
    void delete(String id);

    /**
     * 获得监所事务管理-监室点名
     *
     * @param id 编号
     * @return 监所事务管理-监室点名
     */
    RepresentDO get(String id);

    /**
    * 获得监所事务管理-监室点名分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-监室点名分页
    */
    PageResult<RepresentDO> getPage(RepresentPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-监室点名列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-监室点名列表
    */
    List<RepresentDO> getList(RepresentListReqVO listReqVO);


    /**
     * 获取监室数据
     * @param roomIds
     * @param code
     * @return
     */
    List<RoomInfoVO> getRepresentInfo(String[] roomIds, String code);

    /**
     * 获取主管/协管/关注/所有监室监室数据
     * @param code
     * @param userId
     * @return
     */
    List<Map> getRoomIds(String code, Integer userId,String prisonId);

    /**
     * 获取今日监室点名结果数据
     * @param page
     * @return
     */
    List<RoomInfoRepresentVO> getRepresentRoomResult(Page page, RepresentPageReqVO pageDto);

//    /***
//     * 体温检测台账
//     * @param page
//     * @param pageDto
//     * @return
//     */
//    PageVO<TemperatureListPageVO> getTemperaturePageList(Page page, TemperatureListPageDTO pageDto);

    /**
     * 获取今日监室点名结果统计
     * @param roomIds
     * @return
     */
    RepresentCountVO getRepresentResultCount(String roomIds);

    /**
     * 监室点名-分页列表
     * @param page
     * @param pageDto
     * @return
     */
    List<RepresentPageReqVO> findByPage(Page page, RepresentPageReqVO pageDto);

    /**
     * 根据点名批号查询点名详情
     * @param presentNo
     * @return
     */
    List<RoomInfoRepresentVO> selectById(String presentNo);

    /***
     * 根据点名批号查询测温详情
     * @param id
     * @return
     */
    List<RoomInfoRepresentVO> selectTemperatureById(String id);

    /***
     * 视频点名展示入口
     * @param id
     * @return
     */
    RoomInfoRepresentVO videoInfoList(String id);

    /***
     * 视频核实确认
     * @param dto
     * @return
     */
    Integer confirmByVideo(ConfirmByVideoVO dto);


    /***
     * 根据id查询点名详情
     * @param id
     * @return
     */
    RoomInfoRepresentVO selectByRepresentId(String id);

    /**
     * 手动点名
     * @param rooms
     * @param time 有效期
     * @param initSource 发起方 1-实战平台 2-仓外屏
     * @return
     */
    HandleRepresentVO startRepresent(List<RoomInfoVO> rooms, Integer time, Integer initSource) throws Exception;

    /***
     * 仓内屏点名校验
     * @param roomId
     * @param time
     * @return
     */
    Integer cnpCheckRoom(String roomId,Date time);

    /***
     * 仓内屏发起点名
     * @param roomId
     * @param startDate
     * @return
     */
    HandleRepresentVO startRepresentByCnp(String roomId, Date startDate);

    /**
     * 进行异常处置
     * @param id
     * @param errorMsg
     * @return
     */
    Integer errorHandle(String id,String errorMsg);

    /**
     * 个人签到
     * @param personId
     * @return
     */
    Integer personToRepresent(String personId,String roomId,String temperature);

    /**
     * 获取第一次点名人员数据
     * @param roomId
     * @return
     */
    JSONObject getFirstPersonList(String roomId);

    /**
     * 获取第二次点名人员数据
     * @param roomId
     * @return
     */
    List<InRepresentPersonVO> getSecondPersonList(String roomId);

    /**
     * 结束点名
     * @param roomId
     * @return
     */
    Boolean endRepresent(String roomId);

    /**
     * 结束点名
     * @param roomId
     * @return
     */
    Boolean endRepresentCnp(String roomId);

    /**
     * 结束点名-按照批号
     * @param presentNo
     * @return
     */
    Boolean endRepresentByNo(String presentNo);

    /**
     * 修改配置
     * @param configVo
     * @return
     */
//    Integer updateRepresentConfig(RepresentConfigSaveReqVO configVo, Integer userId, String userName);

    /**
     * 手动点名刷新获取数据显示
     * @param roomIds
     * @return
     */
    HandleRepresentVO getInRepresnetRoom(List<String> roomIds);

    /**
     * 获取配置列表详情
     * @return
     */
    RepresentConfigRespVO getConfigListById(String id);

    /**
     * 删除配置
     * @param ids
     * @return
     */
    Integer delRepresentConfig(List<String> ids);

    /**
     *
     * @param roomId
     * @param startTime
     * @param endTime
     * @return
     */
    List<RoomInfoRepresentVO> getCwpRepresentList(String roomId, Date startTime, Date endTime);

    /***
     * 基础数据初始化
     * @param id
     * @return
     */
    InitBaseDataVO initBaseData(String id);

    /***
     * 获取临时点名
     * @param roomId
     * @return
     */
    InitBaseDataConfigVO getOneTask(String roomId);


    /***
     * 接收离线人员点名数据
     * @param receiveDataDTO
     * @return
     */
    Integer receiveData(ReceiveDataVO receiveDataDTO);


    /***
     * 接收仓内屏内对人脸库及人员库信息进行比对数据
     * @param dtos
     * @return
     */
    Integer checkData(List<RoomRepresentCheckVO> dtos);

    /***
     * 修改补录后仓内屏内对人脸库及人员库信息进行比对数据
     * @param checkDTO
     * @return
     */
    Integer updateCheckData(RoomRepresentCheckVO checkDTO);

    /***
     * 时间同步校验-数据入库
     * @param reqVO
     * @return
     */
    Integer pushData(PushDataVO reqVO);

    /**
     * 获取监室人数
     * @param roomId
     * @return
     */
    Integer getCountInRoom(String roomId);

    /**
     * 获取监室外出人数
     * @param roomId
     * @return
     */
    Integer getCountOutRoom(String roomId);

    /***
     * 根据在押人员编号获取缺失照片
     * @param prisonerId
     * @return
     */
    RoomRepresentCheckVO getPhotoByPrisonerId(String prisonerId);

    RoomInfoRepresentVO getCwpRepresentDetail(String id);

}
