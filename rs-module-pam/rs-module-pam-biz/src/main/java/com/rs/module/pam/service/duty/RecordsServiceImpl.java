package com.rs.module.pam.service.duty;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.duty.vo.RecordsPersonSaveVO;
import com.rs.module.pam.controller.admin.duty.vo.RecordsVO;
import com.rs.module.pam.dao.duty.RecordsDao;
import com.rs.module.pam.dao.duty.RecordsPersonDao;
import com.rs.module.pam.entity.duty.RecordsDO;
import com.rs.module.pam.entity.duty.RecordsPersonDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 监所事务管理-监室值班记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RecordsServiceImpl extends BaseServiceImpl<RecordsDao, RecordsDO> implements RecordsService {

    @Resource
    private RecordsDao recordsDao;
    @Resource
    private RecordsPersonDao recordsPersonDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(RecordsVO recordsSaveVO) {
        RecordsDO recordsDO = BeanUtils.toBean(recordsSaveVO, RecordsDO.class);
        save(recordsDO);
        // 插入子表
        createPerson(recordsSaveVO.getRoomId(), recordsSaveVO.getDutyId(), recordsDO.getId(), recordsSaveVO.getPersonList());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(RecordsVO recordsSaveVO) {
        RecordsDO recordsDO = BeanUtils.toBean(recordsSaveVO, RecordsDO.class);
        updateById(recordsDO);
        // 删除子表旧数据
        recordsPersonDao.deleteByPersonId(recordsSaveVO.getId());
        // 插入子表
        createPerson(recordsSaveVO.getRoomId(), recordsSaveVO.getDutyId(), recordsDO.getId(), recordsSaveVO.getPersonList());
        return true;
    }

    @Override
    public boolean remove(String orgCode, String roomId, Date dutyDate) {
        List<RecordsDO> list = list(new LambdaQueryWrapper<RecordsDO>()
                .eq(RecordsDO::getOrgCode, orgCode)
                .eq(RecordsDO::getRoomId, roomId)
                .eq(RecordsDO::getDutyDate, dutyDate));
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        List<String> ids = list.stream().map(record -> record.getId()).collect(Collectors.toList());
        removeByIds(ids);
        recordsPersonDao.delete(new LambdaUpdateWrapper<RecordsPersonDO>()
                .in(RecordsPersonDO::getRecordsId, ids));
        return true;
    }

    @Override
    public boolean deleteByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        removeByIds(ids);
        recordsPersonDao.delete(new LambdaUpdateWrapper<RecordsPersonDO>()
                .in(RecordsPersonDO::getRecordsId, ids));
        return true;
    }

    private void createPerson(String roomId, String dutyId, String recordsId, List<RecordsPersonSaveVO> personList) {
        List<RecordsPersonDO> list = new ArrayList<>();
        for (RecordsPersonSaveVO person : personList) {
            RecordsPersonDO personDO = new RecordsPersonDO();
            personDO.setRoomId(roomId);
            personDO.setDutyId(dutyId);
            personDO.setRecordsId(recordsId);
            personDO.setJgrybm(person.getJgrybm());
            personDO.setDutyType(person.getDutyType());
            list.add(personDO);
        }
        recordsPersonDao.insertBatch(list);
    }

    @Override
    public List<RecordsVO> selectList(String orgCode, String roomId, Date dutyDate) {
        List<RecordsDO> recordsList = list(new LambdaQueryWrapper<RecordsDO>()
                .eq(RecordsDO::getOrgCode, orgCode)
                .eq(RecordsDO::getRoomId, roomId)
                .eq(RecordsDO::getDutyDate, dutyDate));
        return dataFilling(recordsList);
    }

    @Override
    public List<RecordsVO> selectList(String orgCode, String roomId, Date startDate, Date endDate) {
        List<RecordsDO> recordsList = recordsDao.selectList(orgCode, roomId, startDate, endDate);
        return dataFilling(recordsList);
    }

    private List<RecordsVO> dataFilling(List<RecordsDO> recordsList) {
        if (CollectionUtil.isNotEmpty(recordsList)) {
            List<RecordsVO> list = BeanUtils.toBean(recordsList, RecordsVO.class);
            for (RecordsVO recordsVO : list) {
                List<RecordsPersonDO> personList = recordsPersonDao.selectList(new LambdaQueryWrapper<RecordsPersonDO>()
                        .eq(RecordsPersonDO::getRecordsId, recordsVO.getId()));
                recordsVO.setPersonList(personList.stream().map(person ->
                        new RecordsPersonSaveVO(person.getJgrybm(), person.getDutyType())).collect(Collectors.toList()));
            }
            return list;
        }
        return Collections.emptyList();
    }

    @Override
    public RecordsVO getNowRecords(String orgCode, String roomId, String shiftId) {
        RecordsDO recordsDO = recordsDao.getNowRecords(orgCode, roomId, shiftId);
        RecordsVO recordsVO = BeanUtils.toBean(recordsDO, RecordsVO.class);
        List<RecordsPersonDO> personList = recordsPersonDao.selectList(new LambdaQueryWrapper<RecordsPersonDO>()
                .eq(RecordsPersonDO::getRecordsId, recordsVO.getId()));
        recordsVO.setPersonList(personList.stream().map(person ->
                new RecordsPersonSaveVO(person.getJgrybm(), person.getDutyType())).collect(Collectors.toList()));
        return recordsVO;
    }


}
