package com.rs.module.pam.controller.app;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.complaint.vo.SuggestionListReqVO;
import com.rs.module.pam.controller.admin.complaint.vo.SuggestionPageReqVO;
import com.rs.module.pam.controller.admin.complaint.vo.SuggestionRespVO;
import com.rs.module.pam.controller.admin.complaint.vo.SuggestionSaveReqVO;
import com.rs.module.pam.entity.complaint.SuggestionDO;
import com.rs.module.pam.service.complaint.SuggestionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监所事务管理-投诉建议")
@RestController
@RequestMapping("/app/pam/complaint/suggestion")
@Validated
public class AppSuggestionController {

    @Resource
    private SuggestionService suggestionService;

    @PostMapping("/create")
    @ApiOperation(value = "App-投诉建议创建")
    public CommonResult<String> createSuggestion(@Valid @RequestBody SuggestionSaveReqVO createReqVO) {
        return success(suggestionService.createSuggestion(createReqVO));
    }

    @GetMapping("/pageRecord")
    @ApiOperation(value = "App-投诉建议记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小"),
            @ApiImplicitParam(name = "jgrybm", value = "被监管人员编码"),
            @ApiImplicitParam(name = "roomId", value = "监室号"),
            @ApiImplicitParam(name = "type", value = "周期类型 1 全部，2 今天，3 昨天，4 近一周")
    })
    public CommonResult<PageResult<SuggestionRespVO>> pageRecord(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                                           @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                                           @RequestParam(value = "jgrybm", required = false) String jgrybm,
                                                                           @RequestParam(value = "roomId", required = false) String roomId,
                                                                           @RequestParam("type") String type) {
        return success(suggestionService.pageRecord(pageNo, pageSize, jgrybm, roomId, type));
    }

}
