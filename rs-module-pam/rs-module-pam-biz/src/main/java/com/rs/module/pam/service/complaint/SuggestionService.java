package com.rs.module.pam.service.complaint;

import java.util.*;
import javax.validation.*;
import com.rs.module.pam.controller.admin.complaint.vo.*;
import com.rs.module.pam.entity.complaint.SuggestionDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 监所事务管理-投诉建议 Service 接口
 *
 * <AUTHOR>
 */
public interface SuggestionService extends IBaseService<SuggestionDO>{

    /**
     * 创建监所事务管理-投诉建议
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSuggestion(@Valid SuggestionSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-投诉建议
     *
     * @param updateReqVO 更新信息
     */
    void updateSuggestion(@Valid SuggestionSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-投诉建议
     *
     * @param id 编号
     */
    void deleteSuggestion(String id);

    /**
     * 获得监所事务管理-投诉建议
     *
     * @param id 编号
     * @return 监所事务管理-投诉建议
     */
    SuggestionDO getSuggestion(String id);

    /**
    * 获得监所事务管理-投诉建议分页
    *
    * @param pageReqVO 分页查询
    * @return 监所事务管理-投诉建议分页
    */
    PageResult<SuggestionDO> getSuggestionPage(SuggestionPageReqVO pageReqVO);

    /**
    * 获得监所事务管理-投诉建议列表
    *
    * @param listReqVO 查询条件
    * @return 监所事务管理-投诉建议列表
    */
    List<SuggestionDO> getSuggestionList(SuggestionListReqVO listReqVO);


    PageResult<SuggestionRespVO> pageRecord(int pageNo, int pageSize, String jgrybm, String roomId, String type);
}
