package com.rs.module.pam.controller.admin.duty.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-值班组 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GroupVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监室id")
    private String roomId;
    @ApiModelProperty("值班组名（规则：两个被监管人员的姓名的首字母组合）")
    private String groupName;
    @ApiModelProperty("值班组序号，监室下自增")
    private Integer groupNo;
    @ApiModelProperty("监管人员编码")
    private List<String> jgrybmList;
}
