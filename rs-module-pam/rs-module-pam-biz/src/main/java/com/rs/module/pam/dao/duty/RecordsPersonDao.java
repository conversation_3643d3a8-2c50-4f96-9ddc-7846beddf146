package com.rs.module.pam.dao.duty;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.pam.entity.duty.RecordsPersonDO;
import org.apache.ibatis.annotations.Mapper;

/**
* 监所事务管理-监室值班人员 Dao
*
* <AUTHOR>
*/
@Mapper
public interface RecordsPersonDao extends IBaseDao<RecordsPersonDO> {

    default void deleteByPersonId(String recordsId) {
        delete(new LambdaQueryWrapper<RecordsPersonDO>()
                .eq(RecordsPersonDO::getRecordsId, recordsId));
    }

}
