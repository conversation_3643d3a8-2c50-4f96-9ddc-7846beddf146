package com.rs.module.pam.controller.app;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.common.msg.ResultVO;
import com.gosun.zhjg.common.util.DateUtils;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.dao.pm.PrisonerInDao;
import com.rs.module.pam.controller.admin.represent.vo.RepresentConfigRespVO;
import com.rs.module.pam.controller.admin.represent.vo.extra.*;
import com.rs.module.pam.schedule.RoomPresentAsyTask;
import com.rs.module.pam.service.represent.RepresentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监室点名-内外屏")
@RestController
@RequestMapping("/app/pam/public/represent/")
@Validated
public class AppRepresentController {


    @Resource
    private RepresentService representService;

    @Resource
    private RoomPresentAsyTask roomPresentAsyTask;
    @Resource
    private PrisonerInDao prisonerInDao;

    @ApiOperation(value = "时间同步校验-数据入库")
    @PostMapping("pushData")
    public ResultVO<?> pushData(@RequestBody PushDataVO reqVO) {
        Integer code = representService.pushData(reqVO);
        if(code>0){
            return ResultVO.success();
        }else if(code==-1){
            return ResultVO.error("点名已经完成请勿重复操作","点名已经完成请勿重复操作");
        }else if(code==-3){
            return ResultVO.error("空监室请勿点名","空监室请勿点名");
        }else {
            return ResultVO.error("未知异常，请联系管理员","未知异常，请联系管理员");
        }
    }

    /**
     * 获取第一次点名人员数据
     */
    @ApiOperation(value = "获取第一次点名人员数据",responseContainer = "List", response = InRepresentPersonVO.class)
    @GetMapping("getFirstPersonList/{roomId}")
    @ApiImplicitParam(paramType = "path", name = "roomId", value = "监室号", required = true, dataType = "String")
    public R getFirstPersonList(@PathVariable String roomId) {
        return R.ResponseResult(representService.getFirstPersonList(roomId));
    }

    @ApiOperation(value = "仓内屏点名校验",notes = "1-可以点名 0-不可以")
    @GetMapping("/cnpCheckRoom")
    public CommonResult cnpCheckRoom(@RequestParam("roomId") String roomId, @RequestParam("time") String time) {
        System.out.println("======================cnpCheckRoom==================================");
        Date date = DateUtils.stringToDate(time, DateUtils.DATE_TIME_PATTERN);
        Integer code = representService.cnpCheckRoom(roomId, date);
        return success(code);
    }

    @ApiOperation(value = "仓内屏发起点名", responseContainer = "List", response = HandleRepresentVO.class)
    @PostMapping("/startRepresentByCnp")
    public CommonResult startRepresentByCnp(@RequestParam("roomId") String roomId,@RequestParam("startDate") Date startDate) {
        System.out.println("======================startRepresentByCnp==================================");
        try {
            HandleRepresentVO vo = representService.startRepresentByCnp(roomId,startDate);
            return success(vo);
        } catch (Exception e) {
            e.printStackTrace();
            return error(e.getMessage());
        }
    }

    @ApiOperation(value = "手动点名刷新获取数据显示", response = HandleRepresentVO.class)
    @PostMapping("/getRepresentResultByRoomIds")
    public CommonResult getRepresentResultByRoomIds(@RequestBody List<String> roomIds) {
        return success(representService.getInRepresnetRoom(roomIds));
    }

    @ApiOperation(value = "提交异常处置")
    @PostMapping("/errorHandle")
    public CommonResult errorHandle(@RequestBody ErrorHandleVO vo) {
        Integer num = representService.errorHandle(vo.getId(), vo.getErrorMsg());
        if (num > 0) {
            return success("提交成功");
        } else {
            return error("提交失败");
        }
    }

    @ApiOperation(value = "人员点名")
    @PostMapping("personToRepresent")
    public R personToRepresent(@RequestBody PrisonerSignVO prisonerSignVO) {
        try {
            if (prisonerSignVO.getPersonId() == null || prisonerSignVO.getRoomId() == null || prisonerSignVO.getPersonName() == null) {
                return R.ResponseError("人员签到传入数据不足");
            }
            Integer code = representService.personToRepresent(prisonerSignVO.getPersonId(), prisonerSignVO.getRoomId(), prisonerSignVO.getTemperature());
            if (code == 2) {
                return R.ResponseOk(prisonerSignVO.getPersonName() + "验证通过，下一个");
            } else if (code == 1) {
                return R.ResponseError(prisonerSignVO.getPersonName() + ",你已验证过，不需要重复验证！");
            } else if (code == 3) {
                return R.ResponseOk(prisonerSignVO.getPersonName() + "验证通过，下一个,本次点名已经全部完成!");
            }else if (code == 4) {
                return R.ResponseError(prisonerSignVO.getPersonName() + "您已经报备，无须点名");
            } else {
                return R.ResponseError("验证不通过，请在一边等候，并留意广播通知！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.ResponseError("出错了，请联系管理员");
        }
    }

    @ApiOperation(value = "结束点名")
    @GetMapping("/endRepresent/{roomId}")
    @ApiImplicitParam(paramType = "path", name = "roomId", value = "监室号", required = true, dataType = "String")
    public CommonResult endRepresent(@PathVariable String roomId) {
        Boolean isSuccess = representService.endRepresent(roomId);
        if (isSuccess) {
            return success("结束成功");
        } else {
            return CommonResult.error("所选监室有监室已不在点名中状态，请刷新页面");
        }
    }


    /**
     * 获取配置列表
     */
//    @ApiOperation(value = "获取配置列表", response = RepresentConfigRespVO.class, responseContainer = "List")
//    @GetMapping("getConfigList")
//    public R getConfigList(@ApiParam AbstractPageQueryForm abstractPageQueryForm) {
//        return R.ResponsePage(representService.getConfigList(abstractPageQueryForm));
//    }

    @ApiOperation(value = "配置列表详情", response = RepresentConfigRespVO.class, responseContainer = "Object")
    @GetMapping("getConfigListById/{id}")
    @ApiImplicitParam(paramType = "path", name = "id", value = "编号", required = true, dataType = "String")
    public CommonResult getConfigListById(@PathVariable String id) {
        return success(representService.getConfigListById(id));
    }

    @ApiOperation(value = "基础数据初始化-首次连接", response = InitBaseDataVO.class, responseContainer = "Object")
    @GetMapping("initBaseData/{id}")
    @ApiImplicitParam(paramType = "path", name = "id", value = "序列号", required = true, dataType = "String")
    public CommonResult<?> initBaseData(@PathVariable String id) {
        return success(representService.initBaseData(id));
    }

    @ApiOperation(value = "基础数据同步-websocket推送", response = InitBaseDataVO.class, responseContainer = "Object")
    @GetMapping("pushBaseData")
    public CommonResult<?> pushBaseData() {
        return success(roomPresentAsyTask.asyData());
    }

    @ApiOperation(value = "接收离线点名结果推送")
    @PostMapping("receiveData")
    public CommonResult<?> receiveData(@RequestBody ReceiveDataVO dto) {
        Integer code = representService.receiveData(dto);
        if(code>0){
            return success();
        }else {
            return error("接收离线数据失败");
        }
    }

    @ApiOperation(value = "接收仓内屏内对人脸库及人员库信息进行比对数据")
    @PostMapping("checkData")
    public CommonResult<?> checkData(@RequestBody List<RoomRepresentCheckVO> dtos) {
        Integer code = representService.checkData(dtos);
        if(code>0){
            return success();
        }else {
            return error("数据比对失败！");
        }
    }

    @ApiOperation(value = "更改比对后的人脸补录状态")
    @PostMapping("updateCheckData")
    public CommonResult<?> updateCheckData(@RequestBody RoomRepresentCheckVO checkDTO) {
        Integer code = representService.updateCheckData(checkDTO);
        if(code>0){
            return success();
        }else {
            return error("人脸补录失败");
        }
    }

    @ApiOperation(value = "仓外屏-获取监室人数")
    @GetMapping("cwpPersonCount")
    @ApiImplicitParam(name = "roomId", value = "监室号", required = true, dataType = "String")
    public CommonResult<?> cwpPersonCount(@RequestParam("roomId") String roomId) {
        JSONObject result = new JSONObject();
        Integer inCount = representService.getCountInRoom(roomId);
        Integer outCount = representService.getCountOutRoom(roomId);
        result.put("count", inCount);
        result.put("inCount", inCount - outCount);
        result.put("outCount", outCount);
        return success(result);
    }

    @ApiOperation(value = "仓外屏-监室点名列表")
    @GetMapping("cwpRepresentList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roomId", value = "监室号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "dateType", value = "时间类型(今天:today,昨天:yesterday)", required = true, dataType = "String")
    })
    public CommonResult<List<RoomInfoRepresentVO>> cwpRepresentList(@RequestParam("roomId") String roomId,
                                            @RequestParam("dateType") String dateType) {
        Date startTime = null;
        Date endTime = null;
        switch (dateType) {
            case "today":
                startTime = DateUtil.beginOfDay(new Date());
                endTime = DateUtil.endOfDay(new Date());
                break;
            case "yesterday":
                startTime = DateUtil.beginOfDay(DateUtil.yesterday());
                endTime = DateUtil.endOfDay(DateUtil.yesterday());
                break;
            default:
                break;
        }
        return success(representService.getCwpRepresentList(roomId, startTime, endTime));
    }

    @ApiOperation(value = "仓外屏-监室点名详情")
    @GetMapping("cwpRepresentDetail")
    @ApiImplicitParam(name = "id", value = "点名记录id", required = true, dataType = "String")
    public CommonResult<RoomInfoRepresentVO> cwpRepresentDetail(@RequestParam("id") String id) {
        return success(representService.getCwpRepresentDetail(id));
    }

    @ApiOperation(value = "获取手动点名监室数据", responseContainer = "List", response = RoomInfoVO.class)
    @PostMapping("/getRepresentRoomInfo")
    public CommonResult getRepresentRoomInfo(@RequestBody RoomRepresentInfoVO reqVO) {
        return R.ResponseResult(representService.getRepresentInfo(reqVO.getRoomIds(), reqVO.getCode()));
    }

    @ApiOperation(value = "仓外屏-手动点名")
    @PostMapping("/cwpStartRepresent")
    @LogRecordAnnotation(bizModule = "acp:represent", operateType = LogOperateType.CREATE, title = "监室点名-发起手动点名",
            success = "监室点名-发起手动点名成功", fail = "错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#roomPresentVO}}")
    public CommonResult cwpStartRepresent(@RequestBody RoomPresentVO roomPresentVO) {
        try {
            HandleRepresentVO vo = representService.startRepresent(roomPresentVO.getRooms(), roomPresentVO.getExpiryDate(), 2);
            return success(vo);
        } catch (Exception e) {
            e.printStackTrace();
            return error(e.getMessage());
        }
    }

    @ApiOperation(value = "结束点名-按照批号")
    @GetMapping("endRepresentByNo/{presentNo}")
    @ApiImplicitParam(paramType = "path", name = "presentNo", value = "点名批号", required = true, dataType = "String")
    public CommonResult endRepresentByNo(@PathVariable String presentNo) {
        representService.endRepresentByNo(presentNo);
        return success("结束成功");
    }

}
