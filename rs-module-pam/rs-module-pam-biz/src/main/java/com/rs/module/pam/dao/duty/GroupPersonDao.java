package com.rs.module.pam.dao.duty;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.module.pam.entity.duty.GroupPersonDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* 监所事务管理-值班组人员 Dao
*
* <AUTHOR>
*/
@Mapper
public interface GroupPersonDao extends IBaseDao<GroupPersonDO> {

    default List<GroupPersonDO> getByGroupId(String groupId) {
        return selectList(new LambdaQueryWrapper<GroupPersonDO>()
                .eq(GroupPersonDO::getGroupId, groupId));
    }

}
