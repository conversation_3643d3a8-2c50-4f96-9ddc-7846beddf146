package com.rs.module.pam.dao.screen;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface JsgkScreenDao {

    JSONObject getPmOrgInfo(@Param("orgCode") String orgCode);

    JSONObject sqdt(@Param("orgCode") String orgCode, @Param("type") String type);

    List<JSONObject> getPostByOrgCode(@Param("orgCode") String orgCode);

    JSONObject cnwptj(@Param("orgCode") String orgCode);

    JSONObject zdztfb(@Param("orgCode") String orgCode, @Param("type") String type);
}
