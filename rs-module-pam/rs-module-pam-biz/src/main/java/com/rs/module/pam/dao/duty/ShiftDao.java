package com.rs.module.pam.dao.duty;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.duty.ShiftDO;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
* 监所事务管理-值班班次 Dao
*
* <AUTHOR>
*/
@Mapper
public interface ShiftDao extends IBaseDao<ShiftDO> {

    /**
     * 获取机构下指定监室有效的排班信息
     *
     * @param orgCode
     * @param roomId
     * @return
     */
    default List<ShiftDO> getEffectiveShift(String orgCode, String roomId) {
        return selectList(new LambdaQueryWrapperX<ShiftDO>()
                .eq(ShiftDO::getOrgCode, orgCode)
                .eqIfPresent(ShiftDO::getRoomId, roomId)
                .isNull(ShiftDO::getEffectiveEndDate));
    }

    default List<ShiftDO> selectList(String orgCode, String roomId, List<String> ids) {
        return selectList(new LambdaQueryWrapperX<ShiftDO>()
                .eq(ShiftDO::getOrgCode, orgCode)
                .eq(ShiftDO::getRoomId, roomId)
                .in(ShiftDO::getId, ids));
    }

    /**
     * 获取指定时间段内涉及的排班信息
     * @param orgCode
     * @param roomId
     * @param startDate
     * @param endDate
     * @return
     */
    List<ShiftDO> selectShiftByEffectiveDate(@Param("orgCode") String orgCode, @Param("roomId") String roomId,
                                             @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 获取当前时间的班次信息
     * @param orgCode
     * @param roomId
     * @return
     */
    ShiftDO getNowShift(@Param("orgCode") String orgCode, @Param("roomId") String roomId);

    /**
     * 获取指定日期的班次信息
     * @param orgCode
     * @param roomId
     * @return
     */
    List<ShiftDO> getShift(@Param("orgCode") String orgCode, @Param("roomId") String roomId, @Param("date") Date date);

    default List<ShiftDO> createDefaultShift(String orgCode, String roomId) {
        Date now = new Date();
        ShiftDO shift1 = new ShiftDO();
        shift1.setOrgCode(orgCode);
        shift1.setRoomId(roomId);
        shift1.setEffectiveStartDate(DateUtil.beginOfDay(DateUtil.beginOfYear(now)));
        ShiftDO shift2 = (ShiftDO) SerializationUtils.clone((Serializable) shift1);
        ShiftDO shift3 = (ShiftDO)SerializationUtils.clone((Serializable) shift1);
        ShiftDO shift4 = (ShiftDO) SerializationUtils.clone((Serializable) shift1);
        ShiftDO shift5 = (ShiftDO) SerializationUtils.clone((Serializable) shift1);
        shift1.setShiftName("夜班1");
        shift1.setStartTimeType("1");
        shift1.setStartTime("00:10");
        shift1.setEndTimeType("1");
        shift1.setEndTime("02:15");
        shift1.setSort(1);
        shift2.setShiftName("夜班2");
        shift2.setStartTimeType("1");
        shift2.setStartTime("02:16");
        shift2.setEndTimeType("1");
        shift2.setEndTime("04:20");
        shift2.setSort(2);
        shift3.setShiftName("夜班3");
        shift3.setStartTimeType("1");
        shift3.setStartTime("04:21");
        shift3.setEndTimeType("1");
        shift3.setEndTime("06:30");
        shift3.setSort(3);
        shift4.setShiftName("早班");
        shift4.setStartTimeType("1");
        shift4.setStartTime("12:00");
        shift4.setEndTimeType("1");
        shift4.setEndTime("13:30");
        shift4.setSort(4);
        shift5.setShiftName("晚班");
        shift5.setStartTimeType("1");
        shift5.setStartTime("22:00");
        shift5.setEndTimeType("2");
        shift5.setEndTime("00:10");
        shift5.setSort(5);

        List<ShiftDO> list = new ArrayList<>();
        list.add(shift1);
        list.add(shift2);
        list.add(shift3);
        list.add(shift4);
        list.add(shift5);
        insertBatch(list);
        return list;
    }

    default void deleteShift(String orgCode, String roomId) {
        delete(new LambdaQueryWrapper<ShiftDO>()
                .eq(ShiftDO::getOrgCode, orgCode)
                .eq(ShiftDO::getRoomId, roomId));
    }

}
