package com.rs.module.pam.service.duty;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.controller.admin.duty.vo.GroupSaveReqVO;
import com.rs.module.pam.controller.admin.duty.vo.GroupVO;
import com.rs.module.pam.entity.duty.GroupDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 监所事务管理-值班组 Service 接口
 *
 * <AUTHOR>
 */
public interface GroupService extends IBaseService<GroupDO>{

    /**
     * 创建监所事务管理-值班组
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createGroup(@Valid GroupSaveReqVO createReqVO);

    /**
     * 删除监所事务管理-值班组
     *
     * @param id 编号
     */
    void deleteGroup(String id);

    /**
     * 获取监室中的值班组
     *
     * @param orgCode 机构编号
     * @param roomId 监室编号
     */
    List<GroupVO> selectList(String orgCode, String roomId);

    /**
     * 获取监室中的值班组
     *
     * @param orgCode 机构编号
     * @param roomId 监室编号
     * @param ids 值班组编号
     */
    List<GroupVO> selectList(String orgCode, String roomId, List<String> ids);


}
