package com.rs.module.pam.service.daily;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.UserApi;
import com.rs.adapter.bsp.api.dto.UserRespDTO;
import com.rs.framework.common.enums.MsgBusTypeEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderRespVO;
import com.rs.module.base.service.pm.PrisonRoomWarderService;
import com.rs.module.pam.controller.admin.daily.vo.CleanDailyDetailVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanDailyRespVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanDaliySaveReqVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanPoliceRectificationVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanRespVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanSaveReqVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanleaderInstructionVO;
import com.rs.module.pam.dao.daily.CleanDao;
import com.rs.module.pam.dto.JoinPositionDTO;
import com.rs.module.pam.entity.daily.CleanDO;
import com.rs.module.pam.enums.CleanCheckStatusEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 监所事务管理-日常清监登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CleanServiceImpl extends BaseServiceImpl<CleanDao, CleanDO> implements CleanService {

    @Resource
    private CleanDao cleanDao;
    @Resource
    private PrisonRoomWarderService prisonerRoomService;
    @Resource
    private UserApi userApi;

    @Override
    public String createClean(CleanSaveReqVO createReqVO) {
        // 插入
        CleanDO clean = BeanUtils.toBean(createReqVO, CleanDO.class);
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        clean.setOperatorSfzh(sessionUser.getIdCard());
        clean.setOperatorXm(sessionUser.getName());
        clean.setOperatorTime(new Date());
        if ("2".equals(createReqVO.getCheckType())) {
            // 安全大检查
            if ("1".equals(createReqVO.getIsHiddenDanger()) || "1".equals(createReqVO.getIsViolation())) {
                clean.setStatus(CleanCheckStatusEnum.DCL.getCode());
            } else {
                clean.setStatus(CleanCheckStatusEnum.YWC.getCode());
            }
        } else {
            clean.setStatus(CleanCheckStatusEnum.YWC.getCode());
        }
        cleanDao.insert(clean);
        // 返回
        return clean.getId();
    }

    @Override
    public void updateClean(CleanSaveReqVO updateReqVO) {
        // 更新
        CleanDO updateObj = BeanUtils.toBean(updateReqVO, CleanDO.class);
        cleanDao.updateById(updateObj);
    }

    @Override
    public void deleteClean(String id) {
        // 删除
        cleanDao.deleteById(id);
    }

    @Override
    public CleanDO getClean(String id) {
        return cleanDao.selectById(id);
    }

    @Override
    public CleanRespVO getCleanRespVO(String id) {
        CleanDO cleanDO = cleanDao.selectById(id);
        if (cleanDO == null) {
            throw new IllegalArgumentException("数据不存在");
        }
        CleanRespVO cleanRespVO = BeanUtils.toBean(cleanDO, CleanRespVO.class);
        String[] split = cleanDO.getCheckRoomId().split(",");
        List<String> areaList = cleanDao.getAreaList(CollUtil.newArrayList(split));
        cleanRespVO.setCheckRoomName(String.join(",", areaList));
        return cleanRespVO;
    }

    @Override
    public void policeRectification(CleanPoliceRectificationVO reqVO) {
        CleanDO cleanDO = cleanDao.selectById(reqVO.getId());
        if (cleanDO == null) {
            throw new IllegalArgumentException("数据不存在");
        }
        if (!cleanDO.getCheckType().equals("2")) {
            throw new IllegalArgumentException("非安全大检查，不允许整改");
        }
        if (!cleanDO.getStatus().equals(CleanCheckStatusEnum.DCL.getCode())) {
            throw new IllegalArgumentException("非待处理状态，不允许整改");
        }
        CleanDO updateObj = new CleanDO();
        updateObj.setId(reqVO.getId());
        updateObj.setRectificationrContent(reqVO.getRectificationrContent());
        updateObj.setRectificationrTime(reqVO.getRectificationrTime());
        updateObj.setRectificationrOperSfz(SessionUserUtil.getSessionUser().getIdCard());
        updateObj.setRectificationrOperXm(SessionUserUtil.getSessionUser().getName());
        updateObj.setStatus(CleanCheckStatusEnum.DLDPS.getCode());
        cleanDao.updateById(updateObj);
    }

    @Override
    public void leaderInstruction(CleanleaderInstructionVO reqVO) {
        CleanDO cleanDO = cleanDao.selectById(reqVO.getId());
        if (cleanDO == null) {
            throw new IllegalArgumentException("数据不存在");
        }
        if (!CleanCheckStatusEnum.DLDPS.getCode().equals(cleanDO.getStatus())) {
            throw new IllegalArgumentException("非待领导批示状态，不允许领导批示");
        }
        CleanDO updateObj = new CleanDO();
        updateObj.setId(reqVO.getId());
        updateObj.setLeaderApprovalComments(reqVO.getLeaderApprovalComments());
        updateObj.setLeaderApproverSfzh(SessionUserUtil.getSessionUser().getIdCard());
        updateObj.setLeaderApproverXm(SessionUserUtil.getSessionUser().getName());
        updateObj.setLeaderApproverTime(new Date());
        updateObj.setStatus(CleanCheckStatusEnum.YWC.getCode());
        cleanDao.updateById(updateObj);
    }

    @Override
    public void sendMsgRcqj() {
        // 日常清监
        List<Map<String, String>> noCleanRoomIdList = cleanDao.getNoCleanRoomIdList("1");
        if (CollUtil.isNotEmpty(noCleanRoomIdList)) {
            for (Map<String, String> map : noCleanRoomIdList) {
                String roomId = map.get("room_id");
                String orgCode = map.get("org_code");
                String orgName = map.get("org_name");
                String roomName = map.get("room_name");
                List<PrisonRoomWarderRespVO> zxgList = prisonerRoomService.getListByRoomId(orgCode, roomId);
                if (CollUtil.isNotEmpty(zxgList)) {
                    List<ReceiveUser> receiveUserList = new ArrayList<>();
                    for (PrisonRoomWarderRespVO prisonRoomWarderRespVO : zxgList) {
                        ReceiveUser receiveUser = new ReceiveUser(prisonRoomWarderRespVO.getPoliceSfzh(), orgCode);
                        receiveUserList.add(receiveUser);
                    }
                    // 发送消息
                    String simpleUUID = IdUtil.fastSimpleUUID();
                    String title = String.format("%s监室未进行日常清监登记，请尽快进行登记！", roomName);                           //消息内容
                    String url = "discipline/securitycheck/dailycleaning?roomId=" + roomId + "&pcId=" + simpleUUID;                                //消息处理页面地址
                    //来源应用
                    String fUser = "admin";                        //来源用户身份证号
                    String fUserName = "系统";                      //来源用户姓名
                    String fOrgCode = orgCode;                        //来源机构代码
                    String fOrgName = orgName;                                //来源机构名称
                    String fXxpt = "pc";
                    try {
                        SendMessageUtil.sendTodoMsg(title, "", url, HttpUtils.getAppCode(), fUser, fUserName, fOrgCode, fOrgName,
                                null, simpleUUID, fXxpt, "", receiveUserList, MsgBusTypeEnum.JS_RCQJ.getCode(), null);
                    } catch (Exception e) {
                        logger.error("发送消息失败:{}", e.getMessage());
                    }

                }
            }
        }
    }

    @Override
    public void sendMsgAqdjc() {
        // 安全大检查
        List<Map<String, String>> noCleanRoomIdList = cleanDao.getNoSecurityCheckList();
        if (CollUtil.isNotEmpty(noCleanRoomIdList)) {
            String pamCleanLeaderRole = BspDbUtil.getParam("PAM_CLEAN_LEADER_ROLE");
            for (Map<String, String> map : noCleanRoomIdList) {
                String cleanCount = map.get("clean_count");
                String orgCode = map.get("org_code");
                String orgName = map.get("org_name");
                List<UserRespDTO> users = userApi.getUserByOrgAndRole(orgCode, pamCleanLeaderRole);
                if (CollUtil.isNotEmpty(users)) {
                    List<ReceiveUser> receiveUserList = new ArrayList<>();
                    for (UserRespDTO user : users) {
                        receiveUserList.add(new ReceiveUser(user.getIdCard(), user.getOrgCode()));
                    }
                    // 发送消息
                    String simpleUUID = IdUtil.fastSimpleUUID();
                    String title = "本月未进行安全大检查登记，请尽快进行登记!";
                    String url = "/discipline/securitycheck/safetychecks?orgCode=" + orgCode + "&pcId=" + simpleUUID;                                //消息处理页面地址
                    //来源应用
                    String fUser = "admin";                        //来源用户身份证号
                    String fUserName = "系统";                                //来源用户姓名
                    String fOrgCode = orgCode;                        //来源机构代码
                    String fOrgName = orgName;                                //来源机构名称
                    String fXxpt = "pc";
                    try {
                        SendMessageUtil.sendTodoMsg(title, "", url, HttpUtils.getAppCode(), fUser, fUserName, fOrgCode, fOrgName,
                                null, simpleUUID, fXxpt, "", receiveUserList, MsgBusTypeEnum.JS_RCQJ.getCode(), null);
                    } catch (Exception e) {
                        logger.error("发送消息失败:{}", e.getMessage());
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createCleanDaily(CleanDaliySaveReqVO createReqVO) {
        List<CleanDailyDetailVO> cleanDailyDetails = createReqVO.getCleanDailyDetails();
        if (CollUtil.isEmpty(cleanDailyDetails)) {
            throw new ServerException("请填写检查详情");
        }
        CleanDO cleanDO = new CleanDO();
        BeanUtils.copyProperties(createReqVO, cleanDO);
        cleanDO.setStatus(CleanCheckStatusEnum.YWC.getCode());
        cleanDO.setCheckType("1");
        boolean flag = cleanDailyDetails.stream()
                .filter(cleanDailyDetailVO -> "1".equals(cleanDailyDetailVO.getJcjg()))
                .findAny().isPresent();
        cleanDO.setIsViolation(flag ? "1" : "0");
        cleanDO.setIsHiddenDanger("0");
        cleanDO.setCheckContent(JSONUtil.toJsonStr(cleanDailyDetails));
        cleanDO.setOperatorSfzh(SessionUserUtil.getSessionUser().getIdCard());
        cleanDO.setOperatorXm(SessionUserUtil.getSessionUser().getName());
        cleanDO.setOperatorTime(new Date());
        List<String> sfzList = new ArrayList<>(0);
        // 岗位协同
        if ("1".equals(cleanDO.getIsJoin())) {
            List<JoinPositionDTO> joinPositionDTOS = createReqVO.getJoinPositionDTOS();
            if (CollUtil.isEmpty(joinPositionDTOS)) {
                throw new ServerException("请填写岗位协同信息");
            }
            for (JoinPositionDTO joinPositionDTO : joinPositionDTOS) {
                List<JoinPositionDTO.JoinPositionPeopleDTO> joinPositionPeople = joinPositionDTO.getJoinPositionPeople();
                if (CollUtil.isNotEmpty(joinPositionPeople)) {
                    for (JoinPositionDTO.JoinPositionPeopleDTO joinPositionPeopleDTO : joinPositionPeople) {
                        sfzList.add(joinPositionPeopleDTO.getSfz());
                    }
                }
            }
            cleanDO.setJoinInfo(JSONUtil.toJsonStr(joinPositionDTOS));
        }
        cleanDao.insert(cleanDO);
        sendAlertMsg(sfzList, cleanDO.getId(), createReqVO.getRemarks());
        return cleanDO.getId();
    }

    private void sendAlertMsg(List<String> sfzList, String cleanId, String remarks) {
        if (sfzList.size() == 0) {
            return;
        }
        String simpleUUID = IdUtil.fastSimpleUUID();
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String content = "日常清监提醒";                            //消息内容
        String url = "/discipline/securitycheck/dailycleaning?id=" + cleanId + "&pcId=" + simpleUUID;                                //消息处理页面地址
        //来源应用
        String fUser = sessionUser.getIdCard();                        //来源用户身份证号
        String fUserName = sessionUser.getName();                      //来源用户姓名
        String fOrgCode = sessionUser.getOrgCode();                        //来源机构代码
        String fOrgName = sessionUser.getOrgName();                                //来源机构名称
        String fXxpt = "pc";
        List<ReceiveUser> receiveUserList = new ArrayList<>(sfzList.size());
        for (String sfz : sfzList) {
            receiveUserList.add(new ReceiveUser(sfz, fOrgCode));
        }
        SendMessageUtil.sendAlertMsg(remarks, content, url, HttpUtils.getAppCode(), fUser, fUserName, fOrgCode, fOrgName,
                null, simpleUUID, fXxpt, "", receiveUserList, HttpUtils.getAppCode(), MsgBusTypeEnum.GJ_TSCSQ.getCode(), null);
    }

    @Override
    public CleanDailyRespVO getCleanDaily(String id) {
        CleanDO cleanDO = cleanDao.selectById(id);
        if (cleanDO == null) {
            throw new IllegalArgumentException("数据不存在");
        }
        CleanDailyRespVO cleanRespVO = BeanUtils.toBean(cleanDO, CleanDailyRespVO.class);
        String[] split = cleanDO.getCheckRoomId().split(",");
        List<String> areaList = cleanDao.getAreaList(CollUtil.newArrayList(split));
        cleanRespVO.setCheckRoomName(String.join(",", areaList));
        cleanRespVO.setCleanDailyDetails(JSONUtil.toList(cleanDO.getCheckContent(), CleanDailyDetailVO.class));
        if ("1".equals(cleanDO.getIsJoin())) {
            String joinInfo = cleanDO.getJoinInfo();
            if (StrUtil.isNotBlank(joinInfo)) {
                List<JoinPositionDTO> positionDTOS = JSONUtil.toList(joinInfo, JoinPositionDTO.class);
                cleanRespVO.setJoinPositionDTOS(positionDTOS);
                cleanRespVO.setJoinGw(positionDTOS.stream().map(JoinPositionDTO::getJoinName)
                        .collect(Collectors.joining(",")));
            }
        }
        return cleanRespVO;
    }
}
