package com.rs.module.pam.service.daily;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.module.pam.controller.admin.daily.vo.CleanDailyRespVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanDaliySaveReqVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanPoliceRectificationVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanRespVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanSaveReqVO;
import com.rs.module.pam.controller.admin.daily.vo.CleanleaderInstructionVO;
import com.rs.module.pam.entity.daily.CleanDO;

/**
 * 监所事务管理-日常清监登记 Service 接口
 *
 * <AUTHOR>
 */
public interface CleanService extends IBaseService<CleanDO>{

    /**
     * 创建监所事务管理-日常清监登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createClean(CleanSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-日常清监登记
     *
     * @param updateReqVO 更新信息
     */
    void updateClean(CleanSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-日常清监登记
     *
     * @param id 编号
     */
    void deleteClean(String id);

    /**
     * 获得监所事务管理-日常清监登记
     *
     * @param id 编号
     * @return 监所事务管理-日常清监登记
     */
    CleanDO getClean(String id);


    CleanRespVO getCleanRespVO(String id);

    /**
     * 整改
     * @param reqVO
     */
    void policeRectification(CleanPoliceRectificationVO reqVO);

    /**
     * 领导批示
     * @param reqVO
     */
    void leaderInstruction(CleanleaderInstructionVO reqVO);

    /**
     * 日常清监检查推送消息给主协管民警
     */
    void sendMsgRcqj();

    /**
     * 安全大检查推送消息给监所领导
     */
    void sendMsgAqdjc();

    /**
     * 创建日常清监检查
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCleanDaily(CleanDaliySaveReqVO createReqVO);

    /**
     * 获得日常清监检查
     * @param id
     * @return
     */
    CleanDailyRespVO getCleanDaily(String id);
}
