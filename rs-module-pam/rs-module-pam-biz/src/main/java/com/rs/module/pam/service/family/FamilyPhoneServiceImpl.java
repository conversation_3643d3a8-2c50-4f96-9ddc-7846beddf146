package com.rs.module.pam.service.family;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.pam.controller.admin.family.vo.FamilyPhoneApprovalReqVO;
import com.rs.module.pam.controller.admin.family.vo.FamilyPhoneListReqVO;
import com.rs.module.pam.controller.admin.family.vo.FamilyPhonePageReqVO;
import com.rs.module.pam.controller.admin.family.vo.FamilyPhoneSaveReqVO;
import com.rs.module.pam.dao.family.FamilyPhoneDao;
import com.rs.module.pam.entity.family.FamilyPhoneDO;
import com.rs.module.pam.enums.FamilyPhoneStatusEnum;
import com.rs.module.pam.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 监所事务管理-亲情电话 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class FamilyPhoneServiceImpl extends BaseServiceImpl<FamilyPhoneDao, FamilyPhoneDO> implements FamilyPhoneService {

    @Resource
    private FamilyPhoneDao familyPhoneDao;

    private static final String defKey = "qinqingdianhuashenpiliucheng";

    private static final String msgUrl = "/#/familyContact/phone?id=";

    @Override
    public String createFamilyPhone(FamilyPhoneSaveReqVO createReqVO) {
        // 插入
        FamilyPhoneDO familyPhone = BeanUtils.toBean(createReqVO, FamilyPhoneDO.class);
        familyPhone.setApplyTime(new Date());
        familyPhone.setStatus(FamilyPhoneStatusEnum.DSH.getCode());
        familyPhoneDao.insert(familyPhone);

        //启动流程审批
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", familyPhone.getId());
        JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode() + "-" + defKey, familyPhone.getId(),
                "亲情电话流程审批", msgUrl + familyPhone.getId(), variables);
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            familyPhone.setActInstId(bpmTrail.getString("actInstId"));
            familyPhone.setTaskId(bpmTrail.getString("taskId"));
            familyPhoneDao.updateById(familyPhone);
        } else {
            throw new ServerException("流程启动失败");
        }

        // 返回
        return familyPhone.getId();
    }

    @Override
    public void updateFamilyPhone(FamilyPhoneSaveReqVO updateReqVO) {
        // 校验存在
        validateFamilyPhoneExists(updateReqVO.getId());
        // 更新
        FamilyPhoneDO updateObj = BeanUtils.toBean(updateReqVO, FamilyPhoneDO.class);
        familyPhoneDao.updateById(updateObj);
    }

    @Override
    public void deleteFamilyPhone(String id) {
        // 校验存在
        validateFamilyPhoneExists(id);
        // 删除
        familyPhoneDao.deleteById(id);
    }

    private void validateFamilyPhoneExists(String id) {
        if (familyPhoneDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-亲情电话数据不存在");
        }
    }

    @Override
    public FamilyPhoneDO getFamilyPhone(String id) {
        return familyPhoneDao.selectById(id);
    }

    @Override
    public PageResult<FamilyPhoneDO> getFamilyPhonePage(FamilyPhonePageReqVO pageReqVO) {
        return familyPhoneDao.selectPage(pageReqVO);
    }

    @Override
    public List<FamilyPhoneDO> getFamilyPhoneList(FamilyPhoneListReqVO listReqVO) {
        return familyPhoneDao.selectList(listReqVO);
    }

    @Override
    public void approvalFamilyPhone(FamilyPhoneApprovalReqVO approvalReqVO) {
        FamilyPhoneDO familyPhoneDO = familyPhoneDao.selectById(approvalReqVO.getId());
        if (familyPhoneDO == null) {
            throw new ServerException("监所事务管理-亲情电话数据不存在");
        }
        // 更新
        familyPhoneDO.setStatus(approvalReqVO.getStatus());
        familyPhoneDO.setApprovalComments(approvalReqVO.getApprovalComments());
        BspApproceStatusEnum bspApproceStatusEnum;
        if (StringUtils.equals(FamilyPhoneStatusEnum.SHTG.getCode(), approvalReqVO.getStatus())) {
            bspApproceStatusEnum = BspApproceStatusEnum.PASSED_END;
            familyPhoneDO.setApprovalResult("" + BspApproceStatusEnum.PASSED_END.getCode());
        } else if (StringUtils.equals(FamilyPhoneStatusEnum.SHBTG.getCode(), approvalReqVO.getStatus())) {
            bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
            familyPhoneDO.setApprovalResult("" + BspApproceStatusEnum.NOT_PASSED_END.getCode());
        } else {
            throw new ServerException("非法审批状态：" + approvalReqVO.getStatus());
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        //校验当前人有没有审批权限
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(familyPhoneDO.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }

        familyPhoneDO.setApproverXm(sessionUser.getName());
        familyPhoneDO.setApproverSfzh(sessionUser.getIdCard());
        familyPhoneDO.setApproverTime(new Date());

        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", familyPhoneDO.getId());
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ||
                BspApproceStatusEnum.NOT_PASSED_END.getCode() == bspApproceStatusEnum.getCode();
        JSONObject result = BspApprovalUtil.approvalProcess(HttpUtils.getAppCode() + "-" + defKey, familyPhoneDO.getActInstId(), familyPhoneDO.getTaskId(), familyPhoneDO.getId(),
                bspApproceStatusEnum, approvalReqVO.getApprovalComments(), null, null, terminateTask,
                variables, nowApproveUser, HttpUtils.getAppCode());
        log.info("=======result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            familyPhoneDO.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程启动失败");
        }
        familyPhoneDao.updateById(familyPhoneDO);

    }

    @Override
    public List<FamilyPhoneDO> getFamilyPhoneList10(String jgrybm, String id) {
        LambdaQueryWrapper<FamilyPhoneDO> wrapper = Wrappers.lambdaQuery(FamilyPhoneDO.class).eq(FamilyPhoneDO::getStatus,
                FamilyPhoneStatusEnum.SHTG.getCode()).eq(FamilyPhoneDO::getJgrybm, jgrybm);
        if (StringUtils.isNotEmpty(id)) {
            wrapper.ne(FamilyPhoneDO::getId, id);
        }
        wrapper.orderByDesc(FamilyPhoneDO::getAddTime).last("limit 10");
        return this.list(wrapper);
    }

    @Override
    public PageResult<FamilyPhoneDO> getAppFamilyPhonePage(int pageNo, int pageSize, String jgrybm, String type) {
        Map<String, Date> commonAppRecordPeriod = CommonUtils.getCommonAppRecordPeriod(type);
        return familyPhoneDao.getAppFamilyPhonePage(pageNo, pageSize, jgrybm,
                commonAppRecordPeriod.get("startTime"), commonAppRecordPeriod.get("endTime"));
    }


}
