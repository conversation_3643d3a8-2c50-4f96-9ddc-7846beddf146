package com.rs.module.pam.dao.complaint;

import java.util.*;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rs.module.pam.entity.complaint.SuggestionDO;
import org.apache.ibatis.annotations.Mapper;
import com.rs.module.pam.controller.admin.complaint.vo.*;
import com.bsp.common.orm.mybatis.mapper.IBaseDao;
import org.apache.ibatis.annotations.Param;

/**
 * 监所事务管理-投诉建议 Dao
 *
 * <AUTHOR>
 */
@Mapper
public interface SuggestionDao extends IBaseDao<SuggestionDO> {


    default PageResult<SuggestionDO> selectPage(SuggestionPageReqVO reqVO) {
        Page<SuggestionDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<SuggestionDO> wrapper = new LambdaQueryWrapperX<SuggestionDO>()
                .eqIfPresent(SuggestionDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(SuggestionDO::getJgryxm, reqVO.getJgryxm())
                .eqIfPresent(SuggestionDO::getRoomId, reqVO.getRoomId())
                .likeIfPresent(SuggestionDO::getRoomName, reqVO.getRoomName())
                .eqIfPresent(SuggestionDO::getComplaintType, reqVO.getComplaintType())
                .eqIfPresent(SuggestionDO::getComplaintContent, reqVO.getComplaintContent())
                .eqIfPresent(SuggestionDO::getHandleStatus, reqVO.getHandleStatus())
                .eqIfPresent(SuggestionDO::getHandleUserSfzh, reqVO.getHandleUserSfzh())
                .likeIfPresent(SuggestionDO::getHandleUserName, reqVO.getHandleUserName())
                .eqIfPresent(SuggestionDO::getHandleFeedback, reqVO.getHandleFeedback());
        if (reqVO.getOrderFields() != null) {
            page.setOrders(reqVO.getOrderFields());
        } else {
            wrapper.orderByDesc(SuggestionDO::getAddTime);
        }
        Page<SuggestionDO> suggestionPage = selectPage(page, wrapper);
        return new PageResult<>(suggestionPage.getRecords(), suggestionPage.getTotal());
    }

    default List<SuggestionDO> selectList(SuggestionListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SuggestionDO>()
                .eqIfPresent(SuggestionDO::getJgrybm, reqVO.getJgrybm())
                .eqIfPresent(SuggestionDO::getJgryxm, reqVO.getJgryxm())
                .eqIfPresent(SuggestionDO::getRoomId, reqVO.getRoomId())
                .likeIfPresent(SuggestionDO::getRoomName, reqVO.getRoomName())
                .eqIfPresent(SuggestionDO::getComplaintType, reqVO.getComplaintType())
                .eqIfPresent(SuggestionDO::getComplaintContent, reqVO.getComplaintContent())
                .eqIfPresent(SuggestionDO::getHandleStatus, reqVO.getHandleStatus())
                .eqIfPresent(SuggestionDO::getHandleUserSfzh, reqVO.getHandleUserSfzh())
                .likeIfPresent(SuggestionDO::getHandleUserName, reqVO.getHandleUserName())
                .eqIfPresent(SuggestionDO::getHandleFeedback, reqVO.getHandleFeedback())
                .orderByDesc(SuggestionDO::getAddTime));
    }


    Page<SuggestionDO> pageRecord(Page<SuggestionDO> page, @Param("jgrybm") String jgrybm,
                                        @Param("roomId") String roomId,
                                        @Param("startTime") Date startTime,
                                        @Param("endTime") Date endTime);
}
