package com.rs.module.pam.controller.admin.duty.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CurrentDutyRecordsInfoVO {

    @ApiModelProperty("值班记录id")
    private String id;

    @ApiModelProperty("班次id")
    private String shiftId;

    @ApiModelProperty("班次名称")
    private String shiftName;

    @ApiModelProperty("班次开始时间")
    private Date shiftStartTime;

    @ApiModelProperty("班次结束时间")
    private Date shiftEndTime;

    @ApiModelProperty("值班日期")
    private Date dutyDate;

    @ApiModelProperty("值班人员编号")
    private List<String> jgrybmList;

}
