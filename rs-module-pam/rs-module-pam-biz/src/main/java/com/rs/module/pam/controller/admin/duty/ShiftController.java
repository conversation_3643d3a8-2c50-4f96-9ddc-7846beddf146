package com.rs.module.pam.controller.admin.duty;

import com.bsp.common.cons.CommonConstants;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.pam.controller.admin.duty.vo.DutyShiftSaveReqVO;
import com.rs.module.pam.controller.admin.duty.vo.ShiftRespVO;
import com.rs.module.pam.entity.bed.ConfigDO;
import com.rs.module.pam.entity.duty.ShiftDO;
import com.rs.module.pam.service.duty.ShiftService;
import com.rs.util.ThreadPoolUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "值班管理-值班班次")
@RestController
@RequestMapping("/pam/duty/shift")
@Validated
public class ShiftController {

    @Resource
    private ShiftService shiftService;
    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;

    /**
     * 监室值班-班次管理-保存编辑
     */
    @GetMapping("/getShift")
    @ApiOperation(value = "监室值班-获取班次信息")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "orgCode", value = "机构编号"),
            @ApiImplicitParam(name = "roomId", value = "监室id")
    })
    @BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{\"监室编号\":\"{{#reqVO.roomId}}\"}")
    public CommonResult<List<ShiftRespVO>> getShift(@RequestParam("orgCode") String orgCode, @RequestParam("roomId") String roomId) {
        List<ShiftRespVO> shift = shiftService.getShift(orgCode, roomId);
        for (ShiftRespVO shiftRespVO : shift) {
            shiftRespVO.setContinueTime(calculateTimeDiff(shiftRespVO.getStartTime(), shiftRespVO.getStartTimeType(),
                    shiftRespVO.getEndTime(), shiftRespVO.getEndTimeType()));
        }
        return success(shift);
    }

    /**
     * 计算两个时间之间的持续时间
     * @param startStr
     * @param startType
     * @param endStr
     * @param endType
     * @return
     */
    public static String calculateTimeDiff(String startStr, String startType,
                                           String endStr, String endType) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");

        // 处理开始时间
        LocalTime start = processTime(startStr, startType, formatter);
        // 处理结束时间
        LocalTime end = processTime(endStr, endType, formatter);

        Duration duration = Duration.between(start, end);
        if (duration.isNegative()) {
            duration = duration.plusDays(1);
        }

        long hours = duration.toHours();
        long minutes = duration.toMinutes() % 60;

        return String.format("%02d:%02d", hours, minutes);
    }

    private static LocalTime processTime(String timeStr, String type,
                                         DateTimeFormatter formatter) {
        LocalTime time = LocalTime.parse(timeStr, formatter);
        if ("2".equals(type)) { // 加一天
            return time.plusHours(24);
        }
        return time;
    }

    /**
     * 监室值班-班次管理-保存编辑
     */
    @PostMapping("/shiftManageSave")
    @ApiOperation(value = "监室值班-班次管理-保存编辑")
    @BusTrace(busType = BusTypeEnum.YEWU_JSZB, condition = "false", content = "{\"监室编号\":\"{{#reqVO.roomId}}\"}")
    public CommonResult<String> shiftManageSave(@RequestBody @Validated DutyShiftSaveReqVO reqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        reqVO.setUserIdCard(sessionUser.getIdCard());
        reqVO.setUserName(sessionUser.getName());
        reqVO.setOrgCode(sessionUser.getOrgCode());
        reqVO.setOrgName(sessionUser.getOrgName());
        List<String> roomIds = new ArrayList<>();
        if (CommonConstants.CONSTANTS_TRUE.equals(reqVO.getIsGlobal())) {
            List<AreaPrisonRoomDO> roomList = areaPrisonRoomService.getRoomByOrgCode(sessionUser.getOrgCode());
            roomIds.addAll(roomList.stream().map(room -> room.getId()).collect(Collectors.toList()));
        } else {
            roomIds.add(reqVO.getRoomId());
        }
        ThreadPoolUtil.getPool().execute(new Runnable() {
            @Override
            public void run() {
                for (String roomId : roomIds) {
                    reqVO.setRoomId(roomId);
                    shiftService.shiftManageSave(reqVO);
                }
            }
        });

        return success("保存成功");
    }

    @RequestMapping(value = "/initialize", method = RequestMethod.GET)
    @ApiOperation(value = "值班班次初始化")
    public CommonResult<?> initialize() {
        List<AreaPrisonRoomDO> roomList = areaPrisonRoomService.list();
        List<ShiftDO> shiftList = shiftService.list();
        List<String> idList = shiftList.stream().map(config -> config.getRoomId()).collect(Collectors.toList());
        List<String> result = new ArrayList<>();
        for (AreaPrisonRoomDO prisonRoomDO : roomList) {
            if (idList.contains(prisonRoomDO.getId())) {
                continue;
            }
            try {
                shiftService.createDefaultShift(prisonRoomDO.getOrgCode(), prisonRoomDO.getId());
                result.add(String.format("机构：%s，监室：%s值班班次初始化成功", prisonRoomDO.getOrgCode(), prisonRoomDO.getId()));
            } catch (Exception e) {
                result.add(String.format("机构：%s，监室：%s值班班次初始化失败：", prisonRoomDO.getOrgCode(), prisonRoomDO.getId(), e.getMessage()));
            }
        }
        return success(result);
    }

}
