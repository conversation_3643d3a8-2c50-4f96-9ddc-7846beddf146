package com.rs.module.pam.service.cook;

import java.util.*;
import javax.validation.*;
import com.rs.module.pam.controller.admin.cook.vo.*;
import com.rs.module.pam.entity.cook.PersionInfoDO;
import com.rs.framework.common.pojo.PageResult;
import com.bsp.common.orm.mybatis.service.IBaseService;

/**
 * 监所事务管理-伙房人员信息 Service 接口
 *
 * <AUTHOR>
 */
public interface PersionInfoService extends IBaseService<PersionInfoDO>{

    /**
     * 创建监所事务管理-伙房人员信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPersionInfo(PersionInfoSaveReqVO createReqVO);

    /**
     * 更新监所事务管理-伙房人员信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePersionInfo(PersionInfoSaveReqVO updateReqVO);

    /**
     * 删除监所事务管理-伙房人员信息
     *
     * @param id 编号
     */
    void deletePersionInfo(String id);

    /**
     * 获得监所事务管理-伙房人员信息
     *
     * @param id 编号
     * @return 监所事务管理-伙房人员信息
     */
    PersionInfoDO getPersionInfo(String id);


}
