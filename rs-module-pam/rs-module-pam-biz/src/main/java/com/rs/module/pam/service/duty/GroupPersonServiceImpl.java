package com.rs.module.pam.service.duty;

import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.rs.module.pam.dao.duty.GroupPersonDao;
import com.rs.module.pam.entity.duty.GroupPersonDO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 监所事务管理-值班组人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GroupPersonServiceImpl extends BaseServiceImpl<GroupPersonDao, GroupPersonDO> implements GroupPersonService {

    @Resource
    private GroupPersonDao groupPersonDao;



}
