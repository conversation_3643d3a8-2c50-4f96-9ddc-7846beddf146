package com.rs.module.pam.controller.app.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-安全大检查登记新增/修改 Request VO")
@Data
public class AppCleanSaveReqVO {

    @ApiModelProperty("检查监室，多个逗号分割")
    @NotBlank(message = "检查监室，多个逗号分割不能为空")
    private String checkRoomId;

    @ApiModelProperty("带队领导身份证号，多个逗号分割")
    @NotBlank(message = "带队领导身份证号不能为空")
    private String leaderUserSfzh;

    @ApiModelProperty("带队领导名称，多个逗号分割")
    @NotBlank(message = "带队领导名称不能为空")
    private String leaderUserName;

    @ApiModelProperty("参加民警身份证号，多个逗号分割")
    @NotBlank(message = "参加民警身份证号不能为空")
    private String involvementUserSfzh;

    @ApiModelProperty("参加民警名称，多个逗号分割")
    @NotBlank(message = "参加民警名称不能为空")
    private String involvementUserName;

    @ApiModelProperty("检查类型(1: 日常清监检查; 2:安全大检查)")
    @NotBlank(message = "检查类型不能为空")
    private String checkType;

    @ApiModelProperty("检查时间")
    @NotNull(message = "检查时间不能为空")
    private Date checkTime;

    @ApiModelProperty("检查项明细")
    private List<CheckDetail> checkItems;

    @Getter
    @Setter
    public static class CheckDetail {
        @ApiModelProperty("主键")
        private String id;
        @ApiModelProperty("检查类型")
        private String checkType;
        @ApiModelProperty("检查项目名称")
        private String checkItemName;
        @ApiModelProperty("检查项目结果：0-异常，1-正常")
        private String status;
    }


}
