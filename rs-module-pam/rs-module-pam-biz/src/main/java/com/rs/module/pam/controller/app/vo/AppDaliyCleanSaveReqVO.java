package com.rs.module.pam.controller.app.vo;

import com.rs.module.pam.controller.admin.daily.vo.CleanDailyDetailVO;
import com.rs.module.pam.dto.JoinPositionDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-日常清监登记新增/修改 Request VO")
@Data
public class AppDaliyCleanSaveReqVO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("检查监室，多个逗号分割")
    @NotBlank(message = "检查监室，多个逗号分割不能为空")
    private String checkRoomId;

    @ApiModelProperty("带队领导ID，多个逗号分割")
    private String leaderUserSfzh;

    @ApiModelProperty("带队领导名称，多个逗号分割")
    private String leaderUserName;

    @ApiModelProperty("参加民警，多个逗号分割")
    @NotBlank(message = "参加民警，多个逗号分割不能为空")
    private String involvementUserSfzh;

    @ApiModelProperty("参加民警ID，多个逗号分割")
    @NotBlank(message = "参加民警ID，多个逗号分割不能为空")
    private String involvementUserName;

    @ApiModelProperty("推送内容")
    private String remarks;

    @ApiModelProperty("其他参加人")
    private String otherParticipants;

    @ApiModelProperty("是否岗位协同：0 否, 1 是")
    private String isJoin;

    @ApiModelProperty("协同信息")
    private List<JoinPositionDTO> joinPositionDTOS;

    @ApiModelProperty("检查详情")
    private List<CleanDailyDetailVO> cleanDailyDetails;


}
