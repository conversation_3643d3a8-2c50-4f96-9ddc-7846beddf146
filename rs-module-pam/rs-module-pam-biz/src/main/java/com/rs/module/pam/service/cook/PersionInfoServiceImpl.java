package com.rs.module.pam.service.cook;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rs.module.pam.controller.admin.cook.vo.*;
import com.rs.module.pam.entity.cook.PersionInfoDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.pam.dao.cook.PersionInfoDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 监所事务管理-伙房人员信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PersionInfoServiceImpl extends BaseServiceImpl<PersionInfoDao, PersionInfoDO> implements PersionInfoService {

    @Resource
    private PersionInfoDao persionInfoDao;

    @Override
    public String createPersionInfo(PersionInfoSaveReqVO createReqVO) {
        // 插入
        PersionInfoDO persionInfo = BeanUtils.toBean(createReqVO, PersionInfoDO.class);
        persionInfoDao.insert(persionInfo);
        // 返回
        return persionInfo.getId();
    }

    @Override
    public void updatePersionInfo(PersionInfoSaveReqVO updateReqVO) {
        // 校验存在
        validatePersionInfoExists(updateReqVO.getId());
        // 更新
        PersionInfoDO updateObj = BeanUtils.toBean(updateReqVO, PersionInfoDO.class);
        persionInfoDao.updateById(updateObj);
    }

    @Override
    public void deletePersionInfo(String id) {
        // 校验存在
        validatePersionInfoExists(id);
        // 删除
        persionInfoDao.deleteById(id);
    }

    private void validatePersionInfoExists(String id) {
        if (persionInfoDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-伙房人员信息数据不存在");
        }
    }

    @Override
    public PersionInfoDO getPersionInfo(String id) {
        return persionInfoDao.selectById(id);
    }

}
