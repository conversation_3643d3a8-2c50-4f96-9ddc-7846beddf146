package com.rs.module.pam.controller.admin.psy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanListReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanRespVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPlanSaveReqVO;
import com.rs.module.pam.controller.admin.psy.vo.EvalPushTargePageReqVO;
import com.rs.module.pam.dto.CronConfigDTO;
import com.rs.module.pam.entity.psy.EvalPlanDO;
import com.rs.module.pam.enums.PsyTriggerTypeEnum;
import com.rs.module.pam.service.psy.EvalPlanService;
import com.rs.module.pam.util.CronUtils;
import com.rs.util.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.rs.framework.common.pojo.CommonResult.success;

@Slf4j
@Api(tags = "心理测评-心理测评计划")
@RestController
@RequestMapping("/pam/psy/evalPlan")
@Validated
public class EvalPlanController {

    @Resource
    private EvalPlanService evalPlanService;

    @PostMapping("/getLastExceTime")
    @ApiOperation(value = "心理测评-获取最新执行时间")
    public CommonResult<String> getLastExceTime(@RequestBody Map<String, String> objectMap) {
        String lastTime = "";
        try {
            String triggerType = objectMap.get("triggerType");
            String triggerConfig = objectMap.get("triggerConfig");
            PsyTriggerTypeEnum typeEnum = PsyTriggerTypeEnum.getEnumByCode(triggerType);
            List<String> timeList = new ArrayList<>(0);
            switch (typeEnum) {
                case ZQCF:
                    CronConfigDTO configDTO = JSONUtil.toBean(triggerConfig, CronConfigDTO.class);
                    if (configDTO.getStartTime() != null && configDTO.getEndTime() != null) {
                        String formatStart = DateUtil.format(configDTO.getStartTime(), "yyyy-MM-dd");
                        String formatEnd = DateUtil.format(configDTO.getEndTime(), "yyyy-MM-dd");
                        if (formatStart.equals(formatEnd)) {
                            throw new IllegalArgumentException("开始时间不能和结束时间是同一天");
                        }
                        List<String> lastExceTime = evalPlanService.getLastExceTime(evalPlanService.getCron(configDTO), configDTO.getEndTime());
                        if (CollUtil.isNotEmpty(lastExceTime)) {
                            timeList.addAll(lastExceTime);
                        }
                    }
                    if (StrUtil.isNotBlank(configDTO.getCron())) {
                        List<String> lastExceTime = evalPlanService.getLastExceTime(configDTO.getCron(), configDTO.getEndTime());
                        if (CollUtil.isNotEmpty(lastExceTime)) {
                            timeList.addAll(lastExceTime);
                        }
                    }
                    break;
                case DCCF:
                    Date triggerDate = cn.hutool.core.date.DateUtil.parse(triggerConfig, "yyyy-MM-dd HH:mm:ss");
                    List<String> lastExceTime = evalPlanService.getLastExceTime(CronUtils.generateOneTimeCron(triggerDate), null);
                    if (CollUtil.isNotEmpty(lastExceTime)) {
                        timeList.addAll(lastExceTime);
                    }
                    break;
                case TJCF:
                    break;
                default:
                    throw new ServerException("监所事务管理-心理测评计划触发类型错误");
            }
            if (CollUtil.isNotEmpty(timeList)) {
                lastTime = CollUtil.getFirst(timeList);
            }
        } catch (IllegalArgumentException e) {
            log.error("参数错误", e);
            return CommonResult.error(e.getMessage());
        } catch (Exception e) {
        }
        return success(lastTime);
    }

    @Data
    private static class CronConfigAllDTO {
        private CronConfigDTO cronConfig;
        private String triggerType;
    }

    @PostMapping("/createOrUpdate")
    @ApiOperation(value = "创建或者更新-心理测评计划")
    public CommonResult<String> createOrUpdate(@Valid @RequestBody EvalPlanSaveReqVO createReqVO) {
        return success(evalPlanService.createOrUpdate(createReqVO));
    }

    @PostMapping("/updateEableStatus")
    @ApiOperation(value = "更新启用状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "编号", required = true),
            @ApiImplicitParam(name = "enableStatus", value = "状态(0: 禁用 1: 启用)", required = true)
    })
    public CommonResult updateEableStatus(@RequestParam("id") String id, @RequestParam("enableStatus") String enableStatus) {
        evalPlanService.updateEableStatus(id, enableStatus);
        return success();
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得-心理测评计划", response = EvalPlanRespVO.class)
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<EvalPlanRespVO> getEvalPlan(@RequestParam("id") String id) {
        EvalPlanRespVO evalPlan = evalPlanService.getEvalPlanRespVO(id);
        return success(evalPlan);
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得-心理测评计划列表")
    public CommonResult<List<EvalPlanRespVO>> getEvalPlanList(@Valid @RequestBody EvalPlanListReqVO listReqVO) {
        if (StrUtil.isEmpty(listReqVO.getOrgCode()) && !SessionUserUtil.getSessionUser().getIsAdmin()) {
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        List<EvalPlanDO> list = evalPlanService.getEvalPlanList(listReqVO);
        return success(BeanUtils.toBean(list, EvalPlanRespVO.class));
    }

    @PostMapping("/getPushTargeList")
    @ApiOperation(value = "获得-心理测评计划推送对象")
    public CommonResult getPushTargeList(@Valid @RequestBody EvalPushTargePageReqVO pageReqVO) {
        List<Map<String, String>> pushTargeList = evalPlanService.getPushTargeList(pageReqVO);
        return success(pushTargeList);
    }

    @GetMapping("/eval-plan-test-job")
    @ApiOperation(value = "心理测评定时任务测试")
    public CommonResult<Boolean> evalPlanTestJob(@RequestParam("jobId") String jobId,
                                                 @RequestParam("logId") String logId) {
        evalPlanService.sendPlan(jobId, logId);
        return success(true);
    }

}
