package com.rs.module.pam.service.screen;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.entity.pm.PrisonerTagDO;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.pam.controller.admin.screen.vo.JsswItemRespVO;
import com.rs.module.pam.dao.screen.GjLargeScreenDao;
import com.rs.module.pam.entity.screen.CommonMeetingDO;
import com.rs.module.pam.entity.screen.FocusOnPersonnelDO;
import com.rs.module.pam.entity.screen.WgdjDO;
import com.rs.module.pam.service.screen.task.CswgTask;
import com.rs.module.pam.service.screen.task.WcdtTask;
import com.rs.module.pam.service.screen.task.ZdgzryTask;
import com.rs.util.ThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Service
@Validated
@Slf4j
public class GjLargeScreenServiceImpl implements GjLargeScreenService {

    @Resource
    private GjLargeScreenDao gjLargeScreenDao;

    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;

    @Autowired
    private SqlSessionFactory sqlSessionFactory;


    /**
     * 管教大屏查询粒度-监室 GJ_LARGE_SCREEN_ROOM = "01"
     * 管教大屏查询粒度-区域 GJ_LARGE_SCREEN_AREA = "02"
     * 管教大屏查询粒度-派出所 GJ_LARGE_SCREEN_ORG = "03"
     */

    @Override
    public JSONObject getJydtJyrs(String code, String codeType) {
        //羁押人数
        // 当日：时时刻刻的在押人数 A
        //上月月底人数：A - (本月入所 人数)+（本月出所且非本月入所 人数）
        JSONObject jsonObject = gjLargeScreenDao.getJydtJyxx(code, codeType);
        if (Objects.isNull(jsonObject)) {
            jsonObject = new JSONObject();
        }
        //当前在押人数
        int dqzyrs = jsonObject.getIntValue("dqzyrs");
        //本月入所人数
        int byrsrs = jsonObject.getIntValue("byrsrs");

        //本月出所人数且非本月入所人数
        int bycsrsqfbyrsrs = jsonObject.getIntValue("bycsrsqfbyrsrs");

        //上月底在押人数
        int sydzyrs = dqzyrs - byrsrs + bycsrsqfbyrsrs;

        int cqjyrs = jsonObject.getIntValue("cqjyrs");

        JSONObject result = new JSONObject();
        // 当日羁押人数 == 在押总数
        result.put("todayJyCount", dqzyrs);
        // 上月底羁押人数
        result.put("lastMonthJyCount", sydzyrs);
        // 较上月 差值
        result.put("jyCountDiff", dqzyrs - sydzyrs);
        // 超期羁押人数
        result.put("timeoutJyCount", cqjyrs);

        // 获取未归数据
        getCswgInfo(result, code, codeType);

        return result;
    }

    private void getCswgInfo(JSONObject result, String code, String codeType) {
        List<String> methodNames = new ArrayList<>(Arrays.asList("csjywgCount", "tjcswgCount"));
        CountDownLatch countDownLatch = new CountDownLatch(methodNames.size());
        ThreadPoolExecutor pool = ThreadPoolUtil.getPool();
        //todo 临时出所未归  lscswgCount

        List<Future<JSONObject>> futures = new CopyOnWriteArrayList<>();
        for (String methodName : methodNames) {
            CswgTask wcdt = new CswgTask(gjLargeScreenDao, methodName, code, codeType, countDownLatch);
            futures.add(pool.submit(wcdt));
        }
        try {
            countDownLatch.await(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        for (Future<JSONObject> future : futures) {
            try {
                result.putAll(future.get());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public JSONObject getFxryfb(String code, String codeType) {
        JSONObject jsonObject = gjLargeScreenDao.getFxryfb(code, codeType);
        if (Objects.isNull(jsonObject)) {
            jsonObject = new JSONObject();
        }
        int zs = jsonObject.getIntValue("zs");
        int rl1 = jsonObject.getIntValue("rl1");
        int rl2 = jsonObject.getIntValue("rl2");
        int rl3 = jsonObject.getIntValue("rl3");
        JSONObject result = new JSONObject();
        // 风险人总数
        result.put("riskCount", zs);
        // 一级风险
        result.put("riskLevel1", rl1);
        // 二级分析
        result.put("riskLevel2", rl2);
        // 三级分析
        result.put("riskLevel3", rl3);
        if (zs == 0) {
            result.put("riskLevel1Percent", 0);
            result.put("riskLevel2Percent", 0);
            result.put("riskLevel3Percent", 0);
        } else {
            // 风险占比
            long round1 = Math.round(rl1 * 100d / zs);
            long round2 = Math.round(rl2 * 100d / zs);
            result.put("riskLevel1Percent", round1);
            result.put("riskLevel2Percent", round2);
            result.put("riskLevel3Percent", 100 - round1 - round2);
        }

        return result;
    }

    @Override
    public JSONObject getJydtJstz(String code, String codeType) {

        JSONObject tzrjxx = gjLargeScreenDao.getTzrjxx(code, codeType);
        if (Objects.isNull(tzrjxx)) {
            tzrjxx = new JSONObject();
        }
        JSONObject tzcjxx = gjLargeScreenDao.getTzcjxx(code, codeType);
        if (Objects.isNull(tzcjxx)) {
            tzcjxx = new JSONObject();
        }
        JSONObject result = new JSONObject();
        // 今日调整入监数
        int jrtzrj = tzrjxx.getIntValue("jrtzrj");
        result.put("todayInRoomCount", jrtzrj);
        // 昨日调整入监数
        int zrtzrj = tzrjxx.getIntValue("zrtzrj");
        result.put("yesterdayInRoomCount", zrtzrj);
        result.put("inRoomDiffCount", jrtzrj - zrtzrj);
        // 今日调整出监数
        int jrtzcj = tzcjxx.getIntValue("jrtzcj");
        result.put("todayOutRoomCount", jrtzcj);
        // 昨日调整出监数
        int zrtzcj = tzcjxx.getIntValue("zrtzcj");
        result.put("yesterdayOutRoomCount", zrtzcj);
        result.put("outRoomDiffCount", jrtzcj - zrtzcj);
        return result;
    }

    @Override
    public int getWcdtTx(String code, String codeType) {
        // 当日-提讯数
        return gjLargeScreenDao.getWcdtTx(code, codeType);
    }

    @Override
    public int getWcdtTj(String code, String codeType) {
        // 当日-提解数
        return gjLargeScreenDao.getWcdtTj(code, codeType);
    }

    @Override
    public int getWcdtLshj(String code, String codeType) {
        // 当日-律师会见数
        return gjLargeScreenDao.getWcdtLshj(code, codeType);
    }

    @Override
    public int getWcdtJshj(String code, String codeType) {
        // 当日-家属会见数
        int wcdtJshj = gjLargeScreenDao.getWcdtJshj(code, codeType);
        // 当日-单向视频家属会见
        int wcdtJshjVideo = gjLargeScreenDao.getWcdtJshjVideo(code, codeType);
        return wcdtJshj + wcdtJshjVideo;
    }

    @Override
    public int getWcdtSglshj(String code, String codeType) {
        // 当日-使馆领事会见数
        return gjLargeScreenDao.getWcdtSglshj(code, codeType);
    }

    @Override
    public int getWcdtCsjy(String code, String codeType) {
        // 当日-出所就医数
        return gjLargeScreenDao.getWcdtCsjy(code, codeType);
    }

    // 重点关注人员-单独关押
    @Override
    public PageResult<FocusOnPersonnelDO> getZdgzryDdgy(int pageNo, int pageSize, String code, String codeType) {
        Page<FocusOnPersonnelDO> page = new Page<>(pageNo, pageSize);
        Page<FocusOnPersonnelDO> result = gjLargeScreenDao.getZdgzryDdgy(page, code, codeType);
        return getZdgzCommonPage(result);
    }

    // 重点关注人员-戒具使用
    @Override
    public PageResult<FocusOnPersonnelDO> getZdgzryJjsy(int pageNo, int pageSize, String code, String codeType) {
        Page<FocusOnPersonnelDO> page = new Page<>(pageNo, pageSize);
        Page<FocusOnPersonnelDO> result = gjLargeScreenDao.getZdgzryJjsy(page, code, codeType);
        return getZdgzCommonPage(result);
    }

    // 重点关注人员-临时固定
    @Override
    public PageResult<FocusOnPersonnelDO> getZdgzryLsgd(int pageNo, int pageSize, String code, String codeType) {
        Page<FocusOnPersonnelDO> page = new Page<>(pageNo, pageSize);
        Page<FocusOnPersonnelDO> result = gjLargeScreenDao.getZdgzryLsgd(page, code, codeType);
        return getZdgzCommonPage(result);
    }

    // 重点关注人员-重点人员
    @Override
    public PageResult<FocusOnPersonnelDO> getZdgzryZdry(int pageNo, int pageSize, String code, String codeType) {
        Page<FocusOnPersonnelDO> page = new Page<>(pageNo, pageSize);
        Page<FocusOnPersonnelDO> result = gjLargeScreenDao.getZdgzryZdry(page, code, codeType);
        return getZdgzCommonPage(result);
    }

    // 重点关注人员-外籍人员
    @Override
    public PageResult<FocusOnPersonnelDO> getZdgzryWjry(int pageNo, int pageSize, String code, String codeType) {
        Page<FocusOnPersonnelDO> page = new Page<>(pageNo, pageSize);
        Page<FocusOnPersonnelDO> result = gjLargeScreenDao.getZdgzryWjry(page, code, codeType);
        return getZdgzCommonPage(result);
    }

    // 重点关注人员-今日到期
    @Override
    public PageResult<FocusOnPersonnelDO> getZdgzryJrdq(int pageNo, int pageSize, String code, String codeType) {
        Page<FocusOnPersonnelDO> page = new Page<>(pageNo, pageSize);
        Page<FocusOnPersonnelDO> result = gjLargeScreenDao.getZdgzryJrdq(page, code, codeType);
        return getZdgzCommonPage(result);
    }

    // 重点关注人员-重病号
    @Override
    public PageResult<FocusOnPersonnelDO> getZdgzryZbh(int pageNo, int pageSize, String code, String codeType) {
        Page<FocusOnPersonnelDO> page = new Page<>(pageNo, pageSize);
        Page<FocusOnPersonnelDO> result = gjLargeScreenDao.getZdgzryZbh(page, code, codeType);
        return getZdgzCommonPage(result);
    }

    // 重点关注人员-风险人员
    @Override
    public PageResult<FocusOnPersonnelDO> getZdgzryFxry(int pageNo, int pageSize, String code, String codeType) {
        Page<FocusOnPersonnelDO> page = new Page<>(pageNo, pageSize);
        Page<FocusOnPersonnelDO> result = gjLargeScreenDao.getZdgzryFxry(page, code, codeType);
        return getZdgzCommonPage(result);
    }

    // 重点关注人员-全部人员列表
    @Override
    public PageResult<FocusOnPersonnelDO> getZdgzryAllPage(int pageNo, int pageSize, String code, String codeType, List<String> methodNames) {
        Page<FocusOnPersonnelDO> page = new Page<>(pageNo, pageSize);
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("page", page);
        paramsMap.put("code", code);
        paramsMap.put("codeType", codeType);
        List<String> sqlList = new ArrayList<>();
        checkZdgzMethodList(methodNames);
        for (String methodName : methodNames) {
            sqlList.add(getSql(paramsMap, methodName));
        }
        Page<FocusOnPersonnelDO> result = gjLargeScreenDao.getZdgzryAll(page, sqlList);

        return getZdgzCommonPage(result);
    }

    private void checkZdgzMethodList(List<String> methodNames) {
        if (CollectionUtil.isEmpty(methodNames)) {
            throw new RuntimeException("bizType list 不能为空");
        }
        List<String> allMethodList = new ArrayList<>(Arrays.asList(("getZdgzryDdgy,getZdgzryJjsy,getZdgzryLsgd," +
                "getZdgzryZdry,getZdgzryWjry,getZdgzryJrdq,getZdgzryZbh,getZdgzryFxry").split(",")));
        for (String methodName : methodNames) {
            if (!allMethodList.contains(methodName)) {
                throw new RuntimeException("非法 bizType：" + methodName);
            }
        }
    }

    @Override
    public JSONObject getTopInfo(String orgCode) {
        JSONObject jsonObject = gjLargeScreenDao.getDtcDthCountByOrgCode(orgCode);
        if (Objects.isNull(jsonObject)) {
            jsonObject = new JSONObject();
        }
        JSONObject result = new JSONObject();
        result.put("dtc", jsonObject.getIntValue("dtc"));
        result.put("dth", jsonObject.getIntValue("dth"));
        // 03: 查询机构
        JSONObject wcdtAll = getWcdtAll(orgCode, "03", Arrays.asList("getWcdtTx", "getWcdtTj",
                "getWcdtLshj", "getWcdtJshj", "getWcdtSglshj"));
        result.putAll(wcdtAll);
        return result;
    }

    @Override
    public PageResult<CommonMeetingDO> getTodayMeetingPage(int pageNo, int pageSize, String orgCode) {
        Page<CommonMeetingDO> page = new Page<>(pageNo, pageSize);
        Page<CommonMeetingDO> meetingPage = gjLargeScreenDao.getTodayMeetingPage(page, orgCode);
        List<CommonMeetingDO> records = meetingPage.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            List<String> hjsList = new ArrayList<>();
            for (CommonMeetingDO record : records) {
                if (StringUtils.isNoneBlank(record.getHjs())) {
                    hjsList.add(record.getHjs());
                }
                setBusinessTypeName(record);
                setStatusName(record);
            }

            LambdaQueryWrapper<AreaPrisonRoomDO> roomQueryWrapper = Wrappers.lambdaQuery();
            roomQueryWrapper.select(AreaPrisonRoomDO::getId, AreaPrisonRoomDO::getRoomName).in(AreaPrisonRoomDO::getId, hjsList);
            List<AreaPrisonRoomDO> list = areaPrisonRoomService.list(roomQueryWrapper);
            if (CollectionUtil.isNotEmpty(list)) {
                Map<String, String> roomMap = list.stream().collect(Collectors.toMap(AreaPrisonRoomDO::getId, AreaPrisonRoomDO::getRoomName, (k1, k2) -> k2));
                records.forEach(a -> {
                    if (StringUtils.isNoneBlank(a.getHjs())) {
                        a.setHjsName(roomMap.get(a.getHjs()));
                    }
                });
            }
        }

        return new PageResult<>(records, meetingPage.getTotal());
    }

    @Override
    public List<JSONObject> getJsswItemCount(String code, String codeType) {
        List<JSONObject> list = gjLargeScreenDao.getJsswItems(needSelectSwxx(), code, codeType);
        Integer busCount = list.stream().map(a -> a.getIntValue("buscount")).reduce(Integer::sum).orElse(0);
        JSONObject all = new JSONObject();
        all.put("bustype", "0");
        all.put("busname", "全部");
        all.put("buscount", busCount);
        list.add(all);
        list.sort((o1, o2) -> o2.getIntValue("buscount") - o1.getIntValue("buscount"));
        return list;
    }

    @Override
    public List<JsswItemRespVO> getJsswItemList(String code, String codeType, String busType) {
        List<String> items;
        // 0 代表全部
        if ("0".equals(busType)) {
            items = needSelectSwxx();
        } else {
            items = Collections.singletonList(busType);
        }
        List<JSONObject> jsswItemArray = gjLargeScreenDao.getJsswItemArray(items, code, codeType);
        for (int i = jsswItemArray.size() - 1; i >= 0; i--) {
            try {
                String content = jsswItemArray.get(i).getString("content");
                jsswItemArray.get(i).put("content", JSON.parseObject(content));
            } catch (Exception e) {
                // 容错处理，防止轨迹表content为非json值，前端解析报错
                log.error("getJsswItemList  item  switch content error：", e);
                jsswItemArray.remove(i);
            }
        }
        return JSON.parseArray(JSON.toJSONString(jsswItemArray), JsswItemRespVO.class);
    }

    @Override
    public JSONObject getWcdtAll(String code, String codeType, List<String> methodNames) {
        checkWcdtMethodNames(methodNames);
        CountDownLatch countDownLatch = new CountDownLatch(methodNames.size());
        ThreadPoolExecutor pool = ThreadPoolUtil.getPool();
        List<Future<JSONObject>> futures = new CopyOnWriteArrayList<>();
        for (String methodName : methodNames) {
            WcdtTask wcdt = new WcdtTask(this, methodName, code, codeType, countDownLatch);
            futures.add(pool.submit(wcdt));
        }
        try {
            countDownLatch.await(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        JSONObject result = new JSONObject();
        for (Future<JSONObject> future : futures) {
            try {
                result.putAll(future.get());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    private void checkWcdtMethodNames(List<String> methodNames) {
        if (CollectionUtil.isEmpty(methodNames)) {
            throw new ServerException("外出动态标识不能为空");
        }
        List<String> allMethodNames = new ArrayList<>(Arrays.asList("getWcdtTx", "getWcdtTj", "getWcdtLshj", "getWcdtJshj",
                "getWcdtSglshj", "getWcdtCsjy"));
        for (String methodName : methodNames) {
            if (!allMethodNames.contains(methodName)) {
                throw new ServerException("非法外出动态标识");
            }
        }
    }


    @Override
    public JSONObject getZdgzryAllCount(String code, String codeType, List<String> methodNames) {

        CountDownLatch countDownLatch = new CountDownLatch(methodNames.size());
        ThreadPoolExecutor pool = ThreadPoolUtil.getPool();
        List<Future<JSONObject>> futures = new CopyOnWriteArrayList<>();
        checkZdgzMethodList(methodNames);
        for (String methodName : methodNames) {
            ZdgzryTask zdgzry = new ZdgzryTask(this, methodName, code, codeType, countDownLatch);
            futures.add(pool.submit(zdgzry));
        }
        try {
            countDownLatch.await(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        JSONObject result = new JSONObject();
        for (Future<JSONObject> future : futures) {
            try {
                result.putAll(future.get());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        long allTotal = 0;
        for (Map.Entry<String, Object> entry : result.entrySet()) {
            allTotal += (long) entry.getValue();
        }
        result.put("all", allTotal);

        return result;
    }

    @Override
    public PageResult<WgdjDO> getWggjPage(int pageNo, int pageSize, String timeRange, String handleStatus, String code, String codeType) {
        Page<WgdjDO> page = new Page<>(pageNo, pageSize);
        Page<WgdjDO> pageResult = gjLargeScreenDao.getWggjPage(page, timeRange, handleStatus, code, codeType);

        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public JSONObject getWggjAllCount(String timeRange, String code, String codeType) {

        return gjLargeScreenDao.getWggjAllCount(timeRange, code, codeType);
    }

    @Override
    public void test() {
        Page<WgdjDO> page = new Page<>(1, 5);
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("page", page);
        paramsMap.put("code", "1100001130100030001");
        paramsMap.put("codeType", "01");
        String getZdgzryWjry = getSql(paramsMap, "getZdgzryWjry");
        System.out.println(getZdgzryWjry);
    }

    public String getSql(Map<String, Object> paramMap, String methodName) {

        String id = GjLargeScreenDao.class.getName() + "." + methodName;
        MappedStatement mappedStatement = sqlSessionFactory.getConfiguration().getMappedStatement(id);

        String sql = mappedStatement.getBoundSql(paramMap).getSql();
        List<ParameterMapping> parameterMappings = mappedStatement.getSqlSource().getBoundSql(paramMap).getParameterMappings();
        for (ParameterMapping mapping : parameterMappings) {
            Object o = paramMap.get(mapping.getProperty());
            String val = BeanUtil.getProperty(paramMap, mapping.getProperty());
            if (o instanceof String) {
                sql = sql.replaceFirst("\\?", "'" + val + "'");
            } else {
                sql = sql.replaceFirst("\\?", val);
            }
            log.info("===========get sql mapping: {}  pro: {}  val:{}  sql:{}", mapping, mapping.getProperty(), val, sql);
        }
        return sql;
    }


    /**
     * BusTypeEnum
     * 需要查询的事务选项
     * 监室点名???
     * <p>
     * 提讯:0107 YEWU_TIXUN
     * 提解:0108 YEWU_TIJIE
     * 家属会见:0110 YEWU_JSHJ
     * <p>
     * 使馆/领事会见???
     * <p>
     * 律师会见:0109 YEWU_LSHJ
     * 信息员布建:0120 YEWU_XXYGL
     * 信息员撤销:0130 YEWU_XXYCX
     * 安全检查:0114 YEWU_AQJC
     * 戒具使用:0111 YEWU_XJSY
     * 戒具解除:0131 YEWU_JJJC
     * 谈话教育:0106 YEWU_THJY
     * 调入监室:0128 YEWU_TRJS
     * 调出监室:0129 YEWU_TCJS
     * 面对面管理:0113 YEWU_MDMGL
     */

    private static List<String> needSelectSwxx() {
        List<String> list = new ArrayList<>();
        list.add(BusTypeEnum.YEWU_TIXUN.getBusType());//提讯
        list.add(BusTypeEnum.YEWU_TIJIE.getBusType());//提解
        list.add(BusTypeEnum.YEWU_JSHJ.getBusType());//家属会见
        list.add(BusTypeEnum.YEWU_LSHJ.getBusType());//律师会见
        list.add(BusTypeEnum.YEWU_XXYGL.getBusType());//信息员布建
        list.add(BusTypeEnum.YEWU_XXYCX.getBusType());//信息员撤销
        list.add(BusTypeEnum.YEWU_AQJC.getBusType());//安全检查
        list.add(BusTypeEnum.YEWU_XJSY.getBusType());//戒具使用
        list.add(BusTypeEnum.YEWU_JJJC.getBusType());//戒具解除
        list.add(BusTypeEnum.YEWU_THJY.getBusType());//谈话教育
        list.add(BusTypeEnum.YEWU_TRJS.getBusType());//调入监室
        list.add(BusTypeEnum.YEWU_TCJS.getBusType());//调出监室
        list.add(BusTypeEnum.YEWU_MDMGL.getBusType());//面对面管理
        //list.add(BusTypeEnum.a.getBusType());//使馆/领事会见
        //list.add(BusTypeEnum.a.getBusType());//监室点名
        return list;
    }


    private void setStatusName(CommonMeetingDO record) {
        switch (record.getStatus()) {
            case "dtc":
                record.setStatusName("待提出");
                break;
            case "dth":
                record.setStatusName("待提回");
                break;
            default:
                record.setStatusName("已提回");
        }
    }

    private void setBusinessTypeName(CommonMeetingDO record) {
        switch (record.getBusinessType()) {
            case "tx":
                record.setBusinessTypeName("提讯");
                break;
            case "tj":
                record.setBusinessTypeName("提解");
                break;
            case "lshj":
                record.setBusinessTypeName("律师会见");
                break;
            case "jshj":
                record.setBusinessTypeName("家属会见");
                break;
            case "jsdxsphj":
                record.setBusinessTypeName("家属单项视频会见");
                break;
            default:
                record.setBusinessTypeName("使馆领事会见");
        }
    }


    private PageResult<FocusOnPersonnelDO> getZdgzCommonPage(Page<FocusOnPersonnelDO> result) {
        List<FocusOnPersonnelDO> records = result.getRecords();
        List<String> jgrybmList = records.stream().map(FocusOnPersonnelDO::getJgrybm).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(jgrybmList)) {
            List<PrisonerTagDO> tagList = gjLargeScreenDao.getTagListByJgrybmList(jgrybmList);
            Map<String, List<PrisonerTagDO>> tmpMap = new HashMap<>();
            for (PrisonerTagDO prisonerTagDO : tagList) {
                List<PrisonerTagDO> prisonerTagDOS = tmpMap.computeIfAbsent(prisonerTagDO.getJgrybm(), k -> new ArrayList<>());
                prisonerTagDOS.add(prisonerTagDO);
            }
            for (FocusOnPersonnelDO record : records) {
                record.setTagList(tmpMap.getOrDefault(record.getJgrybm(), Collections.emptyList()));
            }
            tmpMap = null;
        }
        return new PageResult<>(records, result.getTotal());
    }

}
