package com.rs.module.pam.service.letter;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.enums.DataSourceAppEnum;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.pam.controller.admin.letter.vo.*;
import com.rs.module.pam.controller.app.vo.AppConfirmLetterReqVO;
import com.rs.module.pam.dao.letter.LetterReceiveDao;
import com.rs.module.pam.entity.letter.LetterReceiveDO;
import com.rs.module.pam.enums.LetterReceiveAppRecordTypeEnum;
import com.rs.module.pam.enums.LetterReceiveStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;


/**
 * 监所事务管理-家属通信-收信登记 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class LetterReceiveServiceImpl extends BaseServiceImpl<LetterReceiveDao, LetterReceiveDO> implements LetterReceiveService {

    @Resource
    private LetterReceiveDao letterReceiveDao;

    private static final String msgUrl = "/#/familyContact/receiving?id=";

    private static final String defKey = "shouxinguanliliucheng";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createLetterReceive(LetterReceiveSaveReqVO createReqVO) {

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        // 插入
        LetterReceiveDO letterReceive = BeanUtils.toBean(createReqVO, LetterReceiveDO.class);
        letterReceive.setDataSources(DataSourceAppEnum.ACP.getCode());
        letterReceive.setStatus(LetterReceiveStatusEnum.DGJSH.getCode());
        letterReceive.setRegisterUserName(sessionUser.getName());
        letterReceive.setRegisterUserSfzh(sessionUser.getIdCard());
        letterReceive.setRegisterTime(new Date());
        if (Objects.isNull(letterReceive.getSendDate())) {
            letterReceive.setSendDate(new Date());
        }
        letterReceiveDao.insert(letterReceive);

        //启动流程
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", letterReceive.getId());
        JSONObject result = BspApprovalUtil.defaultStartProcess(HttpUtils.getAppCode() + "-" + defKey,
                letterReceive.getId(), "收信流程审批", msgUrl + letterReceive.getId(), variables);
        log.info("==========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            letterReceive.setActInstId(bpmTrail.getString("actInstId"));
            letterReceive.setTaskId(bpmTrail.getString("taskId"));
            letterReceiveDao.updateById(letterReceive);
        } else {
            throw new ServerException("流程启动失败");
        }

        // 返回
        return letterReceive.getId();
    }

    @Override
    public void updateLetterReceive(LetterReceiveSaveReqVO updateReqVO) {
        // 校验存在
        validateLetterReceiveExists(updateReqVO.getId());
        // 更新
        LetterReceiveDO updateObj = BeanUtils.toBean(updateReqVO, LetterReceiveDO.class);
        letterReceiveDao.updateById(updateObj);
    }

    @Override
    public void deleteLetterReceive(String id) {
        // 校验存在
        validateLetterReceiveExists(id);
        // 删除
        letterReceiveDao.deleteById(id);
    }

    private void validateLetterReceiveExists(String id) {
        if (letterReceiveDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-家属通信-收信登记数据不存在");
        }
    }

    @Override
    public LetterReceiveDO getLetterReceive(String id) {
        return letterReceiveDao.selectById(id);
    }

    @Override
    public PageResult<LetterReceiveDO> getLetterReceivePage(LetterReceivePageReqVO pageReqVO) {
        return letterReceiveDao.selectPage(pageReqVO);
    }

    @Override
    public List<LetterReceiveDO> getLetterReceiveList(LetterReceiveListReqVO listReqVO) {
        return letterReceiveDao.selectList(listReqVO);
    }

    @Override
    public void approvalLetterReceive(LetterReceiveApprovalReqVO approvalReqVO) {
        LetterReceiveDO letterReceiveDO = letterReceiveDao.selectById(approvalReqVO.getId());
        if (letterReceiveDO == null) {
            throw new ServerException("监所事务管理-家属通信-收信登记数据不存在");
        }
        if (LetterReceiveStatusEnum.DZJ.getCode().equals(letterReceiveDO.getStatus()) ||
                LetterReceiveStatusEnum.DQR.getCode().equals(letterReceiveDO.getStatus()) ||
                LetterReceiveStatusEnum.YQR.getCode().equals(letterReceiveDO.getStatus()) ||
                LetterReceiveStatusEnum.BTG.getCode().equals(letterReceiveDO.getStatus())) {
            throw new ServerException("当前为【" + LetterReceiveStatusEnum.getByCode(letterReceiveDO.getStatus()).getName()
                    + "】业务流程，无需走审批流程");
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        Boolean isApproval = BspApprovalUtil.getBpmApi().checkIsApproveAuthority(letterReceiveDO.getTaskId(), sessionUser.getIdCard());
        if (!isApproval) {
            throw new ServerException("当前人无审批权限");
        }
        BspApproceStatusEnum bspApproceStatusEnum = BspApproceStatusEnum.getByCode(approvalReqVO.getResult());
        if (Objects.isNull(bspApproceStatusEnum)) {
            throw new ServerException("非法审批结果");
        }
        Map<String, Object> variables = new HashMap<>();
        variables.put("ywbh", letterReceiveDO.getId());
        variables.put("approval_status", BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ? "ty" : "bty");
        // 管教审核
        if (LetterReceiveStatusEnum.DGJSH.getCode().equals(letterReceiveDO.getStatus())) {
            letterReceiveDO.setStatus(BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ? LetterReceiveStatusEnum.DZJ.getCode()
                    : LetterReceiveStatusEnum.DKZZSH.getCode());
            letterReceiveDO.setGjApprovalComments(approvalReqVO.getApprovalComments());
            letterReceiveDO.setGjApprovalResult("" + bspApproceStatusEnum.getCode());
            letterReceiveDO.setGjApproverSfzh(sessionUser.getIdCard());
            letterReceiveDO.setGjApproverXm(sessionUser.getName());
            letterReceiveDO.setGjApproverTime(new Date());
        } else if (LetterReceiveStatusEnum.DKZZSH.getCode().equals(letterReceiveDO.getStatus())) {
            letterReceiveDO.setStatus(BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ? LetterReceiveStatusEnum.DZJ.getCode()
                    : LetterReceiveStatusEnum.DSLDSH.getCode());
            letterReceiveDO.setGroupApprovalComments(approvalReqVO.getApprovalComments());
            letterReceiveDO.setGroupApprovalResult("" + bspApproceStatusEnum.getCode());
            letterReceiveDO.setGroupApproverSfzh(sessionUser.getIdCard());
            letterReceiveDO.setGroupApproverXm(sessionUser.getName());
            letterReceiveDO.setGroupApproverTime(new Date());
        } else if (LetterReceiveStatusEnum.DSLDSH.getCode().equals(letterReceiveDO.getStatus())) {
            letterReceiveDO.setStatus(BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ?
                    LetterReceiveStatusEnum.DZJ.getCode() : LetterReceiveStatusEnum.BTG.getCode());
            // 所领导不同意要结束流程
            if (BspApproceStatusEnum.NOT_PASSED.getCode() == bspApproceStatusEnum.getCode()) {
                bspApproceStatusEnum = BspApproceStatusEnum.NOT_PASSED_END;
            }
            letterReceiveDO.setLeaderApprovalComments(approvalReqVO.getApprovalComments());
            letterReceiveDO.setLeaderApprovalResult("" + bspApproceStatusEnum.getCode());
            letterReceiveDO.setLeaderApproverSfzh(sessionUser.getIdCard());
            letterReceiveDO.setLeaderApproverXm(sessionUser.getName());
            letterReceiveDO.setLeaderApproverTime(new Date());
        } else {
            throw new ServerException("非法审批状态");
        }
        //审批
        JSONObject nowApproveUser = new JSONObject();
        nowApproveUser.put("orgCode", sessionUser.getOrgCode());
        nowApproveUser.put("orgName", sessionUser.getOrgName());
        nowApproveUser.put("idCard", sessionUser.getId());
        nowApproveUser.put("name", sessionUser.getName());
        boolean terminateTask = BspApproceStatusEnum.PASSED_END.getCode() == bspApproceStatusEnum.getCode() ||
                BspApproceStatusEnum.NOT_PASSED_END.getCode() == bspApproceStatusEnum.getCode();
        JSONObject result = BspApprovalUtil.approvalProcess(HttpUtils.getAppCode() + "-" + defKey, letterReceiveDO.getActInstId(),
                letterReceiveDO.getTaskId(), letterReceiveDO.getId(),
                bspApproceStatusEnum, approvalReqVO.getApprovalComments(), "收信流程审批", msgUrl + letterReceiveDO.getId(), terminateTask,
                variables, nowApproveUser, HttpUtils.getAppCode());
        log.info("========result:{}", result);
        if (result.getIntValue("code") == HttpStatus.OK.value()) {
            JSONObject bpmTrail = result.getJSONObject("data").getJSONObject("bpmTrail");
            letterReceiveDO.setTaskId(bpmTrail.getString("taskId"));
        } else {
            throw new ServerException("流程启动失败");
        }
        letterReceiveDao.updateById(letterReceiveDO);

    }

    @Override
    public void passLetter(PassLetterReqVO passLetterReqVO) {
        LetterReceiveDO letterReceiveDO = letterReceiveDao.selectById(passLetterReqVO.getId());
        if (letterReceiveDO == null) {
            throw new ServerException("监所事务管理-家属通信-收信登记数据不存在");
        }
        if (!LetterReceiveStatusEnum.DZJ.getCode().equals(letterReceiveDO.getStatus())) {
            throw new ServerException("非待转交状态，不能转交");
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        letterReceiveDO.setPassUser(sessionUser.getName());
        letterReceiveDO.setPassRemark(passLetterReqVO.getPassRemark());
        letterReceiveDO.setPassTime(new Date());
        letterReceiveDO.setStatus(LetterReceiveStatusEnum.DQR.getCode());
        letterReceiveDao.updateById(letterReceiveDO);
        // todo 是否需要发送消息
    }

    @Override
    public void appConfirmLetter(AppConfirmLetterReqVO appConfirmLetterReqVO) {
        LetterReceiveDO letterReceiveDO = letterReceiveDao.selectById(appConfirmLetterReqVO.getId());
        if (letterReceiveDO == null) {
            throw new ServerException("监所事务管理-家属通信-收信登记数据不存在");
        }
        letterReceiveDO.setReceiptTime(new Date());
        letterReceiveDO.setSignUrl(appConfirmLetterReqVO.getSignUrl());
        letterReceiveDO.setStatus(LetterReceiveStatusEnum.YQR.getCode());
        letterReceiveDao.updateById(letterReceiveDO);
    }

    @Override
    public PageResult<LetterReceiveDO> getAppLetterReceivePage(int pageNo, int pageSize, String jgrybm, String type) {

        LetterReceiveAppRecordTypeEnum appRecordTypeEnum = LetterReceiveAppRecordTypeEnum.getByCode(type);
        if (Objects.isNull(appRecordTypeEnum)) {
            throw new ServerException("非法收信查询类型");
        }
        return letterReceiveDao.getAppLetterReceivePage(pageNo, pageSize, jgrybm, type);
    }


}
