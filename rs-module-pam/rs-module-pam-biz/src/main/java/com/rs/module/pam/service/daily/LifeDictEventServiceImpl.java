package com.rs.module.pam.service.daily;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.security.util.SessionUserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.pam.controller.admin.daily.vo.*;
import com.rs.module.pam.entity.daily.LifeDictEventDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.pam.dao.daily.LifeDictEventDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 监所事务管理-事务选项字典 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LifeDictEventServiceImpl extends BaseServiceImpl<LifeDictEventDao, LifeDictEventDO> implements LifeDictEventService {

    @Resource
    private LifeDictEventDao lifeDictEventDao;

    @Override
    public String createLifeDictEvent(LifeDictEventSaveReqVO createReqVO) {
        // 插入
        checkDictEvent(createReqVO);
        LifeDictEventDO lifeDictEvent = BeanUtils.toBean(createReqVO, LifeDictEventDO.class);
        lifeDictEventDao.insert(lifeDictEvent);
        // 返回
        return lifeDictEvent.getId();
    }

    private void checkDictEvent(LifeDictEventSaveReqVO createReqVO) {
        LambdaQueryWrapper<LifeDictEventDO> wrapper = Wrappers.lambdaQuery(LifeDictEventDO.class)
                .eq(LifeDictEventDO::getEventName, createReqVO.getEventName())
                .eq(LifeDictEventDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode());
        if (StringUtils.isNotEmpty(createReqVO.getId())) {
            wrapper.ne(LifeDictEventDO::getId, createReqVO.getId());
        }
        Integer count = lifeDictEventDao.selectCount(wrapper);
        if (Objects.nonNull(count) && count > 0) {
            throw new ServerException("事务选项名称重复【" + createReqVO.getEventName() + "】");
        }
    }

    @Override
    public void updateLifeDictEvent(LifeDictEventSaveReqVO updateReqVO) {
        // 校验存在
        validateLifeDictEventExists(updateReqVO.getId());
        // 更新
        LifeDictEventDO updateObj = BeanUtils.toBean(updateReqVO, LifeDictEventDO.class);
        lifeDictEventDao.updateById(updateObj);
    }

    @Override
    public void deleteLifeDictEvent(String id) {
        // 校验存在
        validateLifeDictEventExists(id);
        // 删除
        lifeDictEventDao.deleteById(id);
    }

    private void validateLifeDictEventExists(String id) {
        if (lifeDictEventDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-事务选项字典数据不存在");
        }
    }

    @Override
    public LifeDictEventDO getLifeDictEvent(String id) {
        return lifeDictEventDao.selectById(id);
    }

    @Override
    public PageResult<LifeDictEventDO> getLifeDictEventPage(LifeDictEventPageReqVO pageReqVO) {
        return lifeDictEventDao.selectPage(pageReqVO);
    }

    @Override
    public List<LifeDictEventDO> getLifeDictEventList(LifeDictEventListReqVO listReqVO) {
        return lifeDictEventDao.selectList(listReqVO);
    }


}
