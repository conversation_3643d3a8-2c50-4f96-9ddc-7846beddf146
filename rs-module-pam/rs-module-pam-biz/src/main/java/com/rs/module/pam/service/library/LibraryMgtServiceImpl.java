package com.rs.module.pam.service.library;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.util.http.HttpUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rs.module.pam.controller.admin.library.vo.*;
import com.rs.module.pam.entity.library.LibraryMgtDO;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;

import com.rs.module.pam.dao.library.LibraryMgtDao;

import com.rs.framework.common.exception.ServerException;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;


/**
 * 监所事务管理-图书管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LibraryMgtServiceImpl extends BaseServiceImpl<LibraryMgtDao, LibraryMgtDO> implements LibraryMgtService {

    @Resource
    private LibraryMgtDao libraryMgtDao;

    @Resource
    private BorrowingConfigService borrowingConfigService;

    @Value("${conf.dromara.x-file-storage.end-point}")
    private String fileBaseUrl;

    @Override
    public String createLibraryMgt(LibraryMgtSaveReqVO createReqVO) {
        // 插入
        LibraryMgtDO libraryMgt = BeanUtils.toBean(createReqVO, LibraryMgtDO.class);
        libraryMgtDao.insert(libraryMgt);
        borrowingConfigService.createBorrowingDefaultConfigByMgt();
        // 返回
        return libraryMgt.getId();
    }

    @Override
    public void updateLibraryMgt(LibraryMgtSaveReqVO updateReqVO) {
        // 校验存在
        validateLibraryMgtExists(updateReqVO.getId());
        // 更新
        LibraryMgtDO updateObj = BeanUtils.toBean(updateReqVO, LibraryMgtDO.class);
        libraryMgtDao.updateById(updateObj);
    }

    @Override
    public void deleteLibraryMgt(String id) {
        // 校验存在
        validateLibraryMgtExists(id);
        // 删除
        libraryMgtDao.deleteById(id);
    }

    private void validateLibraryMgtExists(String id) {
        if (libraryMgtDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-图书管理数据不存在");
        }
    }

    @Override
    public LibraryMgtDO getLibraryMgt(String id) {
        return libraryMgtDao.selectById(id);
    }

    @Override
    public PageResult<LibraryMgtDO> getLibraryMgtPage(LibraryMgtPageReqVO pageReqVO) {
        return libraryMgtDao.selectPage(pageReqVO);
    }

    @Override
    public List<LibraryMgtDO> getLibraryMgtList(LibraryMgtListReqVO listReqVO) {
        return libraryMgtDao.selectList(listReqVO);
    }

    @Override
    public void updateBorrowedInventory(String libraryId, int livraryCount) {
        libraryMgtDao.updateBorrowedInventory(libraryId, livraryCount);
    }

    @Override
    public PageResult<LibraryMgtDO> getAppLibraryMgtPage(int pageNo, int pageSize, String orgCode) {
        PageResult<LibraryMgtDO> appLibraryMgtPage = libraryMgtDao.getAppLibraryMgtPage(pageNo, pageSize, orgCode);
        List<LibraryMgtDO> list = appLibraryMgtPage.getList();
        if (CollectionUtil.isNotEmpty(list)) {
            for (LibraryMgtDO libraryMgtDO : list) {
                String coverImgUrl = libraryMgtDO.getCoverImgUrl();
                if (StringUtils.isNotEmpty(coverImgUrl)) {
                    List<JSONObject> jsonObjects = JSON.parseArray(coverImgUrl, JSONObject.class);
                    if (CollectionUtil.isNotEmpty(jsonObjects)) {
                        libraryMgtDO.setCoverRealImgUrl(fileBaseUrl + "/" + HttpUtils.getAppCode() + "/" + jsonObjects.get(0).getString("objectName"));
                    }
                }
            }
        }
        return appLibraryMgtPage;
    }

    @Override
    public void shelvingAndDelisting(LibraryMgtSXJReqVO updateReqVO) {
        LibraryMgtDO libraryMgtDO = libraryMgtDao.selectById(updateReqVO.getId());
        if (libraryMgtDO == null) {
            throw new ServerException("监所事务管理-图书管理数据不存在");
        }
        libraryMgtDO.setStatus(updateReqVO.getStatus());
        libraryMgtDao.updateById(libraryMgtDO);
    }


}
