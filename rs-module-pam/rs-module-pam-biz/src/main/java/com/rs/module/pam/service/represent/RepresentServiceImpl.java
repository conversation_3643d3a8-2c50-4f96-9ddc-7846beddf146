package com.rs.module.pam.service.represent;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.gosun.zhjg.common.constant.RabbitTopicsConstants;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.common.util.DateUtils;
import com.gosun.zhjg.common.util.OrderCoderUtil;
import com.gosun.zhjg.common.util.UUIDUtils;
import com.gosun.zhjg.prison.room.terminal.config.socketio.kafka.product.RabbitProduct;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.CustomPushMessageConditionDTO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.PushMessageAckVO;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.dao.pm.RsBaseDeviceInscreenDao;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenDO;
import com.rs.module.base.dao.pm.AreaPrisonRoomDao;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.pam.config.AsyncTaskPoolConfig;
import com.rs.module.pam.cons.RoomRepresentConstant;
import com.rs.module.pam.cons.SocketEventConstant;
import com.rs.module.pam.controller.admin.represent.vo.*;
import com.rs.module.pam.controller.admin.represent.vo.extra.*;
import com.rs.module.pam.dao.represent.CheckDao;
import com.rs.module.pam.dao.represent.PersonRepresentDao;
import com.rs.module.pam.dao.represent.RepresentConfigDao;
import com.rs.module.pam.dao.represent.RepresentDao;
import com.rs.module.pam.entity.represent.CheckDO;
import com.rs.module.pam.entity.represent.PersonRepresentDO;
import com.rs.module.pam.entity.represent.RepresentConfigDO;
import com.rs.module.pam.entity.represent.RepresentDO;
import com.rs.module.pam.enums.RoomRepresentStatusEnum;
import com.rs.module.pam.enums.RoomRepresentTypeEnum;
import com.rs.module.pam.util.CronUtils;
import com.rs.util.DicUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;


/**
 * 监所事务管理-监室点名 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class RepresentServiceImpl extends BaseServiceImpl<RepresentDao, RepresentDO> implements RepresentService {

    @Resource
    private RepresentDao representDao;

//    @Resource
//    private SocketPushFeign socketPushFeign;

    @Resource
    private SocketService socketService;

    @Resource
    private RepresentConfigDao representConfigDao;

    @Resource
    private CheckDao checkDao;

    @Resource
    private PersonRepresentDao  personRepresentDao;

    @Resource
    private AreaPrisonRoomDao areaPrisonRoomDao;

    @Resource
    private BspApi bspApi;

    @Resource
    private RsBaseDeviceInscreenDao rsBaseDeviceInscreenDao;

    @Resource(name = AsyncTaskPoolConfig.TASK_EXECUTOR)
    private Executor executorService;

    @Resource
    private RabbitProduct rabbitProduct;

//    @Autowired
//    ScheduledService scheduledService;


    @Override
    public String create(RepresentSaveReqVO createReqVO) {
        // 插入
        RepresentDO representDO= BeanUtils.toBean(createReqVO, RepresentDO.class);
        representDao.insert(representDO);
        // 返回
        return representDO.getId();
    }

    @Override
    public void update(RepresentSaveReqVO updateReqVO) {
        // 校验存在
        validateExists(updateReqVO.getId());
        // 更新
        RepresentDO updateObj = BeanUtils.toBean(updateReqVO, RepresentDO.class);
        representDao.updateById(updateObj);
    }

    @Override
    public void delete(String id) {
        // 校验存在
        validateExists(id);
        // 删除
        representDao.deleteById(id);
    }

    private void validateExists(String id) {
        if (representDao.selectById(id) == null) {
            throw new ServerException("监所事务管理-监室点名数据不存在");
        }
    }

    @Override
    public RepresentDO get(String id) {
        return representDao.selectById(id);
    }

    @Override
    public PageResult<RepresentDO> getPage(RepresentPageReqVO pageReqVO) {
        return representDao.selectPage(pageReqVO);
    }

    @Override
    public List<RepresentDO> getList(RepresentListReqVO listReqVO) {
        return representDao.selectList(listReqVO);
    }

    @Override
    public List<RoomInfoVO> getRepresentInfo(String[] roomIds, String code) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String orgCode = sessionUser.getOrgCode();
        String userId = sessionUser.getId();
        //只能获取上次点名已结束的监室
        List<RoomInfoVO> vos = representDao.getWarderRoomInfo(code, userId, roomIds, orgCode);
        if (vos != null && vos.size() > 0) {
            Iterator<RoomInfoVO> iterator = vos.iterator();
            while (iterator.hasNext()) {
                RoomInfoVO info = iterator.next();
                List<Map> maps = representDao.getStatusData(info.getRoomId());
                Integer sumNum = maps.size();
                Integer outNum = 0;
                Integer reportNum =0 ;
                if (sumNum > 0) {
                    for (Map map : maps) {
                        if (map.get("outstatus") != null && map.get("outstatus").toString().equals("1")) {
                            outNum++;
                        }
                        if (map.get("report") != null && map.get("report").toString().equals("1")) {
                            reportNum++;
                        }
                    }
                }
                info.setAllInNum(sumNum);
                info.setOutNum(outNum);
                info.setReportNum(reportNum);
                info.setInNum(sumNum - outNum-reportNum);

                // 在所人数0 不展示 2021年8月17日
                if (sumNum == 0) {
                    log.info("{}({})监室 在所人数0不展示", info.getRoomName(), info.getRoomId());
                    iterator.remove();
                }

                if (ObjectUtil.equals(info.getIsInRepresent(), 1)) {
                    info.setIsInRepresent(1);//点名中
                } else {
                    info.setIsInRepresent(0);//正常
                }
            }
        }
        return vos;
    }

    @Override
    public List<Map> getRoomIds(String code, Integer userId, String prisonId) {
        return representDao.getRoomIds(code, userId, prisonId);
    }

    @Override
    public List<RoomInfoRepresentVO> getRepresentRoomResult(Page page, RepresentPageReqVO pageDto) {
        List<RoomInfoRepresentVO> vos = new ArrayList<>();
        if (pageDto.getIsPage() == 1) {
            vos = representDao.getRepresentResult(pageDto);
        } else {
            vos = representDao.getRepresentResult(page, pageDto);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        DateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        for (RoomInfoRepresentVO vo : vos) {
            RoomInfoRepresentVO representSum = representDao.getRepresentSum(vo.getId());
            vo.setAllInNum(representSum.getAllInNum());
            vo.setInNum(representSum.getInNum());
            vo.setPresentNum(representSum.getPresentNum());
            vo.setErrorNum(representSum.getErrorNum());
            vo.setOutNum(representSum.getOutNum());
            vo.setReportNum(representSum.getReportNum()==null?0:representSum.getReportNum());
            vo.setStartTimeStr(sdf.format(representSum.getStartTime()));
            if (representSum.getEndTime() != null) {
                vo.setEndTimeStr(sdf.format(representSum.getEndTime()));
            }
            vo.setRepresentDate(sdf1.format(representSum.getStartTime()));
            vo.setPresentTypeDisplayName(RoomRepresentTypeEnum.getValue(vo.getPresentType() + ""));
            vo.setPresentStatusDisplayName(RoomRepresentStatusEnum.getValue(vo.getPresentStatus() + ""));
            if (vo.getRepresentDate().equals(DateUtil.format(new Date(), DateUtils.DATE_PATTERN)) && RoomRepresentStatusEnum.err.getKey().equals(vo.getPresentStatus()) || RoomRepresentStatusEnum.not.getKey().equals(vo.getPresentStatus())) {
                vo.setIsVideo(1);//异常或离线 就开启视频点名入口
            } else {
                vo.setIsVideo(0);
            }
            if (vo.getIsVideoType() == 1) {
                vo.setPresentTypeDisplayName(vo.getPresentTypeDisplayName() + "," + RoomRepresentTypeEnum.spdm.getValue()); //增加视频点名
            }
        }
        return vos;
    }

//    @Override
//    public PageVO<TemperatureListPageVO> getTemperaturePageList(Page page, TemperatureListPageDTO pageDto) {
//        List<TemperatureListPageVO> vos = new ArrayList<>();
//        vos = representDao.getTemperaturePageList(page, pageDto);
//        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
//        DateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
//        for (TemperatureListPageVO vo : vos) {
//            RoomInfoRepresentVo representSum = representDao.getRepresentSum(vo.getPresentNo());
//            vo.setAllInNum(representSum.getAllInNum());
//            vo.setOutNum(representSum.getAllInNum() - representSum.getInNum());
//            Integer temperatureNum = representSum.getTemperatureNum() == null ? 0 : representSum.getTemperatureNum();
//            Integer temperatureErrNum = representSum.getTemperatureErrNum() == null ? 0 : representSum.getTemperatureErrNum();
//            vo.setTemperatureNum(temperatureNum);
//            vo.setTemperatureErrNum(temperatureErrNum);
//            vo.setTemperatureAllNum(temperatureErrNum + temperatureNum);
//            vo.setRepresentDate(representSum.getStartTime());
//        }
//        PageVO<TemperatureListPageVO> pageVO = new PageVO<>();
//        pageVO.setTotal((int) page.getTotal());
//        pageVO.setRows(vos);
//        return pageVO;
//    }


    @Override
    public RepresentCountVO getRepresentResultCount(String roomIds) {
        RepresentCountVO count = new RepresentCountVO();
        Integer ing = 0;
        Integer err = 0;
        Integer end = 0;
        RepresentPageReqVO pageDto = new RepresentPageReqVO();
        pageDto.setIsPage(1);
        pageDto.setRoomId(roomIds);
        List<RoomInfoRepresentVO> representRoomResult = getRepresentRoomResult(new Page(), pageDto);
        //使用hutool 时间工具类获取当天的开始时间 如:yyy-mm-dd 00:00:00，获取当天的结束时间 如:yyy-mm-dd 23:59:59
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(new Date().getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
        LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);
        pageDto.setStartTime(new Date[]{Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant())});
        pageDto.setEndTime(new Date[]{Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant())});
        for (RoomInfoRepresentVO vo : representRoomResult) {
            if ("0".equals(vo.getPresentStatus())) {
                ing += 1;
            } else if ("1".equals(vo.getPresentStatus())) {
                end += 1;
            } else if ("2".equals(vo.getPresentStatus())) {
                err += 1;
            }
        }
        count.setInRepresent(ing);//进行中
        count.setEndRepresent(end);//已结束
        count.setErrorRepresent(err);//异常
        return count;
    }

    @Override
    public List<RepresentPageReqVO> findByPage(Page page, RepresentPageReqVO pageDto) {
        List<RepresentPageReqVO> vos = representDao.findByPage(page, pageDto);
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        DateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        vos.forEach(vo -> {
            vo.setStartTimeStr(sdf.format(vo.getStartTime()));
            vo.setEndTimeStr(vo.getEndTime() != null ? sdf.format(vo.getEndTime()) : "现在");
            vo.setRepresentDate(sdf1.format(vo.getStartTime()) + " " + vo.getStartTimeStr() + "~" + vo.getEndTimeStr());
            vo.setPresentStatus(DicUtils.translate("ZD_JSDMZT", vo.getPresentStatus()));
        });
        return vos;
    }

    @Override
    public List<RoomInfoRepresentVO> selectById(String presentNo) {
        List<RoomInfoRepresentVO> roomInfoRepresentVos = representDao.selectRepresentById(presentNo);
        for (RoomInfoRepresentVO vo : roomInfoRepresentVos) {
            List<RepresentPersonVO> persons = personRepresentDao.getPersonsByRepresentId(vo.getId());
//            for (PersonRepresentRespVO personVo : persons) {
//                personVo.setFrontPhoto(pathPrefix + personVo.getFrontPhoto());
//                personVo.setSuspectedCharges(dictService.dictValue(WdaDictionaryConstants.C_AJLB, personVo.getSuspectedCharges()));
//                personVo.setLitigationLink(dictService.dictValue(WdaDictionaryConstants.C_KSS_SSJD, personVo.getLitigationLink()));
//            }
            vo.setRepresentPersonVos(persons);
            vo.setReportNum(vo.getReportNum()==null?0:vo.getReportNum());
        }
        return roomInfoRepresentVos;
    }

    @Override
    public List<RoomInfoRepresentVO> selectTemperatureById(String id) {
        List<RoomInfoRepresentVO> roomInfoRepresentVos = representDao.selectTemperatureById(id);
        return roomInfoRepresentVos;
    }

    @Override
    public RoomInfoRepresentVO videoInfoList(String id) {
        RoomInfoRepresentVO vo = new RoomInfoRepresentVO();
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        RepresentDO roomRepresentEntity = representDao.selectById(id);
        if (roomRepresentEntity != null) {
//            org.springframework.beans.BeanUtils.copyProperties(roomRepresentEntity, vo);
            vo = BeanUtils.toBean(roomRepresentEntity, RoomInfoRepresentVO.class);
            vo.setReportNum(vo.getReportNum()==null?0:vo.getReportNum());
            vo.setStartTimeStr(sdf.format(vo.getStartTime()));
            vo.setEndTimeStr(sdf.format(vo.getEndTime()));
            List<RepresentPersonVO> persons = personRepresentDao.getErrPersonsByRepresentId(id);
//            for (RepresentPersonVO personVo : persons) {
//                personVo.setFrontPhoto(FileUrlUtils.concatUrl(personVo.getFrontPhoto(), pathPrefix));
//            }
            vo.setRepresentPersonVos(persons);
        }
        return vo;
    }

    @Override
    public Integer confirmByVideo(ConfirmByVideoVO reqVO) {
        SessionUser user = SessionUserUtil.getSessionUser();
        Date now = new Date();
        RepresentDO representEntity = this.representDao.selectById(reqVO.getId());
        RepresentDO entity = new RepresentDO();
        entity.setId(reqVO.getId());
        Integer errNum = representEntity.getErrorNum();
        Integer presentNum = representEntity.getPresentNum();
        for (String prisonerId : reqVO.getPrisonerIds()) {
            Integer i = personRepresentDao.checkeSignStatusByVideo(reqVO.getId(), prisonerId);
            if (i == 0) {
                personRepresentDao.updateSignStatusByVideo(reqVO.getId(), prisonerId, user.getIdCard(), user.getName(), now);
                if (errNum > 0) {
                    errNum = errNum - 1;
                } else {
                    errNum = 0;
                }
                presentNum = presentNum + 1;
                if (errNum == 0 && presentNum == representEntity.getInNum()) {
                    entity.setPresentStatus("2");
                    break;
                }
            }
        }
        entity.setIsVideo(1);
        entity.setErrorNum(errNum);
        entity.setPresentNum(presentNum);
        this.representDao.updateById(entity);
        return 1;
    }

    @Override
    public RoomInfoRepresentVO selectByRepresentId(String id) {
        RoomInfoRepresentVO vo = new RoomInfoRepresentVO();
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        RepresentDO roomRepresentEntity = representDao.selectById(id);
        if (roomRepresentEntity != null) {
            org.springframework.beans.BeanUtils.copyProperties(roomRepresentEntity, vo);
            vo.setStartTimeStr(sdf.format(vo.getStartTime()));
            vo.setEndTimeStr(vo.getEndTime() != null ? sdf.format(vo.getEndTime()) : "现在");
            vo.setRepresentDate(DateUtil.format(vo.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
            List<RepresentPersonVO> persons = personRepresentDao.getPersonsByRepresentId(id);
            for (RepresentPersonVO personVo : persons) {
//                personVo.setFrontPhoto(personVo.getFrontPhoto());
//                personVo.setSuspectedCharges(dictService.dictValue(WdaDictionaryConstants.C_AJLB, personVo.getSuspectedCharges()));
//                personVo.setLitigationLink(dictService.dictValue(WdaDictionaryConstants.C_KSS_SSJD, personVo.getLitigationLink()));
                if (personVo.getIsVideo() == 1) {
                    personVo.setSignStatus("3");//视频点名
                }
            }
            vo.setRepresentPersonVos(persons);
            vo.setReportNum(vo.getReportNum()==null?0:vo.getReportNum());
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HandleRepresentVO startRepresent(List<RoomInfoVO> rooms, Integer time, Integer initSource) {
        HandleRepresentVO handleRepresentVo = new HandleRepresentVO();
        List<RoomInfoRepresentVO> vos = new ArrayList<>();
        String presentNo = bspApi.executeByRuleCode("param_represent_code", null);

        List<RepresentDO> representEntityList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        Date now = new Date();
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        for (RoomInfoVO room : rooms) {
            Integer status = representDao.getInRepresnetData(room.getOrgCode(), room.getRoomId());
            if (status != null && status > 0) {
                continue;
            }
            RoomInfoRepresentVO vo = new RoomInfoRepresentVO();
            RepresentDO entity = BeanUtils.toBean(room, RepresentDO.class);
            entity.setId(StringUtil.getGuid());
            entity.setPresentNo(presentNo);
            entity.setPresentType(2);
            entity.setStartTime(now);
            entity.setPresentStatus(RoomRepresentConstant.IN_REPRESENT);
            entity.setPresentNum(0);
            entity.setErrorNum(0);
            entity.setOperatePeopleSfzh(sessionUser.getIdCard());
            entity.setOperatePeopleName(sessionUser.getName());
            entity.setOperateTime(now);
            entity.setEndTime(DateUtil.offsetMinute(new Date(), time));
            entity.setExpiryDate(time);
            entity.setAreaId(sessionUser.getOrgCode());
            entity.setInitSource(initSource);
            representEntityList.add(entity);
            BeanUtils.copyProperties(entity, vo);
            vo.setStartTimeStr(sdf.format(vo.getStartTime()));
            vo.setEndTimeStr(vo.getEndTime() != null ? sdf.format(vo.getEndTime()) : "现在");
            vo.setRepresentDate(sdf1.format(vo.getStartTime()));
            vos.add(vo);
        }
        handleRepresentVo.setRooms(vos);
        handleRepresentVo.setInRepresent(vos.size());
        handleRepresentVo.setEndRepresent(0);
        handleRepresentVo.setErrorRepresent(0);
        handleRepresentVo.setExpiryDate(time);
        List<String> roomIds = new ArrayList<>();
        for (RoomInfoRepresentVO vo : handleRepresentVo.getRooms()) {
            roomIds.add(vo.getRoomId());
        }
        if(roomIds.isEmpty()){
            return handleRepresentVo;
        }
        JSONObject message = new JSONObject();
        message.put("content", JSONObject.toJSONString(handleRepresentVo));
        List<String> serialNumbers = socketService.getCnpSerialNumberByRoomId(roomIds);
        List<String> sessionIds = socketService.getSessionBySerialNumber(serialNumbers);
//        List<String> serialNumbers = new ArrayList<>();
//        List<String> sessionIds = new ArrayList<>();

        for (RepresentDO entity : representEntityList) {
            List<String> roomList = new ArrayList<>();
            roomList.add(entity.getRoomId());
            List<String> serialNumber = socketService.getCnpSerialNumberByRoomId(roomList);
//            List<String> serialNumber = new ArrayList<>();
            int insert = representDao.insert(entity);
            log.info(entity.getRoomId() + "监室点名表数据插入结果状态" + insert);
            log.info("点名结束时间任务开始");

//            scheduledService.doEndTask(entity);
            log.info("点名结束时间任务结束");
            //插入人员签到信息
            // TODO 缺少谈话教育数据
            List<RoomPersonVO> personVos = representDao.getPersonInRoom(entity.getRoomId());
            for (RoomPersonVO personVo : personVos) {
                PersonRepresentDO personEntity = new PersonRepresentDO();
                personEntity.setPrisonerId(personVo.getPrisonerId());
                personEntity.setJgrybm(personVo.getJgrybm());
                personEntity.setJgryxm(personVo.getPrisonerName());
                personEntity.setRoomPresentId(entity.getId());
                personEntity.setSignStatus(RoomRepresentConstant.PERSON_REPRESENT_WAIT);
                if ("1".equals(personVo.getReport())) {
                    personEntity.setIsOut("1");
                    personEntity.setIsReport(1);
                    personEntity.setOutReason("已报备，不参与点名");
                } else if ("1".equals(personVo.getOutArraignment())) {
                    personEntity.setIsOut("1");
                    personEntity.setOutReason("提讯");
                } else if ("1".equals(personVo.getOutBringInterrogation())) {
                    personEntity.setIsOut("1");
                    personEntity.setOutReason("提询");
                } else if ("1".equals(personVo.getOutConsularMeeting())) {
                    personEntity.setIsOut("1");
                    personEntity.setOutReason("领事会见");
                } else if ("1".equals(personVo.getOutLawyerMeeting())) {
                    personEntity.setIsOut("1");
                    personEntity.setOutReason("律师会见");
                } else if ("1".equals(personVo.getOutFamilyMeeting())) {
                    personEntity.setIsOut("1");
                    personEntity.setOutReason("家属会见");
                } else if ("1".equals(personVo.getOutEscort())) {
                    personEntity.setIsOut("1");
                    personEntity.setOutReason("提解");
                } else if ("1".equals(personVo.getOutPrisonTreatment())) {
                    personEntity.setIsOut("1");
                    personEntity.setOutReason("出所就医");
                }
                int insert1 = personRepresentDao.insert(personEntity);
                log.info(entity.getRoomId() + "监室点名人员表数据插入结果状态" + insert1);
            }

            //手动点名任务形式socket推到安卓
//            String uuid = StringUtil.getGuid32();
            if (CollectionUtil.isNotEmpty(serialNumber)) {
                String uuid = UUIDUtils.generateShortUuid();
                Date date = new Date();
                String format = DateUtil.format(date, "HH:mm");
                String[] times = format.split(":");
                String configPeriodCode = "3"; //临时点名 执行一次计划
                String cronStr = CronUtils.getCronOne(times[0], times[1]);
                String expiryDate = time + "";
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("configPeriodCode", configPeriodCode);
                jsonObject.put("cronStr", cronStr);
                jsonObject.put("expiryDate", expiryDate);
                jsonObject.put("id", uuid);
                jsonObject.put("roomId", entity.getRoomId());
                jsonObject.put("updateType", "insert");
                PushMessageForm pushFrom = new PushMessageForm();
                pushFrom.setSerialNumbers(serialNumber);
                pushFrom.setTarget(SocketActionConstants.PushMessageTargetEnum.web.name());
                pushFrom.setTerminal(SocketActionConstants.PushMessageTerminalEnum.CNP.name());
                pushFrom.setAction(SocketActionConstants.representTask);
                Object[] objects = new Object[1];
                objects[0] = JSONObject.toJSONString(jsonObject);
                pushFrom.setParams(objects);
                try {
                    log.info("向设备:" + serialNumber + "发起临时点名,参数:" + pushFrom);
                    socketService.pushMessageToSerialNumber(pushFrom);
                }catch (Exception e){
                    log.error("客户端不存在或者离线：", e);
                }
            }
        }
        CustomPushMessageConditionDTO condition = new CustomPushMessageConditionDTO();
        condition.setSessionIds(sessionIds);
        condition.setEventName(SocketEventConstant.REPRESENT_START);
        condition.setMessage(message);
        // 异步操作
        executorService.execute(() -> {
            log.info("仓内屏连接参数：" + condition);
            log.info("sessionIds：" + sessionIds);
            Map<String, PushMessageAckVO> pushMessageAckMap = socketService.pushMessageCustomWithConditionWaitReplyMap(condition);
//            Map<String, PushMessageAckVO> pushMessageAckMap = new ArrayMap<>();
            for (String sessionId : sessionIds) {
                PushMessageAckVO pushMessageAckVO = pushMessageAckMap.get(sessionId);
                if (pushMessageAckVO != null && pushMessageAckVO.getOk()) {
                    log.info("点名开始成功");
                } else {
                    log.error("仓内屏连接失败或者响应超时：{}", JSONObject.toJSONString(pushMessageAckVO));
                }
            }
        });

        return handleRepresentVo;
    }

    @Override
    public Integer cnpCheckRoom(String roomId, Date time) {
        RepresentDO entity = representDao.getNowRepresentByRoomId(roomId);
        if (entity != null) {
            Date startTime = entity.getStartTime();
            Date endTime = DateUtil.offsetMinute(startTime, entity.getExpiryDate());
            if (time.after(startTime) && time.before(endTime)) {
                return 1;
            } else {
                return 0;
            }
        } else {
            return 0;
        }
    }

    @Override
    public HandleRepresentVO startRepresentByCnp(String roomId, Date startDate) {
        HandleRepresentVO handleRepresentVo = new HandleRepresentVO();
        List<RoomInfoRepresentVO> vos = new ArrayList<>();
        RoomInfoRepresentVO vo = new RoomInfoRepresentVO();
        RepresentDO entity = representDao.getNowRepresentByRoomId(roomId);
        org.springframework.beans.BeanUtils.copyProperties(entity, vo);
        vos.add(vo);
        handleRepresentVo.setRooms(vos);
        handleRepresentVo.setInRepresent(vos.size());
        handleRepresentVo.setEndRepresent(0);
        handleRepresentVo.setErrorRepresent(0);
        JSONObject message1 = new JSONObject();
        message1.put("content", JSONObject.toJSONString(handleRepresentVo));
        List<String> roomIds = new ArrayList<>();
        roomIds.add(roomId);
        List<String> serialNumbers = socketService.getCnpSerialNumberByRoomId(roomIds);
        List<String> sessionIds = socketService.getSessionBySerialNumber(serialNumbers);
//        List<String> sessionIds = new ArrayList<>();
        CustomPushMessageConditionDTO condition = new CustomPushMessageConditionDTO();
        condition.setEventName(SocketEventConstant.REPRESENT_START);
        condition.setSessionIds(sessionIds);
        condition.setMessage(message1);
        // 按照前人的写法，只能是去异步执行了
        executorService.execute(() -> {
            Map<String, PushMessageAckVO> pushMessageAckMap =
                    socketService.pushMessageCustomWithConditionWaitReplyMap(condition);
//            Map<String, PushMessageAckVO> pushMessageAckMap = new HashMap<>();
            for (String sessionId : sessionIds) {
                PushMessageAckVO pushMessageAckVO = pushMessageAckMap.get(sessionId);
                if (pushMessageAckVO != null && pushMessageAckVO.getOk()) {
                    Object result = pushMessageAckVO.getResponse();
                    if (Objects.equals(roomId, result.toString())) {
                        PushMessageForm pushMessageForm = new PushMessageForm();
                        pushMessageForm.setAction("getFaceCodesList");
                        List<String> ids = new ArrayList<>();
                        ids.add(sessionId);
                        pushMessageForm.setSessionIds(ids);
                        pushMessageForm.setTarget("android");
                        List<PushMessageAckVO> pushMessageAckVOS = socketService.pushMessageToClientWaitReplyMultiple(pushMessageForm);
//                        List<PushMessageAckVO> pushMessageAckVOS = new ArrayList<>();
                        log.info("仓内屏点名底库为{}", pushMessageAckVOS.toString());
                        log.info("仓内屏点名开始成功{}", result);
                    }
                } else {
                    log.error("仓内屏连接失败：{}", JSONObject.toJSONString(pushMessageAckVO));
                }
            }
        });
        return handleRepresentVo;
    }

    @Override
    public HandleRepresentVO getInRepresnetRoom(List<String> roomIds) {
        HandleRepresentVO handleRepresentVo = new HandleRepresentVO();
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        Integer inRepresent = 0;
        Integer endRepresent = 0;
        Integer errorRepresent = 0;
        List<RoomInfoRepresentVO> vos = representDao.selectInRepresentByRoomIds(roomIds);
        handleRepresentVo.setRooms(vos);
        for (RoomInfoRepresentVO vo : vos) {
            vo.setStartTimeStr(sdf.format(vo.getStartTime()));
            vo.setEndTimeStr(vo.getEndTime() != null ? sdf.format(vo.getEndTime()) : "现在");
            if (vo.getPresentStatus().equals(RoomRepresentConstant.IN_REPRESENT)) {
                inRepresent++;
            } else if (vo.getPresentStatus().equals(RoomRepresentConstant.END_REPRESENT)) {
                endRepresent++;
            } else if (vo.getPresentStatus().equals(RoomRepresentConstant.ERROR_REPRESENT)) {
                errorRepresent++;
            }
        }
        handleRepresentVo.setInRepresent(inRepresent);
        handleRepresentVo.setEndRepresent(endRepresent);
        handleRepresentVo.setErrorRepresent(errorRepresent);
        return handleRepresentVo;
    }

    @Override
    public Integer errorHandle(String id, String errorMsg) {
        Integer num = 0;
        RepresentDO entity = new RepresentDO();
        entity.setId(id);
        entity.setErrorHandleResult(errorMsg);
        entity.setPresentStatus(RoomRepresentConstant.END_REPRESENT);//已结束
        num = representDao.updateById(entity);
        return num;
    }

    @Override
    public Integer personToRepresent(String prisonerId, String roomId, String temperature) {
        //更新个人签到情况
        Date now = new Date();
        RepresentDO represent = representDao.getNowRepresentByRoomId(roomId);
        log.info("点名监室名" + represent.getRoomName());
        String presentId = represent.getId();
        log.info("人脸识别接口 正在点名的点名id为" + presentId);
        PersonRepresentDO personRepresentDO = personRepresentDao.getPersonSignStatus(prisonerId, presentId);
        if(personRepresentDO.getIsReport() != null && personRepresentDO.getIsReport() == 1){
            log.info(prisonerId + "个人签到情况" + "报备点名异常");
            return 4;
        }
        log.info("点名的在押人员姓名" + personRepresentDO.getJgryxm());
        if (personRepresentDO == null) {
            log.info(prisonerId + "个人签到情况外出异常");
            return 0;
        } else {
            if (personRepresentDO.getSignStatus().equals("1")) {
                log.info(prisonerId + "个人签到情况重复点名异常");
                CheckDO checkEntity = new CheckDO();
                checkEntity.setId(StringUtil.getGuid());
                checkEntity.setAddTime(now);
                checkEntity.setAddUser(personRepresentDO.getJgrybm());
                checkEntity.setAddUserName(personRepresentDO.getJgryxm());
                checkEntity.setOrgCode(personRepresentDO.getOrgCode());
                checkEntity.setOrgName(personRepresentDO.getOrgName());
                checkEntity.setRoomId(roomId);
                checkEntity.setRoomName(represent.getRoomName());
                checkEntity.setPresentNo(represent.getPresentNo());
                checkEntity.setPrisonerId(prisonerId);
                checkEntity.setJgrybm(personRepresentDO.getJgrybm());
                checkEntity.setJgryxm(personRepresentDO.getJgryxm());
                checkEntity.setStatus(2);
                this.checkDao.insert(checkEntity);
                //入库纪律重复点名异常
                return 1;
            }
            personRepresentDO.setSignStatus("1");
            personRepresentDO.setTemperature(temperature);
            personRepresentDO.setSignTime(now);
            personRepresentDao.updateById(personRepresentDO);
        }
        //得到主表已签到个数
        RepresentDO roomRepresentEntity = representDao.selectById(presentId);
        roomRepresentEntity.setPresentNum(roomRepresentEntity.getPresentNum() + 1);
        //更新主表
        //判断监室是否点名已经全部完成
        representDao.updateById(roomRepresentEntity);
        if (personRepresentDao.checkIsLastSign(roomId) == 0) {
            roomRepresentEntity.setPresentStatus("2");
            representDao.updateById(roomRepresentEntity);
            log.info(roomId+"点名全部完成"+personRepresentDO.getJgryxm());
            return 3;
        }
        return 2;
    }

    @Override
    public Boolean endRepresent(String roomId) {
        Integer num = 0;
        String[] roomIds = roomId.split(",");
        for (String id : roomIds) {
            Integer n = 0;
            RepresentDO room = representDao.getNowRepresentByRoomId(id);
            if (room != null) {
                //判断主表签到情况
                if (room.getInNum() == room.getPresentNum()) {
                    room.setPresentStatus(RoomRepresentConstant.END_REPRESENT);
                } else {
                    room.setErrorNum(room.getInNum() - room.getPresentNum());
                    room.setPresentStatus(RoomRepresentConstant.ERROR_REPRESENT);
                    rabbitProduct.sendMsg(RabbitTopicsConstants.prisoner_room_present,room.getPresentNo());
//                    kafkaTemplate.send(KafkaTopicsConstants.prisoner_room_present,room.getPresentNo());
                }
                //更新主表
                room.setEndTime(new Date());
                n = representDao.updateById(room);
            }
            num = num + n;
        }
        List<String> list = Arrays.asList(roomId.split(","));
        log.info("结束点名的监室号" + roomId);
        List<BaseDeviceInscreenDO> inscreenList = rsBaseDeviceInscreenDao.getListByRoomIds(list);
        List<String> serialNumbers = inscreenList.stream().map(inscreen -> inscreen.getSerialNumber()).collect(Collectors.toList());
        log.info("开始推送结束点名事件的sid" + serialNumbers);
        PushMessageForm pushFrom = new PushMessageForm();
        pushFrom.setSerialNumbers(serialNumbers);
        pushFrom.setTarget(SocketActionConstants.PushMessageTargetEnum.web.name());
        pushFrom.setTerminal(SocketActionConstants.PushMessageTerminalEnum.CNP.name());
        pushFrom.setAction(SocketActionConstants.endRepresent);
        String form = JSONObject.toJSONString(pushFrom);
        rabbitProduct.sendMsg(RabbitTopicsConstants.Rabbittopic_PushMessageToSerialNumber, form);
//        kafkaTemplate.send(KafkaTopicsConstants.Kafkatopic_PushMessageToSerialNumber, form);
        if (num == roomIds.length) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public Boolean endRepresentCnp(String roomId) {
        RepresentDO room = representDao.getNowRepresentByRoomId(roomId);
        log.info("仓内屏开始发起结束点"+room);
        if (room != null) {
            //判断主表签到情况
            if (room.getInNum().equals(room.getPresentNum())) {
                room.setPresentStatus(RoomRepresentConstant.END_REPRESENT);
            } else {
                room.setErrorNum(room.getInNum() - room.getPresentNum());
                room.setPresentStatus(RoomRepresentConstant.ERROR_REPRESENT);
            }
            //更新主表
            room.setEndTime(new Date());
            int i = representDao.updateById(room);
            log.info("仓内屏发起结束点名结果"+i);
        }
        return true;
    }

    @Override
    public Boolean endRepresentByNo(String presentNo) {
        List<String> strings = representDao.endRepresentByNo(presentNo);
        for (String roomId : strings) {
            endRepresent(roomId);
        }
        return true;
    }

    @Override
    public RepresentConfigRespVO getConfigListById(String id) {
        RepresentConfigDO configDO = representConfigDao.getConfigListById(id);
        if (ObjectUtil.isEmpty(configDO)) {
            throw new ServerException("该点名任务不存在");
        }
        RepresentConfigRespVO vo = BeanUtils.toBean(configDO, RepresentConfigRespVO.class);
        String[] roomIds = configDO.getRoomId().split(",");
        List<String> roomNameList = new ArrayList<>();
        if (!StringUtil.isNullBlank(configDO.getRoomId())) {
            for (String roomId : roomIds) {
                AreaPrisonRoomDO prisonRoomDO = areaPrisonRoomDao.selectByRoomCode(configDO.getOrgCode(), roomId);
                if (ObjectUtil.isNotEmpty(configDO)) {
                    roomNameList.add(prisonRoomDO.getRoomName());
                }
            }
        }
        if (roomNameList.size() > 0) {
            vo.setRoomName(String.join(",", roomNameList));
        }
        if (vo.getStatus() == 0) {
            vo.setStatusDisplayName("启用");
        } else {
            vo.setStatusDisplayName("未启用");
        }
        return vo;
    }

//    @Override
//    public Integer updateRepresentConfig(RepresentConfigSaveReqVO configVo, Integer userId, String userName) {
//        Integer num = 0;
//        String orderCode = bspApi.executeByRuleCode("param_represent_code", null);
//        if (!StringUtil.isNullBlank(configVo.getRoomId())) {
//            RepresentConfigDO entity = new RepresentConfigDO();
//            entity.setRoomId(configVo.getRoomId());
////                entity.setRoomName(roomApi.findRoomNameByRoomId(roomId));
//            entity.setStatus(configVo.getStatus());
//            entity.setConfigPeriodCode(configVo.getConfigPeriodCode());
//            entity.setTaskName(configVo.getTaskName());
//            org.springframework.beans.BeanUtils.copyProperties(configVo, entity);
//            if (entity.getRoomId().equals("")) {
//                entity.setRoomId(null);
//            }
//            Date date = DateUtil.parse(configVo.getStartTime(), "HH:mm:ss");
//            Date endTime = DateUtil.offsetMinute(date, configVo.getExpiryDate());
//            String endTimeStr = DateUtil.format(endTime, "HH:mm:ss");
//            configVo.setEndTime(endTimeStr);
//            String[] times = configVo.getStartTime().split(":");
//            String[] endTimes = configVo.getEndTime().split(":");
//            if (configVo.getConfigPeriodCode().equals("2")) {
//                entity.setCron(CronUtils.getCron(times[0], times[1]));
//                entity.setEndCron(CronUtils.getCron(endTimes[0], endTimes[1]));
//            } else if (configVo.getConfigPeriodCode().equals("1")) {
//                entity.setCron(CronUtils.getCronWeek(times[0], times[1], configVo.getConfigPeriod()));
//                entity.setEndCron(CronUtils.getCronWeek(endTimes[0], endTimes[1], configVo.getConfigPeriod()));
//            } else if (configVo.getConfigPeriodCode().equals("3")) {
//                entity.setCron(CronUtils.getCronOne(times[0], times[1]));
//                entity.setEndCron(CronUtils.getCronOne(endTimes[0], endTimes[1]));
//            }
//            if (entity.getId() == null || entity.getId().equals("")) {
//                entity.setId(UUID.randomUUID().toString().replace("-", ""));
//                //entity.setOperateUserId(userId);
//                //entity.setOperateUserName(userName);
//                //entity.setOperateTime(new Date());
//                num = representConfigDao.insert(entity);
//            } else {
//                num = representConfigDao.updateById(entity);
//            }
//            if (configVo.getStatus() == 0) {
//                String code = entity.getStartTime().replaceAll(":", "").substring(0, 4);
////                scheduledService.doStartTask(entity, orderCode + code + entity.getId());
//            } else {
////                scheduledService.delStartTask(entity.getId());
//            }
//        }
//        return num;
//    }

    @Override
    public Integer delRepresentConfig(List<String> ids) {
        for (String id : ids) {
//            kafkaTemplate.send("del_represent_config", JSONObject.toJSONString(id));
            representConfigDao.deleteById(id);
        }
        return 1;
    }

    @Override
    public List<RoomInfoRepresentVO> getCwpRepresentList(String roomId, Date startTime, Date endTime) {
        List<RoomInfoRepresentVO> cwpRepresentRoomResult = representDao.getCwpRepresentList(roomId, startTime, endTime);
        return cwpRepresentRoomResult;
    }

    @Override
    public InitBaseDataVO initBaseData(String id) {
        InitBaseDataVO initBaseData = representDao.getInitBaseDataList(id);
        List<RepresentConfigDO> configList = representConfigDao.getAllConfig();
        List<InitBaseDataConfigVO> allConfig = BeanUtils.toBean(configList, InitBaseDataConfigVO.class);
        List<InitBaseDataConfigVO> configVOList = new ArrayList<>();
        for(InitBaseDataConfigVO configVO : allConfig){
            String[] split = configVO.getRoomId().split(",");
            for(String roomId:split){
                if(roomId.equals(initBaseData.getRoomId())){
                    configVO.setRoomId(roomId);
                    configVO.setCronStr(configVO.getCron());
                    configVO.setStartTime(configVO.getStartTime());
                    configVOList.add(configVO);
                }
            }
        }
        initBaseData.setConfigVOS(configVOList);
        // 获取监室在押人员
        List<InitBaseDataPersonVO> personList = representDao.findPersonList(initBaseData.getRoomId());
        personList.forEach(person -> {
            if(StringUtil.isNotEmpty(person.getOutStatusName()))
                person.setOutStatus(1);
            else
                person.setOutStatus(0);
        });
        initBaseData.setPersonVOS(personList);
        //查找手动点名数据
        InitBaseDataConfigVO oneTask = getOneTask(initBaseData.getRoomId());
        if (oneTask != null) {
            initBaseData.getConfigVOS().add(oneTask);
        }
        return initBaseData;
    }

    @Override
    public InitBaseDataConfigVO getOneTask(String roomId) {
        RepresentConfigDO configDO = representDao.getOneTask(roomId);
        InitBaseDataConfigVO oneTask = BeanUtils.toBean(configDO, InitBaseDataConfigVO.class);
        if (oneTask != null) {
            String format = DateUtils.format(oneTask.getStartTime(), "HH:mm");
            String[] times = format.split(":");
            String cronStr = CronUtils.getCronOne(times[0], times[1]);
            oneTask.setCronStr(cronStr);
        }
        return BeanUtils.toBean(oneTask, InitBaseDataConfigVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer receiveData(ReceiveDataVO receiveDataDTO) {
        String taskCode = receiveDataDTO.getTaskCode();
        String roomPresentIdByCode = representDao.getRoomPresentIdByCode(taskCode, receiveDataDTO.getRoomId());
        Integer sumNum = representDao.getCountInRoom(receiveDataDTO.getRoomId());
        Integer presentNum = receiveDataDTO.getPersonDTOS().size();
        Integer errNum = sumNum - receiveDataDTO.getPersonDTOS().size();
        if (!StringUtil.isNullBlank(roomPresentIdByCode)) {
            RepresentDO roomRepresent = representDao.selectById(roomPresentIdByCode);
            presentNum = (roomRepresent.getPresentNum() + presentNum) > sumNum ? sumNum : (roomRepresent.getPresentNum() + presentNum);
            errNum = sumNum - receiveDataDTO.getPersonDTOS().size() - roomRepresent.getPresentNum();
            if (errNum < 0) {
                errNum = 0;
            }
            List<ReceiveDataPersonVO> personDTOS = receiveDataDTO.getPersonDTOS();
            for (ReceiveDataPersonVO dto : personDTOS) {
                PersonRepresentDO entity = new PersonRepresentDO();
                entity.setSignTime(dto.getSignTime());
                entity.setJgrybm(dto.getPrisonerId());
                entity.setTemperature(dto.getTemperature());
                entity.setSignStatus(dto.getSignStatus());
                LambdaUpdateWrapper<PersonRepresentDO> updateWrapper = new LambdaUpdateWrapper<PersonRepresentDO>()
                        .eq(PersonRepresentDO::getRoomPresentId, roomPresentIdByCode)
                        .eq(PersonRepresentDO::getJgrybm, dto.getPrisonerId());
                int update = personRepresentDao.update(entity, updateWrapper);
                if (update < 0) {
                    return 0;
                }
            }
            RepresentDO entity = new RepresentDO();
            entity.setId(roomPresentIdByCode);
            entity.setPresentNum(presentNum);
            entity.setErrorNum(errNum);
            entity.setPresentStatus(errNum == 0 ? RoomRepresentConstant.END_REPRESENT : RoomRepresentConstant.ERROR_REPRESENT);
            this.representDao.updateById(entity);
        } else {//我们服务掉线的情况
            Date now = new Date();
            String[] roomIds = new String[1];
            roomIds[0] = receiveDataDTO.getRoomId();
            List<RoomInfoVO> vos = representDao.getWarderRoomInfo(null, null, roomIds, null);
//            Integer outNum = representDao.getNewCountInRoom(receiveDataDTO.getRoomId());//外出人数
            String configId = receiveDataDTO.getTaskCode().substring(12);
            String operatePeopleId = "";
            String operatePeopleName = "admin";
            Integer expiryDate = 0;
            RepresentConfigDO configEntity = representConfigDao.selectById(configId);
            if (configEntity == null) {
                RepresentDO entity = representDao.selectById(configId);
                operatePeopleId = entity.getOperatePeopleSfzh();
                operatePeopleName = entity.getOperatePeopleName();
                expiryDate = entity.getExpiryDate();
            } else {
               // operatePeopleId = configEntity.get();
                //operatePeopleName = configEntity.getOperatePeopleName();
                expiryDate = configEntity.getExpiryDate();
            }
            RoomInfoVO info = vos.get(0);
            RepresentDO roomRepresentEntity = new RepresentDO();
            roomRepresentEntity.setPresentNo(receiveDataDTO.getTaskCode());
            roomRepresentEntity.setAllInNum(sumNum);
            roomRepresentEntity.setOutNum(0);
            roomRepresentEntity.setInNum(sumNum);
            roomRepresentEntity.setId(UUID.randomUUID().toString().replace("-", ""));
            roomRepresentEntity.setRoomId(receiveDataDTO.getRoomId());
            roomRepresentEntity.setRoomName(info.getRoomName());
            roomRepresentEntity.setOperatePeopleSfzh(operatePeopleId);
            roomRepresentEntity.setOperatePeopleName(operatePeopleName);
            roomRepresentEntity.setStartTime(now);
            roomRepresentEntity.setPresentStatus(errNum == 0 ? RoomRepresentConstant.END_REPRESENT : RoomRepresentConstant.ERROR_REPRESENT);
            roomRepresentEntity.setPresentNum(presentNum);
            roomRepresentEntity.setErrorNum(errNum);
            roomRepresentEntity.setOperateTime(now);
            roomRepresentEntity.setPresentType(1);
            roomRepresentEntity.setAreaId(info.getAreaId());
            roomRepresentEntity.setExpiryDate(expiryDate);
            roomRepresentEntity.setEndTime(DateUtil.offsetMinute(now, expiryDate));
            int insert = representDao.insert(roomRepresentEntity);
            log.info("room-termimal离线后接收数据推送插入状态" + insert);


            List<RoomPersonVO> personVos = representDao.getPersonInRoom(roomRepresentEntity.getRoomId());
            for (RoomPersonVO personVo : personVos) {
                PersonRepresentDO personEntity = new PersonRepresentDO();
                personEntity.setId(UUID.randomUUID().toString().replace("-", ""));
                personEntity.setJgrybm(personVo.getJgrybm());
                personEntity.setJgryxm(personVo.getPrisonerName());
                personEntity.setRoomPresentId(roomRepresentEntity.getId());
                personEntity.setSignStatus(RoomRepresentConstant.PERSON_REPRESENT_WAIT);
                if ("1".equals(personVo.getReport())) {
                    personEntity.setIsOut("1");
                    personEntity.setIsReport(1);
                    personEntity.setOutReason("已报备，不参与点名");
                } else if ("1".equals(personVo.getOutArraignment())) {
                    personEntity.setIsOut("1");
                    personEntity.setOutReason("提讯");
                } else if ("1".equals(personVo.getOutBringInterrogation())) {
                    personEntity.setIsOut("1");
                    personEntity.setOutReason("提询");
                } else if ("1".equals(personVo.getOutConsularMeeting())) {
                    personEntity.setIsOut("1");
                    personEntity.setOutReason("领事会见");
                } else if ("1".equals(personVo.getOutLawyerMeeting())) {
                    personEntity.setIsOut("1");
                    personEntity.setOutReason("律师会见");
                } else if ("1".equals(personVo.getOutFamilyMeeting())) {
                    personEntity.setIsOut("1");
                    personEntity.setOutReason("家属会见");
                } else if ("1".equals(personVo.getOutEscort())) {
                    personEntity.setIsOut("1");
                    personEntity.setOutReason("提解");
                } else if ("1".equals(personVo.getOutPrisonTreatment())) {
                    personEntity.setIsOut("1");
                    personEntity.setOutReason("出所就医");
                }
                int insert1 = personRepresentDao.insert(personEntity);
                log.info(roomRepresentEntity.getRoomId() + "监室点名人员表数据插入结果状态" + insert1);
            }
//            //插入人员签到信息
//            List<RoomPersonVo> personVos = representDao.getPersonInRoom(roomRepresentEntity.getRoomId());
            for (ReceiveDataPersonVO personVo : receiveDataDTO.getPersonDTOS()) {
                PersonRepresentDO personEntity = new PersonRepresentDO();
                personEntity.setSignStatus("1");
                UpdateWrapper<PersonRepresentDO> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("room_present_id", roomRepresentEntity.getId());
                updateWrapper.eq("prisoner_id", personVo.getPrisonerId());
                int update = personRepresentDao.update(personEntity, updateWrapper);
                log.info("room-termimal离线后插入人员签到信息" + update);
            }
        }
        return 1;
    }

    @Override
    public Integer checkData(List<RoomRepresentCheckVO> dtos) {
        for (RoomRepresentCheckVO dto : dtos) {
            RepresentDO entity = representDao.getNowRepresentByRoomId(dto.getRoomId());
            CheckDO checkEntity = new CheckDO();
            checkEntity.setId(StringUtil.getGuid());
            checkEntity.setPresentNo(entity.getPresentNo());
            checkEntity.setRoomId(dto.getRoomId());
            checkEntity.setRoomName(representDao.getRoomNameById(dto.getRoomId()));
            checkEntity.setJgrybm(dto.getPrisonerId());
            checkEntity.setJgryxm(dto.getPrisonerName());
//            checkEntity.setCreateTime(new Date());
            checkDao.insert(checkEntity);
        }
        return 1;
    }

    @Override
    public Integer updateCheckData(RoomRepresentCheckVO checkDTO) {
        log.info("补录照片修改校验表状态" + checkDTO);
        RepresentDO entity = representDao.getNowRepresentByRoomId(checkDTO.getRoomId());
        if (entity != null) {
            checkDao.updateCheckData(entity.getPresentNo(), checkDTO.getPrisonerId());
        }
        return 1;
    }

    @Override
    public Integer pushData(PushDataVO reqVO) {
        String roomId = reqVO.getRoomId();
        String configId = reqVO.getConfigId();
        if (StringUtil.isNullBlank(roomId)) {
            log.error("点名时间同步数据接口参数错误-监室号不存在");
            return -1;
        }
        try {
            log.info("点名时间同步数据接口参数" + roomId);
            RepresentDO room = representDao.getNowRepresentByRoomId(roomId);
            if (room == null) {
                RoomInfoVO info = representDao.getRoomInfoByRoomId(roomId);
                if (info == null) {
                    log.error("点名监室" + roomId + "不存在");
                    return 1;
                }
                log.info(info.getRoomName() + "仓内屏开始同步点名数据");
                Date now = new Date();
                RepresentConfigDO configEntity = representConfigDao.selectById(configId);
                if(configEntity==null){
                    log.error("手动点名不需要同步");
                    return 1;
                }
                String code = configEntity.getStartTime().replaceAll(":", "").substring(0, 4);
                String orderCodeNew = OrderCoderUtil.getAutotPresentCode() + code + configId;
                List<String> isPresentNo = representDao.checkRepresentNo(orderCodeNew, roomId);
                if(!isPresentNo.isEmpty()){
                    log.error(roomId+"已经执行过点名,不要反复进去");
                    return -1;
                }
                RepresentDO roomRepresentEntity = new RepresentDO();
                Integer sumNum = representDao.getCountInRoom(roomId);
                Integer outNum = representDao.getCountOutRoom(roomId);//外出人数

                log.info("自动点名  {}({})监室关押{}人, 外出{}人", info.getRoomName(), info.getRoomId(), sumNum, outNum);
                // 2021年8月16日 在所人数0 不统计
                if (sumNum == 0) {
                    log.warn("空监室+退出点名");
                    return -3;
                }
                roomRepresentEntity.setId(StringUtil.getGuid32());
                roomRepresentEntity.setPresentNo(orderCodeNew);
                roomRepresentEntity.setAddUser(configEntity.getAddUser());
                roomRepresentEntity.setAddUserName(configEntity.getAddUserName());
                roomRepresentEntity.setAddTime(now);
                roomRepresentEntity.setOrgCode(configEntity.getOrgCode());
                roomRepresentEntity.setOrgName(configEntity.getOrgName());
                roomRepresentEntity.setRoomId(roomId);
                roomRepresentEntity.setRoomName(info.getRoomName());
                roomRepresentEntity.setAllInNum(sumNum);
                roomRepresentEntity.setOutNum(outNum);
                roomRepresentEntity.setInNum(sumNum - outNum);
                roomRepresentEntity.setOperatePeopleSfzh(configEntity.getAddUser());
                roomRepresentEntity.setOperatePeopleName(configEntity.getAddUserName());
                roomRepresentEntity.setStartTime(now);
                roomRepresentEntity.setPresentStatus(RoomRepresentConstant.IN_REPRESENT);
                roomRepresentEntity.setPresentNum(0);
                roomRepresentEntity.setErrorNum(0);
                roomRepresentEntity.setOperateTime(now);
                roomRepresentEntity.setPresentType(1);
                roomRepresentEntity.setInitSource(1);
                roomRepresentEntity.setOrgCode(info.getOrgCode());
                roomRepresentEntity.setExpiryDate(configEntity.getExpiryDate());
                roomRepresentEntity.setEndTime(DateUtil.offsetMinute(new Date(), configEntity.getExpiryDate()));
                List<String> list = representDao.checkIsNowRepresent(roomId);
                if(list.isEmpty()){
                    int insert = representDao.insert(roomRepresentEntity);
                    log.info(roomRepresentEntity.getRoomId() + "安卓调用自动点名监室插入成功" + insert);
                }else {
                    log.info(roomRepresentEntity.getRoomId() + "安卓调用自动点名监室已经插入成功，不要重复插入");
                }
                log.info(info.getRoomName() + "自动点名+停止点名任务开始");
//                scheduledService.doEndTask(roomRepresentEntity);
                log.info(info.getRoomName() + "自动点名+停止点名任务结束");
                //插入人员签到信息
                List<RoomPersonVO> personVos = representDao.getPersonInRoom(roomId);
                for (RoomPersonVO personVo : personVos) {
                    PersonRepresentDO personEntity = new PersonRepresentDO();
                    personEntity.setId(StringUtil.getGuid32());
                    personEntity.setAddUser(configEntity.getAddUser());
                    personEntity.setAddUserName(configEntity.getAddUserName());
                    personEntity.setAddTime(now);
                    personEntity.setOrgCode(configEntity.getOrgCode());
                    personEntity.setOrgName(configEntity.getOrgName());
                    personEntity.setPrisonerId(personVo.getPrisonerId());
                    personEntity.setJgrybm(personVo.getJgrybm());
                    personEntity.setJgryxm(personVo.getPrisonerName());
                    personEntity.setRoomPresentId(roomRepresentEntity.getId());
                    personEntity.setSignStatus(RoomRepresentConstant.PERSON_REPRESENT_WAIT);
                    if ("1".equals(personVo.getReport())) {
                        personEntity.setIsOut("1");
                        personEntity.setIsReport(1);
                        personEntity.setOutReason("已报备，不参与点名");
                    } else if ("1".equals(personVo.getOutArraignment())) {
                        personEntity.setIsOut("1");
                        personEntity.setOutReason("提讯");
                    } else if ("1".equals(personVo.getOutBringInterrogation())) {
                        personEntity.setIsOut("1");
                        personEntity.setOutReason("提询");
                    } else if ("1".equals(personVo.getOutConsularMeeting())) {
                        personEntity.setIsOut("1");
                        personEntity.setOutReason("领事会见");
                    } else if ("1".equals(personVo.getOutLawyerMeeting())) {
                        personEntity.setIsOut("1");
                        personEntity.setOutReason("律师会见");
                    } else if ("1".equals(personVo.getOutFamilyMeeting())) {
                        personEntity.setIsOut("1");
                        personEntity.setOutReason("家属会见");
                    } else if ("1".equals(personVo.getOutEscort())) {
                        personEntity.setIsOut("1");
                        personEntity.setOutReason("提解");
                    } else if ("1".equals(personVo.getOutPrisonTreatment())) {
                        personEntity.setIsOut("1");
                        personEntity.setOutReason("出所就医");
                    }
                    int insert = personRepresentDao.insert(personEntity);
                    log.info("安卓调用插入" + info.getRoomName() + personVo.getPrisonerName() + "人员签到信息" + insert);
                }
            } else {
                log.info(roomId + "后台已经入库点名数据");
                return 1;
            }
        } catch (Exception e) {
            log.error("点名同步时间异常" + e);
            return -2;
        }
        return 1;
    }

    @Override
    public Integer getCountInRoom(String roomId) {
        return representDao.getCountInRoom(roomId);
    }

    @Override
    public Integer getCountOutRoom(String roomId) {
        return representDao.getCountOutRoom(roomId);
    }

    @Override
    public RoomRepresentCheckVO getPhotoByPrisonerId(String prisonerId) {
        RoomRepresentCheckVO vo = representDao.getCnpPhoto(prisonerId);
        log.info("点名获取仓内屏照片补录" + vo);
        if (vo == null) {
            vo = representDao.getPrisonerPhotoByPrisonerId(prisonerId);
//            if (vo != null) {
//                log.info("点名获取在押人员照片补录" + vo);
//                vo.setPhoto(pathPrefix + vo.getPhoto());
                return vo;
//            } else {
//                log.error("点名获取在押人员找不到图片" + prisonerId);
//                return null;
//            }
//        } else {
//            vo.setPhoto(pathPrefix + vo.getPhoto());
        }
        return vo;
    }

    @Override
    public RoomInfoRepresentVO getCwpRepresentDetail(String id) {
        RoomInfoRepresentVO representSum = representDao.getRepresentSum(id);
        List<RepresentPersonVO> personList = personRepresentDao.getPersonsByRepresentId(id);
        representSum.setRepresentPerson(personList.stream().filter(person -> "1".equals(person.getSignStatus())).collect(Collectors.toList()));
        representSum.setNotRepresentPerson(personList.stream().filter(person -> !"1".equals(person.getSignStatus())).collect(Collectors.toList()));
        return representSum;
    }

    @Override
    public JSONObject getFirstPersonList(String roomId) {
        log.info(roomId + "第一次点名返回的人员数据开始");
        RepresentDO entity = representDao.getNowRepresentByRoomId(roomId);
        Date startTime = entity.getStartTime();
        Integer expiryDate = entity.getExpiryDate();
        Date endTime = DateUtil.offsetMinute(startTime, expiryDate);
        log.info("getFirstPersonList的点名结束时间为" + endTime);
        List<InRepresentPersonVO> vos = personRepresentDao.getPersonsByRoomId(roomId, null);
        for(InRepresentPersonVO vo:vos){
//            vo.setFrontPhoto(pathPrefix + vo.getFrontPhoto());
            if(!StringUtil.isNullBlank(vo.getOutReason())&&vo.getOutReason().contains("报备")){
                vo.setOutReason("卧床");
            }
        }
        log.info(roomId + "第一次点名返回的人员数据结束" + vos.toString());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("endTime", endTime);
        jsonObject.put("list", vos);
        return jsonObject;
    }

    @Override
    public List<InRepresentPersonVO> getSecondPersonList(String roomId) {
        List<InRepresentPersonVO> vos = personRepresentDao.getPersonsByRoomId(roomId, "2");
//        vos.forEach(person -> person.setFrontPhoto(pathPrefix + person.getFrontPhoto()));
        return vos;
    }

}
