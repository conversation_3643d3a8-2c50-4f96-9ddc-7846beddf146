package com.rs.module.pam.controller.admin.duty.vo;

import com.rs.framework.mybatis.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 监所事务管理-值班记录新增/修改 VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RecordsVO extends BaseDO {
private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private String id;
    /**
     * 监室id
     */
    private String roomId;
    /**
     * 值班ID
     */
    private String dutyId;
    /**
     * 值班日期
     */
    private Date dutyDate;
    /**
     * 班次id
     */
    private String shiftId;
    /**
     * 班次名称
     */
    private String shiftName;
    /**
     * 班次开始时间
     */
    private Date shiftStartTime;
    /**
     * 班次结束时间
     */
    private Date shiftEndTime;
    /**
     * 值班组id
     */
    private String groupId;
    /**
     * 值班组名（规则：两个被监管人员的姓名的首字母组合）
     */
    private String groupName;
    /**
     * 监管人员编码
     */
    private List<RecordsPersonSaveVO> personList = new ArrayList<>();
    /**
     * 排班人ID
     */
    private String assignerUserSfzh;
    /**
     * 排班人名称
     */
    private String assignerUserName;
    /**
     * 排班时间
     */
    private Date assignedTime;


}
