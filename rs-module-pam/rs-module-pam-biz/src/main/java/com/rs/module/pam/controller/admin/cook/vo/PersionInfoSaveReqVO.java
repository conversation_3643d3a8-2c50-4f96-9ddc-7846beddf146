package com.rs.module.pam.controller.admin.cook.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-伙房人员信息新增/修改 Request VO")
@Data
public class PersionInfoSaveReqVO {
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("姓名")
    @NotEmpty(message = "姓名不能为空")
    private String xm;

    @ApiModelProperty("身份证号")
    private String sfz;

    @ApiModelProperty("联系电话")
    private String contactPhone;

    @ApiModelProperty("背景审查时间")
    private Date backgroundCheckTime;

    @ApiModelProperty("背景审查情况")
    private String backgroundCheckResult;

    @ApiModelProperty("取得健康证明日期")
    private Date healthCertDate;

    @ApiModelProperty("健康证明材料（可存储图片路径或其他说明）")
    private String healthCertMaterial;

    @ApiModelProperty("食品安全知识和技能培训时间")
    private Date foodSafetyTrainTime;

    @ApiModelProperty("食品安全知识和技能培训情况")
    private String foodSafetyTrainResult;

    @ApiModelProperty("登记时间")
    private Date operTime;

}
