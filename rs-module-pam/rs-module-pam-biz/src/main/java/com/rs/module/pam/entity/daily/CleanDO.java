package com.rs.module.pam.entity.daily;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rs.framework.mybatis.entity.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 监所事务管理-日常清监登记 DO
 *
 * <AUTHOR>
 */
@TableName("pam_daily_clean")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CleanDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 数据来源（字典：ZD_DATA_SOURCES）
     */
    private String dataSources;
    /**
     * 检查监室，多个逗号分割
     */
    private String checkRoomId;
    /**
     * 带队领导ID，多个逗号分割
     */
    private String leaderUserSfzh;
    /**
     * 带队领导ID，多个逗号分割
     */
    private String leaderUserName;
    /**
     * 参加民警，多个逗号分割
     */
    private String involvementUserSfzh;
    /**
     * 参加民警ID，多个逗号分割
     */
    private String involvementUserName;
    /**
     * 检查时间
     */
    private Date checkTime;
    /**
     * 检查内容项ID,多个逗号分割
     */
    private String checkItemId;
    /**
     * 检查内容
     */
    private String checkContent;
    /**
     * 登记经办人
     */
    private String operatorSfzh;
    /**
     * 登记经办人姓名
     */
    private String operatorXm;
    /**
     * 登记状态
     */
    private String status;
    /**
     * 登记时间
     */
    private Date operatorTime;
    /**
     * 是否存在违禁(0:否,1:是)
     */
    private String isViolation;
    /**
     * 违禁情况
     */
    private String violationContent;
    /**
     * 违禁附件URL
     */
    private String violationAttachmentUrl;
    /**
     * 是否存在安全隐患(0:否,1:是)
     */
    private String isHiddenDanger;
    /**
     * 安全隐患情况
     */
    private String hiddenDangerContent;
    /**
     * 安全隐患附件URL
     */
    private String hiddenDangerAttachmentUrl;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 整改时间
     */
    private Date rectificationrTime;
    /**
     * 整改情况
     */
    private String rectificationrContent;
    /**
     * 所领导审批人身份证号
     */
    private String leaderApproverSfzh;
    /**
     * 所领导审批人姓名
     */
    private String leaderApproverXm;
    /**
     * 所领导审批时间
     */
    private Date leaderApproverTime;
    /**
     * 所领导审核意见
     */
    private String leaderApprovalComments;
    /**
     * 检查类型(1: 日常清监检查; 2:安全大检查)
     */
    private String checkType;
    /**
     * 整改操作人身份证号
     */
    private String rectificationrOperSfz;
    /**
     * 整改操作人姓名
     */
    private String rectificationrOperXm;
    /**
     * 其他参加人
     */
    private String otherParticipants;
    /**
     * 是否岗位协同：0 否, 1 是
     */
    private String isJoin;
    /**
     * 值班信息
     */
    private String joinInfo;


}
