package com.rs.module.pam.controller.admin.screen;

import com.alibaba.fastjson.JSONObject;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.entity.screen.CommonMeetingDO;
import com.rs.module.pam.service.screen.GjLargeScreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "监所事务管理-提押岗大屏-大屏业务")
@RestController
@RequestMapping("/pam/screen/TyLargeScreen")
@Validated
public class TyLargeScreenController {

    @Resource
    private GjLargeScreenService gjLargeScreenService;

    // 家属会见，家属单向视频会见， 提讯， 提解，  律师会见， 使馆领事会见

    @GetMapping("/topInfo")
    @ApiOperation(value = "顶部统计数据")
    @ApiImplicitParam(name = "orgCode", value = "机构编码")
    public CommonResult<JSONObject> topInfo(@RequestParam(value = "orgCode", required = false) String orgCode) {
        if(StringUtils.isEmpty(orgCode)){
            orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        }
        return success(gjLargeScreenService.getTopInfo(orgCode));
    }

    // 会见列表
    @GetMapping("/hjPage")
    @ApiOperation(value = "会见列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编码"),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true)
    })
    public CommonResult<PageResult<CommonMeetingDO>> getTodayMeetingPage(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                               @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                               @RequestParam(value = "orgCode", required = false) String orgCode) {
        if(StringUtils.isEmpty(orgCode)){
            orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        }
        return success(gjLargeScreenService.getTodayMeetingPage(pageNo, pageSize, orgCode));
    }
}
