<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.screen.AqdtScreenDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="jyCount" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        count(1) as jyzs,
        coalesce(sum(case when xb ='1' then 1 else 0 end),0) as man_count,
        coalesce(sum(case when gj not in ('156','158','344','446') then 1 else 0 end),0) as foreing_count
        FROM vw_acp_pm_prisoner_in  where is_del = 0 and ryzt = '10' and org_code = #{code}
    </select>

    <select id="jydt" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        coalesce(sum(case when rssj &gt;= DATE_TRUNC('day', NOW()) and rssj &lt; DATE_TRUNC('day', NOW())+ interval ' 1 day' then 1 else 0 end),0) as jrsy,
        coalesce(sum(case when rssj &gt;= DATE_TRUNC('day', NOW()) - interval ' 1 day' and rssj  &lt; DATE_TRUNC('day', NOW())then 1 else 0 end),0) as zrsy,
        coalesce(sum(case when rssj &gt;= DATE_TRUNC('week', NOW()) and rssj  &lt; DATE_TRUNC('day', NOW())+ interval ' 1 day' then 1 else 0 end),0) as bzsy,
        coalesce(sum(case when rssj &gt;= DATE_TRUNC('week', NOW()) - interval ' 7 day' and rssj  &lt; DATE_TRUNC('week', NOW())then 1 else 0 end),0) as szsy,
        coalesce(sum(case when rssj &gt;= DATE_TRUNC('month', NOW()) and rssj  &lt; DATE_TRUNC('day', NOW())+ interval ' 1 day' then 1 else 0 end),0) as bysy,
        coalesce(sum(case when rssj &gt;= DATE_TRUNC('month', NOW()) - interval ' 1 month' and rssj  &lt; DATE_TRUNC('month', NOW())then 1 else 0 end),0) as sysy,

        coalesce(sum(case when cssj &gt;= DATE_TRUNC('day', NOW()) and cssj &lt; DATE_TRUNC('day', NOW())+ interval ' 1 day' then 1 else 0 end),0) as jrcs,
        coalesce(sum(case when cssj &gt;= DATE_TRUNC('day', NOW()) - interval ' 1 day' and cssj &lt; DATE_TRUNC('day', NOW())then 1 else 0 end),0) as zrcs,
        coalesce(sum(case when cssj &gt;= DATE_TRUNC('week', NOW()) and cssj &lt; DATE_TRUNC('day', NOW())+ interval ' 1 day' then 1 else 0 end),0) as bzcs,
        coalesce(sum(case when cssj &gt;= DATE_TRUNC('week', NOW()) - interval ' 7 day' and cssj &lt; DATE_TRUNC('week', NOW())then 1 else 0 end),0) as szcs,
        coalesce(sum(case when cssj &gt;= DATE_TRUNC('month', NOW()) and cssj &lt; DATE_TRUNC('day', NOW())+ interval ' 1 day' then 1 else 0 end),0) as bycs,
        coalesce(sum(case when cssj &gt;= DATE_TRUNC('month', NOW()) - interval ' 1 month' and cssj &lt; DATE_TRUNC('month', NOW())then 1 else 0 end),0) as sycs
        FROM vw_acp_pm_prisoner_list
        where is_del = 0 and org_code = #{code}
    </select>

    <select id="getOneYearYearMonth" resultType="string">
            select to_char(d, 'YYYY-MM') as year_month from generate_series(
				date_trunc('month', CURRENT_DATE) - INTERVAL' 11 month',
				date_trunc('month', CURRENT_DATE),
				INTERVAL' 1 month') d
    </select>

    <select id="getOneWeekMonthDay" resultType="string">
        select to_char(d, 'MM-DD') as month_day from generate_series(
				date_trunc('day', CURRENT_DATE) - INTERVAL' 6 day',
				date_trunc('day', CURRENT_DATE),
				INTERVAL' 1 day') d
    </select>

    <select id="getOneMonthMonthDay" resultType="string">
        select to_char(d, 'MM-DD') as month_day from generate_series(
				date_trunc('day', CURRENT_DATE) - INTERVAL' 29 day',
				date_trunc('day', CURRENT_DATE),
				INTERVAL' 1 day') d
    </select>

    <select id="getMonthSy" resultType="com.alibaba.fastjson.JSONObject">
        select a.year_month, count(1) as month_rs_count from (
            select to_char(rssj, 'YYYY-MM') as year_month from vw_acp_pm_prisoner_list
            where is_del = 0 and rssj >= DATE_TRUNC('month', NOW()) - interval ' 11 month' and org_code = #{code}
            and ryzt is not null
		) a GROUP BY a.year_month
    </select>

    <select id="getMonthAfterZyInfo" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
    select
		(select count(1) from vw_acp_pm_prisoner_in where is_del = 0 and ryzt = '10' and org_code = #{code}
		and rssj &gt;= to_date(concat(#{yearMonth},'-01'), 'yyyy-MM-dd') + interval ' 1 month' ) as after_zy,
		(select count(1) from vw_acp_pm_prisoner_list where is_del = 0 and ryzt = '11' and org_code = #{code}
		and cssj &gt;= to_date(concat(#{yearMonth},'-01'), 'yyyy-MM-dd') + interval ' 1 month'
		and rssj &lt; to_date(concat(#{yearMonth},'-01'), 'yyyy-MM-dd') + interval ' 1 month') as before_zy
    </select>

    <select id="getOrgInfo" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        select * from acp_pm_org where id = #{code}
    </select>

    <select id="gjqryfb" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        select d.id, max(d.area_name) as area_name,
        coalesce(sum(case when d.jsh is not null then 1 else 0 end), 0) as room_count,
        coalesce(sum(d.zyrys),0) as zyrys from (
            select a.id, max(a.area_name) as area_name, b.id as jsh, coalesce(sum(case when c.jgrybm is not null then 1 else 0 end),0) as zyrys
            from acp_pm_area a left join acp_pm_area_prison_room b on a.id = b.area_id
            left join vw_acp_pm_prisoner_in c on b.id = c.jsh
            where a.is_del = 0 and a.org_code = #{code} and a.area_type='0002' and c.ryzt = '10' GROUP BY a.id, b.id
        ) d GROUP BY d.id
    </select>

    <select id="nldfb" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        select
        coalesce(sum(case when a.nl &gt;=0 and a.nl &lt; 18 then 1 else 0 end),0) as age_0_17,
        coalesce(sum(case when a.nl &gt;=18 and a.nl &lt; 41 then 1 else 0 end),0) as age_18_40,
        coalesce(sum(case when a.nl &gt;=41 and a.nl &lt; 66 then 1 else 0 end),0) as age_41_65,
        coalesce(sum(case when a.nl &gt;=66 then 1 else 0 end),0) as age_65
        from (
        select COALESCE(date_part('year', age(CURRENT_DATE, csrq)),0) as nl from vw_acp_pm_prisoner_in
        where is_del=0 and ryzt = '10' and org_code = #{code}
        ) a
    </select>

    <select id="getGaJcyFySshjTj" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            coalesce(SUM(CASE WHEN t.sshj IN ('11', '12', '13', '14') THEN 1 ELSE 0 END),0) AS ga_total,
            coalesce(SUM(CASE WHEN t.sshj IN ('21', '22', '23', '24' ) THEN 1 ELSE 0 END),0) AS jcy_total,
            coalesce(SUM(CASE WHEN t.sshj IN ('31', '32', '33', '34', '35', '36', '37', '38') THEN 1 ELSE 0 END),0) AS fy_total,
            coalesce(SUM(CASE WHEN t.sshj IN ('99', '41', '42', '43', '44') THEN 1 ELSE 0 END),0) AS qt_total
        FROM vw_acp_pm_prisoner_in t where t.is_del = 0 and ryzt = '10'
        and org_code in (<foreach collection="list" item="item" separator=",">#{item}</foreach>)
        and t.sshj is not null and t.sshj !=''
    </select>

    <select id="getAllSshj" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        SELECT t.sshj, count(1) as total
        FROM vw_acp_pm_prisoner_in t
        where t.is_del = 0 and ryzt = '10'
        and org_code in (<foreach collection="list" item="item" separator=",">#{item}</foreach>)
        and t.sshj is not null and t.sshj !='' GROUP BY sshj ORDER BY sshj
    </select>

    <select id="getAllSxzm" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        select a.* from (
            select sxzm, count(1) as total
            from vw_acp_pm_prisoner_in
            where is_del = 0 and ryzt = '10'
            and org_code in (<foreach collection="list" item="item" separator=",">#{item}</foreach>)
             and sxzm is not null GROUP BY sxzm
        ) a order by total desc limit 20
    </select>

    <select id="jycqtj" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        select
        coalesce(sum(case when gyqx &lt; now() then 1 else 0 end),0) as expired,
        coalesce(sum(case when gyqx &gt;= date_trunc('day', now()) and gyqx &lt; date_trunc('day', now()) + INTERVAL '1 day' then 1 else 0 end),0) as todaypass,
        coalesce(sum(case when gyqx &gt; now() and gyqx &lt; now() + INTERVAL '1 day' then 1 else 0 end),0) as onedaypass,
        coalesce(sum(case when gyqx &gt; now() and gyqx &lt; now() + INTERVAL '3 day' then 1 else 0 end),0) as threedaypass
        from vw_acp_pm_prisoner_in where is_del = 0 and ryzt = '10' and org_code = #{code}
    </select>

    <select id="jqdcstj" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        select
        coalesce(sum(case when cssj &lt; now() + INTERVAL '3 day' then 1 else 0 end),0) as future_3day_out,
        coalesce(sum(case when cssj &lt; now() + INTERVAL '7 day' then 1 else 0 end),0) as future_7day_out,
        coalesce(sum(case when cssj &lt; now() + INTERVAL '15 day' then 1 else 0 end),0) as future_15day_out
        from vw_acp_pm_prisoner_list where is_del = 0  and org_code = #{code} and cssj > now()
    </select>

    <select id="fxryfb" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        select
            coalesce(sum(case when fxdj ='1' then 1 else 0 end),0) risk1_total,
            coalesce(sum(case when fxdj ='2' then 1 else 0 end),0) risk2_total,
            coalesce(sum(case when fxdj ='3' then 1 else 0 end),0) risk3_total,
            coalesce(sum(case when fxdj is null or fxdj = '4' then 1 else 0 end),0) risk4_total
        from vw_acp_pm_prisoner_in where is_del = 0 and ryzt = '10'
            and org_code = #{code}
    </select>

    <select id="getFxryqsList" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        select concat(a.month_day,'_', a.risk_level) as map_key,  count(1) as total from (
            SELECT risk_level, to_char(add_time, 'MM-DD') as month_day FROM "acp_gj_risk_assmt"
            where is_del = 0 and status = '2' and org_code = #{code} and risk_level in ('1','2','3')
            and add_time > CURRENT_DATE - INTERVAL ' 30 day'
        ) a GROUP BY a.month_day, a.risk_level
    </select>

    <select id="getWgqs" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        select a.month_day, count(1) as total from(
            SELECT to_char(add_time, 'MM-DD') as month_day FROM acp_pi_sqgl_sqdj where is_del = 0 and add_time > CURRENT_DATE - INTERVAL ' 29 day' and org_code = #{code}
            union all
            SELECT to_char(add_time, 'MM-DD') as month_day FROM acp_pi_violation_record where is_del = 0 and add_time > CURRENT_DATE - INTERVAL ' 29 day' and org_code = #{code}
        ) a GROUP BY a.month_day
    </select>

    <select id="getWggjPage" parameterType="string" resultType="com.rs.module.pam.entity.screen.WgdjDO">
        select a.* from (
            SELECT a.id, a.room_name as address_name, a.add_time,
            concat(a.room_name,a.jgryxm,'，',a.violation_content) as violation_content,
            (case when a.status='03' then '2' else '3'end) as handle_status,
            '2' as wglb
            FROM acp_pi_violation_record a
            where a.is_del = '0'
            and a.org_code = #{code}
            <choose>
                <when test='type=="0"'>
                    and a.add_time &gt;= date_trunc('day', now())
                </when>
                <when test='type=="1"'>
                    and a.add_time &gt;= date_trunc('day', now()) - INTERVAL ' 6 day'
                </when>
                <otherwise>
                    and a.add_time &gt;= date_trunc('day', now()) - INTERVAL ' 29 day'
                </otherwise>
            </choose>
            <choose>
                <when test='handleStatus=="1"'>
                    and  a.status in ('03','04')
                </when>
                <when test='handleStatus=="2"'>
                    and a.status in ('03')
                </when>
                <otherwise>
                    and a.status in ('04')
                </otherwise>
            </choose>

            union all
        SELECT a.id, a.area_name as address_name, a.add_time,  event_details as violation_content,
        (case when a.status in ('1','2','3') then '2' else '3' end) as handle_status,
        '3' as wglb
        FROM acp_pi_sqgl_sqdj a
        where a.is_del = '0'
        and a.org_code = #{code}
        <choose>
            <when test='type=="0"'>
                and a.add_time &gt;= date_trunc('day', now())
            </when>
            <when test='type=="1"'>
                and a.add_time &gt;= date_trunc('day', now()) - INTERVAL ' 6 day'
            </when>
            <otherwise>
                and a.add_time &gt;= date_trunc('day', now()) - INTERVAL ' 29 day'
            </otherwise>
        </choose>
        <choose>
            <when test='handleStatus=="1"'>
                and  1=1
            </when>
            <when test='handleStatus=="2"'>
                and a.status in ('1','2','3')
            </when>
            <otherwise>
                and a.status in '4'
            </otherwise>
        </choose>
        ) a order by a.add_time desc
    </select>

    <select id="getWggjCount" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        select
        count(1) as allCount,
        coalesce(sum(case when a.handle_status = '2' then 1 else 0 end),0) as noHandleCount,
        coalesce(sum(case when a.handle_status = '3' then 1 else 0 end),0) as hasHandleCount
        from (
        SELECT
        (case when a.status='03' then '2' else '3'end) as handle_status
        FROM "acp_pi_violation_record" a
        where a.is_del = '0'
        and  a.status in ('03','04')
        and a.org_code = #{code}
        <choose>
            <when test='type=="0"'>
                and a.add_time &gt;= date_trunc('day', now())
            </when>
            <when test='type=="1"'>
                and a.add_time &gt;= date_trunc('day', now()) - INTERVAL ' 6 day'
            </when>
            <otherwise>
                and a.add_time &gt;= date_trunc('day', now()) - INTERVAL ' 29 day'
            </otherwise>
        </choose>
        union all
        SELECT
        (case when a.status in ('1','2','3') then '2' else '3'end) as handle_status
        FROM acp_pi_sqgl_sqdj a
        where a.is_del = 0
        and a.org_code = #{code}
        <choose>
            <when test='type=="0"'>
                and a.add_time &gt;= date_trunc('day', now())
            </when>
            <when test='type=="1"'>
                and a.add_time &gt;= date_trunc('day', now()) - INTERVAL ' 6 day'
            </when>
            <otherwise>
                and a.add_time &gt;= date_trunc('day', now()) - INTERVAL ' 29 day'
            </otherwise>
        </choose>
        ) a
    </select>


</mapper>
