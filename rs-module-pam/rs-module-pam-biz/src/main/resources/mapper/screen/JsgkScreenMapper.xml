<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.pam.dao.screen.JsgkScreenDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getPmOrgInfo" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        select * from acp_pm_org where is_del = 0 and code = #{orgCode}
    </select>

    <select id="sqdt" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        COALESCE(sum(case when status in ('1','2','3') then 1 else 0 end), 0) as no_handle_status,
        count(1) as total
        FROM  acp_pi_sqgl_sqdj  where is_del = 0 and org_code = #{orgCode}
        <choose>
            <when test='type=="1"'>
                and add_time >= date_trunc('day', now()) - INTERVAL ' 6 day'
            </when>
            <otherwise>
                and add_time >= date_trunc('day', now()) - INTERVAL ' 29 day'
            </otherwise>
        </choose>

    </select>

    <select id="getPostByOrgCode" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        select count(1) total, post from (
            select COALESCE(post, '99') as post from acp_pm_user where is_del = 0 and org_code = #{orgCode}
        ) a GROUP BY post
    </select>

    <select id="cnwptj" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            COALESCE(sum((case when device_type_id = '0008' then 1 else 0 end)),0) as cnp_total,
            COALESCE(sum((case when device_type_id = '0015' then 1 else 0 end)),0) as cwp_total
        FROM acp_pm_device where is_del = 0 and org_code = #{orgCode} and device_type_id in('0015','0008')
    </select>

    <select id="zdztfb" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            COALESCE(sum((case when device_status = '001' then 1 else 0 end)),0) as online_total,
            COALESCE(sum((case when device_status = '002' then 1 else 0 end)),0) as offline_total,
            COALESCE(sum((case when device_status is null or device_status = '003' then 1 else 0 end)),0) as fault_total
        FROM acp_pm_device where is_del = 0 and org_code = #{orgCode}
        <choose>
            <when test='type=="1"'>
                and device_type_id = '0008'
            </when>
            <otherwise>
                and device_type_id = '0015'
            </otherwise>
        </choose>
    </select>

</mapper>
