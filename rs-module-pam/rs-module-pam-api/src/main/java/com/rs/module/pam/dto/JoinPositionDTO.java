package com.rs.module.pam.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/8/13 10:01
 */
@Data
public class JoinPositionDTO {

    @ApiModelProperty(value = "岗位代码")
    private String joinCode;
    @ApiModelProperty(value = "岗位名称")
    private String joinName;
    @ApiModelProperty(value = "岗位人员")
    private List<JoinPositionPeopleDTO> joinPositionPeople;

    @Data
    public static class JoinPositionPeopleDTO {
        @ApiModelProperty(value = "身份证")
        private String sfz;
        @ApiModelProperty(value = "人员名称")
        private String name;
    }
}
