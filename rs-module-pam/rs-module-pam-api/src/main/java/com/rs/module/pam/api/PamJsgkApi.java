package com.rs.module.pam.api;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.pam.ApiConstants;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Arrays;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;


@Component
@FeignClient(name = ApiConstants.NAME, path = "/pam/screen/jsgk")
public interface PamJsgkApi {

    @GetMapping("/cnwptj")
    @ApiOperation(value = "仓内外屏统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
    })
    CommonResult<JSONObject> cnwptj(@RequestParam("code") String code);


    @GetMapping("/zdztfb")
    @ApiOperation(value = "终端状态分布")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "type", value = "1：仓内屏, 2：仓外屏", required = true)
    })
    CommonResult<JSONObject> zdztfb(@RequestParam("code") String code,
                                    @RequestParam(value = "type", defaultValue = "1") String type);

}
