package com.rs.module.pam.api;

import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.ApiConstants;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;


@Component
@FeignClient(name = ApiConstants.NAME, path = "/pam/screen/aqts")
public interface PamAqtsApi {

    @GetMapping("/fxryfb")
    @ApiOperation(value = "风险人员分布")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    CommonResult<JSONObject> fxryfb(@RequestParam("code") String code);

    @GetMapping("/fxryqs")
    @ApiOperation(value = "风险人员趋势")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "type", value = "1 近一周  2 近一月", required = true)
    })
    CommonResult<JSONObject> fxryqs(@RequestParam("code") String code,
                                    @RequestParam(value = "type", defaultValue = "1") String type);

    @GetMapping("/gzry-count")
    @ApiOperation(value = "关注人员-总数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
    })
    CommonResult<JSONObject> gzryCount(@RequestParam("code") String code);

    @GetMapping("/gzry-page")
    @ApiOperation(value = "关注人员-列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true),
            @ApiImplicitParam(name = "bizType", value = "业务类型", required = false)
    })
    CommonResult<Object> gzryPage(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                  @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                  @RequestParam("code") String code,
                                                  @RequestParam(name = "bizType", required = false) String bizType);

    @GetMapping("/wgqs")
    @ApiOperation(value = "违规趋势")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "type", value = "1 近一周  2 近一月", required = true)
    })
    CommonResult<JSONObject> wgqs(@RequestParam("code") String code,
                                  @RequestParam(value = "type", defaultValue = "1") String type);


    @GetMapping("/wggj-page")
    @ApiOperation(value = "违规告警分页列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "type", value = "0 今日  1 近一周  2 近一月", required = true),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true),
            @ApiImplicitParam(name = "wglb", value = "违规类别 1 全部， 2人工巡查， 3智能终端", required = true),
            @ApiImplicitParam(name = "handleStatus", value = "处理类型 1全部 2未处理 3已处理 ", required = true),
    })
    CommonResult<Object> wggjPage(@RequestParam("code") String code,
                                                  @RequestParam(value = "type", defaultValue = "0") String type,
                                                  @RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                  @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                  @RequestParam(name = "wglb", defaultValue = "1") String wglb,
                                                  @RequestParam(name = "handleStatus", defaultValue = "1") String handleStatus);

    @GetMapping("/wggj-count")
    @ApiOperation(value = "违规告警数字统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "type", value = "0 今日  1 近一周  2 近一月", required = true),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true),
            @ApiImplicitParam(name = "wglb", value = "违规类别 1 全部， 2人工巡查， 3智能终端", required = true)
    })
    CommonResult<JSONObject> wggjCount(@RequestParam("code") String code,
                                       @RequestParam(value = "type", defaultValue = "0") String type,
                                       @RequestParam(name = "wglb", defaultValue = "1") String wglb);

}
