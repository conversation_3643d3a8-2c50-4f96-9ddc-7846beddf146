package com.rs.module.pam.api;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.pam.ApiConstants;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collections;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;


@Component
@FeignClient(name = ApiConstants.NAME, path = "/pam/screen/jydt")
public interface PamJydtApi {

    @GetMapping("/jygl")
    @ApiOperation(value = "羁押概览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    CommonResult<JSONObject> jygl(@RequestParam("code") String code);

    @GetMapping("/jyrybhqs")
    @ApiOperation(value = "羁押人员变化趋势")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    CommonResult<JSONObject> jyrybhqs(@RequestParam("code") String code);

    @GetMapping("/gjqryfb")
    @ApiOperation(value = "各监区人员分布")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    CommonResult<List<JSONObject>> gjqryfb(@RequestParam("code") String code);

    @GetMapping("/nldfb")
    @ApiOperation(value = "年龄段分布")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    CommonResult<JSONObject> nldfb(@RequestParam("code") String code);

    @GetMapping("/ssjdfb")
    @ApiOperation(value = "诉讼阶段分布")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    CommonResult<JSONObject> ssjdfb(@RequestParam("code") String code);

    @GetMapping("/ajfbTop5")
    @ApiOperation(value = "案件分布top5")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    CommonResult<List<JSONObject>> ajfbTop5(@RequestParam("code") String code);

    @GetMapping("/jycqtj")
    @ApiOperation(value = "羁押超期统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    CommonResult<JSONObject> jycqtj(@RequestParam("code") String code);

    @GetMapping("/jqdcstj")
    @ApiOperation(value = "近期待出所统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true)
    })
    CommonResult<JSONObject> jqdcstj(@RequestParam("code") String code);

}
