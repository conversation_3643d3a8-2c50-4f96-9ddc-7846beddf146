# RS 项目 Live Templates 配置与使用指南

面向本项目的 Spring Boot + MyBatis-Plus(+ MPJ) + Lombok + Swagger + CommonResult/PageResult 的典型分层（Controller/Service/DAO/VO/DO），本指南提供：
- 可导入 IntelliJ IDEA 的 Live Templates XML（涵盖基础模板 + MapStruct + MPJ）
- 配置、安装与使用说明

文件位置：
- 文档（本文件）：docs/LiveTemplates-MapStruct-MPJ.md
- 可直接导入的 XML：docs/RS-LiveTemplates.xml

---

## 1. 依赖与环境

- Lombok（已广泛使用）
- MyBatis-Plus 与 MPJ（yulichang/mybatis-plus-join）
- MapStruct（若使用 MapStruct 模板）
  - org.mapstruct:mapstruct
  - org.mapstruct:mapstruct-processor
  - IDEA 开启 Annotation Processing:
    - Settings -> Build, Execution, Deployment -> Compiler -> Annotation Processors -> Enable annotation processing

> 提示：不同子模块的 PageResult 构造方法可能略有差异（new 或 of 工厂方法），使用模板后请按模块 API 轻微调整。

---

## 2. 在 IntelliJ 中导入模板 XML

1) 打开 IDEA: Settings -> Editor -> Live Templates
2) 点击右侧的“管理”图标（小齿轮） -> Import…
3) 选择 docs/RS-LiveTemplates.xml 并导入
4) 在 Live Templates 中将分组“RS”勾选启用
5) 确认每个模板勾选：
   - Reformat according to style
   - Shorten FQ names

> 如需手动新增或修改单个模板，可直接在“RS”分组下新建条目并粘贴代码片段。

---

## 3. 模板分组与缩写一览

基础模板（部分）：
- rs-cr：Controller 类骨架（Declaration）
- rs-get：GET 查询接口（Statement）
- rs-post：POST 创建/更新接口（Statement）
- rs-page：分页接口（Statement）
- rs-svc：Service 接口骨架（Declaration）
- rs-svcimpl：Service 实现骨架（Declaration）
- rs-dao-basex：基于 BaseMapperX 的 Mapper（Declaration）
- rs-dao-ibase：基于 IBaseDao 的 Mapper（Declaration）
- rs-qwx：LambdaQueryWrapperX 条件片段（Code）
- rs-bean：Bean 拷贝（单个/列表）（Statement）
- rs-common-ok：返回成功 CommonResult（Statement）
- rs-common-err：返回错误 CommonResult（Statement）
- rs-tran：事务方法装饰（Declaration/方法）
- rs-do：DO 实体骨架（Declaration）
- rs-vo-resp：响应 VO 骨架（Declaration）
- rs-page-req：分页请求 VO（Declaration）
- rs-logrec：操作日志注解（Statement）

MapStruct 模板：
- ms-mapper：Mapper 接口骨架（Declaration）
- ms-map：单对象转换（Statement）
- ms-maplist：列表转换（Statement）
- ms-mappage：分页转换（Statement）
- ms-merge：忽略空值更新（Statement）
- ms-map-annot：字段重命名映射（Mapper 方法注解）

MPJ 模板：
- mpj-2join-list：两表联查-列表（Statement）
- mpj-3join-page：三表联查-分页（Statement）
- mpj-selectas：selectAs 字段映射（Expression/Code）
- mpj-wherex：常用条件拼装（Expression/Code）
- mpj-dao-method：DAO 内封装分页联查（Declaration）

---

## 4. 使用建议与注意事项

- Context 选择：
  - 类骨架/接口声明：Java → Declaration
  - 方法和语句片段：Java → Statement 或 Java → Code（JAVA_CODE）
- 变量占位：使用 $VAR$ 的方式；展开后 Tab 逐个跳转填写
- 导包：启用 Shorten FQ names 后，模板中的全限定名会自动简化并导包
- MapStruct：
  - 使用 @Mapper(componentModel = "spring") 以支持 Spring 注入
  - 忽略空值更新：@BeanMapping(nullValuePropertyMappingStrategy = IGNORE)
  - 字段名不一致使用 @Mapping(source, target)
- MPJ：
  - BaseMapperX 已继承 MPJ 能力，可直接 selectJoinList/selectJoinPage
  - selectAs 推荐 setter 引用形式：.selectAs(Other::getName, Ret::setOtherName)
  - VO/DTO 需有对应 setter 或使用 MapStruct 再次组装

---

## 5. FAQ

1) PageResult 的构造方式不一致？
- 请在模板展开后根据模块统一约定替换为 PageResult.of(list, total) 或构造函数。

2) 模板展开后导包不正确？
- 确保勾选 Shorten FQ names；并执行一次 Optimize Imports。

3) MapStruct 未生效？
- 检查 Annotation Processing 是否开启；确认 mapstruct-processor 已在依赖中；重建工程。

4) MPJ 查询字段映射没生效？
- 确保返回类型中存在 setter；或通过 MapStruct 对查询结果再做对象层次映射。

---

## 6. 版本协作建议

- 团队统一导入 docs/RS-LiveTemplates.xml
- 新增模板时在分支上修改 XML 与本文，并发起合并请求；统一收口使用

---

## 7. 附：覆盖内容

- 基础模板（Controller/Service/DAO/VO/DO/分页/查询/事务/返回/日志）
- MapStruct 模板（Mapper 接口、列表/分页转换、忽略空值更新、字段重命名）
- MPJ 模板（两/三表联查、分页、selectAs、条件拼装、DAO 封装）

```
导入路径：docs/RS-LiveTemplates.xml
```

