<templateSet group="RS">
  <!-- Controller 类骨架 -->
  <template name="rs-cr" value="@Api(tags = &quot;$MODULE$管理&quot;)&#10;@RestController&#10;@RequestMapping(&quot;$BASE_PATH$&quot;)&#10;@Validated&#10;public class $CLASS_NAME$ {&#10;&#10;    @Resource&#10;    private $SERVICE_TYPE$ $SERVICE_VAR$;&#10;&#10;    $END$&#10;}" description="RS Controller skeleton" toReformat="true" toShortenFQNames="true">
    <variable name="MODULE" expression="" defaultValue="&quot;示例&quot;" alwaysStopAt="true" />
    <variable name="BASE_PATH" expression="" defaultValue="&quot;/rs/example&quot;" alwaysStopAt="true" />
    <variable name="CLASS_NAME" expression="className()" defaultValue="&quot;DemoController&quot;" alwaysStopAt="true" />
    <variable name="SERVICE_TYPE" expression="" defaultValue="&quot;DemoService&quot;" alwaysStopAt="true" />
    <variable name="SERVICE_VAR" expression="" defaultValue="&quot;demoService&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="false" />
      <option name="JAVA_DECLARATION" value="true" />
    </context>
  </template>

  <!-- GET 查询接口 -->
  <template name="rs-get" value="@GetMapping(&quot;$PATH$&quot;)&#10;@ApiOperation(value = &quot;$DESC$&quot;)&#10;public CommonResult&lt;$RET$&gt; $METHOD$($PARAMS$) {&#10;    return CommonResult.success($CALL$);&#10;}" description="RS GET endpoint" toReformat="true" toShortenFQNames="true">
    <variable name="PATH" expression="" defaultValue="&quot;/detail/{id}&quot;" alwaysStopAt="true" />
    <variable name="DESC" expression="" defaultValue="&quot;获取详情&quot;" alwaysStopAt="true" />
    <variable name="RET" expression="" defaultValue="&quot;Object&quot;" alwaysStopAt="true" />
    <variable name="METHOD" expression="" defaultValue="&quot;getDetail&quot;" alwaysStopAt="true" />
    <variable name="PARAMS" expression="" defaultValue="&quot;@PathVariable(\&quot;id\&quot;) String id&quot;" alwaysStopAt="true" />
    <variable name="CALL" expression="" defaultValue="&quot;service.getById(id)&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="true" />
      <option name="JAVA_DECLARATION" value="false" />
    </context>
  </template>

  <!-- POST 创建/更新接口 -->
  <template name="rs-post" value="@PostMapping(&quot;$PATH$&quot;)&#10;@ApiOperation(value = &quot;$DESC$&quot;)&#10;public CommonResult&lt;Boolean&gt; $METHOD$(@Valid @RequestBody $REQ$ reqVO) {&#10;    $SERVICE_VAR$.$SERVICE_METHOD$(reqVO);&#10;    return CommonResult.success(true);&#10;}" description="RS POST endpoint" toReformat="true" toShortenFQNames="true">
    <variable name="PATH" expression="" defaultValue="&quot;/create&quot;" alwaysStopAt="true" />
    <variable name="DESC" expression="" defaultValue="&quot;新增&quot;" alwaysStopAt="true" />
    <variable name="METHOD" expression="" defaultValue="&quot;create&quot;" alwaysStopAt="true" />
    <variable name="REQ" expression="" defaultValue="&quot;CreateReqVO&quot;" alwaysStopAt="true" />
    <variable name="SERVICE_VAR" expression="" defaultValue="&quot;service&quot;" alwaysStopAt="true" />
    <variable name="SERVICE_METHOD" expression="" defaultValue="&quot;create&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="true" />
      <option name="JAVA_DECLARATION" value="false" />
    </context>
  </template>

  <!-- 分页接口 -->
  <template name="rs-page" value="@PostMapping(&quot;/page&quot;)&#10;@ApiOperation(&quot;分页查询&quot;)&#10;public CommonResult&lt;PageResult&lt;$VO$&gt;&gt; page(@Valid @RequestBody $PAGE_REQ$ reqVO) {&#10;    return CommonResult.success($SERVICE_VAR$.page(reqVO));&#10;}" description="RS page endpoint" toReformat="true" toShortenFQNames="true">
    <variable name="VO" expression="" defaultValue="&quot;RespVO&quot;" alwaysStopAt="true" />
    <variable name="PAGE_REQ" expression="" defaultValue="&quot;PageReqVO&quot;" alwaysStopAt="true" />
    <variable name="SERVICE_VAR" expression="" defaultValue="&quot;service&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="true" />
      <option name="JAVA_DECLARATION" value="false" />
    </context>
  </template>

  <!-- Service 接口骨架 -->
  <template name="rs-svc" value="public interface $SERVICE$ {&#10;&#10;    PageResult&lt;$VO$&gt; page($PAGE_REQ$ reqVO);&#10;&#10;    String create($CREATE_REQ$ reqVO);&#10;&#10;    void update($UPDATE_REQ$ reqVO);&#10;&#10;    void delete(String id);&#10;}" description="RS service interface" toReformat="true" toShortenFQNames="true">
    <variable name="SERVICE" expression="" defaultValue="&quot;DemoService&quot;" alwaysStopAt="true" />
    <variable name="VO" expression="" defaultValue="&quot;RespVO&quot;" alwaysStopAt="true" />
    <variable name="PAGE_REQ" expression="" defaultValue="&quot;PageReqVO&quot;" alwaysStopAt="true" />
    <variable name="CREATE_REQ" expression="" defaultValue="&quot;CreateReqVO&quot;" alwaysStopAt="true" />
    <variable name="UPDATE_REQ" expression="" defaultValue="&quot;UpdateReqVO&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="false" />
      <option name="JAVA_DECLARATION" value="true" />
    </context>
  </template>

  <!-- Service 实现骨架 -->
  <template name="rs-svcimpl" value="@Service&#10;public class $SERVICE_IMPL$ implements $SERVICE$ {&#10;&#10;    @Resource&#10;    private $DAO_TYPE$ $DAO_VAR$;&#10;&#10;    @Override&#10;    public PageResult&lt;$VO$&gt; page($PAGE_REQ$ reqVO) {&#10;        PageResult&lt;$DO$&gt; page = $DAO_VAR$.selectPage(reqVO, new LambdaQueryWrapperX&lt;$DO$&gt;()&#10;                //.likeIfPresent($DO$::getName, reqVO.getName())&#10;                //.eqIfPresent($DO$::getType, reqVO.getType())&#10;                //.betweenIfPresent($DO$::getAddTime, reqVO.getBeginTime(), reqVO.getEndTime())&#10;                .orderByDesc($DO$::getAddTime));&#10;        java.util.List&lt;$VO$&gt; list = com.rs.framework.common.util.object.BeanUtils.toBean(page.getList(), $VO$.class);&#10;        return new com.rs.framework.common.pojo.PageResult&lt;&gt;(list, page.getTotal());&#10;    }&#10;&#10;    @Override&#10;    @Transactional(rollbackFor = Exception.class)&#10;    public String create($CREATE_REQ$ reqVO) {&#10;        $DO$ entity = com.rs.framework.common.util.object.BeanUtils.toBean(reqVO, $DO$.class);&#10;        $DAO_VAR$.insert(entity);&#10;        return entity.getId();&#10;    }&#10;&#10;    @Override&#10;    @Transactional(rollbackFor = Exception.class)&#10;    public void update($UPDATE_REQ$ reqVO) {&#10;        $DO$ entity = com.rs.framework.common.util.object.BeanUtils.toBean(reqVO, $DO$.class);&#10;        $DAO_VAR$.updateById(entity);&#10;    }&#10;&#10;    @Override&#10;    @Transactional(rollbackFor = Exception.class)&#10;    public void delete(String id) {&#10;        $DAO_VAR$.deleteById(id);&#10;    }&#10;}" description="RS service impl" toReformat="true" toShortenFQNames="true">
    <variable name="SERVICE_IMPL" expression="" defaultValue="&quot;DemoServiceImpl&quot;" alwaysStopAt="true" />
    <variable name="SERVICE" expression="" defaultValue="&quot;DemoService&quot;" alwaysStopAt="true" />
    <variable name="DAO_TYPE" expression="" defaultValue="&quot;DemoDao&quot;" alwaysStopAt="true" />
    <variable name="DAO_VAR" expression="" defaultValue="&quot;demoDao&quot;" alwaysStopAt="true" />
    <variable name="VO" expression="" defaultValue="&quot;RespVO&quot;" alwaysStopAt="true" />
    <variable name="PAGE_REQ" expression="" defaultValue="&quot;PageReqVO&quot;" alwaysStopAt="true" />
    <variable name="DO" expression="" defaultValue="&quot;DemoDO&quot;" alwaysStopAt="true" />
    <variable name="CREATE_REQ" expression="" defaultValue="&quot;CreateReqVO&quot;" alwaysStopAt="true" />
    <variable name="UPDATE_REQ" expression="" defaultValue="&quot;UpdateReqVO&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="false" />
      <option name="JAVA_DECLARATION" value="true" />
    </context>
  </template>

  <!-- DAO 基于 BaseMapperX -->
  <template name="rs-dao-basex" value="@Mapper&#10;public interface $DAO$ extends com.rs.framework.mybatis.core.mapper.BaseMapperX&lt;$DO$&gt; {&#10;&#10;    default com.rs.framework.common.pojo.PageResult&lt;$DO$&gt; selectPage($PAGE_REQ$ reqVO) {&#10;        return this.selectPage((com.rs.framework.common.pojo.PageParam) reqVO,&#10;            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper&lt;$DO$&gt;()&#10;                //.eq($DO$::getIsDel, false)&#10;                //.like(cn.hutool.core.util.StrUtil.isNotBlank(reqVO.getName()), $DO$::getName, reqVO.getName())&#10;                .orderByDesc($DO$::getAddTime));&#10;    }&#10;}" description="RS BaseMapperX DAO" toReformat="true" toShortenFQNames="true">
    <variable name="DAO" expression="" defaultValue="&quot;DemoDao&quot;" alwaysStopAt="true" />
    <variable name="DO" expression="" defaultValue="&quot;DemoDO&quot;" alwaysStopAt="true" />
    <variable name="PAGE_REQ" expression="" defaultValue="&quot;PageReqVO&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="false" />
      <option name="JAVA_DECLARATION" value="true" />
    </context>
  </template>

  <!-- DAO 基于 IBaseDao -->
  <template name="rs-dao-ibase" value="@Mapper&#10;public interface $DAO$ extends com.bsp.common.orm.mybatis.mapper.IBaseDao&lt;$DO$&gt; {&#10;    // 自行补充分页/条件方法&#10;}" description="RS IBaseDao DAO" toReformat="true" toShortenFQNames="true">
    <variable name="DAO" expression="" defaultValue="&quot;DemoDao&quot;" alwaysStopAt="true" />
    <variable name="DO" expression="" defaultValue="&quot;DemoDO&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="false" />
      <option name="JAVA_DECLARATION" value="true" />
    </context>
  </template>

  <!-- LambdaQueryWrapperX 条件片段 -->
  <template name="rs-qwx" value="new com.rs.framework.mybatis.core.query.LambdaQueryWrapperX&lt;$DO$&gt;()&#10;    .eqIfPresent($DO$::$EQ_FIELD$, $EQ_VAL$)&#10;    .likeIfPresent($DO$::getName, $NAME_VAL$)&#10;    .betweenIfPresent($DO$::getAddTime, $BEGIN_TIME$, $END_TIME$)&#10;    .orderByDesc($DO$::getAddTime)$END$" description="RS LambdaQueryWrapperX snippet" toReformat="true" toShortenFQNames="true">
    <variable name="DO" expression="" defaultValue="&quot;DemoDO&quot;" alwaysStopAt="true" />
    <variable name="EQ_FIELD" expression="" defaultValue="&quot;getType&quot;" alwaysStopAt="true" />
    <variable name="EQ_VAL" expression="" defaultValue="&quot;type&quot;" alwaysStopAt="true" />
    <variable name="NAME_VAL" expression="" defaultValue="&quot;name&quot;" alwaysStopAt="true" />
    <variable name="BEGIN_TIME" expression="" defaultValue="&quot;beginTime&quot;" alwaysStopAt="true" />
    <variable name="END_TIME" expression="" defaultValue="&quot;endTime&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="true" />
      <option name="JAVA_DECLARATION" value="false" />
    </context>
  </template>

  <!-- BeanUtils 拷贝 -->
  <template name="rs-bean" value="$TO$ $TO_VAR$ = com.rs.framework.common.util.object.BeanUtils.toBean($FROM_VAR$, $TO$.class);&#10;java.util.List&lt;$TO$&gt; $LIST_VAR$ = com.rs.framework.common.util.object.BeanUtils.toBean($FROM_LIST$, $TO$.class);&#10;$END$" description="RS Bean copy" toReformat="true" toShortenFQNames="true">
    <variable name="TO" expression="" defaultValue="&quot;RespVO&quot;" alwaysStopAt="true" />
    <variable name="TO_VAR" expression="" defaultValue="&quot;vo&quot;" alwaysStopAt="true" />
    <variable name="FROM_VAR" expression="" defaultValue="&quot;entity&quot;" alwaysStopAt="true" />
    <variable name="LIST_VAR" expression="" defaultValue="&quot;voList&quot;" alwaysStopAt="true" />
    <variable name="FROM_LIST" expression="" defaultValue="&quot;entityList&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="true" />
      <option name="JAVA_DECLARATION" value="false" />
    </context>
  </template>

  <!-- 返回成功/错误 -->
  <template name="rs-common-ok" value="return com.rs.framework.common.pojo.CommonResult.success($DATA$);" description="RS CommonResult success" toReformat="true" toShortenFQNames="true">
    <variable name="DATA" expression="" defaultValue="&quot;data&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="true" />
      <option name="JAVA_DECLARATION" value="false" />
    </context>
  </template>
  <template name="rs-common-err" value="return com.rs.framework.common.pojo.CommonResult.error(&quot;$MSG$&quot;);" description="RS CommonResult error" toReformat="true" toShortenFQNames="true">
    <variable name="MSG" expression="" defaultValue="&quot;失败&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="true" />
      <option name="JAVA_DECLARATION" value="false" />
    </context>
  </template>

  <!-- 事务方法装饰 -->
  <template name="rs-tran" value="@Override&#10;@org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)&#10;public $RET$ $METHOD$($PARAMS$) {&#10;    $END$&#10;}" description="RS transactional method" toReformat="true" toShortenFQNames="true">
    <variable name="RET" expression="" defaultValue="&quot;void&quot;" alwaysStopAt="true" />
    <variable name="METHOD" expression="" defaultValue="&quot;methodName&quot;" alwaysStopAt="true" />
    <variable name="PARAMS" expression="" defaultValue="&quot;&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="false" />
      <option name="JAVA_DECLARATION" value="true" />
    </context>
  </template>

  <!-- DO 实体骨架 -->
  <template name="rs-do" value="@Data&#10;@TableName(&quot;$TABLE$&quot;)&#10;public class $DO$ extends com.rs.framework.mybatis.entity.BaseDO {&#10;&#10;    @TableId(type = IdType.ASSIGN_UUID)&#10;    private String id;&#10;&#10;    // TODO: 其它字段...&#10;}" description="RS DO skeleton" toReformat="true" toShortenFQNames="true">
    <variable name="TABLE" expression="" defaultValue="&quot;T_DEMO&quot;" alwaysStopAt="true" />
    <variable name="DO" expression="" defaultValue="&quot;DemoDO&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="false" />
      <option name="JAVA_DECLARATION" value="true" />
    </context>
  </template>

  <!-- VO 响应骨架 -->
  <template name="rs-vo-resp" value="@Data&#10;@ApiModel(&quot;$DESC$响应&quot;)&#10;public class $RESP$ {&#10;    // @ApiModelProperty(&quot;字段说明&quot;)&#10;    // private String name;&#10;}" description="RS RespVO skeleton" toReformat="true" toShortenFQNames="true">
    <variable name="DESC" expression="" defaultValue="&quot;示例&quot;" alwaysStopAt="true" />
    <variable name="RESP" expression="" defaultValue="&quot;RespVO&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="false" />
      <option name="JAVA_DECLARATION" value="true" />
    </context>
  </template>

  <!-- 分页请求 VO -->
  <template name="rs-page-req" value="@Data&#10;@ApiModel(&quot;$DESC$分页查询&quot;)&#10;public class $PAGE_REQ$ extends com.rs.framework.common.pojo.PageParam {&#10;&#10;    // @ApiModelProperty(&quot;名称查询&quot;)&#10;    // private String name;&#10;}" description="RS PageReqVO skeleton" toReformat="true" toShortenFQNames="true">
    <variable name="DESC" expression="" defaultValue="&quot;示例&quot;" alwaysStopAt="true" />
    <variable name="PAGE_REQ" expression="" defaultValue="&quot;PageReqVO&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="false" />
      <option name="JAVA_DECLARATION" value="true" />
    </context>
  </template>

  <!-- 操作日志注解 -->
  <template name="rs-logrec" value="@LogRecordAnnotation(bizModule = &quot;$MODULE$&quot;, operateType = LogOperateType.$OP$, title = &quot;$TITLE$&quot;, bizNo = &quot;{{#$BIZ_NO$}}&quot;, success = &quot;$SUCCESS$&quot;, fail = &quot;错误信息：{{#_ret[msg]}}&quot;, extraInfo = &quot;{TO_JSON{#$EXTRA$}}&quot;)" description="RS log record annotation" toReformat="true" toShortenFQNames="true">
    <variable name="MODULE" expression="" defaultValue="&quot;module:op&quot;" alwaysStopAt="true" />
    <variable name="OP" expression="" defaultValue="&quot;QUERY&quot;" alwaysStopAt="true" />
    <variable name="TITLE" expression="" defaultValue="&quot;操作标题&quot;" alwaysStopAt="true" />
    <variable name="BIZ_NO" expression="" defaultValue="&quot;id&quot;" alwaysStopAt="true" />
    <variable name="SUCCESS" expression="" defaultValue="&quot;操作成功&quot;" alwaysStopAt="true" />
    <variable name="EXTRA" expression="" defaultValue="&quot;dto&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="true" />
      <option name="JAVA_DECLARATION" value="false" />
    </context>
  </template>

  <!-- MapStruct Mapper 接口骨架 -->
  <template name="ms-mapper" value="@Mapper(componentModel = &quot;spring&quot;, unmappedTargetPolicy = ReportingPolicy.IGNORE)&#10;public interface $MAPPER$ {&#10;&#10;    $VO$ toVO($DO$ bean);&#10;&#10;    $DO$ toDO($CREATE_REQ$ req);&#10;&#10;    $DO$ toDO($UPDATE_REQ$ req);&#10;&#10;    java.util.List&lt;$VO$&gt; toVOList(java.util.List&lt;$DO$&gt; list);&#10;&#10;    default com.rs.framework.common.pojo.PageResult&lt;$VO$&gt; toVOPage(com.rs.framework.common.pojo.PageResult&lt;$DO$&gt; page) {&#10;        java.util.List&lt;$VO$&gt; list = toVOList(page.getList());&#10;        return new com.rs.framework.common.pojo.PageResult&lt;&gt;(list, page.getTotal());&#10;    }&#10;&#10;    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)&#10;    void updateFromReq($UPDATE_REQ$ req, @MappingTarget $DO$ target);&#10;}" description="MapStruct mapper interface" toReformat="true" toShortenFQNames="true">
    <variable name="MAPPER" expression="" defaultValue="&quot;DemoConvert&quot;" alwaysStopAt="true" />
    <variable name="VO" expression="" defaultValue="&quot;RespVO&quot;" alwaysStopAt="true" />
    <variable name="DO" expression="" defaultValue="&quot;DemoDO&quot;" alwaysStopAt="true" />
    <variable name="CREATE_REQ" expression="" defaultValue="&quot;CreateReqVO&quot;" alwaysStopAt="true" />
    <variable name="UPDATE_REQ" expression="" defaultValue="&quot;UpdateReqVO&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="false" />
      <option name="JAVA_DECLARATION" value="true" />
    </context>
  </template>

  <!-- MapStruct 单对象/列表/分页/合并 -->
  <template name="ms-map" value="$VO$ $VO_VAR$ = $MAPPER_VAR$.toVO($DO_VAR$);" description="MapStruct toVO" toReformat="true" toShortenFQNames="true">
    <variable name="VO" expression="" defaultValue="&quot;RespVO&quot;" alwaysStopAt="true" />
    <variable name="VO_VAR" expression="" defaultValue="&quot;vo&quot;" alwaysStopAt="true" />
    <variable name="MAPPER_VAR" expression="" defaultValue="&quot;convert&quot;" alwaysStopAt="true" />
    <variable name="DO_VAR" expression="" defaultValue="&quot;entity&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="true" />
      <option name="JAVA_DECLARATION" value="false" />
    </context>
  </template>
  <template name="ms-maplist" value="java.util.List&lt;$VO$&gt; $VO_LIST_VAR$ = $MAPPER_VAR$.toVOList($DO_LIST_VAR$);" description="MapStruct toVO list" toReformat="true" toShortenFQNames="true">
    <variable name="VO" expression="" defaultValue="&quot;RespVO&quot;" alwaysStopAt="true" />
    <variable name="VO_LIST_VAR" expression="" defaultValue="&quot;voList&quot;" alwaysStopAt="true" />
    <variable name="MAPPER_VAR" expression="" defaultValue="&quot;convert&quot;" alwaysStopAt="true" />
    <variable name="DO_LIST_VAR" expression="" defaultValue="&quot;entityList&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="true" />
      <option name="JAVA_DECLARATION" value="false" />
    </context>
  </template>
  <template name="ms-mappage" value="com.rs.framework.common.pojo.PageResult&lt;$VO$&gt; $PAGE_VO_VAR$ = $MAPPER_VAR$.toVOPage($PAGE_DO_VAR$);" description="MapStruct toVO page" toReformat="true" toShortenFQNames="true">
    <variable name="VO" expression="" defaultValue="&quot;RespVO&quot;" alwaysStopAt="true" />
    <variable name="PAGE_VO_VAR" expression="" defaultValue="&quot;pageVO&quot;" alwaysStopAt="true" />
    <variable name="MAPPER_VAR" expression="" defaultValue="&quot;convert&quot;" alwaysStopAt="true" />
    <variable name="PAGE_DO_VAR" expression="" defaultValue="&quot;pageDO&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="true" />
      <option name="JAVA_DECLARATION" value="false" />
    </context>
  </template>
  <template name="ms-merge" value="$MAPPER_VAR$.updateFromReq($REQ_VAR$, $DO_VAR$);" description="MapStruct merge ignore null" toReformat="true" toShortenFQNames="true">
    <variable name="MAPPER_VAR" expression="" defaultValue="&quot;convert&quot;" alwaysStopAt="true" />
    <variable name="REQ_VAR" expression="" defaultValue="&quot;reqVO&quot;" alwaysStopAt="true" />
    <variable name="DO_VAR" expression="" defaultValue="&quot;entity&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="true" />
      <option name="JAVA_DECLARATION" value="false" />
    </context>
  </template>
  <template name="ms-map-annot" value="@Mapping(source = &quot;$SRC_FIELD$&quot;, target = &quot;$TGT_FIELD$&quot;)" description="MapStruct @Mapping" toReformat="true" toShortenFQNames="true">
    <variable name="SRC_FIELD" expression="" defaultValue="&quot;srcName&quot;" alwaysStopAt="true" />
    <variable name="TGT_FIELD" expression="" defaultValue="&quot;targetName&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="false" />
      <option name="JAVA_DECLARATION" value="true" />
    </context>
  </template>

  <!-- MPJ 两表/三表/分页/映射/条件 -->
  <template name="mpj-2join-list" value="java.util.List&lt;$RET$&gt; list = $DAO_VAR$.selectJoinList($RET$.class,&#10;    new com.github.yulichang.wrapper.MPJLambdaWrapper&lt;$MAIN$&gt;()&#10;        .selectAll($MAIN$.class)&#10;        .selectAs($SUB$.class, $SUB$::$SUB_FIELD$, $RET$::$RET_FIELD_SETTER$)&#10;        .leftJoin($SUB$.class, $SUB$::getId, $MAIN$::getSubId)&#10;        //.eq($MAIN$::getIsDel, false)&#10;        //.like(cn.hutool.core.util.StrUtil.isNotBlank($NAME_VAR$), $MAIN$::getName, $NAME_VAR$)&#10;        .orderByDesc($MAIN$::getAddTime)&#10;);" description="MPJ 2-table join list" toReformat="true" toShortenFQNames="true">
    <variable name="RET" expression="" defaultValue="&quot;RespVO&quot;" alwaysStopAt="true" />
    <variable name="DAO_VAR" expression="" defaultValue="&quot;demoDao&quot;" alwaysStopAt="true" />
    <variable name="MAIN" expression="" defaultValue="&quot;MainDO&quot;" alwaysStopAt="true" />
    <variable name="SUB" expression="" defaultValue="&quot;SubDO&quot;" alwaysStopAt="true" />
    <variable name="SUB_FIELD" expression="" defaultValue="&quot;getSubName&quot;" alwaysStopAt="true" />
    <variable name="RET_FIELD_SETTER" expression="" defaultValue="&quot;setSubName&quot;" alwaysStopAt="true" />
    <variable name="NAME_VAR" expression="" defaultValue="&quot;name&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="true" />
      <option name="JAVA_DECLARATION" value="false" />
    </context>
  </template>
  <template name="mpj-3join-page" value="com.baomidou.mybatisplus.core.metadata.IPage&lt;$RET$&gt; page = $DAO_VAR$.selectJoinPage(&#10;    new com.baomidou.mybatisplus.extension.plugins.pagination.Page&lt;&gt;($PAGE_NO$, $PAGE_SIZE$),&#10;    $RET$.class,&#10;    new com.github.yulichang.wrapper.MPJLambdaWrapper&lt;$MAIN$&gt;()&#10;        .selectAll($MAIN$.class)&#10;        .selectAs($SUB1$.class, $SUB1$::$SUB1_FIELD$, $RET$::$RET_FIELD1_SETTER$)&#10;        .selectAs($SUB2$.class, $SUB2$::$SUB2_FIELD$, $RET$::$RET_FIELD2_SETTER$)&#10;        .leftJoin($SUB1$.class, $SUB1$::getId, $MAIN$::getSub1Id)&#10;        .leftJoin($SUB2$.class, $SUB2$::getId, $MAIN$::getSub2Id)&#10;        //.eqIfPresent($MAIN$::getType, $REQ$::getType)&#10;        //.betweenIfPresent($MAIN$::getAddTime, $REQ$::getBeginTime, $REQ$::getEndTime)&#10;        .orderByDesc($MAIN$::getAddTime)&#10;);" description="MPJ 3-table join page" toReformat="true" toShortenFQNames="true">
    <variable name="RET" expression="" defaultValue="&quot;RespVO&quot;" alwaysStopAt="true" />
    <variable name="DAO_VAR" expression="" defaultValue="&quot;demoDao&quot;" alwaysStopAt="true" />
    <variable name="MAIN" expression="" defaultValue="&quot;MainDO&quot;" alwaysStopAt="true" />
    <variable name="SUB1" expression="" defaultValue="&quot;Sub1DO&quot;" alwaysStopAt="true" />
    <variable name="SUB1_FIELD" expression="" defaultValue="&quot;getDeptName&quot;" alwaysStopAt="true" />
    <variable name="RET_FIELD1_SETTER" expression="" defaultValue="&quot;setDeptName&quot;" alwaysStopAt="true" />
    <variable name="SUB2" expression="" defaultValue="&quot;Sub2DO&quot;" alwaysStopAt="true" />
    <variable name="SUB2_FIELD" expression="" defaultValue="&quot;getOrgName&quot;" alwaysStopAt="true" />
    <variable name="RET_FIELD2_SETTER" expression="" defaultValue="&quot;setOrgName&quot;" alwaysStopAt="true" />
    <variable name="PAGE_NO" expression="" defaultValue="&quot;1&quot;" alwaysStopAt="true" />
    <variable name="PAGE_SIZE" expression="" defaultValue="&quot;10&quot;" alwaysStopAt="true" />
    <variable name="REQ" expression="" defaultValue="&quot;reqVO&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="true" />
      <option name="JAVA_DECLARATION" value="false" />
    </context>
  </template>
  <template name="mpj-selectas" value=".selectAs($SRC$.class, $SRC$::$SRC_FIELD$, $RET$::$RET_FIELD_SETTER$)" description="MPJ selectAs mapping" toReformat="true" toShortenFQNames="true">
    <variable name="SRC" expression="" defaultValue="&quot;SubDO&quot;" alwaysStopAt="true" />
    <variable name="SRC_FIELD" expression="" defaultValue="&quot;getName&quot;" alwaysStopAt="true" />
    <variable name="RET" expression="" defaultValue="&quot;RespVO&quot;" alwaysStopAt="true" />
    <variable name="RET_FIELD_SETTER" expression="" defaultValue="&quot;setSubName&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="true" />
      <option name="JAVA_DECLARATION" value="false" />
    </context>
  </template>
  <template name="mpj-wherex" value=".eq($MAIN$::getIsDel, false)&#10;.like(cn.hutool.core.util.StrUtil.isNotBlank($NAME_VAR$), $MAIN$::getName, $NAME_VAR$)&#10;.between($TIME_BEGIN$ != null &amp;&amp; $TIME_END$ != null, $MAIN$::getAddTime, $TIME_BEGIN$, $TIME_END$)" description="MPJ where snippet" toReformat="true" toShortenFQNames="true">
    <variable name="MAIN" expression="" defaultValue="&quot;MainDO&quot;" alwaysStopAt="true" />
    <variable name="NAME_VAR" expression="" defaultValue="&quot;name&quot;" alwaysStopAt="true" />
    <variable name="TIME_BEGIN" expression="" defaultValue="&quot;beginTime&quot;" alwaysStopAt="true" />
    <variable name="TIME_END" expression="" defaultValue="&quot;endTime&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="true" />
      <option name="JAVA_DECLARATION" value="false" />
    </context>
  </template>
  <template name="mpj-dao-method" value="default com.rs.framework.common.pojo.PageResult&lt;$RET$&gt; selectJoinPage($REQ$ req) {&#10;    com.baomidou.mybatisplus.core.metadata.IPage&lt;$RET$&gt; page = this.selectJoinPage(&#10;        new com.baomidou.mybatisplus.extension.plugins.pagination.Page&lt;&gt;(req.getPageNo(), req.getPageSize()),&#10;        $RET$.class,&#10;        new com.github.yulichang.wrapper.MPJLambdaWrapper&lt;$MAIN$&gt;()&#10;            .selectAll($MAIN$.class)&#10;            //.selectAs(...) 映射&#10;            //.leftJoin(...) 关联&#10;            .orderByDesc($MAIN$::getAddTime)&#10;    );&#10;    return new com.rs.framework.common.pojo.PageResult&lt;&gt;(page.getRecords(), page.getTotal());&#10;}" description="MPJ DAO join page method" toReformat="true" toShortenFQNames="true">
    <variable name="RET" expression="" defaultValue="&quot;RespVO&quot;" alwaysStopAt="true" />
    <variable name="REQ" expression="" defaultValue="&quot;PageReqVO&quot;" alwaysStopAt="true" />
    <variable name="MAIN" expression="" defaultValue="&quot;MainDO&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
      <option name="JAVA_STATEMENT" value="false" />
      <option name="JAVA_DECLARATION" value="true" />
    </context>
  </template>
</templateSet>

