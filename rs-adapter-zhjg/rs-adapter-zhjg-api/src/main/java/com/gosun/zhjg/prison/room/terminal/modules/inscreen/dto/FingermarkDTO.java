package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 指纹录入参数
 * <AUTHOR>
 * @Date 2025/8/20 15:08
 */
@Data
public class FingermarkDTO {

    @ApiModelProperty("是否强制通过")
    private String forceFlag;
    @ApiModelProperty("缺指情况(1:缺失,3:受伤)")
    private String qsqkdm;
    private Map<String, Object> transMap;
    @ApiModelProperty("图象质量")
    private String txzl;
    @ApiModelProperty("图片url")
    private String url;
    @ApiModelProperty("指纹代码")
    private String zwdm;
}
