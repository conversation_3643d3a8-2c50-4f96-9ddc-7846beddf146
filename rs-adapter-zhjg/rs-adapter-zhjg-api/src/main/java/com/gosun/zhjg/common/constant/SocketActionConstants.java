package com.gosun.zhjg.common.constant;

/**
 * <AUTHOR>
 */
public interface SocketActionConstants {

    /**
     * 通过图片路径录入人脸
     */
    String enrollFaceByImg = "enrollFaceByImg";

    /**
     * 通过图片路径录入指纹
     */
    String enrollFingermarkByImg = "enrollFingermarkByImg";

    /**
     * 获取储存的了哪些人员的人脸照片
     */
    String getFaceCodesList = "getFaceCodesList";
    String getStoreFaceCode = "getStoreFaceCode";
    /**
     * 获取储存的人员的人脸base64照片
     */
    String getStoreFaceBase64Img = "getStoreFaceBase64Img";
    /**
     * 清除录入的人脸
     */
    String cleanStoreFace = "cleanStoreFace";
    /**
     * 清除录入的指纹
     */
    String cleanStoreFingermark = "cleanStoreFingermark";
    /**
     * 清除录入的人脸。根据人员类型 police、prisoner
     */
    String cleanStoreFaceByType = "cleanStoreFaceByType";

    /**
     * web 主页刷新
     */
    String webReload = "web_homePageReload";

    /**
     * 语音广播
     */
    String VoiceBroadcast = "VoiceBroadcast";
    
    /**
     * web 语音广播
     */
    String WebVoiceBroadcast = "web_VoiceBroadcast";

    String officeScreenReload = "OfficeScreenReload";

    /**
     * 获取温度
     */
    String getTemperature = "getTemperature";

    /**
     * 发起视频通话
     */
    String startVideoCall = "startVideoCall";

    /**
     * 关闭视频通话
     */
    String closeVideoCall = "closeVideoCall";

    /**
     * 视频会议登录
     */
    String android_rtcLogin = "rtcLogin";

    /**
     * 监室值日发生变化，推送内屏，做签到提醒
     * 包含，班次变化、语音播报配置配置变化、排班发生变化
     */
    String web_terminalRoomDayDutyChange = "web_terminalRoomDayDutyChange";

    /**
     * 终端配置变动
     */
    String web_TerminalSettingsChange = "web_TerminalSettingsChange";

    /**
     * 所情管理-通知管教处理
     */
    String PRISON_EVENT_DISPOSE_WARDER = "prison_event_dispose_warder";

    /**
     * 所情管理-通知医生处理
     */
    String PRISON_EVENT_DISPOSE_DOCTOR = "prison_event_dispose_doctor";

    /**
     * 所情管理-通知所领导审批处理
     */
    String PRISON_EVENT_LEADER_AUDIT = "prison_event_leader_audit";


    /**
     * 所情管理-通知所领导驳回管教处理
     */
    String PRISON_EVENT_REJECT_WARDER = "prison_event_reject_warder";

    /**
     * 所情管理-管教处置变化推送
     */
    String PRISON_EVENT_WARDER_REFRESH = "prison_event_warder_refresh";


    /**
     * action 全局消息提醒
     */
    String globalMessage = "global_message";

    /**
     * 检测到其他民警对值班班次进行了更改，将刷新界面”，此时出现一个按钮“确认
     */
    String web_dutyShiftChange = "web_dutyShiftChange";

    /**
     * 监室值班发生变化，推送内屏，做签到提醒
     * 包含，班次变化、语音播报配置配置变化、排班发生变化
     */
    String web_prisonRoomDutyChange = "web_prisonRoomDutyChange";

    /**
     * 对讲分机和内外屏呼叫状态
     */
    String terminalCallState = "terminalCallState";
    /**
     * 枚举类，使用 PushMessageFormTargetEnum.web.name()
     *
     * <AUTHOR>
     */
    public static enum PushMessageTargetEnum {

        android, web,external;
    }

    public static enum PushMessageTerminalEnum {

        CNP, CWP, PLATFORM,EXTERNAL
        ,FWF // 防误放
        ,TDAJP // 筒道安检屏
        ,IHCS // 医疗系统
        ;
    }
    //EXTERNAL 对外服务终端

    ///// 数据变动
    ///// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    /*
     * 监室住管教变动
     */
    String changePrisonroomwarder = "web_change_prisonroomwarder";

    /**
     * 结束点名
     */
    String endRepresent = "end_represent";


    /**
     * 一日生活
     */
    String workArrangeNotice = "work_arrange_notice";

    /**
     * 巡诊预约提醒
     */
    String bookConfigNotice = "book_config_notice";

    /**
     * 消息数据
     */
    String noticeUpdate = "notice_update";


    /**
     * 语音播报
     */
    String swSendMsg = "sw_send_msg";

    /**
     * 点名任务增量变动
     */
    String representTask = "represent_task";

    /**
     * 点名人员变动
     */
    String representChange = "represent_change";

    /**
     * 其他访客-审批结果
     */

    String visitorOtherApprove="visitor_other_approve";

    /**
     * 终端设备状态变更刷新页面
     */
    String refreshApp="refreshApp";

    /***
     * 带出 仓内屏
     */
    String out_room_terminal = "out_room_terminal";

    /**
     * 心理测评计划到达
     */
    String PsyEvaluationPlanArrive="psy_evaluation_plan_arrive";

    /**
     * 发饭任务到达
     */
    String cook_delivery_task = "web_cook_delivery_task";

    /**
     * 发饭任务结束
     */
    String cook_delivery_task_end = "web_cook_delivery_task_end";

}