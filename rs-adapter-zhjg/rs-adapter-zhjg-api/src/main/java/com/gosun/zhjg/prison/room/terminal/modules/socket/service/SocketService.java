package com.gosun.zhjg.prison.room.terminal.modules.socket.service;

import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.common.exception.BaseException;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.CustomPushMessageConditionDTO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.feign.CnpSocketFeign;
import com.gosun.zhjg.prison.room.terminal.modules.socket.feign.SocketPushFeign;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.PushMessageAckVO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.SocketRelationMapVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 与仓内屏交互Socket长连接
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SocketService {

    @Autowired
    private CnpSocketFeign cnpSocketFeign;

    @Autowired
    private SocketPushFeign socketClient;


    /**
     * 获取socket信息
     */
    public SocketRelationMapVO getSocketInfo() {
        SocketRelationMapVO relationMapVO = socketClient.getSocketInfo().getData();
        if (relationMapVO == null) {
            throw new BaseException("获取socket信息异常");
        }
        return relationMapVO;
    }

    /**
     * 推送消息给序列号所在客户端等待响应
     */
    public List<PushMessageAckVO> pushMessageToSerialNumberWaitReplyMultiple(PushMessageForm form) {
        List<String> sessionIds = getSessionBySerialNumber(form.getSerialNumbers());
        form.setSerialNumbers(null);
        form.setSessionIds(sessionIds);
        return this.pushMessageToClientWaitReplyMultiple(form);
    }

    /**
     * 推送消息给序列号，不等待响应
     */
    public void pushMessageToSerialNumber(PushMessageForm form) {
        List<String> sessionIds = getSessionBySerialNumber(form.getSerialNumbers());
        form.setSerialNumbers(null);
        form.setSessionIds(sessionIds);
        this.pushMessageToClient(form);
    }

    /**
     * 推送消息给某监所下所有用户 -平台，不等待响应
     * 支持黑名单
     */
    public void pushMessageToPrison(PushMessageForm form) {
        socketClient.pushMessageToPrison(form);
    }

    /**
     * 推送消息给用户 -平台，不等待响应
     */
    public void pushMessageToUser(PushMessageForm form) {
        socketClient.pushMessageToUser(form);
    }

    /**
     * 推送消息给对外服务终端设备，不等待响应
     */
    public void pushMessageToExternal(PushMessageForm form) {
        SocketRelationMapVO relationMapVO = socketClient.getSocketInfo().getData();
        List<String> sessionIds = relationMapVO.getMacAddressMap()
                .entrySet()
                .stream()
                .filter(f -> form.getMacAddress().contains(f.getValue()))
                .map(Map.Entry::getKey).collect(Collectors.toList());
        form.setSessionIds(sessionIds);
        this.pushMessageToClient(form);
    }

    /**
     * 推送消息给监室不等待响应
     */
    public void pushMessageToRoom(PushMessageForm form) {
        List<String> sessionIds = this.getSessionByRoomIds(form.getRoomIds(), form.getTerminal());
        form.setSerialNumbers(null);
        form.setRoomIds(form.getRoomIds());
        form.setTerminal(null);
        form.setSessionIds(sessionIds);
        this.pushMessageToClient(form);
    }

    /**
     * 推送消息给监室等待响应
     */
    public List<PushMessageAckVO> pushMessageToRoomWaitReplyMultiple(PushMessageForm form) {
        List<String> sessionIds = this.getSessionByRoomIds(form.getRoomIds(), form.getTerminal());
        form.setSerialNumbers(null);
        form.setRoomIds(null);
        form.setTerminal(null);
        form.setSessionIds(sessionIds);
        return this.pushMessageToClientWaitReplyMultiple(form);
    }

    /**
     * 推送给指定客户端等待回应
     */
    public PushMessageAckVO pushMessageToClientWaitReply(PushMessageForm form) {
        String sessionId = Optional.ofNullable(form.getSessionIds())
                .orElse(Collections.emptyList())
                .stream()
                .filter(StringUtils::isNotBlank)
                .findFirst()
                .orElseThrow(() -> new BaseException("发送的客户端为空"));
        form.setSessionIds(Collections.singletonList(sessionId));
        List<PushMessageAckVO> pushMessageAckList = this.pushMessageToClientWaitReplyMultiple(form);
        return Optional.ofNullable(pushMessageAckList)
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    /**
     * 推送消息给多个客户端等待回应
     */
    public List<PushMessageAckVO> pushMessageToClientWaitReplyMultiple(PushMessageForm form) {
        return socketClient.pushMessageToClientWaitReply(form).getData();
    }

    /**
     * 发送消息给客户端不期待回应
     */
    public void pushMessageToClient(PushMessageForm form) {
        socketClient.pushMessageToClient(form);
    }


    /**
     * 获取指定终端下会话
     */
    public List<String> getSessionByRoomIds(List<String> roomId, String terminal) {
        List<String> serialNumberList = null;
        if (SocketActionConstants.PushMessageTerminalEnum.CNP.name().equals(terminal)) {
            serialNumberList = cnpSocketFeign.getCnpSerialNumberByRoomId(roomId).getData();
        } else if (SocketActionConstants.PushMessageTerminalEnum.CWP.name().equals(terminal)) {
            serialNumberList = cnpSocketFeign.getCwpSerialNumberByRoomId(roomId).getData();
        } else if (SocketActionConstants.PushMessageTerminalEnum.FWF.name().equals(terminal)) {
            serialNumberList = cnpSocketFeign.getFwfSerialNumberByOrgCode(roomId).getData();
        } else if (terminal == null) {
            serialNumberList = cnpSocketFeign.getCnpCwpSerialNumberByRooomId(roomId).getData();
        }
        return this.getSessionBySerialNumber(serialNumberList);
    }

    /**
     * 根据序列号获取客户端Id
     */
    public List<String> getSessionBySerialNumber(List<String> serialNumbers) {
        SocketRelationMapVO relationMapVO = socketClient.getSocketInfo().getData();
        return relationMapVO.getSerialNumberMap()
                .entrySet()
                .stream()
                .filter(e -> serialNumbers != null && serialNumbers.contains(e.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /**
     * 根据监室id获取关联序列号
     */
    public List<String> getSerialNumberByRoomIds(List<String> roomId, String terminal) {
        List<String> result = new ArrayList<>();
        List<String> clientIds = this.getSessionByRoomIds(roomId, terminal);
        SocketRelationMapVO relationMapVO = socketClient.getSocketInfo().getData();
        Map<String, String> serialNumberMap = relationMapVO.getSerialNumberMap();
        for (String clientId : clientIds) {
            String serialNumber = serialNumberMap.get(clientId);
            if (StringUtils.isNotBlank(serialNumber)) {
                result.add(serialNumber);
            }
        }
        return result;
    }

    /**
     * 根据监室号获取仓内屏序列号. (device_type is null or device_type = 1)
     */
    public List<String> getCnpSerialNumberByRoomId(List<String> roomId) {
        return cnpSocketFeign.getCnpSerialNumberByRoomId(roomId).getData();
    }

    /**
     * 根据条件推送自定义消息
     */
    public void pushMessageCustomWithCondition(CustomPushMessageConditionDTO condition) {
        socketClient.customPushMessageWithCondition(condition);
    }

    /**
     * 按指定条件发送自定义消息格式到客户端-等待响应
     */
    public List<PushMessageAckVO> pushMessageCustomWithConditionWaitReply(CustomPushMessageConditionDTO condition){
        return socketClient.customPushMessageWithConditionWaitReply(condition).getData();
    }

    public Map<String,PushMessageAckVO> pushMessageCustomWithConditionWaitReplyMap(
            CustomPushMessageConditionDTO condition){
        List<PushMessageAckVO> pushMessageAckList = this.pushMessageCustomWithConditionWaitReply(condition);
        return Optional.ofNullable(pushMessageAckList)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(PushMessageAckVO::getSessionId, Function.identity(),
                        (v1, v2) -> v1));
    }
}
