package com.gosun.zhjg.prison.room.terminal.modules.socket.feign;

import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.common.msg.ResultVO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushVoiceBroadcastForm;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(value = "zhjg-prison-room-terminal", path = "cnp/socket", contextId ="cnp/socket" )
public interface CnpSocketFeign {

    /**
     * 推送仓内屏 语音广播
     *
     * @param form
     * @return
     */
    @PostMapping("pushVoiceBroadcast")
    R<?> pushVoiceBroadcast(@RequestBody PushVoiceBroadcastForm form);

    @PostMapping("sendPersonnelPhoto")
    @ApiOperation(value = "照片-下发照片")
    R<?> sendPersonnelPhoto(@RequestParam(value = "prisonerId") String prisonerId);

    @ApiOperation(value = "据序列号删除设备内人员照片，同时清除cnp_face表")
    @PostMapping("cleanStoreFaceBySerialNumber")
    R<?> cleanStoreFaceBySerialNumber(@RequestParam("rybhAppCode") String rybhAppCode, @RequestParam("serialNumber") String serialNumber);

    @ApiOperation(value = "据序列号删除设备内人员指纹")
    @PostMapping("cleanStoreFingermarkBySerialNumber")
    R<?> cleanStoreFingermarkBySerialNumber(@RequestParam("rybhAppCode") String rybhAppCode, @RequestParam("serialNumber") String serialNumber);

    @PostMapping("deletePersonnelPhoto")
    @ApiOperation(value = "照片-清理照片")
    R<?> deletePersonnelPhoto(@RequestParam(value = "prisonerId") String prisonerId, @RequestParam(required = false, value = "oldRoomId") String oldRoomId);

    @GetMapping("getCnpSerialNumberByRoomId")
    @ApiOperation(value = "根据监室号获取仓内屏序列号")
    ResultVO<List<String>> getCnpSerialNumberByRoomId(@RequestParam(value = "roomIds", required = false) List<String> roomIds);

    @GetMapping("getCwpSerialNumberByRoomId")
    @ApiOperation(value = "根据监室号获取仓外屏序列号")
    ResultVO<List<String>> getCwpSerialNumberByRoomId(@RequestParam(value = "roomIds", required = false) List<String> roomIds);

    @GetMapping("getCnpCwpSerialNumberByRooomId")
    @ApiOperation(value = "根据监室号获取仓内屏和仓外屏序列号")
    ResultVO<List<String>> getCnpCwpSerialNumberByRooomId(@RequestParam(value = "roomIds", required = false) List<String> roomIds);

    @GetMapping("getFwfSerialNumberByOrgCode")
    @ApiOperation(value = "根据机构编号查询防误放终端序列号")
    ResultVO<List<String>> getFwfSerialNumberByOrgCode(@RequestParam(value = "orgCodes", required = false) List<String> orgCodes);

    @GetMapping("getTdajpSerialNumberByOrgCode")
    @ApiOperation(value = "根据机构编号查询筒道安检屏序列号")
    ResultVO<List<String>> getTdajpSerialNumberByOrgCode(@RequestParam(value = "orgCodes", required = false) List<String> orgCodes);
}

