<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.rs</groupId>
		<artifactId>rs-adapter-zhjg</artifactId>
		<version>${rs.version}</version>
	</parent>

	<artifactId>rs-adapter-zhjg-biz</artifactId>
	<packaging>jar</packaging>
	<name>${project.artifactId}</name>
	<description>旧平台适配器-业务模块</description>

	<dependencies>
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-module-base</artifactId>
		</dependency>
		<!-- 注册中心相关 begin -->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>
		<!-- 注册中心相关 begin -->

		<!-- web 相关 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-starter-web</artifactId>
		</dependency>
		<!-- web 相关 end -->

		<!-- bsp 相关 begin -->
		<dependency>
			<groupId>com.bsp</groupId>
			<artifactId>bsp-common</artifactId>
			<exclusions>
				<exclusion>
					<groupId>cn.hutool</groupId>
		  			<artifactId>hutool-all-u</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.bsp</groupId>
			<artifactId>bsp-security</artifactId>
		</dependency>
		<dependency>
			<groupId>com.bsp</groupId>
			<artifactId>bsp-plus-sdk</artifactId>
		</dependency>
		<!-- bsp 相关 end -->

		<!-- DB 相关 begin -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
			<version>2.4.3</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>dynamic-datasource-spring-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
		</dependency>
		<!-- DB 相关 end -->

		<!-- 技术组件 begin -->
		<!-- 技术组件 end -->

		<!-- 工具类相关 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-common</artifactId>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>1.69</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>
		<!-- 工具类相关 end -->

		<!-- api组件 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-adapter-zhjg-api</artifactId>
		</dependency>
		<!-- api组件 end -->

		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>2.0.24</version>
		</dependency>


		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>itextpdf</artifactId>
			<version>5.5.13</version>
		</dependency>
		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>kernel</artifactId>
			<version>7.1.15</version>
		</dependency>
		<dependency>
			<groupId>com.itextpdf.tool</groupId>
			<artifactId>xmlworker</artifactId>
			<version>5.5.13</version>
		</dependency>
		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>itext-asian</artifactId>
			<version>5.2.0</version>
		</dependency>

		<dependency>
			<groupId>com.lowagie</groupId>
			<artifactId>itext</artifactId>
			<version>2.1.7</version>
		</dependency>

		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
			<version>3.3</version>
		</dependency>
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-starter-job</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
			<version>3.2</version>
		</dependency>
		<!-- socket io start -->
		<dependency>
			<groupId>com.corundumstudio.socketio</groupId>
			<artifactId>netty-socketio</artifactId>
			<version>1.7.7</version>
		</dependency>
		<!-- socket io end -->
	</dependencies>

	<build>
		<finalName>rs-zhjg-${rs.version}</finalName>
		<plugins>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<encoding>UTF-8</encoding>
					<fork>true</fork>
					<useIncrementalCompilation>true</useIncrementalCompilation>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-dependencies</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.basedir}/../../build/rs-zhjg/lib</outputDirectory>
							<overWriteReleases>false</overWriteReleases>
							<overWriteSnapshots>false</overWriteSnapshots>
							<overWriteIfNewer>true</overWriteIfNewer>
						</configuration>
					</execution>
					<execution>
						<id>copy-lib-dependencies</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.basedir}/../../build/lib</outputDirectory>
							<!--							<outputDirectory>${project.build.directory}/lib</outputDirectory>-->
							<overWriteReleases>false</overWriteReleases>
							<overWriteSnapshots>false</overWriteSnapshots>
							<overWriteIfNewer>true</overWriteIfNewer>
						</configuration>
					</execution>
					<!-- 复制rs开头的jar包到rs-lib目录 -->
					<execution>
						<id>copy-rs-dependencies</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.basedir}/../../build/rs-zhjg/rs-lib</outputDirectory>
							<includeGroupIds>com.rs</includeGroupIds>
							<overWriteReleases>false</overWriteReleases>
							<overWriteSnapshots>false</overWriteSnapshots>
							<overWriteIfNewer>true</overWriteIfNewer>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<outputDirectory>${project.basedir}/../../build/rs-zhjg</outputDirectory>
					<archive>
						<manifest>
							<mainClass>com.gosun.zhjg.ZhjgAdapterServerApplication</mainClass>
							<addClasspath>true</addClasspath>
							<classpathPrefix>lib/</classpathPrefix>
						</manifest>
					</archive>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-clean-plugin</artifactId>
				<configuration>
					<filesets>
						<fileset>
							<directory>${project.basedir}/../../build/rs-zhjg</directory>
							<includes>
								<include>**/*</include>
							</includes>
							<followSymlinks>false</followSymlinks>
						</fileset>
					</filesets>
				</configuration>
			</plugin>
		</plugins>
	</build>

<profiles>
	<!-- rs-only模式：构建完成后lib只保留rs开头的包 -->
	<profile>
		<id>rs-only</id>
		<build>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-antrun-plugin</artifactId>
					<version>3.1.0</version>
					<executions>
						<execution>
							<id>clean-non-rs-jars</id>
							<phase>package</phase>
							<goals>
								<goal>run</goal>
							</goals>
							<configuration>
								<target>
									<echo message="清理lib目录中的非rs开头的jar包..."/>
									<delete>
										<fileset dir="${project.basedir}/../../build/rs-zhjg/lib" includes="*.jar">
											<not>
												<filename name="rs-*.jar"/>
											</not>
										</fileset>
									</delete>
									<echo message="清理完成，lib目录现在只包含rs开头的jar包"/>
								</target>
							</configuration>
						</execution>
					</executions>
				</plugin>
			</plugins>
		</build>
	</profile>
</profiles>
</project>
