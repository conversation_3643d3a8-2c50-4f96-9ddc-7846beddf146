<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.CnpFaceDao">



    <select id="findPrisonerPage"
            resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpfacePrisonerVO">
        select bpi.id          as personnel_id,
               bpi.xm          as name,
               bpi.front_photo as prisoner_photo,
               bpi.jsh         as room_id,
               bpi.room_name,
               bpi.org_code,
               cf.id           as face_id,
               cf.photo,
               cf.update_time
        from vw_acp_pm_prisoner_in bpi
                 left join acp_pm_cnp_face cf on bpi.jgrybm = cf.personnel_code and cf.personnel_type = 2
        <where>
            <if test="form.prisonId != null and form.prisonId != ''">
                and bpi.dwdm = #{form.prisonId}
            </if>
            <if test="form.rybh != null and form.rybh != ''">
                and bpi.jgrybm = #{form.rybh}
            </if>
            <if test="form.roomId != null and form.roomId != ''">
                and bpi.jsh = #{form.roomId}
            </if>
            <if test="form.name != null and form.name != ''">
                and position(#{form.name} in bpi.xm) &gt; 0
            </if>
            <if test="form.code != null and form.code != ''">
                and position(#{form.code} in bpi.jgrybm) &gt; 0
            </if>
            <if test="form.personnelIdList != null and form.personnelIdList.size > 0">
                (bpi.jgrybm in (<foreach collection="form.personnelIdList" item="item" separator=",">
                #{item}
            </foreach>)
                    or
                bpi.id in (<foreach collection="form.personnelIdList" item="item" separator=",">
                #{item}
            </foreach>))
            </if>
        </where>
        order by cf.update_time desc nulls last
    </select>
    <select id="findPrisonerFingermarkPage" resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.FingermarkPrisonerVO">
        SELECT
        bpi.ID AS personnel_id,
        bpi.xm AS NAME,
        bpi.jsh AS room_id,
        bpi.room_name,
        bpi.jgrybm,
        bpi.org_code,
        dbi.id as finger_id,
        dbi.swtz
        FROM
        vw_acp_pm_prisoner_in bpi
        LEFT JOIN acp_db_biometric_info dbi ON bpi.jgrybm = dbi.jgrybm
        AND dbi.is_del = '0'
        AND dbi.status = '03'
        AND dbi.cjxmlx = '01'
        <where>
            bpi.is_del = '0'
            <if test="form.prisonId != null and form.prisonId != ''">
                and bpi.dwdm = #{form.prisonId}
            </if>
            <if test="form.rybh != null and form.rybh != ''">
                and bpi.jgrybm = #{form.rybh}
            </if>
            <if test="form.roomId != null and form.roomId != ''">
                and bpi.jsh = #{form.roomId}
            </if>
            <if test="form.name != null and form.name != ''">
                and position(#{form.name} in bpi.xm) &gt; 0
            </if>
            <if test="form.code != null and form.code != ''">
                and position(#{form.code} in bpi.jgrybm) &gt; 0
            </if>
            <if test="form.personnelIdList != null and form.personnelIdList.size > 0">
                (bpi.jgrybm in (<foreach collection="form.personnelIdList" item="item" separator=",">
                #{item}
            </foreach>)
                or
                bpi.id in (<foreach collection="form.personnelIdList" item="item" separator=",">
                #{item}
            </foreach>))
            </if>
        </where>


    </select>

    <!-- 人脸库管理-用户人脸补录列表。查询某个人员的人脸补录的下发情况，当前这里 distinct 作用在单列或者所有列都一样，不用去纠结 -->
    <select id="findPoliceImportStatePage"
            resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpFaceImportStateVO">
        select
        distinct a.serial_number,
        a.device_name,
        a.device_type,
        a.room_id,
        (select apr.room_name from acp_pm_area_prison_room apr where apr.id = a.room_id) as room_name,
        pu.policesn as code,
        pu.ispolice as personnel_type,
        pu.name,
        pu.id       as personnel_id,
        cfis.import_state,
        cfis.msg,
        cfis.update_time
        from acp_pm_device_inscreen as a
        cross join
        (select a.userid,
        b.id,
        a.policesn,
        a.ispolice,
        a.name
        from permission_user a
        inner join base_police_info b on
        a.userid = b.user_id
        where (a.isdelete is null
        or a.isdelete = 0)
        and a.userid > 0
        <if test="form.prisonId != null and form.prisonId != ''">
            and b.prison_id = #{form.prisonId}
        </if>
        <if test="form.name != null and form.name != ''">
            and a.name like concat('%',#{form.name},'%')
        </if>
        <if test="form.code != null and form.code != ''">
            and a.policesn like concat('%',#{form.code},'%')
        </if>
        <if test="form.ispolice != null">
            and a.ispolice = #{form.ispolice}
        </if>
        ) pu
        left join cnp_face cf on pu.id = cf.personnel_code and personnel_type = 1
        left join cnp_face_import_status cfis on
        cfis.personnel_type = 1
        and cfis.personnel_code = pu.id
        and cfis.serial_number = a.serial_number
        <where>
            and a.room_id != ''
            and a.serial_number != ''
            <if test="form.importState != null">
                and cfis.import_state = #{form.importState}
            </if>
            <if test="form.prisonId != null and form.prisonId != ''">
                and exists (select apr.id from acp_pm_area_prison_room apr where apr.id = a.room_id and apr.prison_id = #{form.prisonId})
            </if>
            <if test="form.serialNumber != null and form.serialNumber != ''">
                and a.serial_number like concat('%',#{form.serialNumber},'%')
            </if>
            <if test="form.roomId != null and form.roomId != ''">
                and a.room_id = #{form.roomId}
            </if>
        </where>
        order by cfis.update_time desc nulls last
    </select>

    <!-- 人脸库管理-被监管人员人脸补录列表。查询某个人员的人脸补录的下发情况，当前这里 distinct 作用在单列或者所有列都一样，不用去纠结 -->
    <select id="findPrisonerImportStatePage"
            resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpFaceImportStateVO">
        select
        distinct a.serial_number
        , a.device_name
        , a.device_type
        , a.room_id
        , (select apr.room_name from acp_pm_area_prison_room apr where apr.id = a.room_id) as room_name
        , p.rybh as code
        , p.xm   as name
        , cfis.import_state
        , cfis.msg
        , cfis.update_time
        , p.rybh as personnel_id
        from acp_pm_device_inscreen a
        inner join vw_acp_pm_prisoner_in p on a.room_id = p.jsh
        left join cnp_face cf on p.rybh = cf.personnel_code and personnel_type = 2
        left join cnp_face_import_status cfis
        on cfis.personnel_type = 2 and cfis.personnel_code = p.rybh
        and cfis.serial_number = a.serial_number
        <where>
            and a.room_id != ''
            and a.serial_number != ''
            <if test="form.prisonId != null and form.prisonId != ''">
                and p.dwdm = #{form.prisonId}
            </if>
            <if test="form.name != null and form.name != ''">
                and p.xm like concat('%',#{form.name},'%')
            </if>
            <if test="form.code != null and form.code != ''">
                and p.rybh like concat('%',#{form.code},'%')
            </if>
            <if test="form.importState != null">
                and cfis.import_state = #{form.importState}
            </if>
            <if test="form.serialNumber != null and form.serialNumber != ''">
                and a.serial_number like concat('%',#{form.serialNumber},'%')
            </if>
            <if test="form.roomId != null and form.roomId != ''">
                and a.room_id = #{form.roomId}
            </if>
        </where>
        order by cfis.update_time desc nulls last
    </select>

    <select id="findCnpPrisonerNotImportRecords" resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpfacePrisonerVO">
        select
            a.serial_number,
            a.room_id,
            p.rybh              as personnel_id,
            p.front_photo       as prisoner_photo,
            cf.photo
        from acp_pm_device_inscreen a
        inner join vw_acp_pm_prisoner_in p on a.room_id = p.jsh
        left join cnp_face cf on p.rybh = cf.personnel_code and personnel_type = 2
        left join cnp_face_import_status cfis on cfis.personnel_type = 2
            and cfis.personnel_code = p.rybh
            and cfis.serial_number = a.serial_number
        where
        a.room_id != ''
        and cfis.import_state is null
        and (a.device_type is null or a.device_type = 1)
        and a.serial_number in (<foreach collection="serialNumberList" separator="," item="serialNumber">#{serialNumber}</foreach>)
    </select>

    <select id="getPrisonerAssociatedDevices"
            resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpFaceDeviceInscreenVO">
        select bdi.id, bdi.serial_number, bdi.device_type, bdi.device_name
        from acp_pm_device_inscreen bdi
        where bdi.room_id in (select jsh from vw_acp_pm_prisoner bp where rybh = #{rybh})
    </select>

    <select id="getPoliceAssociatedDevicesByOrgCode"
            resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpFaceDeviceInscreenVO">
        select bdi.id, bdi.serial_number, bdi.device_type, bdi.device_name
        from acp_pm_device_inscreen bdi
        where room_id in (select id
                          from acp_pm_area_prison_room apr
                          <where>
                              <if test="orgCode != null and orgCode != ''">
                                  and apr.org_code = #{orgCode}
                              </if>
                              <if test="prisonId != null and prisonId != ''">
                                  and apr.org_code = #{prisonId}
                              </if>
                          </where>) OR ((device_type = '6' OR device_type = '7') AND org_code = #{prisonId})
    </select>

    <select id="getPerTypeUserCount"
            resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpFaceCountVO">
        select a.ispolice as mark, count(1) as count
        from permission_user a
        inner join base_police_info b on a.userid = b.user_id
        where (a.isdelete is null or a.isdelete = 0)
        and a.userid > 0
        <if test="prisonId != null and prisonId != ''">
            and b.prison_id = #{prisonId}
        </if>
        group by a.ispolice
    </select>

    <select id="getPrisonerCount" resultType="java.lang.Integer">
        select count(1) from vw_acp_pm_prisoner_in where dwdm = #{prisonId}
    </select>

    <select id="getPoliceCnpFaceCountByUpdateTime" resultType="java.lang.Integer">
        select count(1)
        from cnp_face cf
        inner join vw_acp_pm_prisoner_in bpi on cf.personnel_type = 2 and bpi.rybh = cf.personnel_code
        where bpi.dwdm = #{prisonId}
        <if test="startTime != null">
            and cf.update_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and cf.update_time &lt;= #{endTime}
        </if>
    </select>

    <select id="getPrisonerCnpFaceCountByUpdateTime" resultType="java.lang.Integer">
        select count(1)
        from permission_user a
        inner join base_police_info b on a.userid = b.user_id
        inner join cnp_face cf on cf.personnel_type = 1 and cf.personnel_code = b.id
        where (a.isdelete is null or a.isdelete = 0)
        and a.userid > 0
        and b.prison_id = #{prisonId}
        <if test="startTime != null">
            and cf.update_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and cf.update_time &lt;= #{endTime}
        </if>
    </select>


</mapper>
