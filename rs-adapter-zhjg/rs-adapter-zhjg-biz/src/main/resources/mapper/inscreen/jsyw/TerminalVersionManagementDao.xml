<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.TerminalVersionManagementDao">
    <sql id="all_entity_columns">
        ${prefix}.id,
        ${prefix}.package_name,
        ${prefix}.fixed,
        ${prefix}.version_number,
        ${prefix}.city_abbr,
        ${prefix}.sys_abbr,
        ${prefix}.machine_abbr,
        ${prefix}.competing_versions,
        ${prefix}.release_notes,
        ${prefix}.state,
        ${prefix}.update_time,
        ${prefix}.major_version,
        ${prefix}.minor_version,
        ${prefix}.patch_version,
        ${prefix}.terminal_type,
        ${prefix}.package_type,
        ${prefix}.url as package_url
    </sql>

    <select id="findByPage"
            resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.TerminalVersionManagementsDto">
        SELECT
        <include refid="all_entity_columns">
            <property name="prefix" value="a"/>
        </include>
        FROM
        acp_pm_terminal_version_management a
        <where>
            <if test="form.fixed != null and form.fixed != ''">
                and a.fixed = #{form.fixed}
            </if>
            <if test="form.packageName != null and form.packageName != ''">
                and a.package_name like concat('%',#{form.packageName},'%')
            </if>
            <if test="form.terminalType != null and form.terminalType != ''">
                and a.terminal_type = #{form.terminalType}
            </if>
            <if test="form.packageType != null and form.packageType != ''">
                and a.package_type = #{form.packageType}
            </if>
            <if test="form.versionNumber != null and form.versionNumber != ''">
                and a.version_number like concat('%',#{form.versionNumber},'%')
            </if>
            <if test="form.startTime != null">
                and a.add_time &gt;= #{form.startTime}
            </if>
            <if test="form.endTime != null">
                and a.add_time &lt; #{form.endTime}
            </if>
            <if test="form.state != null">
                and a.state = #{form.state}
            </if>
            <if test="form.sysAbbr != null and form.sysAbbr != ''">
                and a.sys_abbr = #{form.sysAbbr}
            </if>
            <if test="form.versionMatchRule != null">
                <choose>
                    <when test="form.versionMatchRule == '^'.toString()">
                        AND (a.major_version = #{form.majorVersion} AND a.minor_version &gt; #{form.minorVersion})
                    </when>
                    <when test="form.versionMatchRule == '~'.toString()">
                        AND (a.major_version = #{form.majorVersion} AND a.minor_version = #{form.minorVersion} AND a.patch_version &gt; #{form.patchVersion})
                    </when>
                    <when test="form.versionMatchRule == '>'.toString()">
                        and (a.major_version &gt; #{form.majorVersion}
                        OR (a.major_version >= #{form.majorVersion} AND a.minor_version &gt; #{form.minorVersion})
                        OR (a.major_version >= #{form.majorVersion} AND a.minor_version >= #{form.minorVersion} AND a.patch_version &gt; #{form.patchVersion}))
                    </when>
                    <when test="form.versionMatchRule == '&lt;'.toString()">
                        and (a.major_version &lt; #{form.majorVersion}
                        OR (a.major_version &lt;= #{form.majorVersion} AND a.minor_version &lt; #{form.minorVersion})
                        OR (a.major_version &lt;= #{form.majorVersion} AND a.minor_version &lt;= #{form.minorVersion} AND a.patch_version &lt; #{form.patchVersion}))
                    </when>
                </choose>
            </if>
        </where>
        <choose>
            <when test="form.orderByVersion != null and form.orderByVersion">
                order by a.major_version desc, a.minor_version desc, a.patch_version desc
            </when>
            <otherwise>
                <!--order by a.add_time desc-->
                order by a.major_version desc, a.minor_version desc, a.patch_version desc
            </otherwise>
        </choose>
    </select>

    <select id="findOneById"
            resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.TerminalVersionManagementsDto">
        SELECT
        <include refid="all_entity_columns">
            <property name="prefix" value="a"/>
        </include>
        FROM
        acp_pm_terminal_version_management a
        where a.id = #{id}
    </select>

    <select id="terminalVersionPage"
            resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.TerminalVersionInfoVO">
        select a.serial_number,
            a.device_type,
            a.room_id,
            apr.room_name,
            vinfo.ip,
            vinfo.apk_version,
            vinfo.web_version,
            vinfo.compatible_web_versions,
            vinfo.apk_upgrade_time,
            vinfo.web_upgrade_time,
            vinfo.update_time
        from acp_pm_device_inscreen a
        left join acp_pm_terminal_version_info vinfo on a.serial_number = vinfo.serial_number
        left join acp_pm_area_prison_room apr on apr.id = a.room_id
        <where>
            <if test="form.serialNumber != null and form.serialNumber != ''">
                and a.serial_number like concat('%',#{form.serialNumber},'%')
            </if>
            <if test="form.deviceType != null">
                and a.device_type = #{form.deviceType}
            </if>
            <if test="form.roomId != null and form.roomId != ''">
                and a.room_id = #{form.roomId}
            </if>
            <if test="form.ip != null and form.ip != ''">
                and vinfo.ip like concat('%',#{form.ip},'%')
            </if>
            <if test="form.apkVersion != null and form.apkVersion != ''">
                and vinfo.apk_version like concat('%',#{form.apkVersion},'%')
            </if>
            <if test="form.webVersion != null and form.webVersion != ''">
                and vinfo.web_version like concat('%',#{form.webVersion},'%')
            </if>
        </where>
        order by vinfo.update_time desc
    </select>

    <select id="terminalUpgradeLogPage"
            resultType="com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.TerminalVersionUpgradeLogVO">
        SELECT
            a.id,
            a.serial_number,
            a.ip,
            a.apk_version,
            a. web_version,
            a. upgrade_type,
            a. upgrade_version,
            a. ok,
            a. upgrade_time,
            a. add_time
        , bdi.device_type
        FROM acp_pm_terminal_version_upgrade_log a
        left join acp_pm_device_inscreen bdi on a.serial_number = bdi.serial_number
        <where>
            <if test="form.serialNumber != null and form.serialNumber != ''">
                and a.serial_number like concat('%',#{form.serialNumber},'%')
            </if>
            <if test="form.ip != null and form.ip != ''">
                and a.ip like concat('%',#{form.ip},'%')
            </if>
            <if test="form.apkVersion != null and form.apkVersion != ''">
                and a.apk_version like concat('%',#{form.apkVersion},'%')
            </if>
            <if test="form.webVersion != null and form.webVersion != ''">
                and a.web_version like concat('%',#{form.webVersion},'%')
            </if>
            <if test="form.upgradeType != null and form.upgradeType != ''">
                and a.upgrade_type = #{form.upgradeType}
            </if>
            <if test="form.upgradeVersion != null and form.upgradeVersion != ''">
                and a.upgrade_version like concat('%',#{form.upgradeVersion},'%')
            </if>
            <if test="form.ok != null">
                and a.ok = #{form.ok}
            </if>
            <if test="form.startTime != null">
                and a.upgrade_time &gt;= #{form.startTime}
            </if>
            <if test="form.endTime != null">
                and a.upgrade_time &lt;= #{form.endTime}
            </if>
            <if test="form.deviceType != null">
                and bdi.device_type = #{form.deviceType}
            </if>
        </where>
        order by a.upgrade_time desc
    </select>
</mapper>
