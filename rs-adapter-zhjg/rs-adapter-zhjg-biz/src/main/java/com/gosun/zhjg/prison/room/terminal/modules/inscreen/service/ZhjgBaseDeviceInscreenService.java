package com.gosun.zhjg.prison.room.terminal.modules.inscreen.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gosun.zhjg.auth.common.util.jwt.IJWTInfo;
import com.gosun.zhjg.common.msg.ResultVO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.BaseDeviceInscreenPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.BaseDeviceTerminalPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.BaseDeviceTerminalTypeDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.BaseRoomDeviceTerminalDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.*;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;
import java.util.Map;


/**
 * 仓内屏
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-09-15 10:55:29
 */
public interface ZhjgBaseDeviceInscreenService {

    List<BaseDeviceInscreenPageVO> findByPage(Page page, BaseDeviceInscreenPageDto pageDto);

    BaseDeviceInscreenVO findOneById(String id);

    BaseDeviceInscreenVO getBySerialNum(String serialNum);

    BaseDeviceInscreenVO getByDeviceNum(Integer deviceNum);

    BaseDeviceInscreenVO getByRoom(String roomId);

//    void saveBaseDeviceInscreen(BaseDeviceInscreenSaveDto dto, String userid, String userName);
//
//    void updateBaseDeviceInscreen(BaseDeviceInscreenUpdateDto dto, String userid, String userName);
//
//    void deleteByIds(String[] ids);


    List<BaseDeviceTerminalVo> getRoomDeviceList(Page page, BaseDeviceTerminalPageDto dto);

    Map getRoomDeviceByRoomId(String roomId);

    List<BaseDeviceTerminalInfoVo> getDeviceListByType(Page page, BaseDeviceTerminalTypeDto dto);

    ResultVO updateRoomDevice(BaseRoomDeviceTerminalDto dto, IJWTInfo user);

    /**
     * 仓内屏-根据序列号获取设备信息
     *
     * @param serialNum
     * @return
     */
    CnpDeviceInfoVO getCnpInfo(@PathVariable("serialNum") String serialNum);

    /**
     * 仓外屏-根据序列号获取设备信息
     *
     * @param serialNum
     * @return
     */
    CwpInfoVO getCwpInfo(@PathVariable("serialNum") String serialNum);

    /**
     * 防误放终端-根据序列号获取设备信息
     * @param serialNum
     * @return
     */
    FwfDeviceInfoVO getFwfInfo(String serialNum);

    /**
     * 筒道安检屏-根据序列号获取设备信息
     * @param serialNum
     * @return
     */
    TdajpDeviceInfoVO getTDAJPInfo(String serialNum);
}

