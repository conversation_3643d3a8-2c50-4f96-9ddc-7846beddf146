/*
 * @Author: 丁永泽 <EMAIL>
 * @Date: 2025-07-15 11:18:29
 * @LastEditors: 丁永泽 <EMAIL>
 * @LastEditTime: 2025-08-20 23:11:16
 * @FilePath: \rs-master\rs-adapter-zhjg\rs-adapter-zhjg-biz\src\main\java\com\gosun\zhjg\prison\room\terminal\modules\inscreen\handler\SendFaceHandler.java
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
package com.gosun.zhjg.prison.room.terminal.modules.inscreen.handler;

import cn.hutool.db.Entity;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSON;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.CnpFaceForm;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.CnpUserDTO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.ConnectHandlerDTO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.BaseDeviceInscreenEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.CnpFaceService;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.handler.AbstractHandler;
import com.rs.framework.mybatis.util.BspDbUtil;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName SendFaceHandler
 * @Description 发送人脸
 * <AUTHOR>
 * @Date 2025/7/15 11:18
 * @Version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SendFaceHandler extends AbstractHandler<ConnectHandlerDTO> {
    private final CnpFaceService cnpFaceService;
    private final PrisonerService prisonerService;

    @Override
    protected ConnectHandlerDTO doHandle(ConnectHandlerDTO input) {
        log.info("开始处理人脸发送任务，设备IP: {}, 序列号: {}", input.getIp(), input.getSerialNum());

        String url = String.format("http://%s:6897/systemManager/user/pageList", input.getIp());
        log.info("发送人脸请求:{}", url);
        Map<String, Object> params = new HashMap<>();
        params.put("isPage", 0);
        params.put("curPage", 1);
        params.put("pageSize", 30000);
        log.debug("请求参数: {}", JSONUtil.toJsonStr(params));
        String response = "";
        try {
            log.debug("开始发送HTTP请求到设备: {}", url);
            response = HttpRequest.post(url)
                    .timeout(60000) // 设置超时
                    .body(JSONUtil.toJsonStr(params))
                    .execute().body();
            log.debug("HTTP请求响应长度: {}", response != null ? response.length() : 0);

            // 检查响应是否为空
            if (StringUtils.isBlank(response)) {
                log.warn("HTTP请求返回空响应: {}", url);
                return input;
            }
        } catch (Exception e) {
            log.error("HTTP请求失败: {}", url, e);
            return input;
        }

        log.debug("开始解析设备响应数据");
        CnpUserDTO cnpUserDTO = JSON.parseObject(response, CnpUserDTO.class);
        List<CnpUserDTO.DataDTO.RowsDTO> rows = null;

        BaseDeviceInscreenEntity deviceEntity = input.getBaseDeviceInscreenEntity();
        if (deviceEntity == null) {
            log.error("设备信息不存在或监室ID为空，设备实体: {}", deviceEntity);
            throw new ServerException("设备信息不存在");
        }
        // 下发在押人员人脸
        if (input.isSendPrisoner()) {
            if (input.isAllPrisoner()) {
                if (input.getBaseDeviceInscreenEntity().getOrgCode() == null) {
                    log.error("设备信息不存在或监所ID为空，设备实体: {}", deviceEntity);
                    throw new ServerException("设备信息不存在");
                }
                log.info("获取监所在押人员，监所ID: {}", deviceEntity.getOrgCode());
            } else {
                if (deviceEntity.getRoomId() == null) {
                    log.error("设备信息不存在或监室ID为空，设备实体: {}", deviceEntity);
                    throw new ServerException("设备信息不存在");
                }
                log.info("获取监室在押人员，监室ID: {}", deviceEntity.getRoomId());
            }

            //获取监室的在押人员
            List<PrisonerVwRespVO> prisonerList;
            if (input.isAllPrisoner()) {
                prisonerList = prisonerService.getPrisonerListByOrgCode(input.getBaseDeviceInscreenEntity().getOrgCode());
                log.info("获取到监所: {} 在押人员数量: {}", input.getBaseDeviceInscreenEntity().getOrgCode(), prisonerList != null ? prisonerList.size() : 0);
            } else {
                prisonerList = prisonerService.getPrisonerListByJsh(input.getBaseDeviceInscreenEntity().getRoomId());
                log.info("获取到监室在押人员数量: {}", prisonerList != null ? prisonerList.size() : 0);
            }

            if (cnpUserDTO.getData() != null) {
                rows = cnpUserDTO.getData().getRows();
                log.info("设备已存在用户数量: {}", rows != null ? rows.size() : 0);
            } else {
                log.warn("设备响应数据为空");
            }
            // 已经下发过的不再下发
            log.info("开始处理在押人员人脸下发");
            try {
                int originalPrisonerCount = prisonerList.size();
                if (rows != null) {
                    log.debug("过滤已下发的在押人员");
                    Iterator<PrisonerVwRespVO> iterator = prisonerList.iterator();
                    int filteredCount = 0;
                    while (iterator.hasNext()) {
                        PrisonerVwRespVO next = iterator.next();
                        if (rows.stream().anyMatch(rowsDTO -> Objects.equals(rowsDTO.getFaceCode(), next.getId()))) {
                            iterator.remove();
                            filteredCount++;
                        }
                    }
                    log.info("过滤掉已下发的在押人员数量: {}", filteredCount);
                }

                List<String> idList = prisonerList.stream().map(PrisonerVwRespVO::getId).collect(Collectors.toList());
                log.info("需要下发的在押人员数量: {} (原始: {}, 过滤后: {})",
                        idList.size(), originalPrisonerCount, prisonerList.size());

                if (!idList.isEmpty()) {
                    CnpFaceForm form = new CnpFaceForm();
                    form.setPersonnelType("prisoner");
                    form.setPersonnelIdList(idList);
                    form.setSerialNumbers(ListUtils.newArrayList(input.getSerialNum()));
                    log.debug("开始批量下发在押人员人脸，人员ID列表: {}", idList);
                    cnpFaceService.batchSendFace(form);
                    log.info("在押人员人脸下发完成，下发数量: {}", idList.size());
                } else {
                    log.info("没有需要下发的在押人员");
                }
            } catch (Exception e) {
                log.error("下发在押人员人脸失败", e);
            }
        }

        // 下发民警的人脸
        log.info("开始处理民警人脸下发，机构代码: {}", input.getBaseDeviceDO().getOrgCode());
        Entity where = new Entity();
        where.setTableName("uac_user");
        where.set("ORG_CODE", input.getBaseDeviceDO().getOrgCode());
        where.set("ISDEL", "0");
        log.debug("查询民警条件: {}", where);

        List<Entity> entityList = null;
        try {
            entityList = BspDbUtil.getDb().findAll(where);
            log.info("获取到民警数量: {}", entityList != null ? entityList.size() : 0);
        } catch (SQLException e) {
            log.error("获取民警失败，机构代码: {}", input.getBaseDeviceDO().getOrgCode(), e);
            throw new ServerException("获取民警失败");
        }
        // 已经下发过的不再下发
        try {
            int originalPoliceCount = entityList.size();
            if (rows != null) {
                log.debug("过滤已下发的民警");
                Iterator<Entity> iterator = entityList.iterator();
                int filteredCount = 0;
                while (iterator.hasNext()) {
                    Entity next = iterator.next();
                    if (rows.stream().anyMatch(rowsDTO -> rowsDTO.getFaceCode().equals(next.getStr("ID_CARD")))) {
                        iterator.remove();
                        filteredCount++;
                    }
                }
                log.info("过滤掉已下发的民警数量: {}", filteredCount);
            }

            List<String> userIds = entityList.stream().map(e -> e.getStr("ID_CARD")).collect(Collectors.toList());
            log.info("需要下发的民警数量: {} (原始: {}, 过滤后: {})",
                    userIds.size(), originalPoliceCount, entityList.size());

            if (!userIds.isEmpty()) {
                CnpFaceForm userForm = new CnpFaceForm();
                userForm.setPersonnelType("police_1");
                userForm.setPersonnelIdList(userIds);
                userForm.setSerialNumbers(ListUtils.newArrayList(input.getSerialNum()));
                log.debug("开始批量下发民警人脸，民警ID列表: {}", userIds);
                cnpFaceService.batchSendFace(userForm);
                log.info("民警人脸下发完成，下发数量: {}", userIds.size());
            } else {
                log.info("没有需要下发的民警");
            }
        } catch (Exception e) {
            log.error("下发民警人脸失败，机构代码: {}", input.getBaseDeviceDO().getOrgCode(), e);
        }

        log.info("人脸发送任务处理完成，设备IP: {}, 序列号: {}", input.getIp(), input.getSerialNum());
        return input;
    }
}
