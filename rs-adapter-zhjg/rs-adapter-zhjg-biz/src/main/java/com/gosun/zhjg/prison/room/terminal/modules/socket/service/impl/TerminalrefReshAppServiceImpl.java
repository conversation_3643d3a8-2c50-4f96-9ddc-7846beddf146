package com.gosun.zhjg.prison.room.terminal.modules.socket.service.impl;

import com.alibaba.fastjson.JSON;
import com.gosun.zhjg.common.constant.KafkaTopicsConstants;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.prison.room.terminal.config.socketio.kafka.product.RabbitProduct;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.TerminalrefReshAppService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class TerminalrefReshAppServiceImpl implements TerminalrefReshAppService {

    private final String CNP_DEVICE_TYPE_ID = "0008";

    private final String CWP_DEVICE_TYPE_ID = "0015";

    private final String FWF_DEVICE_TYPE_ID = "0026";

    private final String TDAJP_DEVICE_TYPE_ID = "0034";

    //访客终端
    private final String FKZD_DEVICE_TYPE_ID = "0020";

    //会见终端
    private final String HJZD_DEVICE_TYPE_ID = "0021";

    @Autowired
    private RabbitProduct rabbitProduct;

    public void refreshApp(String deviceTypeId, String socketMark) {
        PushMessageForm pushFrom = new PushMessageForm();
        pushFrom.setAction(SocketActionConstants.refreshApp);
        List<String> socketMarks = Collections.singletonList(socketMark);
        if (deviceTypeId.equals(CNP_DEVICE_TYPE_ID)) {
            pushFrom.setSerialNumbers(socketMarks);
            pushFrom.setTarget(SocketActionConstants.PushMessageTargetEnum.web.name());
            pushFrom.setTerminal(SocketActionConstants.PushMessageTerminalEnum.CNP.name());
            String form = JSON.toJSONString(pushFrom);
            rabbitProduct.sendMsg(KafkaTopicsConstants.Kafkatopic_PushMessageToSerialNumber, form);
        } else if (deviceTypeId.equals(CWP_DEVICE_TYPE_ID)) {
            pushFrom.setSerialNumbers(socketMarks);
            pushFrom.setTarget(SocketActionConstants.PushMessageTargetEnum.web.name());
            pushFrom.setTerminal(SocketActionConstants.PushMessageTerminalEnum.CWP.name());
            String form = JSON.toJSONString(pushFrom);
            rabbitProduct.sendMsg(KafkaTopicsConstants.Kafkatopic_PushMessageToSerialNumber, form);
        } else if (deviceTypeId.equals(FWF_DEVICE_TYPE_ID)) {
            pushFrom.setSerialNumbers(socketMarks);
            pushFrom.setTarget(SocketActionConstants.PushMessageTargetEnum.web.name());
            pushFrom.setTerminal(SocketActionConstants.PushMessageTerminalEnum.FWF.name());
            String form = JSON.toJSONString(pushFrom);
            rabbitProduct.sendMsg(KafkaTopicsConstants.Kafkatopic_PushMessageToSerialNumber, form);
        } else if (deviceTypeId.equals(TDAJP_DEVICE_TYPE_ID)) {
            pushFrom.setSerialNumbers(socketMarks);
            pushFrom.setTarget(SocketActionConstants.PushMessageTargetEnum.web.name());
            pushFrom.setTerminal(SocketActionConstants.PushMessageTerminalEnum.TDAJP.name());
            String form = JSON.toJSONString(pushFrom);
            rabbitProduct.sendMsg(KafkaTopicsConstants.Kafkatopic_PushMessageToSerialNumber, form);
        } else {
            pushFrom.setMacAddress(socketMarks);
            pushFrom.setTarget(SocketActionConstants.PushMessageTargetEnum.external.name());
            pushFrom.setTerminal(SocketActionConstants.PushMessageTerminalEnum.EXTERNAL.name());
            String form = JSON.toJSONString(pushFrom);
            rabbitProduct.sendMsg(KafkaTopicsConstants.Kafkatopic_PushMessageToExternal, form);
        }

    }
}
