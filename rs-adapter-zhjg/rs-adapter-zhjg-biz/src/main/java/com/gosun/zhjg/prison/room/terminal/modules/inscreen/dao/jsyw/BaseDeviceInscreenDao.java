package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.BaseDeviceInscreenPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.BaseDeviceTerminalPageDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.BaseDeviceTerminalTypeDto;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.BaseDeviceInscreenEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.BaseDeviceInscreenPageVO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.BaseDeviceInscreenVO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.BaseDeviceTerminalInfoVo;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.BaseDeviceTerminalVo;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CnpDeviceInfoVO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.CwpDeviceInfoVO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.TdajpDeviceInfoVO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.FwfDeviceInfoVO;
import com.rs.framework.common.util.object.BeanUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 仓内屏
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-09-15 10:55:29
 */
@Mapper
public interface BaseDeviceInscreenDao extends BaseMapper<BaseDeviceInscreenEntity> {

    List<BaseDeviceInscreenPageVO> findByPage(Page page, @Param("form") BaseDeviceInscreenPageDto pageDto);

    BaseDeviceInscreenVO findOneById(@Param("id") String id);

    BaseDeviceInscreenVO getByRoom(@Param("roomId") String roomId);

    BaseDeviceInscreenVO getByDeviceNum(@Param("deviceNum") Integer deviceNum);

    @Select("select a.id, a.serial_number, a.device_id, a.device_ip, a.room_id, a.device_num, a.host_num, a.address_ip, b.room_name, b.prison_id from acp_pm_device_inscreen a left join acp_pm_area_prison_room b on a.room_id = b.id where a.serial_number = #{serialNum} limit 1")
    BaseDeviceInscreenVO getBySerialNum(@Param("serialNum") String serialNum);


    List<BaseDeviceTerminalVo> getRoomDeviceList(Page page, @Param("form") BaseDeviceTerminalPageDto dto);

    BaseDeviceTerminalInfoVo getDeviceInscreenByRoom(@Param("roomId") String roomId, @Param("deviceType") Integer deviceType);

    BaseDeviceTerminalInfoVo getDeviceInscreenBySerialNumber(@Param("serialNumber") String serialNumber);

    List<BaseDeviceTerminalInfoVo> getDeviceListByType(Page page, @Param("form") BaseDeviceTerminalTypeDto dto);

    List<String> getRoomNameByDeviceId(String deviceId);

    default Integer updateRoomTerminal(@Param("roomId") String roomId, @Param("deviceId") String deviceId, @Param("updateUserId") String updateUserId) {
        LambdaUpdateWrapper<BaseDeviceInscreenEntity> lambda = new LambdaUpdateWrapper<>();
        lambda.set(BaseDeviceInscreenEntity::getRoomId, roomId);
        lambda.eq(BaseDeviceInscreenEntity::getDeviceId, deviceId);
        return update(null, lambda);
    }

    Integer deleteRoomTerminal(@Param("roomId") String roomId, @Param("deviceType") Integer deviceType);


    default List<BaseDeviceInscreenVO> getByDeviceId(@Param("deviceId") String deviceId) {
        List<BaseDeviceInscreenEntity> baseDeviceInscreenEntities = selectList(new LambdaUpdateWrapper<BaseDeviceInscreenEntity>().eq(BaseDeviceInscreenEntity::getDeviceId, deviceId));
        return BeanUtils.toBean(baseDeviceInscreenEntities, BaseDeviceInscreenVO.class);
    }

    /**
     * 根据序列获取设备ip号
     *
     * @param serialNumber
     * @return
     */
    @Select("select device_ip from acp_pm_device_inscreen where serial_number = #{serialNumber}")
    List<String> getDeviceIpBySerialNumber(@Param("serialNumber") String serialNumber);

    String getRoomTerminal(@Param("deviceId") String deviceId, @Param("roomId") String roomId, @Param("deviceType") Integer deviceType);

    /**
     * 根据区域查询对讲分机设备
     *
     * @param roomId
     * @return
     */
    List<BaseDeviceInscreenVO> getFjDeviceByAreaId(@Param("roomId") String roomId);

    List<BaseDeviceInscreenVO> getCnpDeviceByRoomId(@Param("roomId") String roomId);

    CnpDeviceInfoVO getCnpDeviceInfo(@Param("serialNum") String serialNum);

    FwfDeviceInfoVO getFwfDeviceInfo(@Param("serialNum") String serialNum);

    TdajpDeviceInfoVO getTDAJPInfo(@Param("serialNum") String serialNum);

    List<CwpDeviceInfoVO> getCwpDeviceInfo(@Param("serialNum") String serialNum);

}
