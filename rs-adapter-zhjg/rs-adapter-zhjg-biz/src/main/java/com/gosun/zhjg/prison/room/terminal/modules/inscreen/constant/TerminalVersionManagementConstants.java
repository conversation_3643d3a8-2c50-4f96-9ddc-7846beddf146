package com.gosun.zhjg.prison.room.terminal.modules.inscreen.constant;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;

import java.util.HashMap;
import java.util.regex.Pattern;

public class TerminalVersionManagementConstants {

    public static final String Package_Folder = "/release";
    public static final String Package_File_Name_Regex = "^(FEproject|Aproject)_(.*?).(tar\\.gz|apk)$";
    public static final Pattern Package_Pattern = Pattern.compile(Package_File_Name_Regex);
    /**
     * 版本号校验
     */
    public static final Pattern Version_Pattern = Pattern.compile("[vV](\\d+)\\.(\\d+)\\.(\\d+)\\.?(\\d)?.*");
    /**
     * 兼容版本校验
     * "^" 升级次版本号和修订号
     * "~" 只升级修订号
     * ">" 必须大于某个版本号
     * "<" 必须小于某个版本号
     */
    public static final Pattern Competing_Version_Pattern = Pattern.compile("^[\\^~><][vV](\\d+)\\.(\\d+)\\.(\\d+)");

    /**
     * 系统缩写 映射。k apk， v 终端类型。（k + "W“）：对应web
     * WS内屏、DT外屏
     * WSW、DTW。W结尾 W表示网页，固定
     * HTFP 提人终端
     */
    public static final BiMap<String, String> SysAbbrTerminalTypeMap = HashBiMap.create(new HashMap<String, String>() {
        {
            put("WS", "0008");
            put("DT", "0015");
            put("HTFP", "0023");
        }
    });

}
