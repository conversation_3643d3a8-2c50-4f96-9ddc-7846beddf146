package com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto;

import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.BaseDeviceInscreenEntity;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;
import lombok.Data;

/**
 * @ClassName ConnectHandlerDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/7/15 10:53
 * @Version 1.0
 */
@Data
public class ConnectHandlerDTO {
    /**
     * 连接ip
     */
    private String ip;
    /**
     * 终端编号
     */
    private String serialNum;
    /**
     * 终端状态，001-在线，002-离线，003-故障  DeviceStatusEnum
     */
    private String status;
    /**
     * 终端
     */
    private BaseDeviceInscreenEntity baseDeviceInscreenEntity;
    /**
     * 设备
     */
    private BaseDeviceDO baseDeviceDO;
    /**
     * 是否下发在押人员数据
     */
    private boolean sendPrisoner = true;

    /**
     * 是否下发所有在押人员数据
     */
    private boolean isAllPrisoner = false;

}
