package com.gosun.zhjg.prison.room.terminal.modules.socket.dao;

import com.alibaba.fastjson.JSONObject;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.DeviceInscreenVO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.PersonalInfoVO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.vo.TerminalDeviceInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@Mapper
public interface CnpSocketDao {

    @Select("select room_name from acp_pm_area_prison_room where id = #{roomId}")
    public String getRoomNameById(@Param("roomId") String roomId);

    @Select("select id, room_name from acp_pm_area_prison_room")
    public List<JSONObject> getAllRoom();

    @Select("select id, room_name from acp_pm_area_prison_room where prison_id = #{prisonId}")
    public List<JSONObject> getRoomByPrison(@Param("prisonId") String prisonId);

    /**
     * 获取所有仓内屏信息，IP
     *
     * @return
     */
    @Select("select serial_number, device_ip, room_id, device_name, (select room_name from acp_pm_area_prison_room apr where apr.id = a.room_id) as room_name from acp_pm_device_inscreen a")
    List<DeviceInscreenVO> getAllDeviceInscreen();

    /**
     * 根据监室查找人员及照片
     *
     * @return
     */
    @Select("select rybh, front_photo as photo, xm as name, 'prisoner' as \"type\" from vw_acp_pm_prisoner_in bpi where bpi.jsh  = #{roomId}")
    public List<PersonalInfoVO> getPrisonerPhotoByRoom(@Param("roomId") String roomId);

//    /**
//     * 根据监室查找民警及照片
//     *
//     * @return
//     */
//    @Select("select distinct b.id as rybh, b.photo, 'police' as \"type\" from prison_room_warder a inner join base_police_info b on a.police_id = b.id where a.room_id= #{roomId} and a.status='1' ")
//    public List<JSONObject> getPolicePhotoByRoom(@Param("roomId") String roomId);

    /**
     * 根据监室查找该监室对应监所下所有警员照片
     *
     * @return
     */
    @Select(" select b.id as rybh, b.photo, a.name,  'police' as \"type\" " + //
            " from permission_user a inner join base_police_info b on a.userid = b.user_id where (a.isdelete is null or a.isdelete = 0) " + //
            " and b.prison_id = (select prison_id from acp_pm_area_prison_room apr where id = #{roomId} limit 1) ")
    public List<PersonalInfoVO> getPrisonPolicePhotoByRoom(@Param("roomId") String roomId);

    /**
     * 根据序列号获取关联的监室
     *
     * @param serialNumber
     * @return
     */
    @Select("select room_id from acp_pm_device_inscreen where serial_number = #{serialNumber}")
    public List<String> getRoomIdBySerialNumber(@Param("serialNumber") String serialNumber);

    /**
     * 根据监室号获取仓内屏序列号. (device_type is null or device_type = 1)
     *
     * @param roomId
     * @return
     */
    public List<String> getCnpSerialNumberByRoomId(@Param("roomIds") List<String> roomId);

    /**
     * 根据监所编号获取仓内屏序列号. (device_type is null or device_type = 1)
     *
     * @param prisonId
     * @return
     */
    public List<String> getCnpSerialNumberByPrisonId(@Param("prisonId") String prisonId);

    /**
     * 根据监室号获取仓外屏序列号
     *
     * @param roomId
     * @return
     */
    public List<String> getCwpSerialNumberByRoomId(@Param("roomIds") List<String> roomId);
    List<String> getCnpCwpSerialNumberByRooomId(@Param("roomIds") List<String> roomId);
    List<String> getFwfSerialNumberByOrgCode(@Param("orgCodes")List<String> orgCodes);
    List<String> getTdajpSerialNumberByOrgCode(@Param("orgCodes")List<String> orgCodes);

    @Select("select id, rybh, front_photo as photo, xm as name, ryzt, jsh as roomId,org_code as orgCode from vw_acp_pm_prisoner where rybh = #{rybh}")
    public PersonalInfoVO getPrisonerInfo(@Param("rybh") String rybh);

    /**
     * 在照片表查询人员照片
     *
     * @param rybh
     * @return
     */
    String getPrisonerPhotoInPhotoTable(@Param("rybh") String rybh);

    @Select("select rybh, front_photo as photo, xm as name, ryzt, jsh as roomId from vw_acp_pm_prisoner_in where rybh = #{rybh}")
    PersonalInfoVO getPrisonerInfoIn(@Param("rybh") String rybh);


    /**
     * 查询仓内外屏设备信息
     *
     * @param deviceTypeId 设备表类型， 0008 仓内屏， 0015 仓外屏
     * @return
     */
    List<TerminalDeviceInfoVO> getTerminalDeviceInfo(@Param("deviceTypeId") String deviceTypeId);

    /**
     * 根据id修改设备表在线状态
     *
     * @param deviceStatus
     * @param deviceIds
     * @return
     */
    int updateDeviceOnlineState(@Param("deviceStatus") String deviceStatus, @Param("deviceIds") List<String> deviceIds);

    /**
     * 获取序列号的 设备类型。 使用时取第一个
     *
     * @param serialNumber
     * @return
     */
    @Select("select device_type from acp_pm_device_inscreen where serial_number = #{serialNumber}")
    List<Integer> getDeviceTypeBySerialNumber(@Param("serialNumber") String serialNumber);

    @Select("UPDATE acp_pm_device \n" +
            "SET online_time = #{time} \n" +
            "WHERE\n" +
            "\tID = #{id} ")
    Integer setDeviceOnlineTime(@Param("id") String id,@Param("time") Date time);

    @Select("UPDATE acp_pm_device \n" +
            "SET online_time = null \n" +
            "WHERE\n" +
            "\tID = #{id} ")
    Integer setDeviceOfflineTime(@Param("id") String id);

}
