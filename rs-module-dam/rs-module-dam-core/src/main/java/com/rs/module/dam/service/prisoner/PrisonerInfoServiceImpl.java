package com.rs.module.dam.service.prisoner;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.dam.dao.material.MaterialInfoDao;
import com.rs.module.dam.dao.prisoner.PrisonerInfoDao;
import com.rs.module.dam.entity.archive.ArchiveCoverDO;
import com.rs.module.dam.entity.material.MaterialInfoDO;
import com.rs.module.dam.entity.prisoner.PrisonerInfoDO;
import com.rs.module.dam.service.archive.ArchiveCoverService;
import com.rs.module.dam.util.CallBackFunction;
import com.rs.module.dam.util.PdfExportUtil;
import com.rs.module.dam.util.PdfGenerateUtil;
import com.rs.module.dam.vo.prisoner.PrisonerInfoListReqVO;
import com.rs.module.dam.vo.prisoner.PrisonerInfoPageReqVO;
import com.rs.module.dam.vo.prisoner.PrisonerInfoSaveReqVO;

import lombok.extern.slf4j.Slf4j;


/**
 * 卷宗监管人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PrisonerInfoServiceImpl extends BaseServiceImpl<PrisonerInfoDao, PrisonerInfoDO> implements PrisonerInfoService {

    @Resource
    private PrisonerInfoDao prisonerInfoDao;
    
    @Resource
    private ArchiveCoverService archiveCoverService;
    
    @Resource
    private MaterialInfoDao materialInfoDao;

    @Override
    public String createPrisonerInfo(PrisonerInfoSaveReqVO createReqVO) {
        // 插入
        PrisonerInfoDO prisonerInfo = BeanUtils.toBean(createReqVO, PrisonerInfoDO.class);
        prisonerInfoDao.insert(prisonerInfo);
        // 返回
        return prisonerInfo.getId();
    }

    @Override
    public void updatePrisonerInfo(PrisonerInfoSaveReqVO updateReqVO) {
        // 校验存在
        validatePrisonerInfoExists(updateReqVO.getId());
        // 更新
        PrisonerInfoDO updateObj = BeanUtils.toBean(updateReqVO, PrisonerInfoDO.class);
        prisonerInfoDao.updateById(updateObj);
    }

    @Override
    public void deletePrisonerInfo(String id) {
        // 校验存在
        validatePrisonerInfoExists(id);
        // 删除
        prisonerInfoDao.deleteById(id);
    }

    private void validatePrisonerInfoExists(String id) {
        if (prisonerInfoDao.selectById(id) == null) {
            throw new ServerException("卷宗监管人员数据不存在");
        }
    }

    @Override
    public PrisonerInfoDO getPrisonerInfo(String id) {
        return prisonerInfoDao.selectById(id);
    }

    @Override
    public PrisonerInfoDO getByDabh(String dabh) {
        return getOne(new LambdaQueryWrapper<PrisonerInfoDO>().eq(PrisonerInfoDO::getDabh, dabh), false);
    }

    @Override
    public PageResult<PrisonerInfoDO> getPrisonerInfoPage(PrisonerInfoPageReqVO pageReqVO) {
        return prisonerInfoDao.selectPage(pageReqVO);
    }

    @Override
    public List<PrisonerInfoDO> getPrisonerInfoList(PrisonerInfoListReqVO listReqVO) {
        return prisonerInfoDao.selectList(listReqVO);
    }

    /**
     * 更新卷宗状态
     * @param jgrybm String 监管人员编码
     * @param jzzt String 卷宗状态
     * @param zjsj Date 组卷时间
     * @param gdsj Date 归档时间
     */
    @Override
    public void updateJzzt(String jgrybm, String jzzt, Date zjsj, Date gdsj) {
    	LambdaUpdateChainWrapper<PrisonerInfoDO> lambdaUpdate = this.lambdaUpdate()
    		.eq(PrisonerInfoDO::getJgrybm, jgrybm)
    		.set(PrisonerInfoDO::getJzzt, jzzt);
    	if(zjsj != null) {
    		lambdaUpdate.set(PrisonerInfoDO::getZjsj, zjsj);
    	}
    	if(gdsj != null) {
    		lambdaUpdate.set(PrisonerInfoDO::getGdsj, gdsj);
    	}
    	lambdaUpdate.update();
    }
    
    /**
     * 获取卷宗导出地址
     * @param jgrybm String 监管人员编码
     * @param zjCatalog String 卷宗目录
     * @return String
     */
    @Override
    public String getExportUrl(String jgrybm, String zjCatalog) {
    	String url = null;
    	JSONArray zjCatalogArray = JSONObject.parseArray(zjCatalog);
    	if(zjCatalogArray == null || zjCatalogArray.isEmpty()) {
    		throw new RuntimeException("导出的组卷目录为空");
    	}
    	
    	Map<String, Object> params = new HashMap<>();
    	params.put("jgrybm", jgrybm);
    	
    	//包含封面的pdf生成
    	coverPdfGenerate(jgrybm, zjCatalogArray, params);
    	
    	try {
    		while(true) {
    			if(params.containsKey("url")) {
    				url = (String)params.get("url");
    				break;
    			}
    			Thread.sleep(1000);
    		}
    	}
    	catch(Exception e) {
    		log.error("【卷宗导出】发生异常，jgrybm：{}，异常信息：{}", jgrybm, e.getMessage());
    	}
    	
    	return url;
    }
    
    /**
     * 包含封面的pdf生成
     * @param jgrybm String 监管人员编码
     * @param zjCatalog String 卷宗目录
     * @param params Map<String, Object> 其它参数
     */
    @Override
    public void coverPdfGenerate(String jgrybm, JSONArray zjCatalogArray, Map<String, Object> params) {
    	List<ArchiveCoverDO> archiveCoverList = new ArrayList<>();
    	
    	//获取每个目录的封面
    	for (int i = 0; i < zjCatalogArray.size(); i++) {
            JSONObject catalogData = zjCatalogArray.getJSONObject(i);
            
            try {
                //获取卷宗封面
            	QueryWrapper<ArchiveCoverDO> coverWrapper = new QueryWrapper<ArchiveCoverDO>()
            			.eq("is_del", 0).eq("jgrybm", jgrybm).eq("fjlx", "0")
            			.eq("part_catalog_id", catalogData.get("id"));
                ArchiveCoverDO archiveCover = archiveCoverService.getOne(coverWrapper);
                
                //没有封面
                if (archiveCover == null) {
                    zjCatalogArray.remove(catalogData);
                }
                else {
                	archiveCoverList.add(archiveCover);
                }
            }
            catch (Exception e) {
                log.error("【生成pdf】发生异常：{}", e.getMessage());
            }
        }
    	
    	//通过计数器实现线程间的协作
    	CountDownLatch latch = new CountDownLatch(zjCatalogArray.size());
    	StringBuffer url = new StringBuffer();
    	
    	//循环生成pdf
    	for (int i = 0; i < zjCatalogArray.size(); i++) {
    		JSONObject catalogData = zjCatalogArray.getJSONObject(i);
    		
    		try {
    			ArchiveCoverDO archiveCover = archiveCoverList.get(i);
    			List<String> catalogList = new ArrayList<>();
    			PdfGenerateUtil.getLeftTreeId(catalogData, catalogList);
    			
    			//查询已组卷的材料
    			QueryWrapper<MaterialInfoDO> materialWrapper = new QueryWrapper<MaterialInfoDO>()
    					.eq("jgrybm", jgrybm).eq("type", "2")
    					.in("catalog_id", catalogList).orderByAsc("xh");
    			List<MaterialInfoDO> materialInfoList = materialInfoDao.selectList(materialWrapper);
    			
    			//异常生成pdf
    			PdfExportUtil.executeAsyncForPartPdf(i, zjCatalogArray.size(), archiveCover,
    					materialInfoList, false, "0", null, new CallBackFunction() {
							
						@Override
						public void execute(JSONObject jsonObject) {
							String pdfUrl = jsonObject.getString("url");
	                        if (StringUtil.isNotEmpty(pdfUrl)) {
	                            url.append(pdfUrl).append(",");
	                        }
	                        latch.countDown();
						}
					}
    			);
    		}
    		catch(Exception e) {
    			log.error("【生成pdf】发生异常：{}", e.getMessage());
    			latch.countDown();
    		}
    	}
    	
    	try {
    		latch.await();
    	}
    	catch(Exception e) {
    		log.error("【生成pdf】发生异常：{}", e.getMessage());
    	}
    	
    	//绑定url到参数中
    	String pdfUrl = "";
    	if (StringUtil.isNotEmpty(url.toString())) {
            pdfUrl = url.toString().substring(0, url.length() - 1);
        }
    	params.put("url", pdfUrl);
    }
}
