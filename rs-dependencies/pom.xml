<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.4.3</version>
		<relativePath />
	</parent>
	<groupId>com.rs</groupId>
	<artifactId>rs-dependencies</artifactId>
	<version>${rs.version}</version>
	<packaging>pom</packaging>
	<name>${project.artifactId}</name>
	<description>监管实战平台-依赖管理模块，管理整个项目的依赖版本</description>

	<properties>
		<rs.version>1.0.0</rs.version>
		<flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>

		<!-- spring 相关 -->
		<spring.framework.version>5.3.4</spring.framework.version>
		<spring.security.version>5.0.3.RELEASE</spring.security.version>
		<spring.boot.version>2.4.3</spring.boot.version>
		<spring-cloud.version>2020.0.1</spring-cloud.version>
		<spring-cloud.nacos.version>2021.1</spring-cloud.nacos.version>
		<spring.boot.validation.version>2.7.18</spring.boot.validation.version>

		<!-- web 相关 -->
		<jakarta.servlet.version>4.0.4</jakarta.servlet.version>
		<springdoc.version>1.7.0</springdoc.version>

		<!-- bsp 相关 -->
		<bsp.security.version>1.4-20240315</bsp.security.version>
		<bsp.common.version>1.3.1-20240827</bsp.common.version>
		<bsp.sdk.version>1.3.1-20240827</bsp.sdk.version>

		<!-- DB 相关 -->
		<druid.version>1.1.24</druid.version>
		<mybatis.version>3.5.17</mybatis.version>
		<mybatis.spring.version>2.0.5</mybatis.spring.version>
        <mybatis-plus.version>3.4.2</mybatis-plus.version>
        <mybatis-plus-join.version>1.4.13</mybatis-plus-join.version>
		<dynamic-datasource.version>4.3.1</dynamic-datasource.version>
		<easy-trans.version>3.0.6</easy-trans.version>
		<redisson.version>3.41.0</redisson.version>
		<mysql.jdbc.version>8.0.11</mysql.jdbc.version>
		<dm8.jdbc.version>8.1.3.140</dm8.jdbc.version>
		<kingbase.jdbc.version>8.6.0</kingbase.jdbc.version>
		<postgresql.version>42.2.18</postgresql.version>

		<!-- 定时任务相关 -->
		<xxl-job.version>2.4.0</xxl-job.version>

		<!-- 监控相关 -->
        <skywalking.version>9.2.0</skywalking.version>
        <opentracing.version>0.33.0</opentracing.version>

		<!-- 工具类相关 -->
		<aspectj.version>1.9.6</aspectj.version>
		<slf4j-api.version>1.7.30</slf4j-api.version>
		<lombok.version>1.18.36</lombok.version>
		<hutool.version>5.8.37</hutool.version>
		<transmittable-thread-local.version>2.14.5</transmittable-thread-local.version>
		<guava.version>33.4.0-jre</guava.version>
		<poi.version>4.1.2</poi.version>
		<ooxml.schemas.version>1.3</ooxml.schemas.version>
		<jackson.version>2.11.4</jackson.version>
		<fastjson.version>1.2.83</fastjson.version>
		<easyexcel.version>3.1.4</easyexcel.version>
		<pinyin4j.version>2.5.1</pinyin4j.version>
		<okio.version>3.3.0</okio.version>
		<knife4j.version>2.0.7</knife4j.version>
		<knife4j.gateway.version>4.0.0</knife4j.gateway.version>
		<mpj.version>1.4.13</mpj.version>
		<commons.io.version>2.5</commons.io.version>
		<commons.io.version>2.5</commons.io.version>
		<apm-toolkit.version>9.2.0</apm-toolkit.version>
		<easyexcel.version>4.0.3</easyexcel.version>
		<x-file-storage.version>2.2.1</x-file-storage.version>
		<minio.version>8.5.2</minio.version>
		<okhttp.version>4.12.0</okhttp.version>
		<commons.scxml.version>0.9</commons.scxml.version>
		<statemachine.core.version>4.0.0</statemachine.core.version>
		<beansearch.version>4.3.6</beansearch.version>
		<ibeetl.version>3.31-RELEASE</ibeetl.version>
		<jpa.version>3.4.4</jpa.version>
		<beetl.version>3.15.14.RELEASE</beetl.version>
		<validation-api.version>2.0.2</validation-api.version>
        <pdfbox.version>2.0.19</pdfbox.version>
        <jbig2-imageio.version>2.0</jbig2-imageio.version>
        <mapstruct.version>1.3.1.Final</mapstruct.version>
        <pdfbox-imageio.version>3.0.2</pdfbox-imageio.version>
        <jai-imageio.version>1.3.1</jai-imageio.version>
        <webp-imageio.version>0.1.6</webp-imageio.version>
        <ofdrw.version>1.17.8</ofdrw.version>
        <itextpdf-kernel.version>7.1.10</itextpdf-kernel.version>
        <qrcode.version>1.0</qrcode.version>
        <common-text.version>1.9</common-text.version>
        <joda-time.version>2.10.5</joda-time.version>
        <mvel2.version>2.4.14.Final</mvel2.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<!-- spring 相关 begin -->
			<dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId> <!-- JDK8 版本独有：保证 Spring Framework 尽量高 -->
                <version>${spring.framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
	            <groupId>org.springframework.boot</groupId>
	            <artifactId>spring-boot-starter</artifactId>
	            <version>${spring.boot.version}</version>
	        </dependency>
	        <dependency>
	            <groupId>org.springframework.security</groupId>
	            <artifactId>spring-security-core</artifactId>
	            <version>${spring.security.version}</version>
	        </dependency>
	        <dependency>
	            <groupId>org.springframework.security</groupId>
	            <artifactId>spring-security-config</artifactId>
	            <version>${spring.security.version}</version>
	        </dependency>
	        <dependency>
	            <groupId>org.springframework.security</groupId>
	            <artifactId>spring-security-web</artifactId>
	            <version>${spring.security.version}</version>
	        </dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
				<version>${spring-cloud.nacos.version}</version>
			</dependency>
			<dependency>
	            <!-- 用于生成自定义的 Spring @ConfigurationProperties 配置类的说明文件 -->
	            <groupId>org.springframework.boot</groupId>
	            <artifactId>spring-boot-configuration-processor</artifactId>
	            <version>${spring.boot.version}</version>
	            <optional>true</optional>
	        </dependency>
	        <dependency>
	            <groupId>org.springframework.boot</groupId>
	            <artifactId>spring-boot-starter-websocket</artifactId>
	            <version>${spring.boot.version}</version>
	        </dependency>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-test</artifactId>
				<version>${spring.boot.version}</version>
			</dependency>
			<!-- spring 相关 end -->

			<!-- Web 相关 begin -->
			<dependency>
	            <groupId>org.springframework.boot</groupId>
	            <artifactId>spring-boot-starter-web</artifactId>
	            <version>${spring.boot.version}</version>
	        </dependency>
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-starter-web</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<dependency>
	            <groupId>jakarta.servlet</groupId>
	            <artifactId>jakarta.servlet-api</artifactId>
	            <version>${jakarta.servlet.version}</version>
	        </dependency>
			<dependency>
                <groupId>org.springdoc</groupId> <!-- 接口文档 UI：默认 -->
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <!-- Web 相关 end -->

			<!-- bsp 相关 begin -->
			<dependency>
				<groupId>com.bsp</groupId>
				<artifactId>bsp-security</artifactId>
				<version>${bsp.security.version}</version>
			</dependency>
			<dependency>
				<groupId>com.bsp</groupId>
				<artifactId>bsp-common</artifactId>
				<version>${bsp.common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.bsp</groupId>
				<artifactId>bsp-plus-sdk</artifactId>
				<version>${bsp.sdk.version}</version>
			</dependency>
			<!-- bsp 相关 end -->

			<!-- DB 相关 begin -->
			<dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
				<groupId>org.mybatis</groupId>
				<artifactId>mybatis-spring</artifactId>
				<version>${mybatis.spring.version}</version>
			</dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
			    <groupId>com.baomidou</groupId>
			    <artifactId>mybatis-plus-extension</artifactId>
			    <version>${mybatis-plus.version}</version>
			</dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser-4.9</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId> <!-- 代码生成器，使用它解析表结构 -->
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId> <!-- 多数据源 -->
                <version>${dynamic-datasource.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId> <!-- MyBatis 联表查询 -->
                <version>${mybatis-plus-join.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fhs-opensource</groupId> <!-- VO 数据翻译 -->
                <artifactId>easy-trans-spring-boot-starter</artifactId>
                <version>${easy-trans.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-commons</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fhs-opensource</groupId>
                <artifactId>easy-trans-mybatis-plus-extend</artifactId>
                <version>${easy-trans.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fhs-opensource</groupId>
                <artifactId>easy-trans-anno</artifactId>
                <version>${easy-trans.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-actuator</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.redisson</groupId>
                        <!-- 使用 redisson-spring-data-27 替代，解决 Tuple NoClassDefFoundError 报错 -->
                        <artifactId>redisson-spring-data-34</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-data-27</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
				<groupId>mysql</groupId>
				<artifactId>mysql-connector-java</artifactId>
				<version>${mysql.jdbc.version}</version>
			</dependency>
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dm8.jdbc.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.kingbase</groupId>
                <artifactId>kingbase8</artifactId>
                <version>${kingbase.jdbc.version}</version>
            </dependency>
            <dependency>
				<groupId>org.postgresql</groupId>
				<artifactId>postgresql</artifactId>
				<version>${postgresql.version}</version>
			</dependency>
			<!-- DB 相关 end -->

			<!-- 定时任务相关 begin -->
			<dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-starter-job</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<!-- 定时任务相关 end -->

			<!-- 监控相关 begin -->
	        <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-opentracing</artifactId>
                <version>${skywalking.version}</version>
	        </dependency>
	        <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-api</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-util</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-noop</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
        	<!-- 监控相关 end -->

			<!-- 工具类相关 begin -->
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-common</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<dependency>
	            <groupId>org.aspectj</groupId>
	            <artifactId>aspectjweaver</artifactId>
	            <version>${aspectj.version}</version>
	        </dependency>
			<dependency>
				<groupId>org.projectlombok</groupId>
				<artifactId>lombok</artifactId>
				<version>${lombok.version}</version>
				<optional>true</optional>
			</dependency>
			<dependency>
				<groupId>cn.hutool</groupId>
			  	<artifactId>hutool-all-u</artifactId>
			  	<version>${hutool.version}</version>
			</dependency>
			<dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId> <!-- 解决 ThreadLocal 父子线程的传值问题 -->
                <version>${transmittable-thread-local.version}</version>
            </dependency>
            <dependency>
	            <groupId>org.slf4j</groupId>
	            <artifactId>slf4j-api</artifactId>
	            <version>${slf4j-api.version}</version>
	        </dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>${guava.version}</version>
			</dependency>
			<dependency>
	            <groupId>org.apache.poi</groupId>
	            <artifactId>poi</artifactId>
	            <version>${poi.version}</version>
	        </dependency>
	        <dependency>
	            <groupId>org.apache.poi</groupId>
	            <artifactId>poi-ooxml</artifactId>
	            <version>${poi.version}</version>
	        </dependency>
	        <dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>ooxml-schemas</artifactId>
				<version>${ooxml.schemas.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>easyexcel</artifactId>
				<version>${easyexcel.version}</version>
			</dependency>
			<dependency>
				<!-- 第三方拼音依赖包，配合 hutool-all 包中的 PinyinUtil 拼音工具使用 start -->
				<groupId>com.belerweb</groupId>
				<artifactId>pinyin4j</artifactId>
				<version>${pinyin4j.version}</version>
			</dependency>
			<dependency>
				<groupId>com.squareup.okio</groupId>
				<artifactId>okio</artifactId>
				<version>${okio.version}</version>
			</dependency>
			<dependency>
	            <groupId>com.fhs-opensource</groupId> <!-- VO 数据翻译 -->
	            <artifactId>easy-trans-anno</artifactId> <!-- 默认引入的原因，方便 xxx-module-api 包使用 -->
	        </dependency>
	        <dependency>
	            <groupId>org.mapstruct</groupId>
	            <artifactId>mapstruct</artifactId>
	            <version>${mapstruct.version}</version>
	        </dependency>
	        <dependency>
	            <groupId>com.fasterxml.jackson.core</groupId>
	            <artifactId>jackson-databind</artifactId>
	            <version>${jackson.version}</version>
	        </dependency>
	        <dependency>
	            <groupId>com.fasterxml.jackson.core</groupId>
	            <artifactId>jackson-core</artifactId>
	            <version>${jackson.version}</version>
	        </dependency>
	        <dependency>
	            <groupId>com.fasterxml.jackson.datatype</groupId>
	            <artifactId>jackson-datatype-jsr310</artifactId>
	            <version>${jackson.version}</version>
	        </dependency>
	        <dependency>
	            <groupId>jakarta.validation</groupId>
	            <artifactId>jakarta.validation-api</artifactId>
	            <version>${validation-api.version}</version>
	        </dependency>
	        <dependency>
		        <groupId>org.springframework.boot</groupId>
		        <artifactId>spring-boot-starter-validation</artifactId>
		        <version>${spring.boot.validation.version}</version>
	      	</dependency>
	      	<dependency>
		        <groupId>com.ibeetl</groupId>
		        <artifactId>beetl</artifactId>
		        <version>${beetl.version}</version>
			</dependency>
			<dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>fontbox</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>
            <dependency>
                <groupId>com.levigo.jbig2</groupId>
                <artifactId>levigo-jbig2-imageio</artifactId>
                <version>${jbig2-imageio.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>jbig2-imageio</artifactId>
                <version>${pdfbox-imageio.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.jai-imageio</groupId>
                <artifactId>jai-imageio-core</artifactId>
                <version>${jai-imageio.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jai-imageio</groupId>
                <artifactId>jai-imageio-jpeg2000</artifactId>
                <version>${jai-imageio.version}</version>
            </dependency>
            <dependency>
			    <groupId>org.sejda.imageio</groupId>
			    <artifactId>webp-imageio</artifactId>
			    <version>${webp-imageio.version}</version>
			</dependency>
            <dependency>
                <groupId>org.ofdrw</groupId>
                <artifactId>ofdrw-full</artifactId>
                <version>${ofdrw.version}</version>
            </dependency>
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>kernel</artifactId>
                <version>${itextpdf-kernel.version}</version>
            </dependency>
            <dependency>
				<groupId>com.swetake</groupId>
				<artifactId>QRCode</artifactId>
				<version>${qrcode.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-text</artifactId>
				<version>${common-text.version}</version>
			</dependency>
			<!-- 工具类相关 end -->

			<!-- 技术组件 begin -->
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-starter-job</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-starter-rpc</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<!-- 技术组件 end -->

			<!-- api组件 begin -->
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-module-infra-api</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-module-ihc-api</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-module-tem-api</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-module-sps-api</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-module-dam-api</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-adapter-bsp-api</artifactId>
				<version>${rs.version}</version>
			</dependency>

			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-adapter-zhjg-api</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-adapter-zhjg-biz</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-module-acp-api</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-module-pam-api</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-module-ptm-api</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<!-- api组件 end -->

			<!-- 其它组件 begin -->
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-module-dam-core</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<!-- 其它组件 end -->

            <dependency>
                <groupId>com.rs</groupId>
                <artifactId>rs-starter-mybatis</artifactId>
                <version>${rs.version}</version>
            </dependency>
			<dependency>
                <groupId>com.rs</groupId>
                <artifactId>rs-starter-liquibase</artifactId>
                <version>${rs.version}</version>
            </dependency>
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-starter-pdf</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<dependency>
                <groupId>com.rs</groupId>
                <artifactId>rs-starter-oss</artifactId>
                <version>${rs.version}</version>
            </dependency>
			<dependency>
                <groupId>com.rs</groupId>
                <artifactId>rs-module-base</artifactId>
                <version>${rs.version}</version>
            </dependency>
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-module-third-api</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rs</groupId>
				<artifactId>rs-module-db</artifactId>
				<version>${rs.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.xiaoymin</groupId>
				<artifactId>knife4j-spring-boot-starter</artifactId>
				<version>${knife4j.version}</version>
			</dependency>
			<dependency>
                <groupId>com.github.xiaoymin</groupId> <!-- 接口文档 UI：knife4j【网关专属】 -->
                <artifactId>knife4j-gateway-spring-boot-starter</artifactId>
                <version>${knife4j.gateway.version}</version>
            </dependency>
			<dependency>
				<groupId>com.github.yulichang</groupId>
				<artifactId>mybatis-plus-join-boot-starter</artifactId>
				<version>${mpj.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>${commons.io.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>easyexcel</artifactId>
				<version>${easyexcel.version}</version>
			</dependency>
			<dependency>
				<groupId>org.dromara.x-file-storage</groupId>
				<artifactId>x-file-storage-spring</artifactId>
				<version>${x-file-storage.version}</version>
			</dependency>
			<dependency>
				<groupId>io.minio</groupId>
				<artifactId>minio</artifactId>
				<version>${minio.version}</version>
			</dependency>
			<dependency>
				<groupId>com.squareup.okhttp3</groupId>
				<artifactId>okhttp</artifactId>
				<version>${okhttp.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.statemachine</groupId>
				<artifactId>spring-statemachine-core</artifactId>
				<version>${statemachine.core.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.zhxu</groupId>
				<artifactId>bean-searcher-boot-starter</artifactId>
				<version>${beansearch.version}</version>
			</dependency>
			<dependency>
				<groupId>com.ibeetl</groupId>
				<artifactId>sql-springboot-starter</artifactId>
				<version>${ibeetl.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-websocket</artifactId>
				<version>${spring.boot.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-data-jpa</artifactId>
				<version>${spring.boot.version}</version>
			</dependency>
			<dependency>
				<groupId>joda-time</groupId>
				<artifactId>joda-time</artifactId>
				<version>${joda-time.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mvel</groupId>
				<artifactId>mvel2</artifactId>
				<version>${mvel2.version}</version>
			</dependency>
			<dependency>
				<groupId>com.antherd</groupId>
				<artifactId>sm-crypto</artifactId>
				<version>0.3.2</version>
			</dependency>
			<dependency>
				<groupId>org.junit.platform</groupId>
				<artifactId>junit-platform-launcher</artifactId>
				<version>1.7.1</version>
			</dependency>
			<dependency>
				<groupId>org.junit.vintage</groupId>
				<artifactId>junit-vintage-engine</artifactId>
				<version>5.7.0</version>
			</dependency>
			<dependency>
				<groupId>org.liquibase</groupId>
				<artifactId>liquibase-core</artifactId>
				<version>4.26.0</version>
			</dependency>

		</dependencies>
	</dependencyManagement>

	<build>
        <plugins>
            <!-- 统一 revision 版本 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <flattenMode>bom</flattenMode>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                    </execution>
                    <execution>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>

