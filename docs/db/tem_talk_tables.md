## 谈话教育相关表结构文档

生成时间：2025-08-19

---

### 概览
- 本文档基于数据库元数据生成，包含以下两张表的字段结构：
  - tem_talk_task（谈话教育-谈话任务表）
  - tem_talk_record（谈话教育-个人谈话教育表）
- 说明：当前数据来源未包含主键/索引/外键等约束信息；如需补充请告知。

---

## 表：tem_talk_task（谈话教育-谈话任务表）

字段列表：

| 字段名 | 数据类型 | 长度 | 非空 | 默认值 | 说明 |
|---|---:|---:|:---:|:---:|---|
| id | varchar | 32 | 是 |  |  |
| is_del | int2 | 5 | 是 | 0 | 逻辑删除标识（0-未删） |
| add_time | timestamp | 29 | 是 | CURRENT_TIMESTAMP | 创建时间 |
| add_user | varchar | 50 | 否 |  | 创建人ID |
| add_user_name | varchar | 30 | 否 |  | 创建人姓名 |
| update_time | timestamp | 29 | 否 |  | 更新时间 |
| update_user | varchar | 50 | 否 |  | 更新人ID |
| update_user_name | varchar | 30 | 否 |  | 更新人姓名 |
| pro_code | varchar | 50 | 否 |  | 省级编码 |
| pro_name | varchar | 100 | 否 |  | 省级名称 |
| city_code | varchar | 50 | 否 |  | 市级编码 |
| city_name | varchar | 100 | 否 |  | 市级名称 |
| reg_code | varchar | 50 | 否 |  | 区县编码 |
| reg_name | varchar | 100 | 否 |  | 区县名称 |
| org_code | varchar | 50 | 是 |  | 机构编码 |
| org_name | varchar | 100 | 是 |  | 机构名称 |
| talk_source | varchar | 10 | 否 |  | 谈话来源 |
| talk_code | varchar | 50 | 否 |  | 谈话编号 |
| jgrybm | varchar | 64 | 否 |  | 监管人员编码 |
| ryxm | varchar | 50 | 否 |  | 人员姓名 |
| talk_reason | varchar | 100 | 否 |  | 谈话事由 |
| talk_user_sfzh | varchar | 18 | 否 |  | 谈话对象身份证号 |
| talk_user_name | varchar | 50 | 否 |  | 谈话对象姓名 |
| jsh | varchar | 32 | 否 |  | 监室号 |
| task_source | varchar | 255 | 否 |  | 任务来源描述 |
| video_url | varchar | 255 | 否 |  | 视频地址 |

---

## 表：tem_talk_record（谈话教育-个人谈话教育表）

字段列表：

| 字段名 | 数据类型 | 长度 | 非空 | 默认值 | 说明 |
|---|---:|---:|:---:|:---:|---|
| id | varchar | 32 | 是 |  |  |
| is_del | int2 | 5 | 是 | 0 | 逻辑删除标识（0-未删） |
| add_time | timestamp | 29 | 是 | CURRENT_TIMESTAMP | 创建时间 |
| add_user | varchar | 50 | 否 |  | 创建人ID |
| add_user_name | varchar | 30 | 否 |  | 创建人姓名 |
| update_time | timestamp | 29 | 否 |  | 更新时间 |
| update_user | varchar | 50 | 否 |  | 更新人ID |
| update_user_name | varchar | 30 | 否 |  | 更新人姓名 |
| pro_code | varchar | 50 | 否 |  | 省级编码 |
| pro_name | varchar | 100 | 否 |  | 省级名称 |
| city_code | varchar | 50 | 否 |  | 市级编码 |
| city_name | varchar | 100 | 否 |  | 市级名称 |
| reg_code | varchar | 50 | 否 |  | 区县编码 |
| reg_name | varchar | 100 | 否 |  | 区县名称 |
| org_code | varchar | 50 | 是 |  | 机构编码 |
| org_name | varchar | 100 | 是 |  | 机构名称 |
| jgrybm | varchar | 64 | 否 |  | 监管人员编码 |
| task_source | varchar | 10 | 否 |  | 任务来源 |
| record_type | varchar | 64 | 否 |  | 记录类型 |
| talk_code | varchar | 50 | 否 |  | 谈话编号 |
| talk_reason | varchar | 64 | 否 |  | 谈话事由 |
| talk_room_id | varchar | 32 | 否 |  | 谈话室ID |
| talk_room_name | varchar | 128 | 否 |  | 谈话室名称 |
| start_time | timestamp | 29 | 否 |  | 开始时间 |
| end_time | timestamp | 29 | 否 |  | 结束时间 |
| talk_content | text | - | 否 |  | 谈话内容 |
| talk_summary | varchar | 255 | 否 |  | 谈话小结 |
| tag_id | varchar | 255 | 否 |  | 标签ID（可能为多值） |
| tag_name | varchar | 255 | 否 |  | 标签名称（可能为多值） |
| psychology_assess | varchar | 64 | 否 |  | 心理评估 |
| job_collaboration | varchar | 64 | 否 |  | 工作协同 |
| push_userid | varchar | 255 | 否 |  | 推送用户ID（可能为多值） |
| push_user_name | varchar | 255 | 否 |  | 推送用户姓名（可能为多值） |
| sign_url | varchar | 255 | 否 |  | 签名图片地址 |
| seal_url | varchar | 255 | 否 |  | 盖章图片地址 |
| sign_seal_url | varchar | 255 | 否 |  | 签名盖章合成图地址 |
| video_url | varchar | 255 | 否 |  | 视频地址 |
| video_url2 | varchar | 255 | 否 |  | 视频地址2 |
| timer | varchar | 255 | 否 |  | 计时信息 |

---

### 备注
- 字段说明列为基于字段名的合理推断，如与实际业务含义不符，请提供字典或注释信息以便完善。
- 如需补充主键/索引/外键/唯一约束、样例SQL、ER图，请告知具体需求。
